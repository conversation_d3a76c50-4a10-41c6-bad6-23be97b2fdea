import OpenAI from 'openai';
import 'dotenv/config';
import { zodToJsonSchema } from 'zod-to-json-schema';
import {
  DictionaryResponse,
  DictionaryResponseWithWordCountType,
  HttpStatusCode,
  DictionaryQueryResult,
  processApiResponse,
} from '../utils/dict-types'; // Updated import path
import { processTokens, logPerformance, DEFAULT_WORD_LIST } from '../utils/dict-utils'; // Updated import path

/**
 * 默认示例词列表（逗号分隔）。
 * 可以根据需要替换成任意词列表以获取相应释义。
 */
const DEFAULT_PROMPT = 'world,apple,banana,cherry,date,elderberry,fig,grape,honeydew,wiki'; // This seems unused now, DEFAULT_WORD_LIST is imported

// 默认 OpenAI 模型
const DEFAULT_MODEL = 'gpt-4o';

// 初始化默认 OpenAI 客户端
const defaultClient = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
});

/**
 * 从 OpenAI API 获取单词释义
 * @param {string} [customPrompt=DEFAULT_WORD_LIST] - 自定义单词列表（逗号分隔），不传则使用默认词表
 * @param {OpenAI} [client=defaultClient] - 自定义 OpenAI 客户端
 * @param {string} [model=DEFAULT_MODEL] - 自定义模型
 * @returns {Promise<DictionaryQueryResult>} 返回字典查询结果，包含数据、错误信息、状态码等
 */
export const fetchDictionary = async (
  customPrompt: string = DEFAULT_WORD_LIST,
  client: OpenAI = defaultClient,
  model: string = DEFAULT_MODEL
): Promise<DictionaryQueryResult> => {
  const startTime = Date.now();
  const logApiTime = logPerformance(startTime, 'OpenAI API调用');

  // 处理并验证输入词条
  const { validTokens, invalidTokens } = processTokens(customPrompt);

  // 如果全部非法，直接返回 400
  if (validTokens.length === 0) {
    return {
      error: { message: '全部词条非法，已取消查询' },
      info: { invalidTokens },
      statusCode: HttpStatusCode.BAD_REQUEST,
    };
  }

  try {
    // 转换 Zod schema 为 JSON schema
    const jsonSchema = zodToJsonSchema(DictionaryResponse);

    const apiCallStartTime = Date.now();

    // 调用 OpenAI API
    const apiResponse = await client.chat.completions.create({
      model: model,
      messages: [
        {
          role: 'system',
          content: `You are a helpful dictionary. Respond ONLY with a JSON object that conforms to the provided JSON schema.

                    The input words may include:
                    1. Common English words
                    2. Technical abbreviations (e.g., 'k8s', 'i18n')
                    3. Brand names (e.g., '1password')
                    4. Proper nouns
                    5. Phrases or word combinations with spaces (e.g., 'good job', 'machine learning')

                    Treat all these as valid terms for definition.

                    For each word or phrase provided:
                    1. If it is a special form of a common word (e.g., plural like 'bananas', past tense like 'walked'), provide the definition for that specific form. The 'word' field in your response MUST be that exact special form (e.g., 'bananas', not 'banana').
                    2. If it is a technical term, abbreviation, or proper noun like 'k8s' or '1password', provide its definition or a description of what it refers to, if known.
                    3. If it is a multi-word phrase (e.g., 'good job', 'machine learning'), treat it as a single term and provide its definition accordingly.
                    4. If a word or phrase is not found in your knowledge base or you cannot provide a definition for it, include that exact word or phrase in an 'illegalWords' array in the JSON response. Do not attempt to guess or define words you don't recognize.

                    Ensure your entire response is a single, valid JSON object adhering to the schema provided by the user in the next message.`,
        },
        {
          role: 'user',
          content: `Provide definitions for the following words: ${validTokens.join(',')}.
                    The response must strictly follow this JSON schema: ${JSON.stringify(jsonSchema)}.
                    Do not include any other text or markdown formatting.`,
        },
      ],
      response_format: { type: 'json_object' },
    });

    const apiCallEndTime = Date.now();
    const apiCallDuration = (apiCallEndTime - apiCallStartTime) / 1000;
    console.log(`API call completed in ${apiCallDuration.toFixed(2)} seconds`);

    // 检查API响应
    const responseContent = apiResponse.choices[0]?.message.content;
    if (!responseContent) {
      return {
        error: { message: '未找到词条或解析失败' },
        statusCode: HttpStatusCode.NOT_FOUND,
      };
    }

    // 处理API响应
    const result = processApiResponse(responseContent, invalidTokens);

    // 记录总耗时
    logApiTime();

    return result;
  } catch (error: unknown) {
    console.error('Error calling OpenAI API:', error);

    // 类型守卫，检查 error 是否具有 status 属性
    const errorStatus =
      error && typeof error === 'object' && 'status' in error
        ? (error.status as number)
        : HttpStatusCode.INTERNAL_SERVER_ERROR;

    // 改进错误信息的序列化
    let errorInfo: Record<string, any> = {};

    if (error instanceof Error) {
      errorInfo = {
        message: error.message,
        name: error.name,
        stack: error.stack,
      };
      // 捕获额外属性
      Object.entries(error).forEach(([key, value]) => {
        errorInfo[key] = value;
      });
    } else if (error && typeof error === 'object') {
      try {
        // 尝试复制所有可枚举属性
        errorInfo = { ...error };
      } catch (e) {
        errorInfo = {
          description: String(error),
          serializationError: String(e),
        };
      }
    } else {
      errorInfo = { description: String(error) };
    }

    return {
      error: errorInfo,
      statusCode: errorStatus,
    };
  }
};
