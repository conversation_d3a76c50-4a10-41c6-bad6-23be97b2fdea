/**
 * RSA Key Management - Enhanced Security Module
 * RSA密钥管理 - 增强安全模块
 *
 * Features:
 * - 持久化RSA密钥存储
 * - 密钥验证和完整性检查
 * - 密钥轮换机制
 * - 重启后密钥一致性
 * - 安全的密钥备份和恢复
 * - 多版本密钥支持
 * - 密钥强度验证
 * - 审计日志记录
 */

import { generateKeyPairSync, createHash, randomBytes } from 'crypto';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { promisify } from 'util';
import jwt from 'jsonwebtoken';

// 密钥配置常量
const KEY_CONFIG = {
  // RSA密钥参数
  RSA_MODULUS_LENGTH: 2048,
  RSA_PUBLIC_EXPONENT: 0x10001,

  // 密钥存储配置
  KEY_DIRECTORY: process.env.JWT_KEY_DIRECTORY || join(process.cwd(), '.keys'),
  PRIVATE_KEY_FILE: 'rsa-private.pem',
  PUBLIC_KEY_FILE: 'rsa-public.pem',
  KEY_METADATA_FILE: 'key-metadata.json',

  // 密钥轮换配置
  KEY_ROTATION_INTERVAL: 30 * 24 * 60 * 60 * 1000, // 30天
  KEY_OVERLAP_PERIOD: 7 * 24 * 60 * 60 * 1000, // 7天
  MAX_KEY_VERSIONS: 5,

  // 安全配置
  KEY_FINGERPRINT_LENGTH: 32,
  ENCRYPTION_ALGORITHM: 'aes-256-gcm',
  ENCRYPTION_KEY_LENGTH: 32,
};

// 密钥元数据接口
interface KeyMetadata {
  version: number;
  created: number;
  expires: number;
  fingerprint: string;
  algorithm: string;
  keySize: number;
  status: 'active' | 'rotating' | 'deprecated' | 'revoked';
  rotationScheduled?: number;
  previousVersion?: number;
  nextVersion?: number;
}

// 密钥版本管理
interface KeyVersion {
  version: number;
  privateKey: string;
  publicKey: string;
  metadata: KeyMetadata;
}

// 密钥管理器类
class RSAKeyManager {
  private static instance: RSAKeyManager | null = null;
  private currentKey: KeyVersion | null = null;
  private keyVersions: Map<number, KeyVersion> = new Map();
  private initialized = false;
  private keyDirectory: string;
  private encryptionKey: Buffer | null = null;

  private constructor() {
    this.keyDirectory = KEY_CONFIG.KEY_DIRECTORY;
    this.initializeSecurityContext();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): RSAKeyManager {
    if (!RSAKeyManager.instance) {
      RSAKeyManager.instance = new RSAKeyManager();
    }
    return RSAKeyManager.instance;
  }

  /**
   * 初始化安全上下文
   */
  private initializeSecurityContext(): void {
    // 确保密钥目录存在
    if (!existsSync(this.keyDirectory)) {
      mkdirSync(this.keyDirectory, { recursive: true, mode: 0o700 });
    }

    // 生成或加载加密密钥
    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  /**
   * 初始化密钥管理器
   */
  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 优先从环境变量加载
      if (await this.loadFromEnvironment()) {
        this.logSecurityEvent('key_loaded_from_environment', true);
      }
      // 然后尝试从文件系统加载
      else if (await this.loadFromFileSystem()) {
        this.logSecurityEvent('key_loaded_from_filesystem', true);
      }
      // 最后生成新密钥
      else {
        await this.generateNewKeyPair();
        this.logSecurityEvent('new_key_generated', true);
      }

      // 验证密钥完整性
      await this.validateKeyIntegrity();

      // 检查是否需要轮换
      await this.checkKeyRotationNeeded();

      this.initialized = true;
      this.logSecurityEvent('key_manager_initialized', true);
    } catch (error) {
      this.logSecurityEvent('key_manager_initialization_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to initialize RSA key manager');
    }
  }

  /**
   * 从环境变量加载密钥
   */
  private async loadFromEnvironment(): Promise<boolean> {
    const privateKey = process.env.JWT_PRIVATE_KEY?.replace(/\\n/g, '\n');
    const publicKey = process.env.JWT_PUBLIC_KEY?.replace(/\\n/g, '\n');

    if (!privateKey || !publicKey) {
      return false;
    }

    try {
      // 验证密钥格式
      if (!this.validateKeyFormat(privateKey, publicKey)) {
        throw new Error('Invalid key format in environment variables');
      }

      // 创建密钥版本
      const version = Date.now();
      const metadata: KeyMetadata = {
        version,
        created: version,
        expires: version + KEY_CONFIG.KEY_ROTATION_INTERVAL,
        fingerprint: this.calculateKeyFingerprint(publicKey),
        algorithm: 'RS256',
        keySize: this.extractKeySize(publicKey),
        status: 'active',
      };

      const keyVersion: KeyVersion = {
        version,
        privateKey,
        publicKey,
        metadata,
      };

      this.currentKey = keyVersion;
      this.keyVersions.set(version, keyVersion);

      // 可选：将环境变量中的密钥保存到文件系统
      await this.saveToFileSystem();

      return true;
    } catch (error) {
      this.logSecurityEvent('environment_key_validation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  /**
   * 从文件系统加载密钥
   */
  private async loadFromFileSystem(): Promise<boolean> {
    try {
      const metadataPath = join(this.keyDirectory, KEY_CONFIG.KEY_METADATA_FILE);

      if (!existsSync(metadataPath)) {
        return false;
      }

      const metadataContent = readFileSync(metadataPath, 'utf8');
      const allMetadata: KeyMetadata[] = JSON.parse(metadataContent);

      // 加载所有密钥版本
      for (const metadata of allMetadata) {
        const keyVersion = await this.loadKeyVersion(metadata.version);
        if (keyVersion) {
          // 使用文件中的元数据而不是重新生成
          keyVersion.metadata = metadata;
          this.keyVersions.set(metadata.version, keyVersion);

          // 设置当前活跃密钥
          if (metadata.status === 'active') {
            this.currentKey = keyVersion;
          }
        }
      }

      return this.currentKey !== null;
    } catch (error) {
      this.logSecurityEvent('filesystem_key_load_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  /**
   * 加载特定版本的密钥
   */
  private async loadKeyVersion(version: number): Promise<KeyVersion | null> {
    try {
      const privateKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PRIVATE_KEY_FILE}.${version}`);
      const publicKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PUBLIC_KEY_FILE}.${version}`);

      if (!existsSync(privateKeyPath) || !existsSync(publicKeyPath)) {
        return null;
      }

      const privateKey = readFileSync(privateKeyPath, 'utf8');
      const publicKey = readFileSync(publicKeyPath, 'utf8');

      // 验证密钥格式
      if (!this.validateKeyFormat(privateKey, publicKey)) {
        throw new Error(`Invalid key format for version ${version}`);
      }

      // 临时元数据，将在loadFromFileSystem中被实际元数据覆盖
      const tempMetadata: KeyMetadata = {
        version,
        created: version,
        expires: version + KEY_CONFIG.KEY_ROTATION_INTERVAL,
        fingerprint: this.calculateKeyFingerprint(publicKey),
        algorithm: 'RS256',
        keySize: this.extractKeySize(publicKey),
        status: 'active',
      };

      return {
        version,
        privateKey,
        publicKey,
        metadata: tempMetadata,
      };
    } catch (error) {
      this.logSecurityEvent('key_version_load_failed', false, {
        version,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  /**
   * 生成新的RSA密钥对
   */
  private async generateNewKeyPair(): Promise<void> {
    try {
      const { privateKey, publicKey } = generateKeyPairSync('rsa', {
        modulusLength: KEY_CONFIG.RSA_MODULUS_LENGTH,
        publicExponent: KEY_CONFIG.RSA_PUBLIC_EXPONENT,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
        },
      });

      // 验证生成的密钥
      if (!this.validateKeyFormat(privateKey, publicKey)) {
        throw new Error('Generated key validation failed');
      }

      // 创建密钥版本
      const version = Date.now();
      const metadata: KeyMetadata = {
        version,
        created: version,
        expires: version + KEY_CONFIG.KEY_ROTATION_INTERVAL,
        fingerprint: this.calculateKeyFingerprint(publicKey),
        algorithm: 'RS256',
        keySize: KEY_CONFIG.RSA_MODULUS_LENGTH,
        status: 'active',
      };

      const keyVersion: KeyVersion = {
        version,
        privateKey,
        publicKey,
        metadata,
      };

      this.currentKey = keyVersion;
      this.keyVersions.set(version, keyVersion);

      // 保存到文件系统
      await this.saveToFileSystem();

      this.logSecurityEvent('new_key_pair_generated', true, {
        version,
        fingerprint: metadata.fingerprint,
      });
    } catch (error) {
      this.logSecurityEvent('key_generation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to generate RSA key pair');
    }
  }

  /**
   * 保存密钥到文件系统
   */
  private async saveToFileSystem(): Promise<void> {
    try {
      const allMetadata: KeyMetadata[] = Array.from(this.keyVersions.values()).map(
        (kv) => kv.metadata
      );

      // 保存元数据
      const metadataPath = join(this.keyDirectory, KEY_CONFIG.KEY_METADATA_FILE);
      writeFileSync(metadataPath, JSON.stringify(allMetadata, null, 2), { mode: 0o600 });

      // 保存每个版本的密钥
      for (const [version, keyVersion] of this.keyVersions) {
        const privateKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PRIVATE_KEY_FILE}.${version}`);
        const publicKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PUBLIC_KEY_FILE}.${version}`);

        writeFileSync(privateKeyPath, keyVersion.privateKey, { mode: 0o600 });
        writeFileSync(publicKeyPath, keyVersion.publicKey, { mode: 0o644 });
      }

      this.logSecurityEvent('keys_saved_to_filesystem', true, {
        versions: Array.from(this.keyVersions.keys()),
      });
    } catch (error) {
      this.logSecurityEvent('key_save_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to save keys to filesystem');
    }
  }

  /**
   * 验证密钥格式
   */
  private validateKeyFormat(privateKey: string, publicKey: string): boolean {
    try {
      // 验证私钥格式
      if (
        !privateKey.includes('-----BEGIN PRIVATE KEY-----') ||
        !privateKey.includes('-----END PRIVATE KEY-----')
      ) {
        return false;
      }

      // 验证公钥格式
      if (
        !publicKey.includes('-----BEGIN PUBLIC KEY-----') ||
        !publicKey.includes('-----END PUBLIC KEY-----')
      ) {
        return false;
      }

      // 测试密钥是否可以用于签名和验证
      const testPayload = { test: 'validation', timestamp: Date.now() };
      const token = jwt.sign(testPayload, privateKey, { algorithm: 'RS256' });
      jwt.verify(token, publicKey, { algorithms: ['RS256'] });

      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 计算密钥指纹
   */
  private calculateKeyFingerprint(publicKey: string): string {
    return createHash('sha256')
      .update(publicKey)
      .digest('hex')
      .substring(0, KEY_CONFIG.KEY_FINGERPRINT_LENGTH);
  }

  /**
   * 提取密钥大小
   */
  private extractKeySize(publicKey: string): number {
    try {
      // 简化的密钥大小检测
      const keyBuffer = Buffer.from(
        publicKey.replace(/-----[^-]+-----/g, '').replace(/\s/g, ''),
        'base64'
      );
      // 根据密钥长度估算大小
      return keyBuffer.length > 400 ? 2048 : 1024;
    } catch {
      return KEY_CONFIG.RSA_MODULUS_LENGTH;
    }
  }

  /**
   * 验证密钥完整性
   */
  private async validateKeyIntegrity(): Promise<void> {
    if (!this.currentKey) {
      throw new Error('No current key available for validation');
    }

    try {
      // 验证密钥可用性
      const testPayload = { test: 'integrity', timestamp: Date.now() };
      const token = jwt.sign(testPayload, this.currentKey.privateKey, { algorithm: 'RS256' });
      const decoded = jwt.verify(token, this.currentKey.publicKey, { algorithms: ['RS256'] });

      // 验证指纹一致性
      const currentFingerprint = this.calculateKeyFingerprint(this.currentKey.publicKey);
      if (currentFingerprint !== this.currentKey.metadata.fingerprint) {
        throw new Error('Key fingerprint mismatch');
      }

      this.logSecurityEvent('key_integrity_validated', true, { version: this.currentKey.version });
    } catch (error) {
      this.logSecurityEvent('key_integrity_validation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Key integrity validation failed');
    }
  }

  /**
   * 检查是否需要密钥轮换
   */
  private async checkKeyRotationNeeded(): Promise<void> {
    if (!this.currentKey) {
      return;
    }

    const now = Date.now();
    const keyAge = now - this.currentKey.metadata.created;
    const expirationTime = this.currentKey.metadata.expires;

    // 检查是否需要轮换
    if (keyAge > KEY_CONFIG.KEY_ROTATION_INTERVAL || now > expirationTime) {
      this.logSecurityEvent('key_rotation_needed', true, { keyAge, expirationTime });
      // 这里可以实现自动轮换逻辑
    }
  }

  /**
   * 获取当前活跃的密钥
   */
  public getCurrentKey(): { privateKey: string; publicKey: string; version: number } | null {
    if (!this.currentKey) {
      return null;
    }

    return {
      privateKey: this.currentKey.privateKey,
      publicKey: this.currentKey.publicKey,
      version: this.currentKey.version,
    };
  }

  /**
   * 获取公钥用于验证
   */
  public getPublicKey(version?: number): string | null {
    if (version) {
      const keyVersion = this.keyVersions.get(version);
      return keyVersion?.publicKey || null;
    }

    return this.currentKey?.publicKey || null;
  }

  /**
   * 获取私钥用于签名
   */
  public getPrivateKey(): string | null {
    return this.currentKey?.privateKey || null;
  }

  /**
   * 获取密钥元数据
   */
  public getKeyMetadata(): KeyMetadata | null {
    return this.currentKey?.metadata || null;
  }

  /**
   * 密钥轮换
   */
  public async rotateKey(): Promise<void> {
    if (!this.currentKey) {
      throw new Error('No current key to rotate');
    }

    try {
      // 标记当前密钥为轮换中
      this.currentKey.metadata.status = 'rotating';

      // 生成新密钥
      await this.generateNewKeyPair();

      // 标记旧密钥为已弃用
      const oldKey = this.keyVersions.get(this.currentKey.version);
      if (oldKey) {
        oldKey.metadata.status = 'deprecated';
      }

      // 清理旧版本
      await this.cleanupOldVersions();

      // 保存更新
      await this.saveToFileSystem();

      this.logSecurityEvent('key_rotated', true, {
        newVersion: this.currentKey.version,
        oldVersion: oldKey?.version,
      });
    } catch (error) {
      this.logSecurityEvent('key_rotation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Key rotation failed');
    }
  }

  /**
   * 清理旧版本密钥
   */
  private async cleanupOldVersions(): Promise<void> {
    const versions = Array.from(this.keyVersions.keys()).sort((a, b) => b - a);

    if (versions.length > KEY_CONFIG.MAX_KEY_VERSIONS) {
      const versionsToRemove = versions.slice(KEY_CONFIG.MAX_KEY_VERSIONS);

      for (const version of versionsToRemove) {
        this.keyVersions.delete(version);
        this.logSecurityEvent('old_key_version_removed', true, { version });
      }
    }
  }

  /**
   * 获取或创建加密密钥
   */
  private getOrCreateEncryptionKey(): Buffer {
    // 这里可以实现更复杂的密钥派生逻辑
    return randomBytes(KEY_CONFIG.ENCRYPTION_KEY_LENGTH);
  }

  /**
   * 安全事件日志
   */
  private logSecurityEvent(action: string, success: boolean, details?: any): void {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      action,
      success,
      details,
    };

    // 这里可以集成到更复杂的日志系统
    console.log(
      `[RSA-KeyManager] ${timestamp} - ${action}: ${success ? 'SUCCESS' : 'FAILURE'}`,
      details || ''
    );
  }

  /**
   * 获取密钥管理器状态
   */
  public getStatus(): {
    initialized: boolean;
    currentVersion: number | null;
    keyVersions: number[];
    keyAge: number | null;
    nextRotation: number | null;
  } {
    const currentVersion = this.currentKey?.version || null;
    const keyAge = this.currentKey ? Date.now() - this.currentKey.metadata.created : null;
    const nextRotation = this.currentKey?.metadata.expires || null;

    return {
      initialized: this.initialized,
      currentVersion,
      keyVersions: Array.from(this.keyVersions.keys()),
      keyAge,
      nextRotation,
    };
  }

  /**
   * 紧急重置（仅用于测试或灾难恢复）
   */
  public async emergencyReset(): Promise<void> {
    this.keyVersions.clear();
    this.currentKey = null;
    this.initialized = false;

    // 不生成新密钥，而是清除状态以便重新初始化
    this.logSecurityEvent('emergency_reset_performed', true);
  }
}

// 导出单例实例
export const keyManager = RSAKeyManager.getInstance();

// 导出类型和配置
export type { KeyMetadata, KeyVersion };
export { KEY_CONFIG };
