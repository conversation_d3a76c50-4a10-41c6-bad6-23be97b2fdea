#!/usr/bin/env tsx

/**
 * 运行冷启动测试的简化脚本
 */

import {
  runConcurrencyTest,
  TestMode,
} from '../app/test/查词响应时间优化/scripts/redis-test-counts-optimized';

async function main() {
  console.log('🚀 运行冷启动数据库连接测试\n');
  console.log('='.repeat(60));

  try {
    // 运行冷启动测试：10个并发，1次迭代
    const result = await runConcurrencyTest(TestMode.COLD_START, 10, 1);

    console.log('\n' + '='.repeat(60));
    console.log('🎯 测试总结:');
    console.log(`   模式: 冷启动 (清空所有缓存)`);
    console.log(`   并发数: 10`);
    console.log(`   成功率: ${result.successRate.toFixed(1)}%`);
    console.log(`   平均响应时间: ${Math.round(result.avgResponseTime)}ms`);
    console.log(`   P95响应时间: ${Math.round(result.p95ResponseTime)}ms`);
    console.log(`   性能目标达成: ${result.targetsMet ? '✅' : '❌'}`);

    console.log('\n🔍 关键观察点:');
    console.log('   1. 查看"[请求前]"和"[请求后]"的数据库连接池状态变化');
    console.log('   2. 确认所有请求都是缓存未命中（cache: miss）');
    console.log('   3. 观察连接池是否根据并发需求创建了多个连接');

    console.log('\n✅ 冷启动测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

main().catch(console.error);
