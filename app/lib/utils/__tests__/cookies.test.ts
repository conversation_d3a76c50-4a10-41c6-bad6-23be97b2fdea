import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';
import crypto from 'crypto';
import {
  SecureCookieManager,
  CSRFProtection,
  ResponseCookieManager,
  SecurityHeadersManager,
  COOKIE_NAMES,
  DEFAULT_SECURE_COOKIE_OPTIONS,
  REFRESH_TOKEN_COOKIE_OPTIONS,
  CSRF_CONFIG,
} from '../cookies';

// Mock Next.js cookies
const mockCookieStore = {
  get: vi.fn(),
  set: vi.fn(),
  delete: vi.fn(),
};

vi.mock('next/headers', () => ({
  cookies: () => mockCookieStore,
}));

// Mock crypto
vi.mock('crypto', () => ({
  default: {
    randomBytes: vi.fn(),
    timingSafeEqual: vi.fn(),
  },
}));

describe('SecureCookieManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('setCookie', () => {
    it('should set cookie with default secure options', () => {
      SecureCookieManager.setCookie('test-cookie', 'test-value');

      expect(mockCookieStore.set).toHaveBeenCalledWith('test-cookie', 'test-value', {
        httpOnly: true,
        secure: false, // NODE_ENV is test
        sameSite: 'strict',
        maxAge: 15 * 60,
        path: '/',
      });
    });

    it('should set cookie with custom options', () => {
      const customOptions = {
        httpOnly: false,
        secure: true,
        sameSite: 'lax' as const,
        maxAge: 3600,
        path: '/api',
        domain: '.example.com',
      };

      SecureCookieManager.setCookie('test-cookie', 'test-value', customOptions);

      expect(mockCookieStore.set).toHaveBeenCalledWith('test-cookie', 'test-value', {
        ...customOptions,
        domain: '.example.com',
      });
    });
  });

  describe('getCookie', () => {
    it('should get cookie value', () => {
      const mockCookie = { value: 'test-value' };
      mockCookieStore.get.mockReturnValueOnce(mockCookie);

      const result = SecureCookieManager.getCookie('test-cookie');

      expect(result).toBe('test-value');
      expect(mockCookieStore.get).toHaveBeenCalledWith('test-cookie');
    });

    it('should return null for non-existent cookie', () => {
      mockCookieStore.get.mockReturnValueOnce(undefined);

      const result = SecureCookieManager.getCookie('non-existent');

      expect(result).toBeNull();
    });
  });

  describe('deleteCookie', () => {
    it('should delete cookie', () => {
      SecureCookieManager.deleteCookie('test-cookie');

      expect(mockCookieStore.delete).toHaveBeenCalledWith('test-cookie');
    });
  });

  describe('token management', () => {
    it('should set access token', () => {
      SecureCookieManager.setAccessToken('access-token-value');

      expect(mockCookieStore.set).toHaveBeenCalledWith(
        COOKIE_NAMES.ACCESS_TOKEN,
        'access-token-value',
        DEFAULT_SECURE_COOKIE_OPTIONS
      );
    });

    it('should get access token', () => {
      const mockCookie = { value: 'access-token-value' };
      mockCookieStore.get.mockReturnValueOnce(mockCookie);

      const result = SecureCookieManager.getAccessToken();

      expect(result).toBe('access-token-value');
      expect(mockCookieStore.get).toHaveBeenCalledWith(COOKIE_NAMES.ACCESS_TOKEN);
    });

    it('should set refresh token with extended expiration', () => {
      SecureCookieManager.setRefreshToken('refresh-token-value');

      expect(mockCookieStore.set).toHaveBeenCalledWith(
        COOKIE_NAMES.REFRESH_TOKEN,
        'refresh-token-value',
        REFRESH_TOKEN_COOKIE_OPTIONS
      );
    });

    it('should get refresh token', () => {
      const mockCookie = { value: 'refresh-token-value' };
      mockCookieStore.get.mockReturnValueOnce(mockCookie);

      const result = SecureCookieManager.getRefreshToken();

      expect(result).toBe('refresh-token-value');
      expect(mockCookieStore.get).toHaveBeenCalledWith(COOKIE_NAMES.REFRESH_TOKEN);
    });
  });

  describe('user session management', () => {
    it('should set user session data', () => {
      const userData = { id: '1', email: '<EMAIL>', name: 'Test User' };

      SecureCookieManager.setUserSession(userData);

      expect(mockCookieStore.set).toHaveBeenCalledWith(
        COOKIE_NAMES.USER_SESSION,
        JSON.stringify(userData),
        DEFAULT_SECURE_COOKIE_OPTIONS
      );
    });

    it('should get user session data', () => {
      const userData = { id: '1', email: '<EMAIL>', name: 'Test User' };
      const mockCookie = { value: JSON.stringify(userData) };
      mockCookieStore.get.mockReturnValueOnce(mockCookie);

      const result = SecureCookieManager.getUserSession();

      expect(result).toEqual(userData);
      expect(mockCookieStore.get).toHaveBeenCalledWith(COOKIE_NAMES.USER_SESSION);
    });

    it('should return null for invalid session data', () => {
      const mockCookie = { value: 'invalid-json' };
      mockCookieStore.get.mockReturnValueOnce(mockCookie);

      const result = SecureCookieManager.getUserSession();

      expect(result).toBeNull();
    });

    it('should return null for non-existent session', () => {
      mockCookieStore.get.mockReturnValueOnce(undefined);

      const result = SecureCookieManager.getUserSession();

      expect(result).toBeNull();
    });
  });

  describe('clearAuthCookies', () => {
    it('should clear all authentication cookies', () => {
      SecureCookieManager.clearAuthCookies();

      expect(mockCookieStore.delete).toHaveBeenCalledTimes(4);
      expect(mockCookieStore.delete).toHaveBeenCalledWith(COOKIE_NAMES.ACCESS_TOKEN);
      expect(mockCookieStore.delete).toHaveBeenCalledWith(COOKIE_NAMES.REFRESH_TOKEN);
      expect(mockCookieStore.delete).toHaveBeenCalledWith(COOKIE_NAMES.USER_SESSION);
      expect(mockCookieStore.delete).toHaveBeenCalledWith(COOKIE_NAMES.CSRF_TOKEN);
    });
  });
});

describe('CSRFProtection', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateToken', () => {
    it('should generate random token', () => {
      const mockBuffer = Buffer.from('random-bytes');
      (crypto.randomBytes as any).mockReturnValueOnce(mockBuffer);

      const token = CSRFProtection.generateToken();

      expect(crypto.randomBytes).toHaveBeenCalledWith(CSRF_CONFIG.tokenLength);
      expect(token).toBe(mockBuffer.toString('hex'));
    });
  });

  describe('setCSRFToken', () => {
    it('should set CSRF token in cookie', () => {
      const mockBuffer = Buffer.from('random-bytes');
      (crypto.randomBytes as any).mockReturnValueOnce(mockBuffer);

      const token = CSRFProtection.setCSRFToken();

      expect(mockCookieStore.set).toHaveBeenCalledWith(
        CSRF_CONFIG.cookieName,
        mockBuffer.toString('hex'),
        expect.objectContaining({
          httpOnly: false,
          secure: false, // NODE_ENV is test
          sameSite: 'strict',
          maxAge: 60 * 60,
          path: '/',
        })
      );
      expect(token).toBe(mockBuffer.toString('hex'));
    });
  });

  describe('getCSRFToken', () => {
    it('should get CSRF token from cookie', () => {
      const mockCookie = { value: 'csrf-token-value' };
      mockCookieStore.get.mockReturnValueOnce(mockCookie);

      const result = CSRFProtection.getCSRFToken();

      expect(result).toBe('csrf-token-value');
      expect(mockCookieStore.get).toHaveBeenCalledWith(CSRF_CONFIG.cookieName);
    });

    it('should return null for non-existent token', () => {
      mockCookieStore.get.mockReturnValueOnce(undefined);

      const result = CSRFProtection.getCSRFToken();

      expect(result).toBeNull();
    });
  });

  describe('validateCSRFToken', () => {
    it('should return true when CSRF is disabled', () => {
      const originalEnabled = CSRF_CONFIG.enabled;
      CSRF_CONFIG.enabled = false;

      const mockRequest = new NextRequest('http://localhost/test');
      const result = CSRFProtection.validateCSRFToken(mockRequest);

      expect(result).toBe(true);

      CSRF_CONFIG.enabled = originalEnabled;
    });

    it('should validate CSRF token successfully', () => {
      CSRF_CONFIG.enabled = true;
      const token = 'valid-csrf-token';

      const mockRequest = new NextRequest('http://localhost/test', {
        headers: { [CSRF_CONFIG.headerName]: token },
        cookies: { [CSRF_CONFIG.cookieName]: token },
      });

      (crypto.timingSafeEqual as any).mockReturnValueOnce(true);

      const result = CSRFProtection.validateCSRFToken(mockRequest);

      expect(result).toBe(true);
      expect(crypto.timingSafeEqual).toHaveBeenCalledWith(Buffer.from(token), Buffer.from(token));
    });

    it('should fail validation when tokens do not match', () => {
      CSRF_CONFIG.enabled = true;
      const headerToken = 'header-token';
      const cookieToken = 'cookie-token';

      const mockRequest = new NextRequest('http://localhost/test', {
        headers: { [CSRF_CONFIG.headerName]: headerToken },
        cookies: { [CSRF_CONFIG.cookieName]: cookieToken },
      });

      (crypto.timingSafeEqual as any).mockReturnValueOnce(false);

      const result = CSRFProtection.validateCSRFToken(mockRequest);

      expect(result).toBe(false);
    });

    it('should fail validation when header token is missing', () => {
      CSRF_CONFIG.enabled = true;

      const mockRequest = new NextRequest('http://localhost/test', {
        cookies: { [CSRF_CONFIG.cookieName]: 'token' },
      });

      const result = CSRFProtection.validateCSRFToken(mockRequest);

      expect(result).toBe(false);
    });

    it('should fail validation when cookie token is missing', () => {
      CSRF_CONFIG.enabled = true;

      const mockRequest = new NextRequest('http://localhost/test', {
        headers: { [CSRF_CONFIG.headerName]: 'token' },
      });

      const result = CSRFProtection.validateCSRFToken(mockRequest);

      expect(result).toBe(false);
    });
  });

  describe('middleware', () => {
    it('should return null for GET requests', () => {
      const mockRequest = new NextRequest('http://localhost/test', {
        method: 'GET',
      });

      const middleware = CSRFProtection.middleware();
      const result = middleware(mockRequest);

      expect(result).toBeNull();
    });

    it('should return 403 response for invalid CSRF token on write operations', () => {
      CSRF_CONFIG.enabled = true;

      const mockRequest = new NextRequest('http://localhost/test', {
        method: 'POST',
      });

      const middleware = CSRFProtection.middleware();
      const result = middleware(mockRequest);

      expect(result).toBeInstanceOf(NextResponse);
      expect(result!.status).toBe(403);
    });

    it('should return null for valid CSRF token on write operations', () => {
      CSRF_CONFIG.enabled = true;
      const token = 'valid-csrf-token';

      const mockRequest = new NextRequest('http://localhost/test', {
        method: 'POST',
        headers: { [CSRF_CONFIG.headerName]: token },
        cookies: { [CSRF_CONFIG.cookieName]: token },
      });

      (crypto.timingSafeEqual as any).mockReturnValueOnce(true);

      const middleware = CSRFProtection.middleware();
      const result = middleware(mockRequest);

      expect(result).toBeNull();
    });
  });
});

describe('ResponseCookieManager', () => {
  let mockResponse: NextResponse;

  beforeEach(() => {
    vi.clearAllMocks();
    mockResponse = new NextResponse();
    mockResponse.headers = new Headers();
    mockResponse.headers.append = vi.fn();
  });

  describe('setCookie', () => {
    it('should set cookie in response headers', () => {
      ResponseCookieManager.setCookie(mockResponse, 'test-cookie', 'test-value');

      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        expect.stringContaining('test-cookie=test-value')
      );
    });

    it('should build cookie string with all options', () => {
      const options = {
        httpOnly: true,
        secure: true,
        sameSite: 'strict' as const,
        maxAge: 3600,
        path: '/test',
        domain: '.example.com',
      };

      ResponseCookieManager.setCookie(mockResponse, 'test-cookie', 'test-value', options);

      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        'test-cookie=test-value; Path=/test; Max-Age=3600; Domain=.example.com; HttpOnly; Secure; SameSite=strict'
      );
    });
  });

  describe('deleteCookie', () => {
    it('should set expired cookie to delete it', () => {
      ResponseCookieManager.deleteCookie(mockResponse, 'test-cookie');

      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        'test-cookie=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict'
      );
    });
  });

  describe('token management', () => {
    it('should set access token', () => {
      ResponseCookieManager.setAccessToken(mockResponse, 'access-token');

      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        expect.stringContaining(`${COOKIE_NAMES.ACCESS_TOKEN}=access-token`)
      );
    });

    it('should set refresh token', () => {
      ResponseCookieManager.setRefreshToken(mockResponse, 'refresh-token');

      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        expect.stringContaining(`${COOKIE_NAMES.REFRESH_TOKEN}=refresh-token`)
      );
    });

    it('should set user session', () => {
      const userData = { id: '1', email: '<EMAIL>' };

      ResponseCookieManager.setUserSession(mockResponse, userData);

      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        expect.stringContaining(`${COOKIE_NAMES.USER_SESSION}=${JSON.stringify(userData)}`)
      );
    });
  });

  describe('clearAuthCookies', () => {
    it('should clear all authentication cookies', () => {
      ResponseCookieManager.clearAuthCookies(mockResponse);

      expect(mockResponse.headers.append).toHaveBeenCalledTimes(4);
      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        expect.stringContaining(`${COOKIE_NAMES.ACCESS_TOKEN}=;`)
      );
      expect(mockResponse.headers.append).toHaveBeenCalledWith(
        'Set-Cookie',
        expect.stringContaining(`${COOKIE_NAMES.REFRESH_TOKEN}=;`)
      );
    });
  });
});

describe('SecurityHeadersManager', () => {
  let mockResponse: NextResponse;

  beforeEach(() => {
    vi.clearAllMocks();
    mockResponse = new NextResponse();
    mockResponse.headers = new Headers();
    mockResponse.headers.set = vi.fn();
  });

  describe('addSecurityHeaders', () => {
    it('should add all security headers', () => {
      SecurityHeadersManager.addSecurityHeaders(mockResponse);

      expect(mockResponse.headers.set).toHaveBeenCalledWith('X-Content-Type-Options', 'nosniff');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('X-Frame-Options', 'DENY');
      expect(mockResponse.headers.set).toHaveBeenCalledWith('X-XSS-Protection', '1; mode=block');
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Referrer-Policy',
        'strict-origin-when-cross-origin'
      );
      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Content-Security-Policy',
        "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"
      );
    });

    it('should add HSTS header in production', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      SecurityHeadersManager.addSecurityHeaders(mockResponse);

      expect(mockResponse.headers.set).toHaveBeenCalledWith(
        'Strict-Transport-Security',
        'max-age=31536000; includeSubDomains'
      );

      process.env.NODE_ENV = originalEnv;
    });

    it('should not add HSTS header in development', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      SecurityHeadersManager.addSecurityHeaders(mockResponse);

      expect(mockResponse.headers.set).not.toHaveBeenCalledWith(
        'Strict-Transport-Security',
        expect.any(String)
      );

      process.env.NODE_ENV = originalEnv;
    });
  });
});
