import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

// 使用简单的Prisma客户端，避免连接池问题
const simplePrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});
import { z } from 'zod';

// Validation schema
const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  name: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Registration request received');
    const body = await request.json();
    console.log('📝 Request body:', body);

    // Validate request body
    const validation = registerSchema.safeParse(body);
    if (!validation.success) {
      console.log('❌ Validation failed:', validation.error.errors);
      return NextResponse.json(
        {
          message: 'Invalid input',
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { email, password, name } = validation.data;
    console.log('✅ Validation passed for email:', email);

    // Check if user already exists
    console.log('🔍 Checking if user exists...');
    console.log(
      '🔧 Database URL being used:',
      process.env.DATABASE_URL?.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')
    );
    const existingUser = await simplePrisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      console.log('📧 User already exists, checking if this is a login attempt...');

      // 智能判断：如果用户已存在且提供了密码，尝试验证登录
      if (existingUser.passwordHash) {
        const isValidPassword = await bcrypt.compare(password, existingUser.passwordHash);

        if (isValidPassword) {
          console.log('✅ Password correct, treating as login attempt');

          // 更新最后登录时间
          await simplePrisma.user.update({
            where: { id: existingUser.id },
            data: {
              lastLoginAt: new Date(),
            },
          });

          return NextResponse.json(
            {
              message: 'Login successful',
              type: 'login',
              user: {
                id: existingUser.id,
                email: existingUser.email,
                name: existingUser.name,
                avatar: existingUser.avatar,
                createdAt: existingUser.createdAt,
                updatedAt: existingUser.updatedAt,
              },
            },
            { status: 200 }
          );
        } else {
          console.log('❌ Password incorrect for existing user');
          // 不提示密码错误，避免泄露账号存在信息
          return NextResponse.json(
            {
              message: 'User already exists with this email',
              type: 'user_exists',
            },
            { status: 400 }
          );
        }
      } else {
        // 用户存在但无密码hash（可能是OAuth用户）
        return NextResponse.json(
          {
            message: 'User already exists with this email',
            type: 'user_exists',
          },
          { status: 400 }
        );
      }
    }

    console.log('✅ User does not exist, proceeding to create...');

    // Hash password
    console.log('🔐 Hashing password...');
    const passwordHash = await bcrypt.hash(password, 12);
    console.log('✅ Password hashed successfully');

    // Create user
    console.log('👤 Creating user in database...');
    const user = await simplePrisma.user.create({
      data: {
        email,
        passwordHash,
        name,
        provider: 'credentials',
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    console.log('✅ User created successfully:', user.id);

    return NextResponse.json(
      {
        message: 'User created successfully',
        user,
      },
      { status: 201 }
    );
  } catch (error: any) {
    console.error('💥 Registration error:', error);
    console.error('Stack trace:', error.stack);
    return NextResponse.json(
      {
        message: 'Internal server error',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
