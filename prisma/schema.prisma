generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String              @id @default(cuid())
  email             String              @unique
  hashedPw          String?
  createdAt         DateTime            @default(now())
  avatar            String?
  isActive          Boolean             @default(true)
  lastLoginAt       DateTime?
  loginIP           String?
  name              String?
  passwordHash      String?
  provider          String?
  providerId        String?
  updatedAt         DateTime            @updatedAt
  accounts          Account[]
  refreshTokens     RefreshToken[]
  sessions          Session[]
  stats             UserWordStat[]
  wordProficiencies UserWordProficiency[] // User word proficiency tracking for typing practice
  rateLimit         RateLimit[]         // Rate limiting records
  passwordResetTokens PasswordResetToken[] // Password reset tokens
  savedSentences    UserSavedSentence[] // User saved sentences for typing practice
}

model UserWordStat {
  userId       String
  vocabularyId Int
  lookupCount  Int      @default(1)
  lastQueried  DateTime @default(now())
  level        Int      @default(0)
  starred      <PERSON>olean  @default(false)
  user         User     @relation(fields: [userId], references: [id])

  @@id([userId, vocabularyId])
  @@map("user_word_stat")
}

/// User word proficiency tracking for typing practice
model UserWordProficiency {
  userId          String
  wordId          Int
  practiceCount   Int      @default(0)  // Number of times practiced
  errorCount      Int      @default(0)  // Number of errors made
  averageTime     Int      @default(0)  // Average typing time in milliseconds
  isMarked        Boolean  @default(false) // Manually marked as difficult
  proficiencyScore Float   @default(0.0) // Calculated proficiency score (0.0 - 1.0)
  lastPracticed   DateTime? // Last practice time
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  vocabulary Vocabulary @relation(fields: [wordId], references: [id], onDelete: Cascade)

  @@id([userId, wordId])
  @@index([userId])
  @@index([wordId])
  @@index([proficiencyScore])
  @@index([lastPracticed])
  @@index([userId, proficiencyScore]) // For sorted proficiency queries
  @@index([userId, isMarked]) // For marked words queries
  @@map("user_word_proficiency")
}

model QueryLog {
  id           Int      @id @default(autoincrement())
  userId       String
  vocabularyId Int
  action       String   @default("lookup")
  createdAt    DateTime @default(now())

  @@index([userId, createdAt])
}

/// 词汇主表
model Vocabulary {
  id        Int          @id @default(autoincrement())
  word      String       @unique
  phonetics String[]
  freqRank  Int?
  explains  Explain[]
  formats   WordFormat[]
  userProficiencies UserWordProficiency[] // User proficiency tracking for typing practice

  @@index([freqRank])
  @@index([word])
}

/// 词性 -> 释义 (Explain) 1-N
model Explain {
  id           Int          @id @default(autoincrement())
  pos          String
  vocabularyId Int
  definitions  Definition[]
  vocabulary   Vocabulary   @relation(fields: [vocabularyId], references: [id])

  @@unique([vocabularyId, pos])
}

/// 单条释义 + 中文
model Definition {
  id         Int      @id @default(autoincrement())
  definition String
  chinese    String
  explainId  Int
  rating     Int      @default(0)
  chineseS   String
  createdAt  DateTime @default(now())
  provider   String?
  updatedAt  DateTime @updatedAt
  explain    Explain  @relation(fields: [explainId], references: [id])

  @@index([explainId])
}

/// 单词形式（复数、第三人称等）
model WordFormat {
  id           Int          @id @default(autoincrement())
  name         String
  form         String
  vocabularyId Int?
  baseFormId   Int?
  baseForm     WordFormat?  @relation("FormToBase", fields: [baseFormId], references: [id])
  derivedForms WordFormat[] @relation("FormToBase")
  vocabulary   Vocabulary?  @relation(fields: [vocabularyId], references: [id])

  @@index([vocabularyId])
  @@index([baseFormId])
  @@index([form], map: "idx_wordformat_form")
  @@index([form, baseFormId], map: "idx_wordformat_form_base")
  @@index([form, vocabularyId], map: "idx_wordformat_form_vocab")
}

/// NextAuth.js required models
model Account {
  id                String   @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PasswordResetToken {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expires   DateTime
  used      Boolean  @default(false)
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([token])
  @@index([userId])
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

/// RefreshToken model for JWT refresh token management
model RefreshToken {
  id         String    @id @default(cuid())
  token      String    @unique
  userId     String
  expiresAt  DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  lastUsedAt DateTime?
  isRevoked  Boolean   @default(false)
  revokedAt  DateTime?
  revokedBy  String?
  sessionId  String?
  ipAddress  String?
  userAgent  String?
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expiresAt])
  @@index([sessionId])
  @@index([revokedAt])
}

/// Token blacklist for JWT invalidation
model TokenBlacklist {
  id        String   @id @default(cuid())
  jti       String   @unique // JWT ID for unique identification
  userId    String?
  tokenHash String   @unique // SHA-256 hash of the token
  reason    String   // Reason for blacklisting: 'logout', 'security', 'expired', 'revoked'
  expiresAt DateTime // Original token expiration time
  createdAt DateTime @default(now())
  ipAddress String?
  userAgent String?
  
  @@index([jti])
  @@index([tokenHash])
  @@index([userId])
  @@index([expiresAt])
  @@index([createdAt])
  @@map("token_blacklist")
}

/// Rate limiting for authentication attempts
model RateLimit {
  id        String   @id @default(cuid())
  userId    String?
  ipAddress String
  endpoint  String   // '/api/auth/signin', '/api/auth/token', etc.
  attempts  Int      @default(1)
  lastAttempt DateTime @default(now())
  windowStart DateTime @default(now())
  blocked   Boolean  @default(false)
  blockUntil DateTime?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  user      User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@unique([ipAddress, endpoint])
  @@index([userId])
  @@index([ipAddress])
  @@index([endpoint])
  @@index([lastAttempt])
  @@index([blockUntil])
  @@map("rate_limit")
}

/// Word list for vocabulary practice (CET-4, CET-6, TOEFL, etc.)
model WordList {
  id          Int      @id @default(autoincrement())
  name        String   @unique // "CET-4", "CET-6", "TOEFL", etc.
  description String
  difficulty  Int      // 1-10 difficulty scale
  totalWords  Int      @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relation to word list entries
  entries     WordListEntry[]
  
  @@index([isActive])
  @@index([difficulty])
  @@map("word_list")
}

/// Word list entries - storing words belonging to specific word lists
model WordListEntry {
  id          Int        @id @default(autoincrement())
  word        String     // Direct storage of word text
  wordListIds Int[]      // JSON array of word list IDs (supports one word in multiple lists)
  wordListId  Int        // Primary word list for the relation
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  // Relation to word list
  wordList    WordList   @relation(fields: [wordListId], references: [id], onDelete: Cascade)
  
  // Index for efficient queries
  @@index([word])
  @@index([wordListId])
  @@index([wordListIds])
  @@map("word_list_entry")
}

/// Sentence model for typing practice
model Sentence {
  id            Int                @id @default(autoincrement())
  content       String             // English sentence content
  source        String?            // Source of the sentence (book, article, user, etc.)
  difficulty    Int                @default(5) // Difficulty level (1-10)
  category      String?            // Category (business, literature, daily, etc.)
  favoriteCount Int                @default(0) // Number of times favorited
  length        Int                // Character length for difficulty assessment
  isActive      Boolean            @default(true) // Whether the sentence is active
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  
  // Relation to user saved sentences
  savedByUsers  UserSavedSentence[]
  
  @@index([isActive])
  @@index([difficulty])
  @@index([category])
  @@index([length])
  @@index([favoriteCount])
  @@map("sentence")
}

/// User saved sentences - many-to-many relation between users and sentences
model UserSavedSentence {
  id              String    @id @default(cuid())
  userId          String    // Foreign key to User
  sentenceId      Int       // Foreign key to Sentence
  savedAt         DateTime  @default(now()) // When the sentence was saved
  practiceCount   Int       @default(0) // Number of times practiced
  lastPracticedAt DateTime? // Last practice time
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  
  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  sentence        Sentence  @relation(fields: [sentenceId], references: [id], onDelete: Cascade)
  
  // Composite unique constraint to prevent duplicate saves
  @@unique([userId, sentenceId])
  @@index([userId])
  @@index([sentenceId])
  @@index([savedAt])
  @@index([practiceCount])
  @@index([lastPracticedAt])
  @@index([userId, savedAt]) // For sorted user sentence queries
  @@index([userId, practiceCount]) // For practice count sorting
  @@map("user_saved_sentence")
}
