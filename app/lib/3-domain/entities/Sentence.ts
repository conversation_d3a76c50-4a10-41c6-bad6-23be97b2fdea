/**
 * Sentence Domain Entity
 * 
 * Represents a practice sentence for typing training.
 * Contains the sentence content, metadata, and related business logic.
 */

export interface Sentence {
  id: number;
  content: string;
  source?: string;
  difficulty: number; // 1-10 scale
  category?: string;
  favoriteCount: number;
  length: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateSentenceInput {
  content: string;
  source?: string;
  difficulty?: number;
  category?: string;
  length?: number;
  isActive?: boolean;
}

export interface UpdateSentenceInput {
  content?: string;
  source?: string;
  difficulty?: number;
  category?: string;
  isActive?: boolean;
}

export interface SentenceWithUserContext extends Sentence {
  isSavedByUser: boolean;
  userPracticeCount?: number;
  userLastPracticedAt?: Date;
}

/**
 * Sentence difficulty levels
 */
export enum SentenceDifficulty {
  BEGINNER = 1,
  ELEMENTARY = 2,
  PRE_INTERMEDIATE = 3,
  INTERMEDIATE = 4,
  UPPER_INTERMEDIATE = 5,
  ADVANCED = 6,
  PROFICIENT = 7,
  EXPERT = 8,
  MASTER = 9,
  NATIVE = 10
}

/**
 * Sentence categories
 */
export enum SentenceCategory {
  BUSINESS = 'business',
  LITERATURE = 'literature',
  DAILY = 'daily',
  ACADEMIC = 'academic',
  TECHNICAL = 'technical',
  NEWS = 'news',
  CONVERSATIONAL = 'conversational',
  FORMAL = 'formal',
  CASUAL = 'casual',
  EDUCATIONAL = 'educational'
}

/**
 * Sentence sources
 */
export enum SentenceSource {
  SYSTEM = 'system',
  USER_GENERATED = 'user_generated',
  BOOK = 'book',
  ARTICLE = 'article',
  NEWS = 'news',
  ACADEMIC_PAPER = 'academic_paper',
  WEBSITE = 'website',
  IMPORTED = 'imported'
}

/**
 * Business logic methods for Sentence entity
 */
export class SentenceEntity {
  /**
   * Calculate sentence difficulty based on content
   */
  static calculateDifficulty(content: string): number {
    const wordCount = content.split(/\s+/).length;
    const avgWordLength = content.replace(/\s+/g, '').length / wordCount;
    const hasComplexPunctuation = /[;:()[\]{}"]/.test(content);
    const hasComplexWords = /\b\w{8,}\b/.test(content);
    
    let difficulty = 1;
    
    // Base difficulty on word count
    if (wordCount > 20) difficulty += 2;
    else if (wordCount > 15) difficulty += 1;
    
    // Adjust for average word length
    if (avgWordLength > 6) difficulty += 2;
    else if (avgWordLength > 4) difficulty += 1;
    
    // Adjust for complex punctuation
    if (hasComplexPunctuation) difficulty += 1;
    
    // Adjust for complex words
    if (hasComplexWords) difficulty += 1;
    
    return Math.min(10, Math.max(1, difficulty));
  }

  /**
   * Validate sentence content
   */
  static validateContent(content: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!content || content.trim().length === 0) {
      errors.push('Sentence content cannot be empty');
    }
    
    if (content.length < 10) {
      errors.push('Sentence content must be at least 10 characters long');
    }
    
    if (content.length > 500) {
      errors.push('Sentence content cannot exceed 500 characters');
    }
    
    // Check for proper sentence structure
    if (!/^[A-Z]/.test(content.trim())) {
      errors.push('Sentence must start with a capital letter');
    }
    
    if (!/[.!?]$/.test(content.trim())) {
      errors.push('Sentence must end with proper punctuation (. ! ?)');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate sentence metadata
   */
  static generateMetadata(content: string): {
    length: number;
    difficulty: number;
    estimatedCategory?: string;
  } {
    const length = content.length;
    const difficulty = this.calculateDifficulty(content);
    
    // Simple category detection based on keywords
    let estimatedCategory: string | undefined;
    const lowerContent = content.toLowerCase();
    
    if (/\b(company|business|market|profit|sales|customer)\b/.test(lowerContent)) {
      estimatedCategory = SentenceCategory.BUSINESS;
    } else if (/\b(research|study|analysis|theory|academic)\b/.test(lowerContent)) {
      estimatedCategory = SentenceCategory.ACADEMIC;
    } else if (/\b(technology|software|computer|digital|system)\b/.test(lowerContent)) {
      estimatedCategory = SentenceCategory.TECHNICAL;
    } else if (/\b(hello|please|thank|sorry|everyday)\b/.test(lowerContent)) {
      estimatedCategory = SentenceCategory.DAILY;
    }
    
    return {
      length,
      difficulty,
      estimatedCategory
    };
  }
}