/**
 * @fileoverview AuthGuard组件测试套件
 *
 * 测试路由保护组件的核心功能，包括：
 * - 基础认证保护
 * - 角色权限控制
 * - 加载状态处理
 * - 重定向逻辑
 * - HOC功能
 * - 边界情况处理
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import {
  AuthGuard,
  withAuth,
  withoutAuth,
  ProtectedRoute,
  AdminRoute,
  UserRoute,
} from '../AuthGuard';
import {
  renderWithAuth,
  createMockUser,
  testScenarios,
  MockAuthContextType,
} from '@/test/utils/auth-test-utils';

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

// Test components
const TestChild: React.FC = () => <div data-testid="test-child">Protected Content</div>;
const CustomFallback: React.FC = () => <div data-testid="custom-fallback">Custom Loading...</div>;

describe('AuthGuard', () => {
  beforeEach(() => {
    vi.mock('next/navigation', () => ({
      useRouter: () => mockRouter,
    }));

    vi.clearAllMocks();
  });

  describe('基础认证保护 (requireAuth=true)', () => {
    it('应该为已认证用户渲染子组件', async () => {
      renderWithAuth(
        <AuthGuard>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.authenticatedUser() }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
      expect(screen.getByText('Protected Content')).toBeInTheDocument();
    });

    it('应该在未认证时重定向到登录页面', async () => {
      renderWithAuth(
        <AuthGuard>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/login');
      });

      expect(screen.queryByTestId('test-child')).not.toBeInTheDocument();
    });

    it('应该支持自定义重定向路径', async () => {
      renderWithAuth(
        <AuthGuard redirectTo="/custom-login">
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/custom-login');
      });
    });

    it('应该在加载时显示默认加载器', () => {
      renderWithAuth(
        <AuthGuard>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.loadingState() }
      );

      expect(screen.getByText('Loading...')).toBeInTheDocument();
      expect(screen.getByRole('status')).toBeInTheDocument(); // Loader2 with aria-label
    });

    it('应该在加载时显示自定义fallback', () => {
      renderWithAuth(
        <AuthGuard fallback={<CustomFallback />}>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.loadingState() }
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
      expect(screen.getByText('Custom Loading...')).toBeInTheDocument();
    });
  });

  describe('反向认证保护 (requireAuth=false)', () => {
    it('应该为未认证用户渲染子组件', () => {
      renderWithAuth(
        <AuthGuard requireAuth={false}>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该将已认证用户重定向到仪表板', async () => {
      renderWithAuth(
        <AuthGuard requireAuth={false}>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.authenticatedUser() }
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
      });

      expect(screen.queryByTestId('test-child')).not.toBeInTheDocument();
    });
  });

  describe('withAuth HOC', () => {
    it('应该包装组件并提供认证保护', () => {
      const ProtectedComponent = withAuth(TestChild);

      renderWithAuth(<ProtectedComponent />, { authValue: testScenarios.authenticatedUser() });

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该在未认证时重定向', async () => {
      const ProtectedComponent = withAuth(TestChild, { redirectTo: '/custom-auth' });

      renderWithAuth(<ProtectedComponent />, { authValue: testScenarios.unauthenticatedUser() });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/custom-auth');
      });
    });

    it('应该支持自定义fallback', () => {
      const ProtectedComponent = withAuth(TestChild, {
        fallback: <CustomFallback />,
      });

      renderWithAuth(<ProtectedComponent />, { authValue: testScenarios.loadingState() });

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
    });
  });

  describe('withoutAuth HOC', () => {
    it('应该为未认证用户渲染组件', () => {
      const PublicComponent = withoutAuth(TestChild);

      renderWithAuth(<PublicComponent />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该将已认证用户重定向到仪表板', async () => {
      const PublicComponent = withoutAuth(TestChild);

      renderWithAuth(<PublicComponent />, { authValue: testScenarios.authenticatedUser() });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
      });
    });

    it('应该支持自定义重定向路径', async () => {
      const PublicComponent = withoutAuth(TestChild, { redirectTo: '/app' });

      renderWithAuth(<PublicComponent />, { authValue: testScenarios.authenticatedUser() });

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/app');
      });
    });
  });

  describe('ProtectedRoute 角色权限控制', () => {
    it('应该为有权限的用户渲染内容', () => {
      const adminUser = createMockUser({ role: 'admin' });

      renderWithAuth(
        <ProtectedRoute roles={['admin', 'moderator']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(adminUser) }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该为没有特定角色要求的认证用户渲染内容', () => {
      const regularUser = createMockUser({ role: 'user' });

      renderWithAuth(
        <ProtectedRoute>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(regularUser) }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该在用户角色不匹配时显示访问拒绝', () => {
      const regularUser = createMockUser({ role: 'user' });

      renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(regularUser) }
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(
        screen.getByText("You don't have permission to access this page.")
      ).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Go to Dashboard' })).toBeInTheDocument();
    });

    it('应该在未认证时重定向到登录页面', async () => {
      renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/login');
      });
    });

    it('应该在加载时显示权限检查提示', () => {
      renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.loadingState() }
      );

      expect(screen.getByText('Checking permissions...')).toBeInTheDocument();
    });

    it('应该支持自定义加载fallback', () => {
      renderWithAuth(
        <ProtectedRoute roles={['admin']} fallback={<CustomFallback />}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.loadingState() }
      );

      expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
    });

    it('应该处理没有角色的用户', () => {
      const userWithoutRole = createMockUser({ role: undefined });

      renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(userWithoutRole) }
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('AdminRoute 专用组件', () => {
    it('应该为管理员用户渲染内容', () => {
      const adminUser = createMockUser({ role: 'admin' });

      renderWithAuth(
        <AdminRoute>
          <TestChild />
        </AdminRoute>,
        { authValue: testScenarios.adminUser() }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该拒绝非管理员用户访问', () => {
      const regularUser = createMockUser({ role: 'user' });

      renderWithAuth(
        <AdminRoute>
          <TestChild />
        </AdminRoute>,
        { authValue: testScenarios.authenticatedUser(regularUser) }
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });
  });

  describe('UserRoute 通用用户组件', () => {
    it('应该为任何认证用户渲染内容', () => {
      const regularUser = createMockUser({ role: 'user' });

      renderWithAuth(
        <UserRoute>
          <TestChild />
        </UserRoute>,
        { authValue: testScenarios.authenticatedUser(regularUser) }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该为管理员用户渲染内容', () => {
      renderWithAuth(
        <UserRoute>
          <TestChild />
        </UserRoute>,
        { authValue: testScenarios.adminUser() }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该拒绝未认证用户访问', async () => {
      renderWithAuth(
        <UserRoute>
          <TestChild />
        </UserRoute>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/login');
      });
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理undefined用户对象', async () => {
      const authValue: MockAuthContextType = {
        user: undefined,
        isAuthenticated: false,
        loading: false,
        isLoading: false,
        error: null,
      };

      renderWithAuth(
        <ProtectedRoute>
          <TestChild />
        </ProtectedRoute>,
        { authValue }
      );

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/login');
      });
    });

    it('应该处理null角色', () => {
      const userWithNullRole = createMockUser({ role: null as any });

      renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(userWithNullRole) }
      );

      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });

    it('应该处理空角色数组', () => {
      const adminUser = createMockUser({ role: 'admin' });

      renderWithAuth(
        <ProtectedRoute roles={[]}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(adminUser) }
      );

      expect(screen.getByTestId('test-child')).toBeInTheDocument();
    });

    it('应该在访问拒绝页面点击按钮时重定向', async () => {
      const { user } = renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.authenticatedUser(createMockUser({ role: 'user' })) }
      );

      const goToDashboardButton = screen.getByRole('button', { name: 'Go to Dashboard' });
      await user.click(goToDashboardButton);

      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
    });
  });

  describe('渲染优化和性能', () => {
    it('应该在重定向期间不渲染任何内容', () => {
      const { container } = renderWithAuth(
        <AuthGuard>
          <TestChild />
        </AuthGuard>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      // 检查是否没有渲染子组件
      expect(screen.queryByTestId('test-child')).not.toBeInTheDocument();
      expect(container.firstChild).toBeNull();
    });

    it('应该在权限不足时正确处理无渲染情况', () => {
      renderWithAuth(
        <ProtectedRoute roles={['admin']}>
          <TestChild />
        </ProtectedRoute>,
        { authValue: testScenarios.unauthenticatedUser() }
      );

      expect(screen.queryByTestId('test-child')).not.toBeInTheDocument();
    });
  });
});
