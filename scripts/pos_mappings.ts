/**
 * 词性映射关系
 * 包含英文词性到缩写和中文的映射
 */

// 最具代表性的词性枚举
export enum RepresentativePos {
  NOUN = 'n.', // 名词 - 最基础的词性
  VERB = 'v.', // 动词 - 最基础的词性
  ADJECTIVE = 'adj.', // 形容词 - 最基础的词性
  ADVERB = 'adv.', // 副词 - 最基础的词性
  PRONOUN = 'pron.', // 代词 - 基础词性
  PREPOSITION = 'prep.', // 介词 - 基础词性
  CONJUNCTION = 'conj.', // 连词 - 基础词性
  ARTICLE = 'art.', // 冠词 - 基础词性
  DETERMINER = 'det.', // 限定词 - 基础词性
  NUMERAL = 'num.', // 数词 - 基础词性
  INTERJECTION = 'int.', // 感叹词 - 基础词性
  ABBREVIATION = 'abbr.', // 缩写 - 现代常见
  SLANG = 'slang', // 俚语 - 现代常见
  PREFIX = 'pref.', // 前缀 - 构词成分
  SUFFIX = 'suff.', // 后缀 - 构词成分
  PHRASE = 'phr.', // 短语 - 语言单位
  PARTICLE = 'part.', // 小品词 - 语法功能词
  QUANTIFIER = 'quant.', // 量词 - 语法功能词
  COMBINING_FORM = 'comb.', // 组合形式 - 构词成分
}

// 代表性词性的中文映射
export const representativePosChinese: Record<RepresentativePos, string> = {
  [RepresentativePos.NOUN]: '名词',
  [RepresentativePos.VERB]: '动词',
  [RepresentativePos.ADJECTIVE]: '形容词',
  [RepresentativePos.ADVERB]: '副词',
  [RepresentativePos.PRONOUN]: '代词',
  [RepresentativePos.PREPOSITION]: '介词',
  [RepresentativePos.CONJUNCTION]: '连词',
  [RepresentativePos.ARTICLE]: '冠词',
  [RepresentativePos.DETERMINER]: '限定词',
  [RepresentativePos.NUMERAL]: '数词',
  [RepresentativePos.INTERJECTION]: '感叹词',
  [RepresentativePos.ABBREVIATION]: '缩写',
  [RepresentativePos.SLANG]: '俚语',
  [RepresentativePos.PREFIX]: '前缀',
  [RepresentativePos.SUFFIX]: '后缀',
  [RepresentativePos.PHRASE]: '短语',
  [RepresentativePos.PARTICLE]: '小品词',
  [RepresentativePos.QUANTIFIER]: '量词',
  [RepresentativePos.COMBINING_FORM]: '组合形式',
};

// 将任意词性映射到代表性词性
export function mapToRepresentativePos(pos: string): RepresentativePos {
  const abbr = getPosAbbreviation(pos);

  // 名词类
  if (abbr === 'n.' || pos.includes('noun') || pos.includes('Noun')) {
    return RepresentativePos.NOUN;
  }

  // 动词类
  if (abbr === 'v.' || pos.includes('verb') || pos.includes('gerund')) {
    return RepresentativePos.VERB;
  }

  // 形容词类
  if (abbr === 'adj.' || pos.includes('adjective')) {
    return RepresentativePos.ADJECTIVE;
  }

  // 副词类
  if (abbr === 'adv.' || pos.includes('adverb')) {
    return RepresentativePos.ADVERB;
  }

  // 代词类
  if (abbr === 'pron.' || pos.includes('pronoun')) {
    return RepresentativePos.PRONOUN;
  }

  // 介词类
  if (abbr === 'prep.' || pos.includes('preposition')) {
    return RepresentativePos.PREPOSITION;
  }

  // 连词类
  if (abbr === 'conj.' || pos.includes('conjunction')) {
    return RepresentativePos.CONJUNCTION;
  }

  // 冠词类
  if (abbr === 'art.' || pos.includes('article')) {
    return RepresentativePos.ARTICLE;
  }

  // 限定词类
  if (abbr === 'det.' || pos.includes('determiner')) {
    return RepresentativePos.DETERMINER;
  }

  // 数词类
  if (abbr === 'num.' || pos.includes('number') || pos.includes('numeral')) {
    return RepresentativePos.NUMERAL;
  }

  // 感叹词类
  if (abbr === 'int.' || pos.includes('interjection') || pos.includes('exclamation')) {
    return RepresentativePos.INTERJECTION;
  }

  // 缩写类
  if (
    abbr === 'abbr.' ||
    pos.includes('abbreviation') ||
    pos.includes('acronym') ||
    pos === '缩写'
  ) {
    return RepresentativePos.ABBREVIATION;
  }

  // 俚语类
  if (pos.includes('slang') || pos.includes('informal')) {
    return RepresentativePos.SLANG;
  }

  // 前缀类
  if (pos.includes('prefix') || pos.includes('Prefix')) {
    return RepresentativePos.PREFIX;
  }

  // 后缀类
  if (pos.includes('suffix')) {
    return RepresentativePos.SUFFIX;
  }

  // 短语类
  if (pos.includes('phrase') || pos.includes('idiom')) {
    return RepresentativePos.PHRASE;
  }

  // 小品词类
  if (pos.includes('particle')) {
    return RepresentativePos.PARTICLE;
  }

  // 量词类
  if (pos.includes('quantifier')) {
    return RepresentativePos.QUANTIFIER;
  }

  // 组合形式类
  if (pos.includes('combining') || pos.includes('comb')) {
    return RepresentativePos.COMBINING_FORM;
  }

  // 默认返回名词（最常见的词性）
  return RepresentativePos.NOUN;
}

// 词性到缩写的映射
export const posToAbbreviation: Record<string, string> = {
  // 基础词性
  determiner: 'det.',
  preposition: 'prep.',
  particle: 'part.',
  pronoun: 'pron.',
  conjunction: 'conj.',
  adverb: 'adv.',
  verb: 'v.',
  'modal verb': 'v.',
  noun: 'n.',
  numeral: 'num.',
  adjective: 'adj.',
  interjection: 'int.',
  number: 'num.',
  contraction: 'contr.',
  phrase: 'phr.',
  abbreviation: 'abbr.',
  'ordinal number': 'num.',
  article: 'art.',
  缩写: 'abbr.',
  'auxiliary verb': 'v.',
  exclamation: 'excl.',

  // 已经是缩写的保持原样
  adj: 'adj.',
  adv: 'adv.',
  informal: 'inf.',
  num: 'num.',
  pron: 'pron.',
  det: 'det.',
  conj: 'conj.',
  prep: 'prep.',

  // 特殊词性
  'interrogative pronoun': 'pron.',
  prefix: 'pref.',
  quantifier: 'quant.',
  'infinitive marker': 'inf.',
  'past participle': 'v.',
  'definite article': 'art.',
  'indefinite article': 'art.',
  suffix: 'suff.',
  'proper noun': 'n.',
  'plural noun': 'n.',
  pos: 'pos',
  'Proper Noun': 'n.',

  // 复合词性
  'noun/verb': 'n./v.',
  'noun/adjective': 'n./adj.',
  'golf term': 'n.',
  'Proper noun': 'n.',
  'pronoun and verb': 'pron./v.',
  'noun plural': 'n.',
  acronym: 'abbr.',
  trademark: 'tm.',
  'informal noun': 'n.',
  gerund: 'v.',
  'roman numeral': 'num.',
  idiom: 'idiom',
  'computing noun': 'n.',
  abbr: 'abbr.',

  // 带描述的词性（忽略描述，保留本质）
  'verb (present participle)': 'v.',
  'noun (informal)': 'n.',
  'adjective (comparative)': 'adj.',
  'verb (third person singular present)': 'v.',
  'noun (abbreviation)': 'n.',
  'noun (plural)': 'n.',
  'verb (past participle)': 'v.',
  'verb (past tense)': 'v.',
  'noun (derogatory, offensive)': 'n.',
  'verb (present participle, informal)': 'v.',
  'noun (plural abbreviation)': 'n.',
  'adjective (informal)': 'adj.',
  'offensive slang': 'slang',
  properNoun: 'n.',
  'combining form': 'comb.',
  honorific: 'hon.',
  slang: 'slang',
  'offensive, informal noun': 'n.',
  'offensive, dated': 'dated',
  'brand name': 'brand',
  'legal term': 'legal',
  'comb. form': 'comb.',
  'noun phrase': 'n.',
  'Latin Prefix': 'pref.',
  Abbreviation: 'abbr.',
  'proper noun/slang': 'n./slang',
  'abbr.': 'abbr.',
  'verb/noun': 'v./n.',
  'adverb/adjective': 'adv./adj.',
  'proper noun/noun': 'n.',
  'adjective/noun': 'adj./n.',
};

// 词性到中文的映射
export const posToChinese: Record<string, string> = {
  // 基础词性
  determiner: '限定词',
  preposition: '介词',
  particle: '小品词',
  pronoun: '代词',
  conjunction: '连词',
  adverb: '副词',
  verb: '动词',
  'modal verb': '情态动词',
  noun: '名词',
  numeral: '数词',
  adjective: '形容词',
  interjection: '感叹词',
  number: '数字',
  contraction: '缩略词',
  phrase: '短语',
  abbreviation: '缩写',
  'ordinal number': '序数词',
  article: '冠词',
  缩写: '缩写',
  'auxiliary verb': '助动词',
  exclamation: '感叹词',

  // 已经是缩写的
  adj: '形容词',
  adv: '副词',
  informal: '非正式',
  num: '数词',
  pron: '代词',
  det: '限定词',
  conj: '连词',
  prep: '介词',

  // 特殊词性
  'interrogative pronoun': '疑问代词',
  prefix: '前缀',
  quantifier: '量词',
  'infinitive marker': '不定式标记',
  'past participle': '过去分词',
  'definite article': '定冠词',
  'indefinite article': '不定冠词',
  suffix: '后缀',
  'proper noun': '专有名词',
  'plural noun': '复数名词',
  pos: '词性',
  'Proper Noun': '专有名词',

  // 复合词性
  'noun/verb': '名词/动词',
  'noun/adjective': '名词/形容词',
  'golf term': '高尔夫术语',
  'Proper noun': '专有名词',
  'pronoun and verb': '代词和动词',
  'noun plural': '复数名词',
  acronym: '首字母缩略词',
  trademark: '商标',
  'informal noun': '非正式名词',
  gerund: '动名词',
  'roman numeral': '罗马数字',
  idiom: '习语',
  'computing noun': '计算机术语',
  abbr: '缩写',

  // 带描述的词性
  'verb (present participle)': '动词（现在分词）',
  'noun (informal)': '名词（非正式）',
  'adjective (comparative)': '形容词（比较级）',
  'verb (third person singular present)': '动词（第三人称单数现在时）',
  'noun (abbreviation)': '名词（缩写）',
  'noun (plural)': '名词（复数）',
  'verb (past participle)': '动词（过去分词）',
  'verb (past tense)': '动词（过去时）',
  'noun (derogatory, offensive)': '名词（贬义，冒犯性）',
  'verb (present participle, informal)': '动词（现在分词，非正式）',
  'noun (plural abbreviation)': '名词（复数缩写）',
  'adjective (informal)': '形容词（非正式）',
  'offensive slang': '冒犯性俚语',
  properNoun: '专有名词',
  'combining form': '组合形式',
  honorific: '敬语',
  slang: '俚语',
  'offensive, informal noun': '冒犯性非正式名词',
  'offensive, dated': '冒犯性过时用语',
  'brand name': '品牌名',
  'legal term': '法律术语',
  'comb. form': '组合形式',
  'noun phrase': '名词短语',
  'Latin Prefix': '拉丁前缀',
  Abbreviation: '缩写',
  'proper noun/slang': '专有名词/俚语',
  'abbr.': '缩写',
  'verb/noun': '动词/名词',
  'adverb/adjective': '副词/形容词',
  'proper noun/noun': '专有名词/名词',
  'adjective/noun': '形容词/名词',
};

// 辅助函数：获取词性的缩写
export function getPosAbbreviation(pos: string): string {
  return posToAbbreviation[pos] || pos;
}

// 辅助函数：获取词性的中文翻译
export function getPosChinese(pos: string): string {
  return posToChinese[pos] || pos;
}

// 辅助函数：获取代表性词性的中文翻译
export function getRepresentativePosChinese(pos: RepresentativePos): string {
  return representativePosChinese[pos];
}

// 辅助函数：获取所有支持的词性列表
export function getAllSupportedPos(): string[] {
  return Object.keys(posToAbbreviation);
}

// 辅助函数：获取所有代表性词性列表
export function getAllRepresentativePos(): RepresentativePos[] {
  return Object.values(RepresentativePos);
}
