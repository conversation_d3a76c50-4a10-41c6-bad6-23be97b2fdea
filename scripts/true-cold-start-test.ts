#!/usr/bin/env tsx

/**
 * 真正的冷启动测试
 * 使用 ?fresh=true 参数忽略缓存，无需手动清理
 */

const baseUrl = 'http://localhost:3000';

/**
 * 检查数据库连接池状态
 */
async function checkDbPoolStatus(): Promise<any> {
  try {
    const { prisma } = await import('../app/lib/4-infrastructure/database/prisma');
    const prismaWithMetrics = prisma as any;

    if (!prismaWithMetrics.$metrics) {
      console.log('⚠️ Prisma metrics 未启用');
      return null;
    }

    const metrics = await prismaWithMetrics.$metrics.json();
    const gauges = metrics.gauges;
    const counters = metrics.counters;

    const getGauge = (name: string) => gauges.find((g: any) => g.key === name)?.value ?? 0;
    const getCounter = (name: string) => counters.find((c: any) => c.key === name)?.value ?? 0;

    const stats = {
      open: getGauge('prisma_pool_connections_open'),
      idle: getGauge('prisma_pool_connections_idle'),
      busy: getGauge('prisma_pool_connections_busy'),
      waitedCount: getCounter('prisma_pool_wait_count'),
    };

    console.log('📊 数据库连接池状态:');
    console.log(
      `   总连接: ${stats.open} | 空闲: ${stats.idle} | 繁忙: ${stats.busy} | 等待数: ${stats.waitedCount}`
    );

    return stats;
  } catch (error) {
    console.error('❌ 检查数据库连接池失败:', error);
    return null;
  }
}

/**
 * 测试API请求（真正的冷启动）
 * 使用 ?fresh=true 参数强制忽略所有缓存层
 */
async function testTrueColdStartApiRequests(): Promise<void> {
  console.log('\n🌐 真正的冷启动API测试...');
  console.log('💡 使用 ?fresh=true 参数强制忽略所有缓存层，无需手动清理缓存');

  // 使用不同的单词，避免之前的查询影响
  const testWords = ['apple', 'banana', 'orange', 'grape', 'strawberry'];

  console.log('🚀 执行5个并发API请求（冷启动）...');
  const startTime = performance.now();

  const promises = testWords.map(async (word, index) => {
    const requestStart = performance.now();
    console.log(`   🌐 API请求 ${index + 1}: ${word} (fresh=true)`);

    try {
      const response = await fetch(`${baseUrl}/api/dictionary/en/${word}?fresh=true`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'User-Agent': 'True-Cold-Start-Test/1.0',
        },
        signal: AbortSignal.timeout(10000),
      });

      const responseTime = performance.now() - requestStart;
      const cacheStatus = response.headers.get('x-cache-status');
      const cacheSource = response.headers.get('x-cache-source');

      console.log(
        `   ✅ API请求 ${index + 1} 完成: ${word} (${response.status}, ${Math.round(responseTime)}ms, cache: ${cacheStatus || 'unknown'}, source: ${cacheSource || 'unknown'})`
      );

      return {
        word,
        success: response.ok,
        status: response.status,
        cacheStatus,
        cacheSource,
        responseTime,
      };
    } catch (error) {
      const responseTime = performance.now() - requestStart;
      console.log(`   ❌ API请求 ${index + 1} 失败: ${word} - ${error}`);
      return {
        word,
        success: false,
        error,
        responseTime,
        cacheStatus: 'error',
        cacheSource: 'error',
      };
    }
  });

  const results = await Promise.all(promises);
  const endTime = performance.now();

  // 分析结果
  const successfulRequests = results.filter((r) => r.success);
  const avgResponseTime =
    successfulRequests.length > 0
      ? successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length
      : 0;

  console.log(`\n📊 冷启动测试结果:`);
  console.log(`   总耗时: ${Math.round(endTime - startTime)}ms`);
  console.log(`   成功请求: ${successfulRequests.length}/${results.length}`);
  console.log(`   平均响应时间: ${Math.round(avgResponseTime)}ms`);
  console.log(`   数据来源: 所有请求都应该直接从数据库获取 (fresh=true)`);

  // 显示每个请求的详细信息
  console.log(`\n📋 详细结果:`);
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌';
    const responseTime = Math.round(result.responseTime);
    console.log(
      `   ${status} ${result.word}: ${responseTime}ms (source: ${result.cacheSource || 'unknown'})`
    );
  });

  // 验证是否真的绕过了缓存
  const databaseSources = results.filter((r) => r.cacheSource === 'database').length;
  console.log(`\n🔍 缓存绕过验证:`);
  console.log(`   直接从数据库获取: ${databaseSources}/${results.length}`);

  if (databaseSources === results.length) {
    console.log('✅ 完美！所有请求都直接从数据库获取，成功绕过缓存');
  } else if (databaseSources > 0) {
    console.log('🟡 部分请求绕过缓存，部分仍使用缓存');
  } else {
    console.log('❌ 所有请求都使用了缓存，fresh=true 参数可能未生效');
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🔧 真正的冷启动数据库连接测试\n');
  console.log('💡 使用 ?fresh=true 参数，无需手动清理缓存');

  try {
    // 1. 检查初始状态
    console.log('📋 初始数据库连接池状态:');
    await checkDbPoolStatus();

    // 2. 直接执行冷启动API测试（使用 fresh=true 参数）
    await testTrueColdStartApiRequests();

    // 3. 检查测试后连接池状态
    console.log('\n📋 冷启动测试后连接池状态:');
    const finalStats = await checkDbPoolStatus();

    // 4. 分析结果
    if (finalStats) {
      console.log('\n🔍 数据库连接池分析:');
      if (finalStats.open > 1) {
        console.log(`🎉 成功！连接池创建了 ${finalStats.open} 个连接`);
        console.log('   这证明了数据库连接池配置正常工作');
        console.log('   每个并发请求都正确访问了数据库');
      } else if (finalStats.open === 1) {
        console.log('🤔 仍然只有1个连接');
        console.log('   可能的原因：');
        console.log('   1. 连接复用效率极高');
        console.log('   2. 请求处理速度很快，连接快速释放');
        console.log('   3. Prisma连接池的优化行为');
      }

      if (finalStats.waitedCount > 0) {
        console.log(`⚠️ 有 ${finalStats.waitedCount} 个请求等待连接`);
      } else {
        console.log('✅ 没有请求等待连接');
      }
    }

    console.log('\n✅ 冷启动测试完成');
    console.log('💡 优势：使用 ?fresh=true 参数，测试更简单、更可靠');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
