#!/bin/bash

echo "🧪 Testing NextAuth CORS Configuration"
echo "======================================"

BASE_URL="http://localhost:4000"

echo ""
echo "1. Testing OPTIONS preflight for /api/auth/signin..."
echo "   Origin: https://blog.google"
curl -v -X OPTIONS \
     -H "Origin: https://blog.google" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     "$BASE_URL/api/auth/signin" 2>&1 | grep -E "(< HTTP|< Access-Control|> Origin)"

echo ""
echo "2. Testing OPTIONS preflight for Chrome Extension..."
echo "   Origin: chrome-extension://abcdefghijklmnop"
curl -v -X OPTIONS \
     -H "Origin: chrome-extension://abcdefghijklmnop" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     "$BASE_URL/api/auth/signin" 2>&1 | grep -E "(< HTTP|< Access-Control|> Origin)"

echo ""
echo "3. Testing actual POST request with CORS..."
echo "   Origin: https://blog.google"
curl -v -X POST \
     -H "Origin: https://blog.google" \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","password":"test123"}' \
     "$BASE_URL/api/auth/signin" 2>&1 | grep -E "(< HTTP|< Access-Control|> Origin)" | head -10

echo ""
echo "4. Testing GET request to /api/auth/session..."
curl -v -H "Origin: https://blog.google" \
     "$BASE_URL/api/auth/session" 2>&1 | grep -E "(< HTTP|< Access-Control|> Origin)"

echo ""
echo "✅ NextAuth CORS testing complete!"
echo ""
echo "🔍 If you see 'Access-Control-Allow-Origin: https://blog.google' in the responses above,"
echo "   then CORS is working correctly for your Chrome extension!"
