// Performance monitoring and metrics collection
export interface PerformanceMetrics {
  requestCount: number;
  totalResponseTime: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  throughput: number;
  errorRate: number;
  concurrentRequests: number;
  maxConcurrentRequests: number;
}

export interface SecurityMetrics {
  threatsDetected: number;
  threatsBlocked: number;
  falsePositives: number;
  cacheHitRate: number;
  processingTime: number;
  patternMatchTime: number;
  validationTime: number;
}

export class PerformanceTracker {
  private metrics: PerformanceMetrics;
  private responseTimes: number[] = [];
  private errorCount = 0;
  private currentConcurrency = 0;
  private startTime = Date.now();
  private maxSamples = 10000;

  constructor() {
    this.metrics = {
      requestCount: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      throughput: 0,
      errorRate: 0,
      concurrentRequests: 0,
      maxConcurrentRequests: 0,
    };
  }

  startRequest(): { requestId: string; startTime: number } {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const startTime = performance.now();

    this.currentConcurrency++;
    this.metrics.concurrentRequests = this.currentConcurrency;
    this.metrics.maxConcurrentRequests = Math.max(
      this.metrics.maxConcurrentRequests,
      this.currentConcurrency
    );

    return { requestId, startTime };
  }

  endRequest(requestId: string, startTime: number, isError = false): void {
    const endTime = performance.now();
    const responseTime = endTime - startTime;

    this.currentConcurrency--;
    this.metrics.concurrentRequests = this.currentConcurrency;
    this.metrics.requestCount++;
    this.metrics.totalResponseTime += responseTime;

    if (isError) {
      this.errorCount++;
    }

    // Update response time statistics
    this.updateResponseTimeStats(responseTime);

    // Calculate derived metrics
    this.calculateDerivedMetrics();
  }

  private updateResponseTimeStats(responseTime: number): void {
    this.metrics.minResponseTime = Math.min(this.metrics.minResponseTime, responseTime);
    this.metrics.maxResponseTime = Math.max(this.metrics.maxResponseTime, responseTime);

    // Store response time for percentile calculations
    this.responseTimes.push(responseTime);
    if (this.responseTimes.length > this.maxSamples) {
      this.responseTimes.shift();
    }

    // Calculate percentiles
    const sorted = [...this.responseTimes].sort((a, b) => a - b);
    this.metrics.p95ResponseTime = sorted[Math.floor(sorted.length * 0.95)] || 0;
    this.metrics.p99ResponseTime = sorted[Math.floor(sorted.length * 0.99)] || 0;
  }

  private calculateDerivedMetrics(): void {
    this.metrics.averageResponseTime = this.metrics.totalResponseTime / this.metrics.requestCount;
    this.metrics.errorRate = this.errorCount / this.metrics.requestCount;

    const elapsedSeconds = (Date.now() - this.startTime) / 1000;
    this.metrics.throughput = this.metrics.requestCount / elapsedSeconds;
  }

  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      requestCount: 0,
      totalResponseTime: 0,
      averageResponseTime: 0,
      minResponseTime: Infinity,
      maxResponseTime: 0,
      p95ResponseTime: 0,
      p99ResponseTime: 0,
      throughput: 0,
      errorRate: 0,
      concurrentRequests: this.currentConcurrency,
      maxConcurrentRequests: 0,
    };
    this.responseTimes = [];
    this.errorCount = 0;
    this.startTime = Date.now();
  }
}

export class SecurityMetricsTracker {
  private metrics: SecurityMetrics;
  private processingTimes: number[] = [];
  private patternMatchTimes: number[] = [];
  private validationTimes: number[] = [];

  constructor() {
    this.metrics = {
      threatsDetected: 0,
      threatsBlocked: 0,
      falsePositives: 0,
      cacheHitRate: 0,
      processingTime: 0,
      patternMatchTime: 0,
      validationTime: 0,
    };
  }

  recordThreatDetection(blocked: boolean, falsePositive = false): void {
    this.metrics.threatsDetected++;

    if (blocked) {
      this.metrics.threatsBlocked++;
    }

    if (falsePositive) {
      this.metrics.falsePositives++;
    }
  }

  recordCacheHit(hit: boolean): void {
    // Simple moving average for cache hit rate
    this.metrics.cacheHitRate = this.metrics.cacheHitRate * 0.9 + (hit ? 1 : 0) * 0.1;
  }

  recordProcessingTime(time: number): void {
    this.processingTimes.push(time);
    if (this.processingTimes.length > 1000) {
      this.processingTimes.shift();
    }

    this.metrics.processingTime =
      this.processingTimes.reduce((a, b) => a + b, 0) / this.processingTimes.length;
  }

  recordPatternMatchTime(time: number): void {
    this.patternMatchTimes.push(time);
    if (this.patternMatchTimes.length > 1000) {
      this.patternMatchTimes.shift();
    }

    this.metrics.patternMatchTime =
      this.patternMatchTimes.reduce((a, b) => a + b, 0) / this.patternMatchTimes.length;
  }

  recordValidationTime(time: number): void {
    this.validationTimes.push(time);
    if (this.validationTimes.length > 1000) {
      this.validationTimes.shift();
    }

    this.metrics.validationTime =
      this.validationTimes.reduce((a, b) => a + b, 0) / this.validationTimes.length;
  }

  getMetrics(): SecurityMetrics {
    return { ...this.metrics };
  }

  reset(): void {
    this.metrics = {
      threatsDetected: 0,
      threatsBlocked: 0,
      falsePositives: 0,
      cacheHitRate: 0,
      processingTime: 0,
      patternMatchTime: 0,
      validationTime: 0,
    };
    this.processingTimes = [];
    this.patternMatchTimes = [];
    this.validationTimes = [];
  }
}

// Real-time performance dashboard
export class PerformanceDashboard {
  private performanceTracker: PerformanceTracker;
  private securityTracker: SecurityMetricsTracker;
  private alertThresholds: {
    responseTime: number;
    errorRate: number;
    concurrency: number;
    threatRate: number;
  };

  constructor(
    alertThresholds = {
      responseTime: 1000, // 1 second
      errorRate: 0.05, // 5%
      concurrency: 100,
      threatRate: 0.1, // 10%
    }
  ) {
    this.performanceTracker = new PerformanceTracker();
    this.securityTracker = new SecurityMetricsTracker();
    this.alertThresholds = alertThresholds;
  }

  startRequest() {
    return this.performanceTracker.startRequest();
  }

  endRequest(requestId: string, startTime: number, isError = false) {
    this.performanceTracker.endRequest(requestId, startTime, isError);
    this.checkAlerts();
  }

  recordSecurityEvent(event: {
    threatDetected?: boolean;
    blocked?: boolean;
    falsePositive?: boolean;
    cacheHit?: boolean;
    processingTime?: number;
    patternMatchTime?: number;
    validationTime?: number;
  }) {
    if (event.threatDetected) {
      this.securityTracker.recordThreatDetection(event.blocked || false, event.falsePositive);
    }

    if (event.cacheHit !== undefined) {
      this.securityTracker.recordCacheHit(event.cacheHit);
    }

    if (event.processingTime) {
      this.securityTracker.recordProcessingTime(event.processingTime);
    }

    if (event.patternMatchTime) {
      this.securityTracker.recordPatternMatchTime(event.patternMatchTime);
    }

    if (event.validationTime) {
      this.securityTracker.recordValidationTime(event.validationTime);
    }
  }

  private checkAlerts(): void {
    const perfMetrics = this.performanceTracker.getMetrics();
    const secMetrics = this.securityTracker.getMetrics();

    const alerts: string[] = [];

    if (perfMetrics.averageResponseTime > this.alertThresholds.responseTime) {
      alerts.push(`High response time: ${perfMetrics.averageResponseTime.toFixed(2)}ms`);
    }

    if (perfMetrics.errorRate > this.alertThresholds.errorRate) {
      alerts.push(`High error rate: ${(perfMetrics.errorRate * 100).toFixed(2)}%`);
    }

    if (perfMetrics.concurrentRequests > this.alertThresholds.concurrency) {
      alerts.push(`High concurrency: ${perfMetrics.concurrentRequests} requests`);
    }

    const threatRate = secMetrics.threatsDetected / (perfMetrics.requestCount || 1);
    if (threatRate > this.alertThresholds.threatRate) {
      alerts.push(`High threat rate: ${(threatRate * 100).toFixed(2)}%`);
    }

    if (alerts.length > 0) {
      this.handleAlerts(alerts);
    }
  }

  private handleAlerts(alerts: string[]): void {
    // In a real application, this would send alerts to monitoring systems
    console.warn('Performance Alerts:', alerts);
  }

  getStatus() {
    return {
      performance: this.performanceTracker.getMetrics(),
      security: this.securityTracker.getMetrics(),
      timestamp: new Date().toISOString(),
    };
  }

  generateReport() {
    const perfMetrics = this.performanceTracker.getMetrics();
    const secMetrics = this.securityTracker.getMetrics();

    return {
      summary: {
        totalRequests: perfMetrics.requestCount,
        avgResponseTime: `${perfMetrics.averageResponseTime.toFixed(2)}ms`,
        throughput: `${perfMetrics.throughput.toFixed(2)} req/s`,
        errorRate: `${(perfMetrics.errorRate * 100).toFixed(2)}%`,
        cacheHitRate: `${(secMetrics.cacheHitRate * 100).toFixed(2)}%`,
        threatsBlocked: secMetrics.threatsBlocked,
      },
      performance: {
        responseTime: {
          min: `${perfMetrics.minResponseTime.toFixed(2)}ms`,
          max: `${perfMetrics.maxResponseTime.toFixed(2)}ms`,
          avg: `${perfMetrics.averageResponseTime.toFixed(2)}ms`,
          p95: `${perfMetrics.p95ResponseTime.toFixed(2)}ms`,
          p99: `${perfMetrics.p99ResponseTime.toFixed(2)}ms`,
        },
        concurrency: {
          current: perfMetrics.concurrentRequests,
          max: perfMetrics.maxConcurrentRequests,
        },
      },
      security: {
        threats: {
          detected: secMetrics.threatsDetected,
          blocked: secMetrics.threatsBlocked,
          falsePositives: secMetrics.falsePositives,
        },
        performance: {
          processing: `${secMetrics.processingTime.toFixed(2)}ms`,
          patternMatch: `${secMetrics.patternMatchTime.toFixed(2)}ms`,
          validation: `${secMetrics.validationTime.toFixed(2)}ms`,
        },
      },
    };
  }
}

// Factory function for creating performance dashboard
export function createPerformanceDashboard(alertThresholds?: {
  responseTime?: number;
  errorRate?: number;
  concurrency?: number;
  threatRate?: number;
}): PerformanceDashboard {
  const defaultThresholds = {
    responseTime: 1000,
    errorRate: 0.05,
    concurrency: 100,
    threatRate: 0.1,
  };
  return new PerformanceDashboard(
    alertThresholds ? { ...defaultThresholds, ...alertThresholds } : defaultThresholds
  );
}

// Health check utility
export class HealthChecker {
  private checks: Map<string, () => Promise<boolean>> = new Map();

  addCheck(name: string, check: () => Promise<boolean>): void {
    this.checks.set(name, check);
  }

  async runHealthChecks(): Promise<{ healthy: boolean; checks: Record<string, boolean> }> {
    const results: Record<string, boolean> = {};
    let allHealthy = true;

    for (const [name, check] of this.checks) {
      try {
        results[name] = await check();
        if (!results[name]) {
          allHealthy = false;
        }
      } catch (error) {
        results[name] = false;
        allHealthy = false;
      }
    }

    return {
      healthy: allHealthy,
      checks: results,
    };
  }
}
