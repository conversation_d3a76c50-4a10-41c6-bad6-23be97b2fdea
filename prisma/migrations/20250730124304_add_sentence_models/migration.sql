-- CreateTable
CREATE TABLE "sentence" (
    "id" SERIAL NOT NULL,
    "content" TEXT NOT NULL,
    "source" TEXT,
    "difficulty" INTEGER NOT NULL DEFAULT 5,
    "category" TEXT,
    "favoriteCount" INTEGER NOT NULL DEFAULT 0,
    "length" INTEGER NOT NULL,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "sentence_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_saved_sentence" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "sentenceId" INTEGER NOT NULL,
    "savedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "practiceCount" INTEGER NOT NULL DEFAULT 0,
    "lastPracticedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_saved_sentence_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "sentence_isActive_idx" ON "sentence"("isActive");

-- CreateIndex
CREATE INDEX "sentence_difficulty_idx" ON "sentence"("difficulty");

-- CreateIndex
CREATE INDEX "sentence_category_idx" ON "sentence"("category");

-- CreateIndex
CREATE INDEX "sentence_length_idx" ON "sentence"("length");

-- CreateIndex
CREATE INDEX "sentence_favoriteCount_idx" ON "sentence"("favoriteCount");

-- CreateIndex
CREATE INDEX "user_saved_sentence_userId_idx" ON "user_saved_sentence"("userId");

-- CreateIndex
CREATE INDEX "user_saved_sentence_sentenceId_idx" ON "user_saved_sentence"("sentenceId");

-- CreateIndex
CREATE INDEX "user_saved_sentence_savedAt_idx" ON "user_saved_sentence"("savedAt");

-- CreateIndex
CREATE INDEX "user_saved_sentence_practiceCount_idx" ON "user_saved_sentence"("practiceCount");

-- CreateIndex
CREATE INDEX "user_saved_sentence_lastPracticedAt_idx" ON "user_saved_sentence"("lastPracticedAt");

-- CreateIndex
CREATE INDEX "user_saved_sentence_userId_savedAt_idx" ON "user_saved_sentence"("userId", "savedAt");

-- CreateIndex
CREATE INDEX "user_saved_sentence_userId_practiceCount_idx" ON "user_saved_sentence"("userId", "practiceCount");

-- CreateIndex
CREATE UNIQUE INDEX "user_saved_sentence_userId_sentenceId_key" ON "user_saved_sentence"("userId", "sentenceId");

-- AddForeignKey
ALTER TABLE "user_saved_sentence" ADD CONSTRAINT "user_saved_sentence_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_saved_sentence" ADD CONSTRAINT "user_saved_sentence_sentenceId_fkey" FOREIGN KEY ("sentenceId") REFERENCES "sentence"("id") ON DELETE CASCADE ON UPDATE CASCADE;
