/**
 * SentenceRepository 单元测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { SentenceRepository } from '@/lib/2-repositories/SentenceRepository';
import { SentenceEntity, SentenceCategory, SentenceSource } from '@/lib/3-domain/entities/Sentence';

const prisma = new PrismaClient();
const sentenceRepo = new SentenceRepository();

describe('SentenceRepository', () => {
  beforeEach(async () => {
    // 清理测试数据
    await prisma.userSavedSentence.deleteMany();
    await prisma.sentence.deleteMany();
  });

  afterEach(async () => {
    // 清理测试数据
    await prisma.userSavedSentence.deleteMany();
    await prisma.sentence.deleteMany();
  });

  describe('create', () => {
    it('should create a new sentence', async () => {
      const input = {
        content: 'This is a test sentence for unit testing.',
        category: SentenceCategory.TECHNICAL,
        source: SentenceSource.SYSTEM
      };

      const sentence = await sentenceRepo.create(input);

      expect(sentence).toBeDefined();
      expect(sentence.content).toBe(input.content);
      expect(sentence.category).toBe(input.category);
      expect(sentence.source).toBe(input.source);
      expect(sentence.difficulty).toBeGreaterThan(0);
      expect(sentence.length).toBe(input.content.length);
      expect(sentence.isActive).toBe(true);
      expect(sentence.favoriteCount).toBe(0);
    });

    it('should calculate difficulty automatically', async () => {
      const simpleSentence = {
        content: 'This is simple.',
        category: SentenceCategory.DAILY
      };

      const complexSentence = {
        content: 'The implementation of sophisticated algorithms requires comprehensive understanding of computational complexity theory.',
        category: SentenceCategory.ACADEMIC
      };

      const simple = await sentenceRepo.create(simpleSentence);
      const complex = await sentenceRepo.create(complexSentence);

      expect(complex.difficulty).toBeGreaterThan(simple.difficulty);
    });
  });

  describe('findById', () => {
    it('should find sentence by id', async () => {
      const input = {
        content: 'Test sentence for finding by ID.',
        category: SentenceCategory.TECHNICAL
      };

      const created = await sentenceRepo.create(input);
      const found = await sentenceRepo.findById(created.id);

      expect(found).toBeDefined();
      expect(found?.id).toBe(created.id);
      expect(found?.content).toBe(input.content);
    });

    it('should return null for non-existent id', async () => {
      const found = await sentenceRepo.findById(99999);
      expect(found).toBeNull();
    });
  });

  describe('findMany', () => {
    beforeEach(async () => {
      // 创建测试数据
      await sentenceRepo.create({
        content: 'Easy sentence for beginners.',
        category: SentenceCategory.DAILY,
        difficulty: 1
      });

      await sentenceRepo.create({
        content: 'This is a moderately complex sentence for intermediate learners.',
        category: SentenceCategory.BUSINESS,
        difficulty: 5
      });

      await sentenceRepo.create({
        content: 'Extraordinarily sophisticated and intricate sentence construction demonstrating advanced linguistic proficiency.',
        category: SentenceCategory.ACADEMIC,
        difficulty: 9
      });
    });

    it('should find sentences with difficulty filter', async () => {
      const sentences = await sentenceRepo.findMany({
        difficulty: [1, 5],
        limit: 10
      });

      expect(sentences).toHaveLength(2);
      expect(sentences.every(s => [1, 5].includes(s.difficulty))).toBe(true);
    });

    it('should find sentences with category filter', async () => {
      const sentences = await sentenceRepo.findMany({
        category: SentenceCategory.DAILY
      });

      expect(sentences).toHaveLength(1);
      expect(sentences[0].category).toBe(SentenceCategory.DAILY);
    });

    it('should search sentences by content', async () => {
      const sentences = await sentenceRepo.findMany({
        search: 'moderately'
      });

      expect(sentences).toHaveLength(1);
      expect(sentences[0].content).toContain('moderately');
    });

    it('should paginate results', async () => {
      const page1 = await sentenceRepo.findMany({
        page: 1,
        limit: 2
      });

      const page2 = await sentenceRepo.findMany({
        page: 2,
        limit: 2
      });

      expect(page1).toHaveLength(2);
      expect(page2).toHaveLength(1);
      expect(page1[0].id).not.toBe(page2[0].id);
    });
  });

  describe('getRandomSentence', () => {
    beforeEach(async () => {
      // 创建多个测试句子
      for (let i = 0; i < 5; i++) {
        await sentenceRepo.create({
          content: `Random test sentence number ${i + 1}.`,
          category: SentenceCategory.DAILY
        });
      }
    });

    it('should return a random sentence', async () => {
      const sentence = await sentenceRepo.getRandomSentence();
      expect(sentence).toBeDefined();
      expect(sentence?.content).toContain('Random test sentence');
    });

    it('should return null when no sentences match criteria', async () => {
      const sentence = await sentenceRepo.getRandomSentence({
        difficulty: 10,
        category: SentenceCategory.TECHNICAL
      });
      expect(sentence).toBeNull();
    });
  });

  describe('count', () => {
    beforeEach(async () => {
      await sentenceRepo.create({
        content: 'First test sentence.',
        category: SentenceCategory.DAILY
      });

      await sentenceRepo.create({
        content: 'Second test sentence.',
        category: SentenceCategory.BUSINESS
      });
    });

    it('should count all sentences', async () => {
      const count = await sentenceRepo.count();
      expect(count).toBe(2);
    });

    it('should count sentences with filters', async () => {
      const count = await sentenceRepo.count({
        category: SentenceCategory.DAILY
      });
      expect(count).toBe(1);
    });
  });
});