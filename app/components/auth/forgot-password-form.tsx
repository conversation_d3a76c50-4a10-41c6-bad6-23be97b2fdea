'use client';

import Link from 'next/link';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface FormData {
  email: string;
}

interface FormErrors {
  email?: string;
  general?: string;
}

export function ForgotPasswordForm({ className, ...props }: React.ComponentProps<'div'>) {
  const [formData, setFormData] = useState<FormData>({ email: '' });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.email) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      console.log('📧 发送密码重置请求:', formData.email);

      const response = await fetch('/api/auth/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
        }),
      });

      const data = await response.json();
      console.log('🔍 API响应:', data);

      if (response.ok) {
        console.log('✅ 密码重置请求成功');
        setIsSuccess(true);

        // 开发环境显示重置链接
        if (data.debug?.resetLink) {
          console.log('🔗 开发环境重置链接:', data.debug.resetLink);
          // 可以选择在UI中显示这个链接
        }
      } else {
        console.log('❌ 密码重置请求失败:', data.message);
        setErrors({ general: data.message || '请求失败，请稍后重试' });
      }
    } catch (error) {
      console.error('💥 请求错误:', error);
      setErrors({ general: '网络错误，请检查连接后重试' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // 清除相关错误
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  if (isSuccess) {
    return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl">邮件已发送</CardTitle>
            <CardDescription>如果该邮箱已注册，您将很快收到密码重置链接</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              <div className="text-center space-y-2">
                <p className="text-sm text-muted-foreground">
                  邮件发送到: <strong>{formData.email}</strong>
                </p>
                <p className="text-sm text-muted-foreground">
                  请检查您的邮箱（包括垃圾邮件文件夹）
                </p>
              </div>
              <Button
                variant="outline"
                className="w-full"
                onClick={() => {
                  setIsSuccess(false);
                  setFormData({ email: '' });
                }}
              >
                发送其他邮箱
              </Button>
              <div className="text-center text-sm">
                想起密码了？{' '}
                <Link href="/login" className="underline underline-offset-4">
                  立即登录
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">忘记密码</CardTitle>
          <CardDescription>输入您的邮箱地址，我们将向您发送密码重置链接</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-6">
              {errors.general && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {errors.general}
                </div>
              )}

              <div className="grid gap-3">
                <Label htmlFor="email">邮箱地址</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={errors.email ? 'border-red-500' : ''}
                  required
                />
                {errors.email && <p className="text-sm text-red-600">{errors.email}</p>}
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? '发送中...' : '发送重置邮件'}
              </Button>

              <div className="text-center text-sm">
                想起密码了？{' '}
                <Link href="/login" className="underline underline-offset-4">
                  立即登录
                </Link>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      <div className="text-muted-foreground text-center text-xs text-balance">
        如果您没有收到邮件，请检查垃圾邮件文件夹或{' '}
        <Link href="/signup" className="underline underline-offset-4 hover:text-primary">
          创建新账户
        </Link>
        。
      </div>
    </div>
  );
}
