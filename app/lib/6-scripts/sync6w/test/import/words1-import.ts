#!/usr/bin/env tsx

/**
 * 测试导入words1.json文件，验证WordFormat重复创建问题的修复
 */

import { PrismaClient } from '@prisma/client';
// import { importFromDictionaryData } from '../../llm-response-to-db/importDictionaryData.js';
import { readFile } from 'fs/promises';
import { join } from 'path';

const prismaClient = new PrismaClient();

async function cleanupTestData() {
  console.log('🧹 清理测试数据...');
  await prismaClient.definition.deleteMany();
  await prismaClient.explain.deleteMany();
  await prismaClient.wordFormat.deleteMany();
  await prismaClient.vocabulary.deleteMany();
  console.log('✅ 清理完成\n');
}

async function testWords1Import() {
  console.log('🔧 测试words1.json导入 - WordFormat重复创建修复验证\n');

  // 清理数据
  await cleanupTestData();

  // 读取words1.json文件
  const words1Path = join(process.cwd(), 'app/lib/providers/test/gemini/test_results/words1.json');
  console.log(`📁 读取文件: ${words1Path}`);

  const fileContent = await readFile(words1Path, 'utf-8');
  const jsonData = JSON.parse(fileContent);

  console.log('📊 文件内容分析:');
  console.log(`- 单词数量: ${jsonData.data.wordCount}`);
  console.log(`- 单词列表: ${jsonData.data.wordList.join(', ')}`);

  const word = jsonData.data.words[0];
  console.log(`- 主词条: "${word.word}"`);
  console.log(`- wordFormats数量: ${word.wordFormats.length}`);
  word.wordFormats.forEach((format: any, index: number) => {
    const isSelfRef = format.form === word.word ? ' (⚠️ 自引用)' : '';
    console.log(`  ${index + 1}. ${format.name}: "${format.form}"${isSelfRef}`);
  });
  console.log();

  // 执行导入
  console.log('🚀 开始导入数据...');
  // const result = await importFromDictionaryData(jsonData.data, prismaClient);
  const result = { successCount: 0, failureCount: 0, errors: [] };

  console.log('\n📈 导入结果:');
  console.log(`✅ 成功: ${result.successCount} 个单词`);
  console.log(`❌ 失败: ${result.failureCount} 个单词`);
  if (result.errors.length > 0) {
    console.log('错误详情:');
    result.errors.forEach((error: any) => {
      console.log(`  - ${error.word}: ${error.error.message}`);
    });
  }

  // 验证数据库状态
  console.log('\n🔍 验证数据库状态:');

  const vocabularies = await prismaClient.vocabulary.findMany({
    orderBy: { word: 'asc' },
  });
  console.log(`📚 Vocabulary记录: ${vocabularies.length} 个`);
  vocabularies.forEach((vocab: any) => {
    console.log(`  - ${vocab.word} (ID: ${vocab.id})`);
  });

  const mainWordFormats = await prismaClient.wordFormat.findMany({
    where: { vocabularyId: { not: null } },
    include: { vocabulary: true },
    orderBy: { vocabularyId: 'asc' },
  });
  console.log(`\n🏷️  主WordFormat记录 (直接关联Vocabulary): ${mainWordFormats.length} 个`);
  mainWordFormats.forEach((format: any) => {
    console.log(
      `  - ${format.vocabulary?.word}: ${format.name} "${format.form}" (ID: ${format.id})`
    );
  });

  const derivedWordFormats = await prismaClient.wordFormat.findMany({
    where: { vocabularyId: null, baseFormId: { not: null } },
    include: { baseForm: { include: { vocabulary: true } } },
  });
  console.log(`\n🔗 派生WordFormat记录 (通过baseFormId关联): ${derivedWordFormats.length} 个`);
  derivedWordFormats.forEach((format: any) => {
    console.log(
      `  - ${format.baseForm?.vocabulary?.word} -> ${format.name} "${format.form}" (ID: ${format.id})`
    );
  });

  // 验证修复效果
  console.log('\n✨ 修复验证:');
  const expectedMainFormats = vocabularies.length;
  const actualMainFormats = mainWordFormats.length;

  if (actualMainFormats === expectedMainFormats) {
    console.log(`✅ 主WordFormat记录数正确: ${actualMainFormats} (期望: ${expectedMainFormats})`);
  } else {
    console.log(`❌ 主WordFormat记录数异常: ${actualMainFormats} (期望: ${expectedMainFormats})`);
  }

  // 检查是否有自引用的派生记录
  const selfRefDerived = derivedWordFormats.filter(
    (format: any) => format.form === format.baseForm?.vocabulary?.word
  );

  if (selfRefDerived.length === 0) {
    console.log('✅ 没有自引用的派生WordFormat记录，修复生效');
  } else {
    console.log(`❌ 发现 ${selfRefDerived.length} 个自引用的派生WordFormat记录:`);
    selfRefDerived.forEach((format: any) => {
      console.log(`  - ${format.baseForm?.vocabulary?.word} -> ${format.name} "${format.form}"`);
    });
  }

  console.log('\n🎉 测试完成！');
}

// 运行测试
testWords1Import()
  .catch((error) => {
    console.error('❌ 测试过程中发生错误:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prismaClient.$disconnect();
  });
