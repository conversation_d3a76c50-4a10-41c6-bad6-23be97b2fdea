import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

// 使用简单的Prisma客户端
const simplePrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});

const deleteSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();

    const validation = deleteSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          message: 'Invalid input',
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { email } = validation.data;

    // 使用事务删除用户及其关联数据
    const result = await simplePrisma.$transaction(async (tx) => {
      // 先检查用户是否存在
      const user = await tx.user.findUnique({
        where: { email },
      });

      if (!user) {
        throw new Error('User not found');
      }

      // 删除关联数据（如果存在）
      await tx.account.deleteMany({
        where: { userId: user.id },
      });

      await tx.session.deleteMany({
        where: { userId: user.id },
      });

      await tx.refreshToken.deleteMany({
        where: { userId: user.id },
      });

      await tx.rateLimit.deleteMany({
        where: { userId: user.id },
      });

      await tx.userWordStat.deleteMany({
        where: { userId: user.id },
      });

      // 最后删除用户
      const deletedUser = await tx.user.delete({
        where: { id: user.id },
        select: {
          id: true,
          email: true,
          name: true,
        },
      });

      return deletedUser;
    });

    return NextResponse.json(
      {
        message: 'User deleted successfully',
        user: result,
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Delete user error:', error);

    if (error.message === 'User not found') {
      return NextResponse.json(
        {
          message: 'User not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        message: 'Internal server error',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
