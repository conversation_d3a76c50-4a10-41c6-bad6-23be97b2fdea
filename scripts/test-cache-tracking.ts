#!/usr/bin/env tsx

/**
 * 缓存来源跟踪功能验证脚本
 * 验证多层缓存架构的缓存来源标识和日志功能
 */

const baseUrl = 'http://localhost:3000';

interface CacheTestResult {
  success: boolean;
  data: any;
  cacheSource?: string;
  cacheStatus?: string;
  cacheHitTime?: string;
  cacheLayer?: string;
  responseTime: number;
}

/**
 * 执行API请求并提取缓存信息
 */
async function makeApiRequest(word: string, fresh: boolean = false): Promise<CacheTestResult> {
  const startTime = performance.now();

  const url = `${baseUrl}/api/dictionary/en/${word}${fresh ? '?fresh=true' : ''}`;
  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      'User-Agent': 'Cache-Source-Test/1.0',
    },
  });

  const responseTime = performance.now() - startTime;
  const data = await response.json();

  return {
    success: response.ok,
    data,
    cacheSource: response.headers.get('x-cache-source') || undefined,
    cacheStatus: response.headers.get('x-cache-status') || undefined,
    cacheHitTime: response.headers.get('x-cache-hit-time') || undefined,
    cacheLayer: response.headers.get('x-cache-layer') || undefined,
    responseTime,
  };
}

/**
 * 清空所有缓存
 */
async function clearAllCaches(): Promise<void> {
  try {
    // 清空Redis缓存
    const { redis } = await import('../app/lib/4-infrastructure/cache/redis');
    const keys = await redis.keys('lucid:*');
    if (keys.length > 0) {
      await redis.del(...keys);
      console.log(`✅ 清空 ${keys.length} 个Redis缓存键`);
    }

    // 清空应用级缓存
    const { OptimizedVocabularyRepository } = await import(
      '../app/lib/2-repositories/OptimizedVocabularyRepository'
    );
    const tempRepo = new OptimizedVocabularyRepository();
    tempRepo.clearCache();
    console.log('✅ 清空应用级Repository缓存');

    // 清空全局应用缓存
    const { vocabularyCache, queryResultCache, relationshipCache } = await import(
      '../app/lib/4-infrastructure/cache/ApplicationCache'
    );
    vocabularyCache.clear();
    queryResultCache.clear();
    relationshipCache.clear();
    console.log('✅ 清空全局应用缓存');

    // 重置性能跟踪器
    const { CachePerformanceTracker } = await import('../app/lib/utils/cache-types');
    CachePerformanceTracker.reset();
    console.log('✅ 重置缓存性能跟踪器');
  } catch (error) {
    console.warn('⚠️ 清空缓存时出现错误:', error);
  }
}

/**
 * 测试1：冷启动测试
 * 使用 ?fresh=true 参数强制忽略缓存
 */
async function testColdStart(): Promise<void> {
  console.log('\n🔍 测试1：冷启动测试');
  console.log('='.repeat(50));
  console.log('💡 使用 ?fresh=true 参数强制忽略所有缓存层');

  const testWord = 'computer';

  // 第一次请求，应该从数据库获取
  const result = await makeApiRequest(testWord, true);

  console.log('📊 冷启动测试结果:');
  console.log(`   单词: ${testWord}`);
  console.log(`   成功: ${result.success ? '✅' : '❌'}`);
  console.log(`   缓存状态: ${result.cacheStatus}`);
  console.log(`   缓存来源: ${result.cacheSource}`);
  console.log(`   响应时间: ${result.responseTime.toFixed(1)}ms`);
  console.log(`   搜索策略: ${result.data.queryMetadata?.searchStrategy}`);
  console.log(`   缓存命中: ${result.data.queryMetadata?.cacheHit ? '是' : '否'}`);

  if (result.success && result.cacheStatus === 'miss' && result.cacheSource === 'database') {
    console.log('✅ 冷启动测试通过');
  } else {
    console.log('❌ 冷启动测试失败');
  }
}

/**
 * 测试2：Redis缓存命中测试
 */
async function testRedisCache(): Promise<void> {
  console.log('\n🎯 测试2：Redis缓存命中测试');
  console.log('='.repeat(50));

  const testWord = 'keyboard';

  // 第一次请求 - 建立缓存
  console.log('📝 第一次请求（建立缓存）...');
  const firstResult = await makeApiRequest(testWord);
  console.log(`   缓存状态: ${firstResult.cacheStatus}`);
  console.log(`   响应时间: ${firstResult.responseTime.toFixed(1)}ms`);

  // 等待缓存写入完成
  console.log('⏳ 等待缓存写入完成...');
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 第二次请求 - 应该命中Redis缓存
  console.log('📝 第二次请求（应该命中Redis缓存）...');
  const secondResult = await makeApiRequest(testWord);

  console.log('📊 Redis缓存测试结果:');
  console.log(`   单词: ${testWord}`);
  console.log(`   第一次请求:`);
  console.log(`     缓存状态: ${firstResult.cacheStatus}`);
  console.log(`     响应时间: ${firstResult.responseTime.toFixed(1)}ms`);
  console.log(`   第二次请求:`);
  console.log(`     缓存状态: ${secondResult.cacheStatus}`);
  console.log(`     缓存来源: ${secondResult.cacheSource}`);
  console.log(`     缓存层级: ${secondResult.cacheLayer}`);
  console.log(`     响应时间: ${secondResult.responseTime.toFixed(1)}ms`);
  console.log(`     缓存命中时间: ${secondResult.cacheHitTime}`);

  if (
    secondResult.success &&
    secondResult.cacheStatus === 'hit' &&
    secondResult.cacheSource === 'redis_cache'
  ) {
    console.log('✅ Redis缓存测试通过');
  } else {
    console.log('❌ Redis缓存测试失败');
  }
}

/**
 * 测试3：内存缓存命中测试
 */
async function testMemoryCache(): Promise<void> {
  console.log('\n💾 测试3：内存缓存命中测试');
  console.log('='.repeat(50));

  const testWord = 'monitor';

  try {
    // 使用Repository直接测试内存缓存
    const { OptimizedVocabularyRepository } = await import(
      '../app/lib/2-repositories/OptimizedVocabularyRepository'
    );
    const repo = new OptimizedVocabularyRepository();

    // 第一次查询 - 建立内存缓存
    console.log('📝 第一次Repository查询（建立内存缓存）...');
    const firstResult = await repo.findByWordWithMetadata(testWord);

    // 第二次查询 - 应该命中内存缓存
    console.log('📝 第二次Repository查询（应该命中内存缓存）...');
    const secondResult = await repo.findByWordWithMetadata(testWord);

    console.log('📊 内存缓存测试结果:');
    console.log(`   单词: ${testWord}`);
    console.log(`   第一次查询:`);
    console.log(`     缓存来源: ${firstResult.cacheMetadata.source}`);
    console.log(`     命中时间: ${firstResult.cacheMetadata.hitTime?.toFixed(2)}ms`);
    console.log(`   第二次查询:`);
    console.log(`     缓存来源: ${secondResult.cacheMetadata.source}`);
    console.log(`     命中时间: ${secondResult.cacheMetadata.hitTime?.toFixed(2)}ms`);
    console.log(`     缓存层级: ${secondResult.cacheMetadata.layer}`);

    if (
      firstResult.cacheMetadata.source === 'database' &&
      secondResult.cacheMetadata.source === 'memory_cache'
    ) {
      console.log('✅ 内存缓存测试通过');
    } else {
      console.log('❌ 内存缓存测试失败');
    }
  } catch (error) {
    console.log('❌ 内存缓存测试失败:', error);
  }
}

/**
 * 测试4：缓存统计API测试
 */
async function testCacheStatsAPI(): Promise<void> {
  console.log('\n📊 测试4：缓存统计API测试');
  console.log('='.repeat(50));

  try {
    const response = await fetch(`${baseUrl}/api/debug/cache-stats?detailed=true`);
    const data = await response.json();

    if (response.ok && data.success) {
      console.log('📊 缓存统计API结果:');
      console.log(`   总请求数: ${data.data.performance.totalRequests}`);
      console.log(`   命中率: ${data.data.performance.hitRate}`);
      console.log(`   平均响应时间: ${data.data.performance.averageResponseTime}`);
      console.log(
        `   分布情况: ${data.data.performance.distribution.database || '0%'} 数据库, ${data.data.performance.distribution.redis || '0%'} Redis, ${data.data.performance.distribution.memory || '0%'} 内存`
      );
      console.log('✅ 缓存统计API测试通过');
    } else {
      console.log('❌ 缓存统计API测试失败:', data);
    }
  } catch (error) {
    console.log('❌ 缓存统计API测试失败:', error);
  }
}

/**
 * 主测试函数
 */
async function main(): Promise<void> {
  console.log('🔧 缓存来源跟踪功能验证测试');
  console.log('='.repeat(60));
  console.log('💡 使用 ?fresh=true 参数进行冷启动测试，无需手动清理缓存');
  console.log('');

  try {
    // 运行测试
    await testColdStart();
    await testRedisCache();
    await testMemoryCache();
    await testCacheStatsAPI();

    console.log('\n' + '='.repeat(60));
    console.log('🎉 所有测试完成！');
  } catch (error) {
    console.error('❌ 测试过程中出现错误:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
