# CORS Configuration Guide

This document explains the CORS (Cross-Origin Resource Sharing) configuration implemented in the Lucid Dictionary project.

## Overview

The CORS configuration has been implemented to match the Express.js style configuration you provided, but adapted for Next.js architecture using middleware and security headers.

## Configuration Details

### Allowed Origins

The following origins are allowed to make cross-origin requests:

- `chrome-extension://*` - All Chrome extensions
- `https://*` - All HTTPS websites
- `http://localhost:*` - All localhost ports (for development)

### Allowed Methods

- `GET`
- `POST`
- `PUT`
- `DELETE`
- `OPTIONS`

### Allowed Headers

- `Content-Type`
- `Authorization`
- `X-Requested-With`
- `X-CSRF-Token`

### Additional Settings

- **Credentials**: `true` - Allows cookies and authentication headers
- **Max Age**: `86400` seconds (24 hours) - Cache preflight responses
- **Exposed Headers**: `X-Total-Count`, `X-Request-ID` - Headers accessible to client

## Implementation

### 1. SecurityHeadersManager (`app/lib/utils/cookies.ts`)

The main CORS logic is implemented in the `SecurityHeadersManager` class:

```typescript
// Default CORS configuration
export const DEFAULT_CORS_CONFIG: CORSConfig = {
  origins: ['chrome-extension://*', 'https://*', 'http://localhost:*'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
  maxAge: 86400,
};
```

### 2. Middleware Integration (`middleware.ts`)

The middleware automatically applies CORS headers to all responses:

```typescript
// Handle OPTIONS requests for CORS preflight
if (request.method === 'OPTIONS') {
  const response = new NextResponse(null, { status: 200 });
  SecurityHeadersManager.addSecurityHeaders(response, request);
  return response;
}
```

### 3. Next.js Configuration (`next.config.ts`)

Fallback CORS headers are configured at the Next.js level:

```typescript
{
  source: '/api/:path*',
  headers: [
    {
      key: 'Access-Control-Allow-Methods',
      value: 'GET, POST, PUT, DELETE, OPTIONS',
    },
    // ... other headers
  ],
}
```

## Testing

### Test Endpoint

A test endpoint is available at `/api/test-cors` to verify CORS functionality:

```bash
# Test GET request
curl -H "Origin: https://example.com" http://localhost:3000/api/test-cors

# Test OPTIONS preflight
curl -X OPTIONS -H "Origin: https://example.com" http://localhost:3000/api/test-cors

# Test POST request
curl -X POST -H "Origin: https://example.com" -H "Content-Type: application/json" \
  -d '{"test": "data"}' http://localhost:3000/api/test-cors
```

### Expected Response Headers

For allowed origins, you should see these headers in the response:

```
Access-Control-Allow-Origin: https://example.com
Access-Control-Allow-Credentials: true
Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With, X-CSRF-Token
Access-Control-Max-Age: 86400
Access-Control-Expose-Headers: X-Total-Count, X-Request-ID
```

## Security Considerations

1. **Origin Validation**: Only explicitly allowed origins receive CORS headers
2. **Wildcard Patterns**: Patterns like `https://*` are safely implemented with proper validation
3. **Credentials**: Enabled to support authenticated requests from extensions and web apps
4. **CSRF Protection**: CSRF tokens are supported through the `X-CSRF-Token` header

## Browser Extension Support

The configuration specifically supports Chrome extensions by allowing `chrome-extension://*` origins. This enables:

- Extension popup requests to the API
- Content script requests from web pages
- Background script API calls

## Development vs Production

The CORS configuration works in both environments:

- **Development**: Allows `http://localhost:*` for local development
- **Production**: Maintains security while allowing legitimate cross-origin requests

## Troubleshooting

### Common Issues

1. **CORS Error**: Check if the origin is in the allowed list
2. **Preflight Failure**: Ensure OPTIONS method is handled
3. **Credentials Issues**: Verify `Access-Control-Allow-Credentials` is set to `true`

### Debug Headers

Each response includes an `X-Request-ID` header for debugging purposes.

## Migration from Express.js

This Next.js implementation provides equivalent functionality to the Express.js CORS configuration:

```javascript
// Express.js (original)
app.use(
  cors({
    origin: ['chrome-extension://*', 'https://*', 'http://localhost:*'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  })
);

// Next.js (implemented)
SecurityHeadersManager.addSecurityHeaders(response, request);
```

The Next.js implementation provides the same security and functionality with additional features like request ID tracking and enhanced security headers.
