/**
 * Authentication Error Type Definitions
 * 认证系统错误类型定义
 */

export interface AuthError {
  /** 错误代码 */
  code: string;
  /** 错误消息 */
  message: string;
  /** 详细信息 */
  details?: any;
  /** 时间戳 */
  timestamp: string;
}

export interface ValidationError extends AuthError {
  /** 验证错误字段 */
  field: string;
  /** 验证规则 */
  rule: string;
}

export interface AuthApiError {
  /** 错误状态 */
  error: true;
  /** 错误消息 */
  message: string;
  /** 错误代码 */
  code: string;
  /** 错误详情 */
  details?: any;
  /** 时间戳 */
  timestamp: string;
}

export interface RateLimitError extends AuthApiError {
  /** 重试间隔(秒) */
  retryAfter: number;
  /** 剩余尝试次数 */
  remainingAttempts: number;
}

export interface TokenError extends AuthApiError {
  /** 令牌类型 */
  tokenType: 'access' | 'refresh';
  /** 过期时间 */
  expiredAt?: string;
}

export interface ValidationErrorResponse extends AuthApiError {
  /** 验证错误列表 */
  errors: ValidationError[];
}

export enum AuthErrorCode {
  // 认证错误
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  ACCOUNT_DISABLED = 'ACCOUNT_DISABLED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',

  // 令牌错误
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_REVOKED = 'TOKEN_REVOKED',
  REFRESH_TOKEN_EXPIRED = 'REFRESH_TOKEN_EXPIRED',
  REFRESH_TOKEN_INVALID = 'REFRESH_TOKEN_INVALID',

  // 验证错误
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  EMAIL_INVALID = 'EMAIL_INVALID',
  PASSWORD_WEAK = 'PASSWORD_WEAK',
  EMAIL_ALREADY_EXISTS = 'EMAIL_ALREADY_EXISTS',

  // 限制错误
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  TOO_MANY_REQUESTS = 'TOO_MANY_REQUESTS',

  // 授权错误
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  FORBIDDEN = 'FORBIDDEN',

  // 服务器错误
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',

  // 第三方认证错误
  OAUTH_ERROR = 'OAUTH_ERROR',
  PROVIDER_ERROR = 'PROVIDER_ERROR',
}

export const AUTH_ERROR_MESSAGES: Record<AuthErrorCode, string> = {
  [AuthErrorCode.INVALID_CREDENTIALS]: '邮箱或密码错误',
  [AuthErrorCode.ACCOUNT_DISABLED]: '账户已被禁用',
  [AuthErrorCode.ACCOUNT_LOCKED]: '账户已被锁定',
  [AuthErrorCode.TOKEN_EXPIRED]: '访问令牌已过期',
  [AuthErrorCode.TOKEN_INVALID]: '访问令牌无效',
  [AuthErrorCode.TOKEN_REVOKED]: '访问令牌已被撤销',
  [AuthErrorCode.REFRESH_TOKEN_EXPIRED]: '刷新令牌已过期',
  [AuthErrorCode.REFRESH_TOKEN_INVALID]: '刷新令牌无效',
  [AuthErrorCode.VALIDATION_ERROR]: '数据验证失败',
  [AuthErrorCode.EMAIL_INVALID]: '邮箱格式无效',
  [AuthErrorCode.PASSWORD_WEAK]: '密码强度不够',
  [AuthErrorCode.EMAIL_ALREADY_EXISTS]: '邮箱已存在',
  [AuthErrorCode.RATE_LIMIT_EXCEEDED]: '请求过于频繁',
  [AuthErrorCode.TOO_MANY_REQUESTS]: '请求次数过多',
  [AuthErrorCode.INSUFFICIENT_PERMISSIONS]: '权限不足',
  [AuthErrorCode.FORBIDDEN]: '禁止访问',
  [AuthErrorCode.INTERNAL_SERVER_ERROR]: '服务器内部错误',
  [AuthErrorCode.SERVICE_UNAVAILABLE]: '服务暂时不可用',
  [AuthErrorCode.OAUTH_ERROR]: '第三方认证错误',
  [AuthErrorCode.PROVIDER_ERROR]: '认证提供商错误',
};

export class AuthenticationError extends Error {
  public readonly code: AuthErrorCode;
  public readonly timestamp: string;
  public readonly details?: any;

  constructor(code: AuthErrorCode, message?: string, details?: any) {
    super(message || AUTH_ERROR_MESSAGES[code]);
    this.name = 'AuthenticationError';
    this.code = code;
    this.timestamp = new Date().toISOString();
    this.details = details;
  }

  toJSON(): AuthError {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
    };
  }
}
