/**
 * 打字练习服务
 * 管理所有与打字练习相关的业务逻辑，包括生词本管理和词库练习
 */

import { UserWordProficiencyRepository, UserWordProficiencyWithVocabulary } from '@/lib/2-repositories/UserWordProficiencyRepository';
import { VocabularyRepository } from '@/lib/2-repositories/VocabularyRepository';
import { WordListRepository } from '@/lib/2-repositories/WordListRepository';
import { WordListEntryRepository } from '@/lib/2-repositories/WordListEntryRepository';
import { SentenceRepository } from '@/lib/2-repositories/SentenceRepository';
import { UserSavedSentenceRepository } from '@/lib/2-repositories/UserSavedSentenceRepository';
import { ProficiencyCalculationService } from './ProficiencyCalculationService';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import {
  WordWithProficiency,
  UserWordList,
  PaginationInfo,
  GetWordsQuery,
  ConflictError,
  NotFoundError,
  ValidationError,
} from '@/lib/types/api-types';
import {
  WordListStats,
  WordList
} from '@/lib/3-domain/entities/WordList';
import {
  WordListPracticeWord,
  WordListPracticeSession,
  WordListEntryQueryOptions
} from '@/lib/3-domain/entities/WordListEntry';
import { 
  SentenceWithUserContext,
  Sentence
} from '@/lib/3-domain/entities/Sentence';
import {
  UserSavedSentence,
  UserSavedSentenceWithSentence,
  UserSentencePracticeStats,
  CreateUserSavedSentenceInput
} from '@/lib/3-domain/entities/UserSavedSentence';
import { SentenceQueryOptions, SentenceWithContextOptions } from '@/lib/2-repositories/SentenceRepository';
import { UserSavedSentenceQueryOptions } from '@/lib/2-repositories/UserSavedSentenceRepository';

export interface AddWordsResult {
  added: number;
  skipped: number;
  errors: string[];
}

export interface DeleteWordsResult {
  deleted: number;
  notFound: number;
}

export class PracticeService {
  constructor(
    private userProficiencyRepo: UserWordProficiencyRepository,
    private vocabularyRepo: VocabularyRepository,
    private proficiencyService: ProficiencyCalculationService,
    private wordListRepo: WordListRepository,
    private wordListEntryRepo: WordListEntryRepository,
    private sentenceRepo: SentenceRepository,
    private userSavedSentenceRepo: UserSavedSentenceRepository
  ) {}

  /**
   * 获取用户生词列表（分页）
   */
  async getUserWordList(
    userId: string,
    query: GetWordsQuery
  ): Promise<UserWordList> {
    const { page, limit, sortBy, order } = query;
    const offset = (page - 1) * limit;

    // 确定排序字段
    let sortField: 'proficiencyScore' | 'lastPracticed' | 'practiceCount';
    switch (sortBy) {
      case 'proficiency':
        sortField = 'proficiencyScore';
        break;
      case 'time':
        sortField = 'lastPracticed';
        break;
      case 'alphabetical':
        // 对于字母序，我们需要通过vocabulary.word排序
        return this.getUserWordListAlphabetical(userId, page, limit, order);
      default:
        sortField = 'lastPracticed';
    }

    // 获取数据
    const [proficiencies, total] = await Promise.all([
      this.userProficiencyRepo.findManyByUser(userId, {
        sortBy: sortField,
        sortOrder: order,
        limit,
        offset,
      }),
      this.getUserWordCount(userId),
    ]);

    // 转换为API格式
    const words: WordWithProficiency[] = proficiencies.map((prof) => ({
      id: prof.wordId,
      word: prof.vocabulary.word,
      phonetics: prof.vocabulary.phonetics,
      freqRank: prof.vocabulary.freqRank,
      proficiency: {
        practiceCount: prof.practiceCount,
        errorCount: prof.errorCount,
        averageTime: prof.averageTime,
        proficiencyScore: prof.proficiencyScore,
        isMarked: prof.isMarked,
        lastPracticed: prof.lastPracticed,
      },
    }));

    const pagination: PaginationInfo = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total,
    };

    return { words, pagination };
  }

  /**
   * 按字母序获取用户生词列表
   */
  private async getUserWordListAlphabetical(
    userId: string,
    page: number,
    limit: number,
    order: 'asc' | 'desc'
  ): Promise<UserWordList> {
    const offset = (page - 1) * limit;

    const proficiencies = await prisma.userWordProficiency.findMany({
      where: { userId },
      include: {
        vocabulary: {
          select: {
            id: true,
            word: true,
            phonetics: true,
            freqRank: true,
          },
        },
      },
      orderBy: {
        vocabulary: {
          word: order,
        },
      },
      take: limit,
      skip: offset,
    });

    const total = await this.getUserWordCount(userId);

    const words: WordWithProficiency[] = proficiencies.map((prof) => ({
      id: prof.wordId,
      word: prof.vocabulary.word,
      phonetics: prof.vocabulary.phonetics,
      freqRank: prof.vocabulary.freqRank,
      proficiency: {
        practiceCount: prof.practiceCount,
        errorCount: prof.errorCount,
        averageTime: prof.averageTime,
        proficiencyScore: prof.proficiencyScore,
        isMarked: prof.isMarked,
        lastPracticed: prof.lastPracticed,
      },
    }));

    const pagination: PaginationInfo = {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total,
    };

    return { words, pagination };
  }

  /**
   * 批量添加生词到用户生词本
   */
  async addWordsToUserList(
    userId: string,
    words?: string[],
    wordIds?: number[]
  ): Promise<AddWordsResult> {
    const result: AddWordsResult = {
      added: 0,
      skipped: 0,
      errors: [],
    };

    if (!words && !wordIds) {
      throw new ValidationError('Either words or wordIds must be provided');
    }

    try {
      let targetWordIds: number[] = [];

      // 处理通过单词文本添加的情况
      if (words && words.length > 0) {
        const vocabularies = await this.vocabularyRepo.findManyByWords(words);
        const foundWords = vocabularies.map((v) => v.word);
        const missingWords = words.filter((w) => !foundWords.includes(w));

        // 记录未找到的单词
        if (missingWords.length > 0) {
          result.errors.push(`Words not found: ${missingWords.join(', ')}`);
        }

        targetWordIds = vocabularies.map((v) => v.id);
      }

      // 处理通过ID添加的情况
      if (wordIds && wordIds.length > 0) {
        // 验证词汇ID是否存在
        const existingVocabs = await this.vocabularyRepo.findManyByIds(wordIds);
        const existingIds = existingVocabs.map((v) => v.id);
        const missingIds = wordIds.filter((id) => !existingIds.includes(id));

        if (missingIds.length > 0) {
          result.errors.push(`Word IDs not found: ${missingIds.join(', ')}`);
        }

        targetWordIds = [...targetWordIds, ...existingIds];
      }

      if (targetWordIds.length === 0) {
        return result;
      }

      // 检查哪些单词已经在用户生词本中
      const existingProficiencies = await prisma.userWordProficiency.findMany({
        where: {
          userId,
          wordId: { in: targetWordIds },
        },
        select: { wordId: true },
      });

      const existingWordIds = existingProficiencies.map((p) => p.wordId);
      const newWordIds = targetWordIds.filter((id) => !existingWordIds.includes(id));

      result.skipped = existingWordIds.length;

      // 批量创建新的用户-单词关联
      if (newWordIds.length > 0) {
        const newProficiencies = newWordIds.map((wordId) => ({
          userId,
          wordId,
          practiceCount: 0,
          errorCount: 0,
          averageTime: 0,
          isMarked: false,
          proficiencyScore: 0.0,
        }));

        await this.userProficiencyRepo.upsertMany(newProficiencies);
        result.added = newWordIds.length;
      }

      return result;
    } catch (error) {
      console.error('Error adding words to user list:', error);
      throw new Error('Failed to add words to user list');
    }
  }

  /**
   * 批量从用户生词本删除单词
   */
  async removeWordsFromUserList(
    userId: string,
    wordIds: number[]
  ): Promise<DeleteWordsResult> {
    const result: DeleteWordsResult = {
      deleted: 0,
      notFound: 0,
    };

    try {
      // 检查哪些单词在用户生词本中
      const existingProficiencies = await prisma.userWordProficiency.findMany({
        where: {
          userId,
          wordId: { in: wordIds },
        },
        select: { wordId: true },
      });

      const existingWordIds = existingProficiencies.map((p) => p.wordId);
      result.notFound = wordIds.length - existingWordIds.length;

      // 批量删除
      if (existingWordIds.length > 0) {
        await prisma.userWordProficiency.deleteMany({
          where: {
            userId,
            wordId: { in: existingWordIds },
          },
        });
        result.deleted = existingWordIds.length;
      }

      return result;
    } catch (error) {
      console.error('Error removing words from user list:', error);
      throw new Error('Failed to remove words from user list');
    }
  }

  /**
   * 获取用户生词总数
   */
  async getUserWordCount(userId: string): Promise<number> {
    return await prisma.userWordProficiency.count({
      where: { userId },
    });
  }

  /**
   * 获取用户生词统计信息
   */
  async getUserWordStats(userId: string) {
    const stats = await this.userProficiencyRepo.getUserStats(userId);
    const proficiencyLevel = await this.proficiencyService.getUserProficiencyStats(userId);

    return {
      ...stats,
      proficiencyLevel: proficiencyLevel.proficiencyLevel,
    };
  }

  /**
   * 获取推荐练习的单词
   */
  async getRecommendedWords(userId: string, limit: number = 20) {
    return await this.proficiencyService.getRecommendedWordsForPractice(userId, limit);
  }

  /**
   * 获取需要复习的单词
   */
  async getWordsForReview(userId: string, daysSinceLastPractice: number = 7) {
    return await this.proficiencyService.getWordsForReview(userId, daysSinceLastPractice);
  }

  /**
   * 切换单词标记状态
   */
  async toggleWordMarking(userId: string, wordId: number, isMarked: boolean) {
    // 检查用户是否有该单词
    const proficiency = await this.userProficiencyRepo.findByUserAndWord(userId, wordId);
    if (!proficiency) {
      throw new NotFoundError('Word not found in user\'s vocabulary list');
    }

    await this.proficiencyService.toggleWordMarking(userId, wordId, isMarked);
  }

  // ===== 词库相关方法 =====

  /**
   * 获取所有可用的词库列表
   */
  async getAvailableWordLists(userId?: string): Promise<WordListStats[]> {
    return await this.wordListRepo.getStatsWithUserProgress(userId);
  }

  /**
   * 获取特定词库信息
   */
  async getWordListById(wordListId: number): Promise<WordList | null> {
    return await this.wordListRepo.findById(wordListId);
  }

  /**
   * 获取词库的练习单词
   */
  async getWordListWords(
    wordListId: number,
    userId: string,
    options: WordListEntryQueryOptions
  ): Promise<WordListPracticeWord[]> {
    // 验证词库是否存在且激活
    const wordList = await this.wordListRepo.findById(wordListId);
    if (!wordList) {
      throw new NotFoundError(`Word list with ID ${wordListId} not found`);
    }

    if (!wordList.isActive) {
      throw new ValidationError(`Word list "${wordList.name}" is not currently active`);
    }

    return await this.wordListEntryRepo.getPracticeWords(wordListId, userId, options);
  }

  /**
   * 获取词库练习会话
   */
  async getWordListPracticeSession(
    wordListId: number,
    userId: string,
    options: WordListEntryQueryOptions
  ): Promise<WordListPracticeSession> {
    // 验证词库是否存在且激活
    const wordList = await this.wordListRepo.findById(wordListId);
    if (!wordList) {
      throw new NotFoundError(`Word list with ID ${wordListId} not found`);
    }

    if (!wordList.isActive) {
      throw new ValidationError(`Word list "${wordList.name}" is not currently active`);
    }

    return await this.wordListEntryRepo.getPracticeSession(wordListId, userId, options);
  }

  /**
   * 记录词库单词练习进度
   */
  async recordWordListProgress(
    userId: string,
    wordListId: number,
    word: string,
    practiceResult: {
      isCorrect: boolean;
      timeTaken: number;
      errorCount?: number;
      sessionType?: 'typing' | 'quiz' | 'review';
    }
  ): Promise<{
    proficiency: any;
    improvement: number;
    suggestion: string;
  }> {
    // 验证词库是否存在
    const wordList = await this.wordListRepo.findById(wordListId);
    if (!wordList) {
      throw new NotFoundError(`Word list with ID ${wordListId} not found`);
    }

    // 验证单词是否在词库中
    const wordEntry = await this.wordListEntryRepo.findByWord(word);
    if (!wordEntry || !wordEntry.wordListIds.includes(wordListId)) {
      throw new ValidationError(`Word "${word}" is not in the specified word list`);
    }

    // 查找或创建Vocabulary记录
    let vocabulary = await this.vocabularyRepo.findByWord(word);
    if (!vocabulary) {
      // 创建基础词汇记录
      const newVocabulary = await prisma.vocabulary.create({
        data: {
          word: word.toLowerCase(),
          phonetics: [],
          freqRank: null,
        }
      });
      // 转换为Domain实体
      const { Vocabulary } = await import('@/lib/3-domain/entities/Vocabulary');
      vocabulary = Vocabulary.create({
        id: newVocabulary.id,
        word: newVocabulary.word,
        phonetics: newVocabulary.phonetics,
        freqRank: newVocabulary.freqRank || undefined,
        explains: [],
        formats: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // 查找或创建UserWordProficiency记录
    let userProficiency = await this.userProficiencyRepo.findByUserAndWord(userId, vocabulary.id);

    if (!userProficiency) {
      // 创建新的熟练度记录
      const newProficiencyData = {
        userId,
        wordId: vocabulary.id,
        practiceCount: 1,
        errorCount: practiceResult.isCorrect ? 0 : (practiceResult.errorCount || 1),
        averageTime: practiceResult.timeTaken,
        proficiencyScore: practiceResult.isCorrect ? 0.2 : 0.1,
        lastPracticed: new Date(),
        isMarked: false
      };

      userProficiency = await this.userProficiencyRepo.create(newProficiencyData);
    } else {
      // 更新现有熟练度记录
      const newPracticeCount = userProficiency.practiceCount + 1;
      const newErrorCount = userProficiency.errorCount + (practiceResult.isCorrect ? 0 : (practiceResult.errorCount || 1));
      
      // 计算新的平均时间
      const newAverageTime = Math.round(
        (userProficiency.averageTime * userProficiency.practiceCount + practiceResult.timeTaken) / newPracticeCount
      );

      // 计算新的熟练度分数
      const accuracy = 1 - (newErrorCount / newPracticeCount);
      const speedFactor = Math.max(0.1, Math.min(1.0, 3000 / newAverageTime));
      const practiceFactor = Math.min(1.0, newPracticeCount / 10);
      
      const newProficiencyScore = Math.min(1.0, accuracy * 0.5 + speedFactor * 0.3 + practiceFactor * 0.2);

      const updateData = {
        userId,
        wordId: vocabulary.id,
        practiceCount: newPracticeCount,
        errorCount: newErrorCount,
        averageTime: newAverageTime,
        proficiencyScore: newProficiencyScore,
        lastPracticed: new Date(),
        isMarked: userProficiency.isMarked
      };

      userProficiency = await this.userProficiencyRepo.update(userId, vocabulary.id, {
        practiceCount: newPracticeCount,
        errorCount: newErrorCount,
        averageTime: newAverageTime,
        proficiencyScore: newProficiencyScore,
        lastPracticed: new Date(),
      });
    }

    // 计算改进度和建议
    const improvement = userProficiency.practiceCount > 1 ? 
      userProficiency.proficiencyScore - (userProficiency.practiceCount === 2 ? 0.1 : 0) : 0;
    
    const suggestion = userProficiency.proficiencyScore < 0.3 ? 'needs_more_practice' :
                      userProficiency.proficiencyScore < 0.7 ? 'good_progress' : 'mastered';

    return {
      proficiency: {
        practiceCount: userProficiency.practiceCount,
        errorCount: userProficiency.errorCount,
        averageTime: userProficiency.averageTime,
        proficiencyScore: userProficiency.proficiencyScore,
        isMarked: userProficiency.isMarked,
        lastPracticed: userProficiency.lastPracticed
      },
      improvement,
      suggestion
    };
  }

  /**
   * 将词库中的单词加入用户生词本
   */
  async addWordListWordToUserWordBook(
    userId: string,
    wordListId: number,
    word: string
  ): Promise<boolean> {
    // 验证词库和单词
    const wordList = await this.wordListRepo.findById(wordListId);
    if (!wordList) {
      throw new NotFoundError(`Word list with ID ${wordListId} not found`);
    }

    const wordEntry = await this.wordListEntryRepo.findByWord(word);
    if (!wordEntry || !wordEntry.wordListIds.includes(wordListId)) {
      throw new ValidationError(`Word "${word}" is not in the specified word list`);
    }

    // 查找或创建Vocabulary记录  
    let vocabulary = await this.vocabularyRepo.findByWord(word);
    if (!vocabulary) {
      const newVocabulary = await prisma.vocabulary.create({
        data: {
          word: word.toLowerCase(),
          phonetics: [],
          freqRank: null
        }
      });
      // 转换为Domain实体
      const { Vocabulary } = await import('@/lib/3-domain/entities/Vocabulary');
      vocabulary = Vocabulary.create({
        id: newVocabulary.id,
        word: newVocabulary.word,
        phonetics: newVocabulary.phonetics,
        freqRank: newVocabulary.freqRank || undefined,
        explains: [],
        formats: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    }

    // 查找或创建UserWordProficiency记录，并标记为生词本
    const existingProficiency = await this.userProficiencyRepo.findByUserAndWord(userId, vocabulary.id);
    
    if (existingProficiency) {
      // 如果已存在，标记为生词本
      await this.userProficiencyRepo.update(userId, vocabulary.id, {
        isMarked: true
      });
    } else {
      // 创建新记录并标记为生词本
      await this.userProficiencyRepo.create({
        userId,
        wordId: vocabulary.id,
        practiceCount: 0,
        errorCount: 0,
        averageTime: 0,
        proficiencyScore: 0.0,
        isMarked: true,
        lastPracticed: undefined
      });
    }

    return true;
  }

  /**
   * 获取用户在特定词库中的进度统计
   */
  async getUserWordListProgress(userId: string, wordListId: number): Promise<{
    totalWords: number;
    practicedWords: number;
    masteredWords: number;
    progressPercentage: number;
    averageProficiency: number;
  }> {
    const wordList = await this.wordListRepo.findById(wordListId);
    if (!wordList) {
      throw new NotFoundError(`Word list with ID ${wordListId} not found`);
    }

    // 获取词库中的所有单词
    const wordEntries = await this.wordListEntryRepo.findMany({
      wordListIds: [wordListId],
      limit: 10000 // 设置一个较大的限制以获取所有单词
    });

    const totalWords = wordEntries.length;
    
    if (totalWords === 0) {
      return {
        totalWords: 0,
        practicedWords: 0,
        masteredWords: 0,
        progressPercentage: 0,
        averageProficiency: 0
      };
    }

    // 获取用户对这些单词的熟练度数据
    const words = wordEntries.map(entry => entry.word);
    const vocabularies = await this.vocabularyRepo.findManyByWords(words);
    const vocabularyIds = vocabularies.map(v => v.id);

    const userProficiencies = await prisma.userWordProficiency.findMany({
      where: {
        userId,
        wordId: { in: vocabularyIds }
      }
    });

    const practicedWords = userProficiencies.length;
    const masteredWords = userProficiencies.filter(p => p.proficiencyScore >= 0.7).length;
    const totalProficiency = userProficiencies.reduce((sum, p) => sum + p.proficiencyScore, 0);

    return {
      totalWords,
      practicedWords,
      masteredWords,
      progressPercentage: Math.round((practicedWords / totalWords) * 100),
      averageProficiency: practicedWords > 0 ? totalProficiency / practicedWords : 0
    };
  }

  // ===== 句子练习相关方法 =====

  /**
   * 获取练习句子（随机或根据难度筛选）
   */
  async getPracticeSentence(
    userId: string,
    options: SentenceWithContextOptions = {}
  ): Promise<SentenceWithUserContext | null> {
    // 设置默认选项
    const sentenceOptions: SentenceWithContextOptions = {
      userId,
      isActive: true,
      sortBy: 'random',
      limit: 1,
      ...options
    };

    return await this.sentenceRepo.getRandomSentence(sentenceOptions);
  }

  /**
   * 保存句子到用户收藏夹
   */
  async saveSentenceToFavorites(
    userId: string,
    sentenceId: number
  ): Promise<UserSavedSentence> {
    // 检查句子是否存在
    const sentence = await this.sentenceRepo.findById(sentenceId);
    if (!sentence) {
      throw new NotFoundError(`Sentence with ID ${sentenceId} not found`);
    }

    if (!sentence.isActive) {
      throw new ValidationError('Cannot save inactive sentence');
    }

    // 检查是否已经收藏
    const existingSaved = await this.userSavedSentenceRepo.findByUserAndSentence(userId, sentenceId);
    if (existingSaved) {
      throw new ConflictError('Sentence is already saved to favorites');
    }

    // 保存到收藏夹
    const input: CreateUserSavedSentenceInput = {
      userId,
      sentenceId
    };

    return await this.userSavedSentenceRepo.save(input);
  }

  /**
   * 从用户收藏夹移除句子
   */
  async removeSentenceFromFavorites(
    userId: string,
    sentenceId: number
  ): Promise<boolean> {
    const removed = await this.userSavedSentenceRepo.remove(userId, sentenceId);
    if (!removed) {
      throw new NotFoundError('Sentence not found in user favorites');
    }
    return removed;
  }

  /**
   * 检查句子是否已被用户收藏
   */
  async isSentenceSaved(userId: string, sentenceId: number): Promise<boolean> {
    return await this.userSavedSentenceRepo.isSaved(userId, sentenceId);
  }

  /**
   * 获取用户收藏的句子列表
   */
  async getUserFavoriteSentences(
    userId: string,
    options: UserSavedSentenceQueryOptions
  ): Promise<UserSavedSentenceWithSentence[]> {
    const { userId: _, ...queryOptions } = options as any;
    return await this.userSavedSentenceRepo.findByUserWithSentences({
      userId,
      ...queryOptions
    });
  }

  /**
   * 记录句子练习结果
   */
  async recordSentencePractice(
    userId: string,
    sentenceId: number
  ): Promise<UserSavedSentence | null> {
    // 检查用户是否收藏了这个句子
    const userSavedSentence = await this.userSavedSentenceRepo.findByUserAndSentence(userId, sentenceId);
    if (!userSavedSentence) {
      throw new ValidationError('Sentence must be saved to favorites before recording practice');
    }

    // 增加练习次数
    return await this.userSavedSentenceRepo.incrementPracticeCount(userId, sentenceId);
  }

  /**
   * 获取用户句子练习统计
   */
  async getUserSentencePracticeStats(userId: string): Promise<UserSentencePracticeStats> {
    return await this.userSavedSentenceRepo.getUserPracticeStats(userId);
  }

  /**
   * 获取需要练习的句子
   */
  async getSentencesNeedingPractice(
    userId: string,
    limit: number = 10
  ): Promise<UserSavedSentenceWithSentence[]> {
    return await this.userSavedSentenceRepo.getSentencesNeedingPractice(userId, limit);
  }

  /**
   * 搜索句子
   */
  async searchSentences(
    userId: string,
    searchTerm: string,
    options: SentenceWithContextOptions = {}
  ): Promise<SentenceWithUserContext[]> {
    const searchOptions: SentenceWithContextOptions = {
      userId,
      search: searchTerm,
      isActive: true,
      ...options
    };

    return await this.sentenceRepo.findManyWithUserContext(searchOptions);
  }

  /**
   * 获取句子列表（带用户上下文）
   */
  async getSentencesWithUserContext(
    userId: string,
    options: SentenceWithContextOptions = {}
  ): Promise<SentenceWithUserContext[]> {
    const sentenceOptions: SentenceWithContextOptions = {
      userId,
      isActive: true,
      ...options
    };

    return await this.sentenceRepo.findManyWithUserContext(sentenceOptions);
  }

  /**
   * 获取用户收藏句子数量
   */
  async getUserFavoriteSentenceCount(
    userId: string,
    filters?: {
      difficulty?: number | number[];
      category?: string | string[];
      practiceFrequency?: 'never' | 'rarely' | 'sometimes' | 'often' | 'frequently';
      needsPractice?: boolean;
    }
  ): Promise<number> {
    return await this.userSavedSentenceRepo.countByUser(userId, filters);
  }
}

// 创建默认实例
export function createPracticeService(): PracticeService {
  const userProficiencyRepo = new UserWordProficiencyRepository(prisma);
  const vocabularyRepo = new VocabularyRepository();
  const proficiencyService = new ProficiencyCalculationService(userProficiencyRepo);
  const wordListRepo = new WordListRepository();
  const wordListEntryRepo = new WordListEntryRepository();
  const sentenceRepo = new SentenceRepository();
  const userSavedSentenceRepo = new UserSavedSentenceRepository();

  return new PracticeService(
    userProficiencyRepo, 
    vocabularyRepo, 
    proficiencyService,
    wordListRepo,
    wordListEntryRepo,
    sentenceRepo,
    userSavedSentenceRepo
  );
}