/**
 * 安全的环境变量处理工具
 * 避免直接使用 process.env 的非空断言，提供更安全的访问方式
 */

export class EnvError extends Error {
  constructor(envVar: string, message?: string) {
    super(message || `环境变量 ${envVar} 未定义或无效`);
    this.name = 'EnvError';
  }
}

/**
 * 安全获取JWT相关环境变量
 */
export function getJWTSecret(): string {
  const secret = process.env.JWT_SECRET;
  if (!secret || secret.trim().length === 0) {
    throw new EnvError('JWT_SECRET', 'JWT密钥未配置，请检查环境变量');
  }
  
  // 验证密钥强度（至少32字符）
  if (secret.length < 32) {
    throw new EnvError('JWT_SECRET', 'JWT密钥长度不足，应至少包含32个字符');
  }
  
  return secret;
}

export function getJWTRefreshSecret(): string {
  const secret = process.env.JWT_REFRESH_SECRET;
  if (!secret || secret.trim().length === 0) {
    throw new EnvError('JWT_REFRESH_SECRET', 'JWT刷新密钥未配置，请检查环境变量');
  }
  
  // 验证密钥强度（至少32字符）
  if (secret.length < 32) {
    throw new EnvError('JWT_REFRESH_SECRET', 'JWT刷新密钥长度不足，应至少包含32个字符');
  }
  
  return secret;
}

/**
 * 安全获取数据库相关环境变量
 */
export function getDatabaseUrl(): string {
  const url = process.env.DATABASE_URL;
  if (!url || url.trim().length === 0) {
    throw new EnvError('DATABASE_URL', '数据库连接URL未配置');
  }
  return url;
}

/**
 * 安全获取Redis相关环境变量
 */
export function getRedisUrl(): string {
  const url = process.env.REDIS_URL;
  if (!url || url.trim().length === 0) {
    throw new EnvError('REDIS_URL', 'Redis连接URL未配置');
  }
  return url;
}

/**
 * 安全获取Node环境变量
 */
export function getNodeEnv(): 'development' | 'production' | 'test' {
  const env = process.env.NODE_ENV;
  if (env === 'production' || env === 'test' || env === 'development') {
    return env;
  }
  return 'development'; // 默认值
}

/**
 * 获取可选的环境变量（带默认值）
 */
export function getOptionalEnv(key: string, defaultValue: string): string {
  const value = process.env[key];
  return value && value.trim().length > 0 ? value : defaultValue;
}

/**
 * 验证所有必需的环境变量是否已配置
 */
export function validateRequiredEnvVars(): void {
  const requiredVars = [
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'DATABASE_URL',
  ];
  
  const missingVars: string[] = [];
  
  for (const varName of requiredVars) {
    try {
      switch (varName) {
        case 'JWT_SECRET':
          getJWTSecret();
          break;
        case 'JWT_REFRESH_SECRET':
          getJWTRefreshSecret();
          break;
        case 'DATABASE_URL':
          getDatabaseUrl();
          break;
      }
    } catch (error) {
      if (error instanceof EnvError) {
        missingVars.push(varName);
      }
    }
  }
  
  if (missingVars.length > 0) {
    throw new EnvError(
      missingVars.join(', '),
      `以下必需的环境变量未配置: ${missingVars.join(', ')}`
    );
  }
}

/**
 * 在应用启动时验证环境变量
 */
export function initializeEnv(): void {
  try {
    validateRequiredEnvVars();
    console.log('✅ 环境变量验证通过');
  } catch (error) {
    console.error('❌ 环境变量验证失败:', error);
    process.exit(1);
  }
}