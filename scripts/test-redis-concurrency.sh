#!/bin/bash

# Redis高并发测试运行脚本
# 用于快速启动Redis高并发性能测试

set -e

echo "🚀 Redis高并发性能测试启动脚本"
echo "=================================="

# 检查环境
echo "📋 检查测试环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

# 检查tsx
if ! command -v npx &> /dev/null; then
    echo "❌ npx未找到，请确保npm已正确安装"
    exit 1
fi

# 检查应用服务
echo "🔍 检查应用服务状态..."
if ! curl -s http://localhost:3000/api/health > /dev/null; then
    echo "❌ 应用服务未运行，请先启动应用服务 (npm run dev)"
    echo "   或检查服务是否运行在 http://localhost:3000"
    exit 1
fi

# 检查Redis连接
echo "🔍 检查Redis连接..."
REDIS_CHECK=$(curl -s http://localhost:3000/api/health | grep -o '"redis":{[^}]*"status":"[^"]*"' | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
if [ "$REDIS_CHECK" != "healthy" ]; then
    echo "❌ Redis连接异常，请检查Redis服务状态"
    echo "   当前状态: $REDIS_CHECK"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 设置测试环境变量
export NODE_ENV=test
export ENABLE_CACHE_LOGS=true
export ENABLE_DETAILED_CACHE_LOGS=false  # 避免高并发时日志过多
export HIGH_CONCURRENCY_MODE=true

echo "🎯 开始执行Redis高并发测试..."
echo "测试目标: 100并发稳定在500ms内"
echo "测试环境变量已设置:"
echo "  - NODE_ENV=test"
echo "  - ENABLE_CACHE_LOGS=true"
echo "  - HIGH_CONCURRENCY_MODE=true"
echo ""

# 运行测试
npx tsx scripts/test-redis-high-concurrency.ts

echo ""
echo "🎉 测试完成!"
echo "📊 如需查看详细分析，请检查控制台输出"
echo "💡 如需优化建议，请参考测试报告中的建议部分"
