/**
 * @fileoverview 生词本管理API集成测试（简化版）
 * 
 * 使用简化的Mock策略避免复杂的中间件模拟
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';



// Mock AuthMiddleware - 返回高阶函数
vi.mock('@/lib/1-services/auth/AuthMiddleware', () => ({
  requireAuth: vi.fn(() => 
    vi.fn((handler) => 
      vi.fn(async (request) => {
        const mockAuthContext = {
          user: { 
            id: 'user-123', 
            name: 'Test User', 
            email: '<EMAIL>',
            isActive: true,
          },
          authMethod: 'session' as const,
          clientType: 'web' as const,
          permissions: ['word:read', 'word:write'],
          scopes: ['word:read', 'word:write'],
          ipAddress: '127.0.0.1',
          userAgent: 'test-agent',
        };
        return handler(request, mockAuthContext);
      })
    )
  ),
}));



// 使用vi.hoisted来避免变量hoisting问题
const mockPracticeService = vi.hoisted(() => ({
  getUserWordList: vi.fn(),
  addWordsToUserList: vi.fn(), 
  removeWordsFromUserList: vi.fn(),
}));

vi.mock('@/lib/1-services/practice/PracticeService', () => ({
  createPracticeService: vi.fn(() => mockPracticeService),
}));

// Mock types module
vi.mock('@/lib/types/api-types', () => ({
  GetWordsQuerySchema: {
    safeParse: vi.fn(),
  },
  AddWordsBodySchema: {
    safeParse: vi.fn(),
  },
  DeleteWordsBodySchema: {
    safeParse: vi.fn(),
  },
  ValidationError: class ValidationError extends Error {
    constructor(message, details) {
      super(message);
      this.code = 'VALIDATION_ERROR';
      this.statusCode = 400;
      this.details = details;
    }
  },
  NotFoundError: class NotFoundError extends Error {
    constructor(message) {
      super(message);
      this.code = 'NOT_FOUND_ERROR';
      this.statusCode = 404;
    }
  },
  ConflictError: class ConflictError extends Error {
    constructor(message) {
      super(message);
      this.code = 'CONFLICT_ERROR';
      this.statusCode = 409;
    }
  },
}));

// Import mocked modules
import { requireAuth } from '@/lib/1-services/auth/AuthMiddleware';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { 
  GetWordsQuerySchema, 
  AddWordsBodySchema, 
  DeleteWordsBodySchema 
} from '@/lib/types/api-types';

// Import route handlers after mocks are set up
import { GET, POST, DELETE } from '@/api/word/route';

const mockRequireAuth = requireAuth as any;
const mockCreatePracticeService = createPracticeService as any;

describe('生词本管理API集成测试（简化版）', () => {
  const mockAuthContext = {
    user: { 
      id: 'user-123', 
      name: 'Test User', 
      email: '<EMAIL>',
      isActive: true,
    },
    authMethod: 'session' as const,
    clientType: 'web' as const,
    permissions: ['word:read', 'word:write'],
    scopes: ['word:read', 'word:write'],
    ipAddress: '127.0.0.1',
    userAgent: 'test-agent',
  };

  const mockWordList = {
    words: [
      {
        id: 1,
        word: 'example',
        phonetics: ['ɪɡˈzæmpl'],
        freqRank: 100,
        proficiency: {
          practiceCount: 5,
          errorCount: 1,
          averageTime: 2500,
          proficiencyScore: 0.8,
          isMarked: false,
          lastPracticed: '2025-07-30T00:00:00.000Z',
        },
      },
    ],
    pagination: {
      page: 1,
      limit: 20,
      total: 1,
      totalPages: 1,
      hasMore: false,
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();

    // 设置PracticeService Mock - 使用预定义的mock返回值
    mockCreatePracticeService.mockReturnValue(mockPracticeService);

    // 设置默认的requireAuth行为
    mockRequireAuth.mockImplementation((options = {}) => {
      return (handler) => {
        return async (request) => {
          // 简化的认证检查
          const authHeader = request.headers.get('authorization');
          const hasAuth = authHeader?.startsWith('Bearer ') || true; // 默认通过认证

          if (!hasAuth) {
            return NextResponse.json(
              {
                success: false,
                error: 'No valid authentication found',
                code: 'UNAUTHORIZED_ERROR',
              },
              { status: 401 }
            );
          }

          // 检查权限作用域
          if (options.scopes && options.scopes.length > 0) {
            const hasRequiredScope = options.scopes.some((scope) =>
              mockAuthContext.scopes.includes(scope)
            );
            if (!hasRequiredScope) {
              return NextResponse.json(
                {
                  success: false,
                  error: `Required scopes: ${options.scopes.join(', ')}`,
                  code: 'FORBIDDEN_ERROR',
                },
                { status: 403 }
              );
            }
          }

          // 调用处理器
          return handler(request, mockAuthContext);
        };
      };
    });

    // 设置Schema验证Mock
    (GetWordsQuerySchema.safeParse as any).mockReturnValue({
      success: true,
      data: {
        page: 1,
        limit: 20,
        sortBy: 'time',
        order: 'desc',
      },
    });

    (AddWordsBodySchema.safeParse as any).mockReturnValue({
      success: true,
      data: {
        words: ['example', 'test'],
      },
    });

    (DeleteWordsBodySchema.safeParse as any).mockReturnValue({
      success: true,
      data: {
        wordIds: [1, 2],
      },
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('GET /api/word - 生词列表测试', () => {
    it('应该成功获取生词列表', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest(
        'http://localhost:3000/api/word?page=1&limit=20&sortBy=time&order=desc'
      );

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(responseData).toEqual({
        success: true,
        data: mockWordList.words,
        pagination: mockWordList.pagination,
        message: 'Successfully retrieved 1 words',
      });

      expect(mockPracticeService.getUserWordList).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 20,
        sortBy: 'time',
        order: 'desc',
      });
    });

    it('应该验证查询参数并返回400错误', async () => {
      // Arrange
      (GetWordsQuerySchema.safeParse as any).mockReturnValue({
        success: false,
        error: {
          issues: [
            { message: 'Invalid page number', path: ['page'] },
          ],
        },
      });

      const request = new NextRequest(
        'http://localhost:3000/api/word?page=0&limit=200&sortBy=invalid'
      );

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(responseData).toEqual({
        success: false,
        error: 'Invalid query parameters',
        code: 'VALIDATION_ERROR',
        details: [{ message: 'Invalid page number', path: ['page'] }],
      });
    });

    it('应该处理服务层错误', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockRejectedValue(
        new Error('Database connection failed')
      );

      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(responseData).toEqual({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      });
    });
  });

  describe('POST /api/word - 添加生词测试', () => {
    it('应该成功添加生词到用户生词本', async () => {
      // Arrange
      const addResult = {
        added: 2,
        skipped: 0,
        errors: [],
      };
      mockPracticeService.addWordsToUserList.mockResolvedValue(addResult);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['example', 'test'] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(responseData).toEqual({
        success: true,
        data: addResult,
        message: 'Successfully added 2 words to your vocabulary list',
      });

      expect(mockPracticeService.addWordsToUserList).toHaveBeenCalledWith(
        'user-123',
        ['example', 'test'],
        undefined
      );
    });

    it('应该验证请求体格式', async () => {
      // Arrange
      (AddWordsBodySchema.safeParse as any).mockReturnValue({
        success: false,
        error: {
          issues: [
            { message: 'Required field missing', path: ['words'] },
          ],
        },
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({}), // 空请求体
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(responseData).toEqual({
        success: false,
        error: 'Invalid request body',
        code: 'VALIDATION_ERROR',
        details: [{ message: 'Required field missing', path: ['words'] }],
      });
    });

    it('应该要求正确的权限作用域', async () => {
      // Arrange - 模拟用户没有write权限，直接返回403响应
      mockRequireAuth.mockImplementation((options = {}) => {
        return (handler) => {
          return async (request) => {
            // 检查是否需要word:write权限，如果需要则拒绝访问
            if (options.scopes && options.scopes.includes('word:write')) {
              return NextResponse.json(
                {
                  success: false,
                  error: 'Required scopes: word:write',
                  code: 'FORBIDDEN_ERROR',
                },
                { status: 403 }
              );
            }
            return handler(request, mockAuthContext);
          };
        };
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['test'] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(responseData).toEqual({
        success: false,
        error: 'Required scopes: word:write',
        code: 'FORBIDDEN_ERROR',
      });
    });
  });

  describe('DELETE /api/word - 删除生词测试', () => {
    it('应该成功从用户生词本删除单词', async () => {
      // Arrange
      const deleteResult = {
        deleted: 2,
        notFound: 0,
      };
      mockPracticeService.removeWordsFromUserList.mockResolvedValue(deleteResult);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [1, 2] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await DELETE(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(responseData).toEqual({
        success: true,
        data: deleteResult,
        message: 'Successfully removed 2 words from your vocabulary list',
      });

      expect(mockPracticeService.removeWordsFromUserList).toHaveBeenCalledWith(
        'user-123',
        [1, 2]
      );
    });

    it('应该验证wordIds参数', async () => {
      // Arrange
      (DeleteWordsBodySchema.safeParse as any).mockReturnValue({
        success: false,
        error: {
          issues: [
            { message: 'wordIds cannot be empty', path: ['wordIds'] },
          ],
        },
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [] }), // 空数组
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await DELETE(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(responseData).toEqual({
        success: false,
        error: 'Invalid request body',
        code: 'VALIDATION_ERROR',
        details: [{ message: 'wordIds cannot be empty', path: ['wordIds'] }],
      });
    });
  });
});