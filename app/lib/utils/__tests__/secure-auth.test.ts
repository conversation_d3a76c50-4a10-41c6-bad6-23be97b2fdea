import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { SecureTokenStorage, SecureAuthenticatedAPI, AutoTokenManager } from '../secure-auth';

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock window object
Object.defineProperty(global, 'window', {
  value: {
    location: {
      origin: 'http://localhost:3000',
    },
  },
  writable: true,
});

describe('SecureTokenStorage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('setTokens', () => {
    it('should set tokens securely via API call', async () => {
      const mockTokens = {
        accessToken: 'mock.access.token',
        refreshToken: 'mock.refresh.token',
        userData: { id: '1', email: '<EMAIL>', name: 'Test User' },
      };

      // Mock CSRF token request
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ csrfToken: 'mock-csrf-token' }),
        })
        // Mock token storage request
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              success: true,
              message: 'Tokens stored securely',
              csrfToken: 'mock-csrf-token',
            }),
        });

      const result = await SecureTokenStorage.setTokens(
        mockTokens.accessToken,
        mockTokens.refreshToken,
        mockTokens.userData
      );

      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);

      // Check CSRF token request
      expect(mockFetch).toHaveBeenNthCalledWith(1, '/api/auth/csrf-token', {
        method: 'GET',
        credentials: 'include',
      });

      // Check token storage request
      expect(mockFetch).toHaveBeenNthCalledWith(2, '/api/auth/secure-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-csrf-token': 'mock-csrf-token',
        },
        credentials: 'include',
        body: JSON.stringify({
          accessToken: mockTokens.accessToken,
          refreshToken: mockTokens.refreshToken,
          userData: mockTokens.userData,
        }),
      });
    });

    it('should return false on API error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await SecureTokenStorage.setTokens('token', 'refresh', {
        id: '1',
        email: '<EMAIL>',
      });

      expect(result).toBe(false);
    });
  });

  describe('getAuthStatus', () => {
    it('should return authentication status', async () => {
      const mockAuthStatus = {
        isAuthenticated: true,
        hasValidAccessToken: true,
        hasValidRefreshToken: true,
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        tokenExpiresIn: 900,
        csrfToken: 'mock-csrf-token',
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockAuthStatus),
      });

      const result = await SecureTokenStorage.getAuthStatus();

      expect(result).toEqual({
        isAuthenticated: true,
        hasValidAccessToken: true,
        hasValidRefreshToken: true,
        user: mockAuthStatus.user,
        tokenExpiresIn: 900,
      });

      expect(mockFetch).toHaveBeenCalledWith('/api/auth/secure-token', {
        method: 'GET',
        credentials: 'include',
      });
    });

    it('should return default state on error', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const result = await SecureTokenStorage.getAuthStatus();

      expect(result).toEqual({
        isAuthenticated: false,
        hasValidAccessToken: false,
        hasValidRefreshToken: false,
        user: null,
        tokenExpiresIn: 0,
      });
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      // Mock CSRF token request
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ csrfToken: 'mock-csrf-token' }),
        })
        // Mock refresh token request
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              success: true,
              message: 'Token refreshed successfully',
              expiresIn: 900,
            }),
        });

      const result = await SecureTokenStorage.refreshToken();

      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);

      expect(mockFetch).toHaveBeenNthCalledWith(2, '/api/auth/secure-token/refresh', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-csrf-token': 'mock-csrf-token',
        },
        credentials: 'include',
      });
    });

    it('should return false on refresh failure', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Refresh failed'));

      const result = await SecureTokenStorage.refreshToken();

      expect(result).toBe(false);
    });
  });

  describe('clearTokens', () => {
    it('should clear tokens successfully', async () => {
      // Mock CSRF token request
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ csrfToken: 'mock-csrf-token' }),
        })
        // Mock clear tokens request
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              success: true,
              message: 'Tokens cleared successfully',
            }),
        });

      const result = await SecureTokenStorage.clearTokens();

      expect(result).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);

      expect(mockFetch).toHaveBeenNthCalledWith(2, '/api/auth/secure-token', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-csrf-token': 'mock-csrf-token',
        },
        credentials: 'include',
      });
    });
  });

  describe('isTokenValid', () => {
    it('should return true for valid authentication', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ isAuthenticated: true }),
      });

      const result = await SecureTokenStorage.isTokenValid();

      expect(result).toBe(true);
    });

    it('should return false for invalid authentication', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ isAuthenticated: false }),
      });

      const result = await SecureTokenStorage.isTokenValid();

      expect(result).toBe(false);
    });
  });
});

describe('SecureAuthenticatedAPI', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('request', () => {
    it('should make authenticated request successfully', async () => {
      // Mock auth status check
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              isAuthenticated: true,
              tokenExpiresIn: 1800, // 30 minutes
              csrfToken: 'mock-csrf-token',
            }),
        })
        // Mock actual API request
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ data: 'test' }),
        });

      const response = await SecureAuthenticatedAPI.request('/api/test');

      expect(response.ok).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(2);

      // Check the actual API request
      expect(mockFetch).toHaveBeenNthCalledWith(2, '/api/test', {
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
    });

    it('should refresh token when nearing expiration', async () => {
      // Mock auth status check (token expires soon)
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () =>
            Promise.resolve({
              isAuthenticated: true,
              tokenExpiresIn: 200, // 3 minutes (less than 5 minute threshold)
              csrfToken: 'mock-csrf-token',
            }),
        })
        // Mock CSRF token for refresh
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ csrfToken: 'mock-csrf-token' }),
        })
        // Mock token refresh
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ success: true }),
        })
        // Mock actual API request
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({ data: 'test' }),
        });

      const response = await SecureAuthenticatedAPI.request('/api/test');

      expect(response.ok).toBe(true);
      expect(mockFetch).toHaveBeenCalledTimes(4);
    });

    it('should handle 401 responses with retry', async () => {
      let callCount = 0;
      mockFetch.mockImplementation(async (url) => {
        callCount++;

        if (url === '/api/auth/secure-token' && callCount === 1) {
          // Auth status check
          return {
            ok: true,
            json: () =>
              Promise.resolve({
                isAuthenticated: true,
                tokenExpiresIn: 1800,
                csrfToken: 'mock-csrf-token',
              }),
          };
        }

        if (url === '/api/test' && callCount === 2) {
          // First request returns 401
          return { status: 401, ok: false };
        }

        if (url === '/api/auth/csrf-token' && callCount === 3) {
          // CSRF token for refresh
          return {
            ok: true,
            json: () => Promise.resolve({ csrfToken: 'mock-csrf-token' }),
          };
        }

        if (url === '/api/auth/secure-token/refresh' && callCount === 4) {
          // Token refresh
          return {
            ok: true,
            json: () => Promise.resolve({ success: true }),
          };
        }

        if (url === '/api/test' && callCount === 5) {
          // Retry request succeeds
          return {
            ok: true,
            json: () => Promise.resolve({ data: 'test' }),
          };
        }

        throw new Error('Unexpected call');
      });

      const response = await SecureAuthenticatedAPI.request('/api/test');

      expect(response.ok).toBe(true);
      expect(callCount).toBe(5);
    });

    it('should throw error when not authenticated', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ isAuthenticated: false }),
      });

      await expect(SecureAuthenticatedAPI.request('/api/test')).rejects.toThrow(
        'Not authenticated'
      );
    });

    it('should throw error after max retry attempts', async () => {
      // Mock auth status (authenticated)
      mockFetch.mockResolvedValue({
        ok: true,
        json: () =>
          Promise.resolve({
            isAuthenticated: true,
            tokenExpiresIn: 1800,
            csrfToken: 'mock-csrf-token',
          }),
      });

      // Mock all API requests to fail
      mockFetch.mockImplementation(async (url) => {
        if (url === '/api/auth/secure-token') {
          return {
            ok: true,
            json: () =>
              Promise.resolve({
                isAuthenticated: true,
                tokenExpiresIn: 1800,
                csrfToken: 'mock-csrf-token',
              }),
          };
        }
        throw new Error('Network error');
      });

      await expect(SecureAuthenticatedAPI.request('/api/test')).rejects.toThrow(
        'Maximum retry attempts exceeded'
      );
    });
  });

  describe('HTTP methods', () => {
    beforeEach(() => {
      // Mock successful auth status for all method tests
      mockFetch.mockImplementation(async (url, options) => {
        if (url === '/api/auth/secure-token') {
          return {
            ok: true,
            json: () =>
              Promise.resolve({
                isAuthenticated: true,
                tokenExpiresIn: 1800,
                csrfToken: 'mock-csrf-token',
              }),
          };
        }

        return {
          ok: true,
          json: () => Promise.resolve({ method: options?.method || 'GET' }),
        };
      });
    });

    it('should make GET request', async () => {
      await SecureAuthenticatedAPI.get('/api/test');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({ method: 'GET' })
      );
    });

    it('should make POST request with data', async () => {
      const testData = { name: 'test' };
      await SecureAuthenticatedAPI.post('/api/test', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testData),
        })
      );
    });

    it('should make PUT request with data', async () => {
      const testData = { id: 1, name: 'updated' };
      await SecureAuthenticatedAPI.put('/api/test', testData);

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(testData),
        })
      );
    });

    it('should make DELETE request', async () => {
      await SecureAuthenticatedAPI.delete('/api/test');

      expect(mockFetch).toHaveBeenCalledWith(
        '/api/test',
        expect.objectContaining({ method: 'DELETE' })
      );
    });
  });
});

describe('AutoTokenManager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    AutoTokenManager.stop(); // Ensure clean state
  });

  afterEach(() => {
    vi.useRealTimers();
    AutoTokenManager.stop();
  });

  it('should start and stop token management', () => {
    const setTimeoutSpy = vi.spyOn(global, 'setTimeout');

    AutoTokenManager.start();

    expect(setTimeoutSpy).toHaveBeenCalled();

    AutoTokenManager.stop();

    expect(vi.getTimerCount()).toBe(0);
  });

  it('should schedule token refresh based on expiration time', async () => {
    const setTimeoutSpy = vi.spyOn(global, 'setTimeout');

    // Mock auth status with token expiring in 20 minutes
    mockFetch.mockResolvedValue({
      ok: true,
      json: () =>
        Promise.resolve({
          isAuthenticated: true,
          tokenExpiresIn: 1200, // 20 minutes
        }),
    });

    AutoTokenManager.start();

    // Fast-forward to trigger the auth status check
    await vi.runOnlyPendingTimersAsync();

    // Should schedule refresh for 15 minutes (20 - 5 minute buffer)
    expect(setTimeoutSpy).toHaveBeenCalledWith(
      expect.any(Function),
      900000 // 15 minutes in milliseconds
    );
  });

  it('should handle unauthenticated state gracefully', async () => {
    const setTimeoutSpy = vi.spyOn(global, 'setTimeout');

    // Mock unauthenticated status
    mockFetch.mockResolvedValue({
      ok: true,
      json: () =>
        Promise.resolve({
          isAuthenticated: false,
          tokenExpiresIn: 0,
        }),
    });

    AutoTokenManager.start();

    await vi.runOnlyPendingTimersAsync();

    // Should schedule next check in 1 minute
    expect(setTimeoutSpy).toHaveBeenCalledWith(
      expect.any(Function),
      60000 // 1 minute
    );
  });
});
