/**
 * 邮件发送服务提供商
 * 使用 Resend 作为邮件发送服务
 */

import { Resend } from 'resend';

// 初始化 Resend 客户端（仅在有API Key时）
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

export interface EmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
  from?: string;
}

/**
 * 发送邮件的通用函数
 */
export async function sendEmail(options: EmailOptions) {
  try {
    const { to, subject, html, text, from = '<EMAIL>' } = options;

    console.log('📧 Sending email to:', to);
    console.log('📝 Subject:', subject);

    // 开发环境下直接返回成功（避免实际发送邮件）
    if (process.env.NODE_ENV === 'development' && !resend) {
      console.log('🚧 Development mode: Email not sent, but would contain:');
      console.log('HTML:', html);
      if (text) console.log('Text:', text);

      return {
        success: true,
        id: 'dev-' + Date.now(),
        message: 'Email sent successfully (development mode)',
      };
    }

    // 检查是否有Resend客户端
    if (!resend) {
      throw new Error('Resend API key not configured');
    }

    // 发送邮件
    const result = await resend.emails.send({
      from,
      to,
      subject,
      html,
      text,
    });

    console.log('✅ Email sent successfully:', result.data?.id);

    return {
      success: true,
      id: result.data?.id,
      message: 'Email sent successfully',
    };
  } catch (error: any) {
    console.error('❌ Email sending failed:', error);

    return {
      success: false,
      error: error.message || 'Failed to send email',
      message: 'Failed to send email',
    };
  }
}

/**
 * 发送密码重置邮件
 */
export async function sendPasswordResetEmail(email: string, resetToken: string, userName?: string) {
  const resetUrl = `${process.env.NEXTAUTH_URL || 'http://localhost:4000'}/auth/reset-password?token=${resetToken}`;

  const subject = '密码重置请求 - Lucid BD';

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>密码重置</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
            .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; margin: 20px 0; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🔑 密码重置请求</h1>
            </div>
            <div class="content">
                <p>你好${userName ? ` ${userName}` : ''},</p>
                
                <p>我们收到了你的密码重置请求。请点击下面的按钮来重置你的密码：</p>
                
                <p style="text-align: center;">
                    <a href="${resetUrl}" class="button">重置密码</a>
                </p>
                
                <p>或者你也可以复制以下链接到浏览器中打开：</p>
                <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 4px;">
                    ${resetUrl}
                </p>
                
                <div class="warning">
                    <strong>⚠️ 重要提示：</strong>
                    <ul>
                        <li>此链接将在 <strong>1小时</strong> 后失效</li>
                        <li>此链接只能使用一次</li>
                        <li>如果你没有请求密码重置，请忽略此邮件</li>
                        <li>为了你的账户安全，请不要将此链接分享给他人</li>
                    </ul>
                </div>
                
                <p>如果你在重置密码时遇到任何问题，请联系我们的技术支持。</p>
                
                <p>祝好，<br>Lucid BD 团队</p>
            </div>
            <div class="footer">
                <p>此邮件由系统自动发送，请勿回复。</p>
                <p>如果你有任何问题，请访问我们的帮助中心或联系客服。</p>
            </div>
        </div>
    </body>
    </html>
  `;

  const text = `
    密码重置请求
    
    你好${userName ? ` ${userName}` : ''},
    
    我们收到了你的密码重置请求。请使用以下链接来重置你的密码：
    
    ${resetUrl}
    
    重要提示：
    - 此链接将在1小时后失效
    - 此链接只能使用一次
    - 如果你没有请求密码重置，请忽略此邮件
    - 为了你的账户安全，请不要将此链接分享给他人
    
    如果你在重置密码时遇到任何问题，请联系我们的技术支持。
    
    祝好，
    Lucid BD 团队
  `;

  return await sendEmail({
    to: email,
    subject,
    html,
    text,
  });
}

/**
 * 发送密码重置成功通知邮件
 */
export async function sendPasswordResetSuccessEmail(email: string, userName?: string) {
  const subject = '密码重置成功 - Lucid BD';

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>密码重置成功</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #d4edda; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: #ffffff; padding: 30px; border: 1px solid #e9ecef; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 12px; color: #6c757d; }
            .success { background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 4px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>✅ 密码重置成功</h1>
            </div>
            <div class="content">
                <p>你好${userName ? ` ${userName}` : ''},</p>
                
                <div class="success">
                    <strong>🎉 你的密码已成功重置！</strong>
                </div>
                
                <p>你现在可以使用新密码登录你的账户了。</p>
                
                <p><strong>安全提示：</strong></p>
                <ul>
                    <li>请确保你的新密码足够安全</li>
                    <li>不要与他人分享你的密码</li>
                    <li>如果你怀疑账户存在安全问题，请立即联系我们</li>
                </ul>
                
                <p>如果你没有进行此操作，请立即联系我们的技术支持团队。</p>
                
                <p>祝好，<br>Lucid BD 团队</p>
            </div>
            <div class="footer">
                <p>此邮件由系统自动发送，请勿回复。</p>
                <p>重置时间: ${new Date().toLocaleString('zh-CN')}</p>
            </div>
        </div>
    </body>
    </html>
  `;

  const text = `
    密码重置成功
    
    你好${userName ? ` ${userName}` : ''},
    
    你的密码已成功重置！
    
    你现在可以使用新密码登录你的账户了。
    
    安全提示：
    - 请确保你的新密码足够安全
    - 不要与他人分享你的密码
    - 如果你怀疑账户存在安全问题，请立即联系我们
    
    如果你没有进行此操作，请立即联系我们的技术支持团队。
    
    祝好，
    Lucid BD 团队
    
    重置时间: ${new Date().toLocaleString('zh-CN')}
  `;

  return await sendEmail({
    to: email,
    subject,
    html,
    text,
  });
}
