/**
 * 简单的并发测试
 * 快速验证优化效果
 */

async function testConcurrency(concurrency: number): Promise<void> {
  console.log(`🚀 测试 ${concurrency} 个并发请求...`);

  const words = ['test', 'apple', 'book', 'car', 'dog'];
  const startTime = performance.now();

  const promises = Array.from({ length: concurrency }, (_, i) => {
    const word = words[i % words.length];
    const requestStart = performance.now();

    return fetch(`http://localhost:3000/api/dictionary/en/${word}?fresh=true`)
      .then(async (response) => {
        const responseTime = performance.now() - requestStart;
        const data = await response.json();
        return {
          word,
          success: response.ok,
          responseTime,
          processingTime: data.data?.queryMetadata?.processingTimeMs || 0,
        };
      })
      .catch((error) => {
        const responseTime = performance.now() - requestStart;
        return {
          word,
          success: false,
          responseTime,
          processingTime: 0,
          error: error.message,
        };
      });
  });

  const results = await Promise.all(promises);
  const totalTime = performance.now() - startTime;

  const successful = results.filter((r) => r.success);
  const avgResponseTime =
    successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length;
  const avgProcessingTime =
    successful.reduce((sum, r) => sum + r.processingTime, 0) / successful.length;

  console.log(`✅ ${concurrency} 并发完成:`);
  console.log(`   - 成功率: ${Math.round((successful.length / concurrency) * 100)}%`);
  console.log(`   - 总耗时: ${Math.round(totalTime)}ms`);
  console.log(`   - 平均响应时间: ${Math.round(avgResponseTime)}ms`);
  console.log(`   - 平均处理时间: ${Math.round(avgProcessingTime)}ms`);
  console.log(`   - RPS: ${Math.round((successful.length / totalTime) * 1000)}`);

  // 显示失败的请求
  const failed = results.filter((r) => !r.success);
  if (failed.length > 0) {
    console.log(`   - 失败请求: ${failed.length}`);
    failed.forEach((f) => console.log(`     ${f.word}: ${f.error}`));
  }

  console.log('');
}

async function runTests(): Promise<void> {
  console.log('🔍 简单并发性能测试\n');

  const levels = [1, 5, 10, 20, 50];

  for (const level of levels) {
    await testConcurrency(level);
    // 等待一下让系统稳定
    await new Promise((resolve) => setTimeout(resolve, 500));
  }

  console.log('🎉 测试完成！');
}

runTests().catch(console.error);
