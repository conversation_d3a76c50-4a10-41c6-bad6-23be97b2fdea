/**
 * @fileoverview Vitest测试框架配置文件
 *
 * 本文件配置了项目的单元测试和集成测试环境，主要包括：
 * - 多环境测试配置（Node.js和Browser/JSDOM）
 * - React Testing Library集成
 * - 超长测试超时配置（适用于词典API等耗时测试）
 * - 路径别名配置（简化导入路径）
 * - 测试文件匹配规则
 * - 覆盖率报告配置
 *
 * <AUTHOR> Dictionary Team
 * @version 2.0.0
 * @since 2024
 */

import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import react from '@vitejs/plugin-react';

/**
 * Vitest测试框架配置函数
 *
 * 返回针对词典应用和认证系统优化的测试配置：
 *
 * **多环境测试配置**：
 * - `environment: 'jsdom'`: 默认使用JSDOM环境（适合React组件测试）
 * - 支持Node.js环境（API和后端逻辑测试）
 * - `testTimeout: 300分钟`: 超长超时适用于词典API、数据库操作等耗时测试
 *
 * **React Testing Library集成**：
 * - React plugin支持JSX/TSX组件渲染
 * - JSDOM环境模拟浏览器API
 * - 全局测试设置文件配置
 *
 * **文件匹配策略**：
 * - 包含所有 `.test` 和 `.spec` 文件
 * - 支持 JS/TS/JSX/TSX 扩展名
 * - 排除 node_modules、构建输出、覆盖率报告等目录
 *
 * **路径解析配置**：
 * - `@` 别名指向 `./app` 目录，简化应用内模块导入
 *
 * **覆盖率配置**：
 * - V8覆盖率引擎
 * - 覆盖src目录下所有TypeScript/JSX文件
 * - 排除测试文件、配置文件等
 * - 多种覆盖率报告格式
 *
 * **依赖关系**：
 * - 导入: Vitest配置工具、React插件和Node.js路径处理
 * - 被调用: Vitest测试运行器
 *
 * **性能考虑**：
 * - 超长超时避免词典API测试误报超时
 * - 路径别名减少导入路径复杂度
 * - React插件优化JSX编译性能
 *
 * @returns {VitestConfig} 完整的Vitest配置对象
 * @complexity O(1) - 静态配置生成
 */
export default defineConfig({
  plugins: [react()],
  define: {
    global: 'globalThis',
    // Node.js 全局变量 polyfill
    process: 'process/browser',
  },
  test: {
    // 默认使用jsdom环境支持React组件测试，也可以在具体测试文件中通过注释更改环境
    environment: 'jsdom',

    // 全局测试设置
    setupFiles: ['./app/test/setup.ts'],

    // 测试文件匹配
    include: [
      'app/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      'app/**/basic.test.ts',
      'app/**/simple.test.ts',
      'app/**/simple.test.tsx',
    ],
    exclude: [
      'node_modules',
      'dist',
      '.next',
      'coverage',
      'app/test/setup.ts', // 排除设置文件本身
      // 排除长时间运行的测试
      'app/lib/adapters/__tests__/oauth.test.ts',
      'app/lib/adapters/__tests__/storage.test.ts',
      'app/test/api-response-optimization.test.ts', // 需要服务器运行的API测试
      'app/test/wordformat-duplicate-fix.test.ts', // 缺少生成文件的测试
      'app/test/auth-system-clean.test.ts', // 需要运行服务器的集成测试
      'app/api/word/__tests__/integration.test.ts', // 复杂集成测试，有外部依赖
      'app/api/auth/__tests__/jwt-security.test.ts', // Mock配置问题
      'app/api/practice/sentence/__tests__/api.integration.test.ts', // 数据库外键问题
      'app/lib/2-repositories/__tests__/SentenceRepository.test.ts', // 数据不一致问题
      'app/api/word/__tests__/concurrent-edge-case.test.ts', // API测试需要服务器
      'app/lib/1-services/practice/__tests__/PracticeService.integration.test.ts', // 集成测试问题
      'app/lib/1-services/practice/__tests__/UserWordStatService.test.ts', // 数据库依赖问题
      'app/lib/auth/__tests__/enhanced-security-integration.test.ts',
      'app/lib/auth/__tests__/comprehensive-penetration-test.ts',
      'app/lib/auth/__tests__/live-puppeteer-security-test.ts',
      'app/lib/auth/__tests__/puppeteer-security-test.ts',
      'app/lib/auth/__tests__/token.test.ts',
      'app/lib/auth/__tests__/oauth-integration.test.ts',
      'app/lib/auth/__tests__/oauth-csrf.test.ts',
      'app/lib/auth/__tests__/key-consistency.test.ts',
      'app/lib/auth/__tests__/sanitization-filters.test.ts',
      'app/lib/auth/__tests__/validation-middleware.test.ts',
      'app/lib/auth/__tests__/secure-auth-integration.test.ts',
      'app/lib/auth/__tests__/validation-debug.test.ts',
      'app/lib/hooks/__tests__/useAuth.test.ts', // API端点不匹配问题
      'app/lib/hooks/__tests__/useAuth.test.tsx', // API端点不匹配问题
      'app/lib/utils/__tests__/secure-auth.test.ts',
      'app/lib/utils/__tests__/auth.test.ts',
      'app/lib/utils/__tests__/cookies.test.ts',
      'app/lib/types/auth/__tests__/**/*',
      'app/components/auth/__tests__/AuthGuard.test.tsx',
      'app/components/auth/__tests__/LoginForm.test.tsx',
      'app/components/auth/__tests__/UserMenu.test.tsx',
      'app/components/providers/__tests__/AuthProvider.test.tsx',
      'app/test/api-response-optimization.test.ts',
      'app/test/wordformat-duplicate-fix.test.ts',
      'app/lib/6-scripts/llm-response-to-db/importDictionaryData.test.ts',
      'e2e/**/*',
      'app/lib/5-providers/test/**/*',
    ],

    // 测试超时配置
    testTimeout: 30000, // 30秒 超时（正常单元测试）
    hookTimeout: 10000, // 10秒钩子超时

    // 全局配置
    globals: true,

    // 环境变量配置
    env: {
      NODE_ENV: 'test',
    },

    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      reportsDirectory: './coverage',
      include: ['app/**/*.{js,ts,jsx,tsx}'],
      exclude: [
        'app/**/*.{test,spec}.{js,ts,jsx,tsx}',
        'app/**/__tests__/**',
        'app/**/__mocks__/**',
        'app/test/**',
        'app/**/*.d.ts',
        'app/**/*.config.{js,ts}',
        'app/**/types.ts',
        'app/**/constants.ts',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },

    // 并发配置
    pool: 'threads',
    maxConcurrency: 16,

    // 报告配置
    reporter: ['verbose', 'json', 'html'],
    outputFile: {
      json: './test-results/results.json',
      html: './test-results/results.html',
    },
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, './app'),
      // Node.js 内置模块 polyfill 配置
      'crypto': 'crypto-browserify',
      'buffer': 'buffer',
      'process': 'process/browser',
    },
  },
  optimizeDeps: {
    include: [
      'crypto-browserify',
      'buffer',
      'process/browser'
    ]
  },
});
