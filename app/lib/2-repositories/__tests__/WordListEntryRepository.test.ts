/**
 * WordListEntryRepository 单元测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WordListEntryRepository } from '../WordListEntryRepository';
import { prisma } from '@/lib/4-infrastructure/database/prisma';

vi.mock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: {
    wordListEntry: {
      findMany: vi.fn(),
      findFirst: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn()
    },
    vocabulary: {
      findMany: vi.fn()
    },
    userWordProficiency: {
      findMany: vi.fn(),
      findFirst: vi.fn()
    },
    $queryRaw: vi.fn()
  }
}));

describe('WordListEntryRepository', () => {
  let repository: WordListEntryRepository;

  beforeEach(() => {
    repository = new WordListEntryRepository();
    vi.clearAllMocks();
  });

  describe('findMany', () => {
    it('应该返回指定词库的单词条目', async () => {
      const mockEntries = [
        {
          id: 1,
          word: 'hello',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 2,
          word: 'world',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordListEntry.findMany as any).mockResolvedValue(mockEntries);

      const options = {
        wordListIds: [1],
        limit: 20,
        offset: 0
      };

      const result = await repository.findMany(options);

      expect(prisma.wordListEntry.findMany).toHaveBeenCalledWith({
        where: {
          wordListIds: {
            hasSome: [1]
          }
        },
        take: 20,
        skip: 0,
        orderBy: { word: 'asc' }
      });
      expect(result).toEqual(mockEntries);
    });

    it('应该支持单词搜索', async () => {
      const mockEntries = [
        {
          id: 1,
          word: 'hello',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordListEntry.findMany as any).mockResolvedValue(mockEntries);

      const options = {
        wordListIds: [1],
        word: 'hello',
        limit: 20,
        offset: 0
      };

      const result = await repository.findMany(options);

      expect(prisma.wordListEntry.findMany).toHaveBeenCalledWith({
        where: {
          wordListIds: {
            hasSome: [1]
          },
          word: {
            contains: 'hello',
            mode: 'insensitive'
          }
        },
        take: 20,
        skip: 0,
        orderBy: { word: 'asc' }
      });
      expect(result).toEqual(mockEntries);
    });

    it('应该支持随机排序', async () => {
      const mockEntries = [
        {
          id: 2,
          word: 'world',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 1,
          word: 'hello',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordListEntry.findMany as any).mockResolvedValue(mockEntries);

      // Mock随机化函数
      const shuffleSpy = vi.spyOn(repository as any, 'shuffleArray').mockReturnValue(mockEntries);

      const options = {
        wordListIds: [1],
        random: true,
        limit: 20,
        offset: 0
      };

      const result = await repository.findMany(options);

      expect(shuffleSpy).toHaveBeenCalledWith(mockEntries);
      expect(result).toEqual(mockEntries);
    });
  });

  describe('findByWord', () => {
    it('应该返回指定单词的条目', async () => {
      const mockEntry = {
        id: 1,
        word: 'hello',
        wordListIds: [1, 2],
        wordListId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.wordListEntry.findFirst as any).mockResolvedValue(mockEntry);

      const result = await repository.findByWord('hello');

      expect(prisma.wordListEntry.findFirst).toHaveBeenCalledWith({
        where: { word: 'hello' }
      });
      expect(result).toEqual(mockEntry);
    });

    it('应该在单词不存在时返回null', async () => {
      (prisma.wordListEntry.findFirst as any).mockResolvedValue(null);

      const result = await repository.findByWord('nonexistent');

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('应该成功创建新的词库条目', async () => {
      const createData = {
        word: 'test',
        wordListIds: [1],
        wordListId: 1
      };

      const mockCreatedEntry = {
        id: 3,
        ...createData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.wordListEntry.create as any).mockResolvedValue(mockCreatedEntry);

      const result = await repository.create(createData);

      expect(prisma.wordListEntry.create).toHaveBeenCalledWith({
        data: createData
      });
      expect(result).toEqual(mockCreatedEntry);
    });
  });

  describe('updateWordListAssociation', () => {
    it('应该成功更新单词的词库关联', async () => {
      const mockExistingEntry = {
        id: 1,
        word: 'hello',
        wordListIds: [1],
        wordListId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const mockUpdatedEntry = {
        id: 1,
        word: 'hello',
        wordListIds: [1, 2, 3],
        wordListId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock findByWord to return existing entry
      (prisma.wordListEntry.findFirst as any).mockResolvedValue(mockExistingEntry);
      (prisma.wordListEntry.update as any).mockResolvedValue(mockUpdatedEntry);

      const result = await repository.updateWordListAssociation('hello', [2, 3]);

      expect(prisma.wordListEntry.findFirst).toHaveBeenCalledWith({
        where: { word: 'hello' }
      });
      expect(prisma.wordListEntry.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { wordListIds: [1, 2, 3] }
      });
      expect(result).toEqual(mockUpdatedEntry);
    });
  });

  describe('delete', () => {
    it('应该成功删除词库条目', async () => {
      const mockDeletedEntry = {
        id: 1,
        word: 'hello',
        wordListIds: [1],
        wordListId: 1,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.wordListEntry.delete as any).mockResolvedValue(mockDeletedEntry);

      const result = await repository.delete(1);

      expect(prisma.wordListEntry.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
      expect(result).toEqual(mockDeletedEntry);
    });
  });

  describe('getPracticeWords', () => {
    it('应该返回带用户熟练度信息的练习单词', async () => {
      const mockWords = ['hello', 'world'];
      const mockEntries = [
        {
          id: 1,
          word: 'hello',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 2,
          word: 'world',
          wordListIds: [1],
          wordListId: 1,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockProficiencies = [
        {
          id: 1,
          userId: 'user-123',
          vocabularyId: 1,
          practiceCount: 5,
          errorCount: 1,
          averageTime: 2000,
          proficiencyScore: 0.8,
          isMarked: false,
          lastPracticed: new Date(),
          vocabulary: {
            id: 1,
            word: 'hello',
            phonetics: ['/həˈloʊ/'],
            freqRank: 100,
            explains: [
              {
                id: 1,
                pos: 'noun',
                definitions: [
                  {
                    id: 1,
                    definition: 'a greeting',
                    chinese: '问候'
                  }
                ]
              }
            ]
          }
        }
      ];

      // Mock findMany to return word list entries
      (prisma.wordListEntry.findMany as any).mockResolvedValue(mockEntries);

      // Mock user proficiency queries - first call returns data for 'hello', second returns null for 'world'
      (prisma.userWordProficiency.findFirst as any)
        .mockResolvedValueOnce(mockProficiencies[0])
        .mockResolvedValueOnce(null);

      const options = {
        wordListIds: [1],
        limit: 20,
        offset: 0
      };

      const result = await repository.getPracticeWords(1, 'user-123', options);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        id: 1,
        word: 'hello',
        wordListIds: [1],
        practiceCount: 5,
        errorCount: 1,
        averageTime: 2000,
        proficiencyScore: 0.8,
        isMarked: false,
        lastPracticed: expect.any(Date),
        phonetics: ['/həˈloʊ/'],
        definitions: [
          {
            pos: 'noun',
            definition: 'a greeting',
            chinese: '问候'
          }
        ]
      });
      expect(result[1]).toEqual({
        id: 2,
        word: 'world',
        wordListIds: [1],
        practiceCount: 0,
        errorCount: 0,
        averageTime: 0,
        proficiencyScore: 0,
        isMarked: false,
        lastPracticed: undefined
      });
    });
  });

  describe('getPracticeSession', () => {
    it('应该返回完整的练习会话数据', async () => {
      const mockWords = [
        {
          id: 1,
          word: 'hello',
          phonetics: ['/həˈloʊ/'],
          freqRank: 100,
          userProficiency: null
        }
      ];

      // Mock getPracticeWords
      vi.spyOn(repository, 'getPracticeWords').mockResolvedValue(mockWords);

      // Mock总数查询
      (prisma.wordListEntry.count as any).mockResolvedValue(1000);

      const options = {
        wordListIds: [1],
        limit: 20,
        offset: 0,
        random: true
      };

      const result = await repository.getPracticeSession(1, 'user-123', options);

      expect(result).toEqual({
        wordListId: 1,
        words: mockWords,
        sessionConfig: {
          pageSize: 20,
          currentPage: 1,
          totalWords: 1000,
          isRandomized: true,
          filterDifficult: false,
          skipMastered: false
        }
      });
    });
  });

  describe('shuffleArray', () => {
    it('应该随机打乱数组顺序', () => {
      const originalArray = [1, 2, 3, 4, 5];
      const shuffledArray = (repository as any).shuffleArray([...originalArray]);

      // 数组长度应该保持不变
      expect(shuffledArray).toHaveLength(originalArray.length);

      // 数组元素应该保持不变
      expect(shuffledArray.sort()).toEqual(originalArray.sort());
    });

    it('应该处理空数组', () => {
      const emptyArray: any[] = [];
      const result = (repository as any).shuffleArray(emptyArray);

      expect(result).toEqual([]);
    });

    it('应该处理单元素数组', () => {
      const singleArray = [1];
      const result = (repository as any).shuffleArray([...singleArray]);

      expect(result).toEqual(singleArray);
    });
  });
});