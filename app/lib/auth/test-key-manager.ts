/**
 * Test RSA Key Manager Factory
 * 测试专用RSA密钥管理器工厂
 *
 * 为测试创建隔离的密钥管理器实例
 */

import { generateKeyPairSync, createHash, randomBytes } from 'crypto';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import jwt from 'jsonwebtoken';

// 从原始密钥管理器复制配置
const KEY_CONFIG = {
  RSA_MODULUS_LENGTH: 2048,
  RSA_PUBLIC_EXPONENT: 0x10001,
  PRIVATE_KEY_FILE: 'rsa-private.pem',
  PUBLIC_KEY_FILE: 'rsa-public.pem',
  KEY_METADATA_FILE: 'key-metadata.json',
  KEY_ROTATION_INTERVAL: 30 * 24 * 60 * 60 * 1000, // 30天
  KEY_OVERLAP_PERIOD: 7 * 24 * 60 * 60 * 1000, // 7天
  MAX_KEY_VERSIONS: 5,
  KEY_FINGERPRINT_LENGTH: 32,
  ENCRYPTION_ALGORITHM: 'aes-256-gcm',
  ENCRYPTION_KEY_LENGTH: 32,
};

interface KeyMetadata {
  version: number;
  created: number;
  expires: number;
  fingerprint: string;
  algorithm: string;
  keySize: number;
  status: 'active' | 'rotating' | 'deprecated' | 'revoked';
  rotationScheduled?: number;
  previousVersion?: number;
  nextVersion?: number;
}

interface KeyVersion {
  version: number;
  privateKey: string;
  publicKey: string;
  metadata: KeyMetadata;
}

export class TestRSAKeyManager {
  private currentKey: KeyVersion | null = null;
  private keyVersions: Map<number, KeyVersion> = new Map();
  private initialized = false;
  private keyDirectory: string;
  private encryptionKey: Buffer | null = null;

  constructor(keyDirectory: string) {
    this.keyDirectory = keyDirectory;
    this.initializeSecurityContext();
  }

  private initializeSecurityContext(): void {
    if (!existsSync(this.keyDirectory)) {
      mkdirSync(this.keyDirectory, { recursive: true, mode: 0o700 });
    }

    this.encryptionKey = this.getOrCreateEncryptionKey();
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    try {
      // 尝试从文件系统加载
      if (await this.loadFromFileSystem()) {
        this.logSecurityEvent('key_loaded_from_filesystem', true);
      }
      // 生成新密钥
      else {
        await this.generateNewKeyPair();
        this.logSecurityEvent('new_key_generated', true);
      }

      // 验证密钥完整性
      await this.validateKeyIntegrity();

      this.initialized = true;
      this.logSecurityEvent('key_manager_initialized', true);
    } catch (error) {
      this.logSecurityEvent('key_manager_initialization_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to initialize RSA key manager');
    }
  }

  private async loadFromFileSystem(): Promise<boolean> {
    try {
      const metadataPath = join(this.keyDirectory, KEY_CONFIG.KEY_METADATA_FILE);

      if (!existsSync(metadataPath)) {
        return false;
      }

      const metadataContent = readFileSync(metadataPath, 'utf8');
      const allMetadata: KeyMetadata[] = JSON.parse(metadataContent);

      // 加载所有密钥版本
      for (const metadata of allMetadata) {
        const keyVersion = await this.loadKeyVersion(metadata.version);
        if (keyVersion) {
          keyVersion.metadata = metadata;
          this.keyVersions.set(metadata.version, keyVersion);

          if (metadata.status === 'active') {
            this.currentKey = keyVersion;
          }
        }
      }

      return this.currentKey !== null;
    } catch (error) {
      this.logSecurityEvent('filesystem_key_load_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  private async loadKeyVersion(version: number): Promise<KeyVersion | null> {
    try {
      const privateKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PRIVATE_KEY_FILE}.${version}`);
      const publicKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PUBLIC_KEY_FILE}.${version}`);

      if (!existsSync(privateKeyPath) || !existsSync(publicKeyPath)) {
        return null;
      }

      const privateKey = readFileSync(privateKeyPath, 'utf8');
      const publicKey = readFileSync(publicKeyPath, 'utf8');

      if (!this.validateKeyFormat(privateKey, publicKey)) {
        throw new Error(`Invalid key format for version ${version}`);
      }

      const tempMetadata: KeyMetadata = {
        version,
        created: version,
        expires: version + KEY_CONFIG.KEY_ROTATION_INTERVAL,
        fingerprint: this.calculateKeyFingerprint(publicKey),
        algorithm: 'RS256',
        keySize: this.extractKeySize(publicKey),
        status: 'active',
      };

      return {
        version,
        privateKey,
        publicKey,
        metadata: tempMetadata,
      };
    } catch (error) {
      this.logSecurityEvent('key_version_load_failed', false, {
        version,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  private async generateNewKeyPair(): Promise<void> {
    try {
      const { privateKey, publicKey } = generateKeyPairSync('rsa', {
        modulusLength: KEY_CONFIG.RSA_MODULUS_LENGTH,
        publicExponent: KEY_CONFIG.RSA_PUBLIC_EXPONENT,
        publicKeyEncoding: {
          type: 'spki',
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8',
          format: 'pem',
        },
      });

      if (!this.validateKeyFormat(privateKey, publicKey)) {
        throw new Error('Generated key validation failed');
      }

      const version = Date.now();
      const metadata: KeyMetadata = {
        version,
        created: version,
        expires: version + KEY_CONFIG.KEY_ROTATION_INTERVAL,
        fingerprint: this.calculateKeyFingerprint(publicKey),
        algorithm: 'RS256',
        keySize: KEY_CONFIG.RSA_MODULUS_LENGTH,
        status: 'active',
      };

      const keyVersion: KeyVersion = {
        version,
        privateKey,
        publicKey,
        metadata,
      };

      this.currentKey = keyVersion;
      this.keyVersions.set(version, keyVersion);

      await this.saveToFileSystem();

      this.logSecurityEvent('new_key_pair_generated', true, {
        version,
        fingerprint: metadata.fingerprint,
      });
    } catch (error) {
      this.logSecurityEvent('key_generation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to generate RSA key pair');
    }
  }

  private async saveToFileSystem(): Promise<void> {
    try {
      const allMetadata: KeyMetadata[] = Array.from(this.keyVersions.values()).map(
        (kv) => kv.metadata
      );

      const metadataPath = join(this.keyDirectory, KEY_CONFIG.KEY_METADATA_FILE);
      writeFileSync(metadataPath, JSON.stringify(allMetadata, null, 2), { mode: 0o600 });

      for (const [version, keyVersion] of this.keyVersions) {
        const privateKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PRIVATE_KEY_FILE}.${version}`);
        const publicKeyPath = join(this.keyDirectory, `${KEY_CONFIG.PUBLIC_KEY_FILE}.${version}`);

        writeFileSync(privateKeyPath, keyVersion.privateKey, { mode: 0o600 });
        writeFileSync(publicKeyPath, keyVersion.publicKey, { mode: 0o644 });
      }

      this.logSecurityEvent('keys_saved_to_filesystem', true, {
        versions: Array.from(this.keyVersions.keys()),
      });
    } catch (error) {
      this.logSecurityEvent('key_save_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Failed to save keys to filesystem');
    }
  }

  private validateKeyFormat(privateKey: string, publicKey: string): boolean {
    try {
      if (
        !privateKey.includes('-----BEGIN PRIVATE KEY-----') ||
        !privateKey.includes('-----END PRIVATE KEY-----')
      ) {
        return false;
      }

      if (
        !publicKey.includes('-----BEGIN PUBLIC KEY-----') ||
        !publicKey.includes('-----END PUBLIC KEY-----')
      ) {
        return false;
      }

      const testPayload = { test: 'validation', timestamp: Date.now() };
      const token = jwt.sign(testPayload, privateKey, { algorithm: 'RS256' });
      jwt.verify(token, publicKey, { algorithms: ['RS256'] });

      return true;
    } catch (error) {
      return false;
    }
  }

  private calculateKeyFingerprint(publicKey: string): string {
    return createHash('sha256')
      .update(publicKey)
      .digest('hex')
      .substring(0, KEY_CONFIG.KEY_FINGERPRINT_LENGTH);
  }

  private extractKeySize(publicKey: string): number {
    try {
      const keyBuffer = Buffer.from(
        publicKey.replace(/-----[^-]+-----/g, '').replace(/\s/g, ''),
        'base64'
      );
      return keyBuffer.length > 400 ? 2048 : 1024;
    } catch {
      return KEY_CONFIG.RSA_MODULUS_LENGTH;
    }
  }

  private async validateKeyIntegrity(): Promise<void> {
    if (!this.currentKey) {
      throw new Error('No current key available for validation');
    }

    try {
      const testPayload = { test: 'integrity', timestamp: Date.now() };
      const token = jwt.sign(testPayload, this.currentKey.privateKey, { algorithm: 'RS256' });
      jwt.verify(token, this.currentKey.publicKey, { algorithms: ['RS256'] });

      const currentFingerprint = this.calculateKeyFingerprint(this.currentKey.publicKey);
      if (currentFingerprint !== this.currentKey.metadata.fingerprint) {
        throw new Error('Key fingerprint mismatch');
      }

      this.logSecurityEvent('key_integrity_validated', true, { version: this.currentKey.version });
    } catch (error) {
      this.logSecurityEvent('key_integrity_validation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Key integrity validation failed');
    }
  }

  public getCurrentKey(): { privateKey: string; publicKey: string; version: number } | null {
    if (!this.currentKey) {
      return null;
    }

    return {
      privateKey: this.currentKey.privateKey,
      publicKey: this.currentKey.publicKey,
      version: this.currentKey.version,
    };
  }

  public getPublicKey(version?: number): string | null {
    if (version) {
      const keyVersion = this.keyVersions.get(version);
      return keyVersion?.publicKey || null;
    }

    return this.currentKey?.publicKey || null;
  }

  public getPrivateKey(): string | null {
    return this.currentKey?.privateKey || null;
  }

  public getKeyMetadata(): KeyMetadata | null {
    return this.currentKey?.metadata || null;
  }

  public async rotateKey(): Promise<void> {
    if (!this.currentKey) {
      throw new Error('No current key to rotate');
    }

    try {
      this.currentKey.metadata.status = 'rotating';

      await this.generateNewKeyPair();

      const oldKey = this.keyVersions.get(this.currentKey.version);
      if (oldKey) {
        oldKey.metadata.status = 'deprecated';
      }

      await this.cleanupOldVersions();
      await this.saveToFileSystem();

      this.logSecurityEvent('key_rotated', true, {
        newVersion: this.currentKey.version,
        oldVersion: oldKey?.version,
      });
    } catch (error) {
      this.logSecurityEvent('key_rotation_failed', false, {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw new Error('Key rotation failed');
    }
  }

  private async cleanupOldVersions(): Promise<void> {
    const versions = Array.from(this.keyVersions.keys()).sort((a, b) => b - a);

    if (versions.length > KEY_CONFIG.MAX_KEY_VERSIONS) {
      const versionsToRemove = versions.slice(KEY_CONFIG.MAX_KEY_VERSIONS);

      for (const version of versionsToRemove) {
        this.keyVersions.delete(version);
        this.logSecurityEvent('old_key_version_removed', true, { version });
      }
    }
  }

  private getOrCreateEncryptionKey(): Buffer {
    return randomBytes(KEY_CONFIG.ENCRYPTION_KEY_LENGTH);
  }

  private logSecurityEvent(action: string, success: boolean, details?: any): void {
    const timestamp = new Date().toISOString();
    console.log(
      `[Test-RSA-KeyManager] ${timestamp} - ${action}: ${success ? 'SUCCESS' : 'FAILURE'}`,
      details || ''
    );
  }

  public getStatus(): {
    initialized: boolean;
    currentVersion: number | null;
    keyVersions: number[];
    keyAge: number | null;
    nextRotation: number | null;
  } {
    const currentVersion = this.currentKey?.version || null;
    const keyAge = this.currentKey ? Date.now() - this.currentKey.metadata.created : null;
    const nextRotation = this.currentKey?.metadata.expires || null;

    return {
      initialized: this.initialized,
      currentVersion,
      keyVersions: Array.from(this.keyVersions.keys()),
      keyAge,
      nextRotation,
    };
  }

  public async emergencyReset(): Promise<void> {
    this.keyVersions.clear();
    this.currentKey = null;
    this.initialized = false;

    this.logSecurityEvent('emergency_reset_performed', true);
  }
}

export function createTestKeyManager(keyDirectory: string): TestRSAKeyManager {
  return new TestRSAKeyManager(keyDirectory);
}
