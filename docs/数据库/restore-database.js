#!/usr/bin/env node

/**
 * 灵活的数据恢复工具
 * 支持schema变更兼容和增量恢复
 */

import { PrismaClient } from '@prisma/client';
import { readFile, readdir } from 'fs/promises';
import { join } from 'path';

const prisma = new PrismaClient();

/**
 * 字段映射配置 - 处理schema变更
 */
const FIELD_MAPPING = {
  // 示例：如果你添加了新字段或重命名了字段
  Vocabulary: {
    // 旧字段名 -> 新字段名
    // 'oldFieldName': 'newFieldName',
    
    // 默认值设置（用于新增字段）
    defaults: {
      // 'newField': 'defaultValue'
    },
    
    // 忽略的字段（用于删除的字段）
    ignore: [
      // 'removedField'
    ]
  },
  
  User: {
    defaults: {
      // 如果添加了新的必填字段
      // 'newRequiredField': 'defaultValue'
    },
    ignore: []
  }
  
  // 可以为每个表添加映射配置
};

/**
 * 智能字段映射函数
 */
function mapFields(tableName, oldRecord, currentSchema = null) {
  const mapping = FIELD_MAPPING[tableName] || { defaults: {}, ignore: [] };
  const newRecord = { ...oldRecord };
  
  // 应用字段重命名映射
  Object.entries(mapping).forEach(([oldField, newField]) => {
    if (typeof newField === 'string' && oldRecord.hasOwnProperty(oldField)) {
      newRecord[newField] = oldRecord[oldField];
      delete newRecord[oldField];
    }
  });
  
  // 添加默认值
  Object.entries(mapping.defaults || {}).forEach(([field, defaultValue]) => {
    if (!newRecord.hasOwnProperty(field)) {
      newRecord[field] = defaultValue;
    }
  });
  
  // 移除被忽略的字段
  (mapping.ignore || []).forEach(field => {
    delete newRecord[field];
  });
  
  return newRecord;
}

/**
 * 获取表的当前schema（从Prisma模型推断）
 */
async function getCurrentTableSchema(tableName) {
  // 这里可以通过Prisma client的类型信息或者数据库查询获取当前schema
  // 简化实现：返回null，表示使用动态适配
  return null;
}

/**
 * 恢复单个表的数据
 */
async function restoreTable(tableName, backupData, options = {}) {
  const { 
    dryRun = false, 
    skipExisting = true, 
    batchSize = 1000,
    onProgress = null 
  } = options;
  
  console.log(`🔄 ${dryRun ? '预览' : '恢复'} ${tableName} 表...`);
  
  if (!backupData || !Array.isArray(backupData)) {
    console.log(`⚠️  ${tableName}: 无效的备份数据`);
    return { success: false, processed: 0, skipped: 0, errors: 0 };
  }
  
  if (backupData.length === 0) {
    console.log(`📝 ${tableName}: 无数据需要恢复`);
    return { success: true, processed: 0, skipped: 0, errors: 0 };
  }
  
  let processed = 0;
  let skipped = 0;
  let errors = 0;
  
  // 获取对应的Prisma模型
  const modelMap = {
    'Vocabulary': prisma.vocabulary,
    'Explain': prisma.explain,
    'Definition': prisma.definition,
    'WordFormat': prisma.wordFormat,
    'User': prisma.user,
    'Account': prisma.account,
    'Session': prisma.session,
    'RefreshToken': prisma.refreshToken,
    'TokenBlacklist': prisma.tokenBlacklist,
    'RateLimit': prisma.rateLimit,
    'PasswordResetToken': prisma.passwordResetToken,
    'UserWordStat': prisma.userWordStat,
    'UserWordProficiency': prisma.userWordProficiency,
    'QueryLog': prisma.queryLog,
    'WordList': prisma.wordList,
    'WordListEntry': prisma.wordListEntry,
  };
  
  const model = modelMap[tableName];
  if (!model) {
    console.error(`❌ 未知表: ${tableName}`);
    return { success: false, processed: 0, skipped: 0, errors: 0 };
  }
  
  // 获取当前schema
  const currentSchema = await getCurrentTableSchema(tableName);
  
  // 分批处理
  for (let i = 0; i < backupData.length; i += batchSize) {
    const batch = backupData.slice(i, i + batchSize);
    const batchResults = [];
    
    for (const record of batch) {
      try {
        // 应用字段映射
        const mappedRecord = mapFields(tableName, record, currentSchema);
        
        if (dryRun) {
          console.log(`👀 预览记录 ${processed + 1}: ${JSON.stringify(mappedRecord).substring(0, 100)}...`);
          processed++;
          continue;
        }
        
        // 检查是否已存在（对于有唯一键的表）
        if (skipExisting) {
          let existingRecord = null;
          
          try {
            // 根据表的特征字段检查是否存在
            if (tableName === 'Vocabulary' && mappedRecord.word) {
              existingRecord = await model.findFirst({ where: { word: mappedRecord.word } });
            } else if (tableName === 'User' && mappedRecord.email) {
              existingRecord = await model.findFirst({ where: { email: mappedRecord.email } });
            } else if (tableName === 'WordList' && mappedRecord.name) {
              existingRecord = await model.findFirst({ where: { name: mappedRecord.name } });
            } else if (mappedRecord.id) {
              existingRecord = await model.findUnique({ where: { id: mappedRecord.id } });
            }
          } catch (findError) {
            // 忽略查找错误，继续创建
          }
          
          if (existingRecord) {
            skipped++;
            if (onProgress) onProgress({ type: 'skipped', table: tableName, record: mappedRecord });
            continue;
          }
        }
        
        // 创建记录
        try {
          // 移除可能的自增ID字段，让数据库自动生成
          const createData = { ...mappedRecord };
          if (tableName !== 'User' && createData.id && typeof createData.id === 'number') {
            delete createData.id;
          }
          
          await model.create({ data: createData });
          processed++;
          
          if (onProgress) onProgress({ type: 'created', table: tableName, record: createData });
          
        } catch (createError) {
          errors++;
          console.warn(`⚠️  ${tableName} 记录创建失败:`, createError.message);
          
          if (onProgress) onProgress({ type: 'error', table: tableName, record: mappedRecord, error: createError });
        }
        
      } catch (error) {
        errors++;
        console.warn(`⚠️  ${tableName} 记录处理失败:`, error.message);
      }
    }
    
    // 显示进度
    if (i + batchSize < backupData.length) {
      const progress = Math.round(((i + batchSize) / backupData.length) * 100);
      console.log(`📊 ${tableName} 进度: ${progress}% (${i + batchSize}/${backupData.length})`);
    }
  }
  
  const result = { success: errors === 0, processed, skipped, errors };
  
  console.log(`✅ ${tableName} ${dryRun ? '预览' : '恢复'}完成:`);
  console.log(`  📥 处理: ${processed.toLocaleString()}`);
  console.log(`  ⏭️  跳过: ${skipped.toLocaleString()}`);  
  console.log(`  ❌ 错误: ${errors.toLocaleString()}`);
  
  return result;
}

/**
 * 从备份目录恢复数据
 */
async function restoreFromBackup(backupDir, categories = ['core'], options = {}) {
  console.log(`🚀 开始从备份恢复数据...`);
  console.log(`📁 备份目录: ${backupDir}`);
  console.log(`📂 恢复分类: ${categories.join(', ')}\n`);
  
  const results = {};
  
  for (const category of categories) {
    console.log(`📂 处理分类: ${category.toUpperCase()}`);
    
    const categoryDir = join(backupDir, category.toLowerCase());
    
    try {
      const files = await readdir(categoryDir);
      const jsonFiles = files.filter(f => f.endsWith('.json'));
      
      for (const jsonFile of jsonFiles) {
        const tableName = jsonFile.replace(/_\\d{4}-\\d{2}-\\d{2}\\.json$/, '');
        const tableNamePascal = tableName.charAt(0).toUpperCase() + tableName.slice(1);
        
        try {
          const filePath = join(categoryDir, jsonFile);
          const backupContent = JSON.parse(await readFile(filePath, 'utf-8'));
          
          console.log(`\\n📄 处理文件: ${jsonFile}`);
          console.log(`📊 原始记录数: ${backupContent.recordCount?.toLocaleString() || backupContent.data?.length?.toLocaleString() || '未知'}`);
          
          const result = await restoreTable(
            backupContent.table || tableNamePascal, 
            backupContent.data,
            options
          );
          
          results[tableNamePascal] = result;
          
        } catch (error) {
          console.error(`❌ 处理 ${jsonFile} 失败:`, error.message);
          results[tableNamePascal] = { success: false, error: error.message };
        }
      }
      
    } catch (error) {
      console.error(`❌ 访问分类目录 ${category} 失败:`, error.message);
    }
  }
  
  // 汇总统计
  console.log('\\n📊 恢复结果汇总:');
  let totalProcessed = 0;
  let totalSkipped = 0;
  let totalErrors = 0;
  let successTables = 0;
  
  Object.entries(results).forEach(([table, result]) => {
    if (result.success !== false) {
      totalProcessed += result.processed || 0;
      totalSkipped += result.skipped || 0;
      totalErrors += result.errors || 0;
      if (result.success) successTables++;
    }
    
    const status = result.success ? '✅' : '❌';
    console.log(`  ${status} ${table}: ${result.processed || 0}处理 ${result.skipped || 0}跳过 ${result.errors || 0}错误`);
  });
  
  console.log(`\\n🎉 总计:`);
  console.log(`  📥 成功处理: ${totalProcessed.toLocaleString()} 条记录`);
  console.log(`  ⏭️  跳过: ${totalSkipped.toLocaleString()} 条记录`);
  console.log(`  ❌ 错误: ${totalErrors.toLocaleString()} 条记录`);
  console.log(`  📋 成功表: ${successTables}/${Object.keys(results).length}`);
  
  return results;
}

/**
 * 命令行接口
 */
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔧 数据恢复工具使用方法:

基本用法:
  node restore-database.js <备份目录> [分类...] [选项]

参数:
  备份目录      备份文件所在目录路径
  分类         要恢复的数据分类 (core, user, learning, auxiliary, all)

选项:
  --dry-run    预览模式，不实际写入数据
  --replace    替换模式，覆盖已存在记录（默认跳过）
  --batch=N    批处理大小（默认1000）

示例:
  node restore-database.js ./backups/backup_2024-01-15T10-30 core --dry-run
  node restore-database.js ./backups/backup_2024-01-15T10-30 core user
  node restore-database.js ./backups/backup_2024-01-15T10-30 all --replace
`);
    process.exit(0);
  }
  
  const backupDir = args[0];
  const categories = [];
  const options = { dryRun: false, skipExisting: true, batchSize: 1000 };
  
  // 解析参数
  for (let i = 1; i < args.length; i++) {
    const arg = args[i];
    
    if (arg === '--dry-run') {
      options.dryRun = true;
    } else if (arg === '--replace') {
      options.skipExisting = false;
    } else if (arg.startsWith('--batch=')) {
      options.batchSize = parseInt(arg.split('=')[1]) || 1000;
    } else if (!arg.startsWith('--')) {
      if (arg === 'all') {
        categories.push('core', 'user', 'learning', 'auxiliary');
      } else {
        categories.push(arg);
      }
    }
  }
  
  if (categories.length === 0) {
    categories.push('core'); // 默认恢复核心数据
  }
  
  try {
    await restoreFromBackup(backupDir, categories, options);
    console.log('\\n✅ 数据恢复完成');
  } catch (error) {
    console.error('❌ 数据恢复失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { restoreFromBackup, restoreTable, mapFields };