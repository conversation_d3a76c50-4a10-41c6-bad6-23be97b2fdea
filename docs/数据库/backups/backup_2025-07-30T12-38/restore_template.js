#!/usr/bin/env node

/**
 * 数据恢复脚本模板
 * 使用方法: node restore_template.js [category] [--dry-run]
 * 
 * 支持的分类:
 * - core: 核心词典数据 (321,875条)
 * - user: 用户账户数据 (5条)
 * - learning: 学习记录数据 (0条)
 * - auxiliary: 辅助功能数据 (14,593条)
 * - all: 全部数据
 * 
 * 示例:
 * node restore_template.js core --dry-run    # 预览恢复核心数据
 * node restore_template.js core              # 实际恢复核心数据
 * node restore_template.js all               # 恢复全部数据
 */

import { PrismaClient } from '@prisma/client';
import { readFile } from 'fs/promises';
import { join } from 'path';

const prisma = new PrismaClient();

const BACKUP_DATA = {
  "timestamp": "2025-07-30T12:38:13.653Z",
  "backupDir": "backups/backup_2025-07-30T12-38",
  "categories": {
    "CORE": {
      "tables": [
        {
          "name": "Vocabulary",
          "count": 58991,
          "files": [
            "backups/backup_2025-07-30T12-38/core/vocabulary_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/core/vocabulary_2025-07-30.sql"
          ]
        },
        {
          "name": "Explain",
          "count": 67907,
          "files": [
            "backups/backup_2025-07-30T12-38/core/explain_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/core/explain_2025-07-30.sql"
          ]
        },
        {
          "name": "Definition",
          "count": 78807,
          "files": [
            "backups/backup_2025-07-30T12-38/core/definition_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/core/definition_2025-07-30.sql"
          ]
        },
        {
          "name": "WordFormat",
          "count": 116170,
          "files": [
            "backups/backup_2025-07-30T12-38/core/wordformat_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/core/wordformat_2025-07-30.sql"
          ]
        }
      ],
      "totalRecords": 321875
    },
    "USER": {
      "tables": [
        {
          "name": "User",
          "count": 4,
          "files": [
            "backups/backup_2025-07-30T12-38/user/user_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/user/user_2025-07-30.sql"
          ]
        },
        {
          "name": "Account",
          "count": 0,
          "files": []
        },
        {
          "name": "Session",
          "count": 0,
          "files": []
        },
        {
          "name": "RefreshToken",
          "count": 0,
          "files": []
        },
        {
          "name": "TokenBlacklist",
          "count": 0,
          "files": []
        },
        {
          "name": "RateLimit",
          "count": 0,
          "files": []
        },
        {
          "name": "PasswordResetToken",
          "count": 1,
          "files": [
            "backups/backup_2025-07-30T12-38/user/passwordresettoken_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/user/passwordresettoken_2025-07-30.sql"
          ]
        }
      ],
      "totalRecords": 5
    },
    "LEARNING": {
      "tables": [
        {
          "name": "UserWordStat",
          "count": 0,
          "files": []
        },
        {
          "name": "UserWordProficiency",
          "count": 0,
          "files": []
        },
        {
          "name": "QueryLog",
          "count": 0,
          "files": []
        }
      ],
      "totalRecords": 0
    },
    "AUXILIARY": {
      "tables": [
        {
          "name": "WordList",
          "count": 7,
          "files": [
            "backups/backup_2025-07-30T12-38/auxiliary/wordlist_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/auxiliary/wordlist_2025-07-30.sql"
          ]
        },
        {
          "name": "WordListEntry",
          "count": 14586,
          "files": [
            "backups/backup_2025-07-30T12-38/auxiliary/wordlistentry_2025-07-30.json",
            "backups/backup_2025-07-30T12-38/auxiliary/wordlistentry_2025-07-30.sql"
          ]
        }
      ],
      "totalRecords": 14593
    }
  }
};

async function restoreCategory(categoryName, dryRun = false) {
  console.log(`🔄 ${dryRun ? '预览' : '开始'}恢复 ${categoryName} 数据...`);
  
  const category = BACKUP_DATA.categories[categoryName.toUpperCase()];
  if (!category) {
    console.error(`❌ 未找到分类: ${categoryName}`);
    return;
  }
  
  for (const table of category.tables) {
    if (table.count === 0) {
      console.log(`⏭️  跳过空表: ${table.name}`);
      continue;
    }
    
    const jsonFile = table.files.find(f => f.endsWith('.json'));
    if (!jsonFile) {
      console.log(`⚠️  ${table.name}: 未找到JSON备份文件`);
      continue;
    }
    
    if (dryRun) {
      console.log(`👀 预览: ${table.name} - ${table.count.toLocaleString()}条记录`);
      continue;
    }
    
    try {
      console.log(`📥 恢复 ${table.name}...`);
      const backupContent = JSON.parse(await readFile(jsonFile, 'utf-8'));
      
      // 这里需要根据具体表实现恢复逻辑
      // 注意: schema变更时需要调整字段映射
      
      console.log(`✅ ${table.name}: ${backupContent.data.length}条记录已恢复`);
      
    } catch (error) {
      console.error(`❌ ${table.name} 恢复失败:`, error.message);
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const category = args[0] || 'core';
const dryRun = args.includes('--dry-run');

if (category === 'all') {
  // 恢复所有分类
  ['CORE', 'AUXILIARY', 'USER', 'LEARNING'].forEach(async (cat) => {
    await restoreCategory(cat, dryRun);
  });
} else {
  restoreCategory(category, dryRun);
}
