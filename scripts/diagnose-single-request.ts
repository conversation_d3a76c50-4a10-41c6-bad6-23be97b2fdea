/**
 * 诊断单个请求的详细时间分解
 * 用于识别性能瓶颈的具体位置
 */

// 使用内置的fetch API

interface TimingBreakdown {
  total: number;
  dns?: number;
  tcp?: number;
  tls?: number;
  firstByte?: number;
  download?: number;
  [key: string]: number | undefined;
}

async function diagnoseSingleRequest(word: string = 'test'): Promise<void> {
  console.log(`🔍 诊断单个请求: ${word}\n`);

  try {
    // 1. 测试API可用性
    console.log('1. 检查API可用性...');
    const healthStart = performance.now();
    const healthResponse = await fetch('http://localhost:3000/api/health');
    const healthTime = performance.now() - healthStart;
    console.log(`   ✅ API健康检查: ${Math.round(healthTime)}ms\n`);

    // 2. 清空缓存
    console.log('2. 清空缓存...');
    const clearStart = performance.now();
    await fetch('http://localhost:3000/api/cache/clear', { method: 'POST' });
    const clearTime = performance.now() - clearStart;
    console.log(`   ✅ 缓存清空: ${Math.round(clearTime)}ms\n`);

    // 3. 第一次请求（冷启动）
    console.log('3. 第一次请求（冷启动）...');
    const firstStart = performance.now();
    const firstResponse = await fetch(`http://localhost:3000/api/dictionary/en/${word}`);
    const firstTime = performance.now() - firstStart;
    const firstData = await firstResponse.json();

    console.log(`   ✅ 第一次请求: ${Math.round(firstTime)}ms`);
    console.log(`   - 状态: ${firstResponse.status}`);
    console.log(`   - 缓存命中: ${firstData.queryMetadata?.cacheHit || false}`);
    console.log(`   - 查询策略: ${firstData.queryMetadata?.searchStrategy || 'unknown'}`);
    console.log(`   - 处理时间: ${firstData.queryMetadata?.processingTimeMs || 'unknown'}ms\n`);

    // 4. 第二次请求（应该命中缓存）
    console.log('4. 第二次请求（应该命中缓存）...');
    const secondStart = performance.now();
    const secondResponse = await fetch(`http://localhost:3000/api/dictionary/en/${word}`);
    const secondTime = performance.now() - secondStart;
    const secondData = await secondResponse.json();

    console.log(`   ✅ 第二次请求: ${Math.round(secondTime)}ms`);
    console.log(`   - 状态: ${secondResponse.status}`);
    console.log(`   - 缓存命中: ${secondData.queryMetadata?.cacheHit || false}`);
    console.log(`   - 缓存来源: ${secondData.queryMetadata?.cacheSource || 'unknown'}`);
    console.log(`   - 处理时间: ${secondData.queryMetadata?.processingTimeMs || 'unknown'}ms\n`);

    // 5. 连续多次请求测试
    console.log('5. 连续5次请求测试...');
    const consecutiveTimes: number[] = [];

    for (let i = 0; i < 5; i++) {
      const start = performance.now();
      const response = await fetch(`http://localhost:3000/api/dictionary/en/${word}`);
      const time = performance.now() - start;
      consecutiveTimes.push(time);

      const data = await response.json();
      console.log(
        `   请求 ${i + 1}: ${Math.round(time)}ms (缓存: ${data.queryMetadata?.cacheHit || false})`
      );
    }

    const avgConsecutive =
      consecutiveTimes.reduce((sum, time) => sum + time, 0) / consecutiveTimes.length;
    console.log(`   平均时间: ${Math.round(avgConsecutive)}ms\n`);

    // 6. 并发请求测试
    console.log('6. 5个并发请求测试...');
    const concurrentStart = performance.now();

    const concurrentPromises = Array.from({ length: 5 }, (_, i) =>
      fetch(`http://localhost:3000/api/dictionary/en/${word}${i % 2 === 0 ? '' : '2'}`).then(
        async (response) => {
          const data = await response.json();
          return {
            time: performance.now() - concurrentStart,
            status: response.status,
            cacheHit: data.queryMetadata?.cacheHit || false,
            processingTime: data.queryMetadata?.processingTimeMs || 0,
          };
        }
      )
    );

    const concurrentResults = await Promise.all(concurrentPromises);
    const concurrentTime = performance.now() - concurrentStart;

    console.log(`   ✅ 并发请求完成: ${Math.round(concurrentTime)}ms`);
    concurrentResults.forEach((result, i) => {
      console.log(
        `   请求 ${i + 1}: ${Math.round(result.time)}ms (处理: ${result.processingTime}ms, 缓存: ${result.cacheHit})`
      );
    });

    const avgConcurrent =
      concurrentResults.reduce((sum, result) => sum + result.processingTime, 0) /
      concurrentResults.length;
    console.log(`   平均处理时间: ${Math.round(avgConcurrent)}ms\n`);

    // 7. 获取系统状态
    console.log('7. 获取系统状态...');
    try {
      const statusResponse = await fetch('http://localhost:3000/api/debug/redis-pool');
      const statusData = await statusResponse.json();

      console.log('   Redis连接池状态:');
      console.log(`   - 最大连接: ${statusData.max}`);
      console.log(`   - 当前连接: ${statusData.size}`);
      console.log(`   - 可用连接: ${statusData.available}`);
      console.log(`   - 使用率: ${statusData.utilizationPercentage}%`);
    } catch (error) {
      console.log('   ⚠️ 无法获取Redis状态');
    }

    // 8. 性能分析
    console.log('\n📊 性能分析:');
    console.log(`   - 冷启动时间: ${Math.round(firstTime)}ms`);
    console.log(`   - 缓存命中时间: ${Math.round(secondTime)}ms`);
    console.log(`   - 连续请求平均: ${Math.round(avgConsecutive)}ms`);
    console.log(`   - 并发请求总时间: ${Math.round(concurrentTime)}ms`);
    console.log(`   - 并发请求平均处理: ${Math.round(avgConcurrent)}ms`);

    // 性能评估
    if (firstTime > 500) {
      console.log('   🔴 冷启动性能需要优化');
    } else {
      console.log('   ✅ 冷启动性能良好');
    }

    if (secondTime > 100) {
      console.log('   🔴 缓存性能需要优化');
    } else {
      console.log('   ✅ 缓存性能良好');
    }

    if (avgConcurrent > 500) {
      console.log('   🔴 并发性能需要优化');
    } else {
      console.log('   ✅ 并发性能良好');
    }
  } catch (error) {
    console.error('❌ 诊断失败:', error);
  }
}

// 运行诊断
const word = process.argv[2] || 'test';
diagnoseSingleRequest(word).catch(console.error);
