/**
 * 词汇领域实体
 * 封装词汇的核心业务逻辑和规则
 */

export interface VocabularyData {
  id: number;
  word: string;
  phonetics: string[];
  freqRank?: number;
  explains: ExplainData[];
  formats: WordFormatData[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ExplainData {
  id: number;
  pos: string;
  definitions: DefinitionData[];
}

export interface DefinitionData {
  id: number;
  definition: string;
  chinese: string;
  chineseS: string;
}

export interface WordFormatData {
  id: number;
  name: string;
  form: string;
  vocabularyId?: number;
  baseFormId?: number;
  derivedForms?: WordFormatData[]; // 预加载的派生词形
  baseForm?: WordFormatData; // 预加载的基础词形
}

/**
 * 词汇实体类
 * 包含词汇相关的业务逻辑
 */
export class Vocabulary {
  private constructor(private data: VocabularyData) {}

  static create(data: VocabularyData): Vocabulary {
    // 业务规则验证
    if (!data.word || data.word.trim().length === 0) {
      throw new Error('词汇不能为空');
    }

    if (data.word.length > 100) {
      throw new Error('词汇长度不能超过100个字符');
    }

    return new Vocabulary(data);
  }

  // Getters
  get id(): number {
    return this.data.id;
  }

  get word(): string {
    return this.data.word;
  }

  get phonetics(): string[] {
    return this.data.phonetics;
  }

  get freqRank(): number | undefined {
    return this.data.freqRank;
  }

  get explains(): ExplainData[] {
    return this.data.explains;
  }

  get formats(): WordFormatData[] {
    return this.data.formats;
  }

  get createdAt(): Date {
    return this.data.createdAt;
  }

  get updatedAt(): Date {
    return this.data.updatedAt;
  }

  // 业务方法

  /**
   * 获取美式音标
   */
  getUSPhonetic(): string | null {
    return this.data.phonetics[0] || null;
  }

  /**
   * 获取英式音标
   */
  getUKPhonetic(): string | null {
    return this.data.phonetics[1] || null;
  }

  /**
   * 检查是否为高频词汇
   */
  isHighFrequency(): boolean {
    return this.data.freqRank !== undefined && this.data.freqRank <= 1000;
  }

  /**
   * 获取指定词性的解释
   */
  getExplainsByPos(pos: string): ExplainData[] {
    return this.data.explains.filter((explain) => explain.pos === pos);
  }

  /**
   * 获取所有词性
   */
  getAllPOS(): string[] {
    return this.data.explains.map((explain) => explain.pos);
  }

  /**
   * 获取主要形式（直接关联到vocabulary的WordFormat）
   */
  getMainForms(): WordFormatData[] {
    return this.data.formats.filter((format) => format.vocabularyId === this.data.id);
  }

  /**
   * 获取原型形式
   */
  getBaseForm(): WordFormatData | null {
    return this.data.formats.find((format) => format.name === '原型') || null;
  }

  /**
   * 检查是否包含指定的词形
   */
  hasWordForm(form: string): boolean {
    return this.data.formats.some((format) => format.form === form);
  }

  /**
   * 获取词汇的复杂度评分
   * 基于词性数量、释义数量等因素
   */
  getComplexityScore(): number {
    const posCount = this.data.explains.length;
    const definitionCount = this.data.explains.reduce(
      (total, explain) => total + explain.definitions.length,
      0
    );
    const formCount = this.data.formats.length;

    // 简单的复杂度计算公式
    return posCount * 2 + definitionCount + formCount * 0.5;
  }

  /**
   * 获取最简短的中文解释
   */
  getShortestChineseDefinition(): string | null {
    let shortest: string | null = null;
    let shortestLength = Infinity;

    for (const explain of this.data.explains) {
      for (const definition of explain.definitions) {
        if (definition.chineseS && definition.chineseS.length < shortestLength) {
          shortest = definition.chineseS;
          shortestLength = definition.chineseS.length;
        }
      }
    }

    return shortest;
  }

  /**
   * 转换为API响应格式
   */
  toApiResponse() {
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-06-10 13:08:00 +08:00]
    // Reason: [移除wordFormats字段，避免与新的wordForms字段重复，简化API响应结构]
    // Principle_Applied: [KISS - 简化数据结构，避免重复字段]
    // Optimization: [统一使用wordForms字段处理词形信息，移除冗余的wordFormats]
    // Architectural_Note (AR): [保持API响应结构的一致性，避免字段重复]
    // Documentation_Note (DW): [移除wordFormats字段，词形信息现在统一通过wordForms提供]
    // }}
    // {{START MODIFICATIONS}}
    return {
      word: this.data.word,
      explain: this.data.explains.map((explain) => ({
        pos: explain.pos,
        definitions: explain.definitions.map((def) => ({
          definition: def.definition,
          chinese: def.chinese,
          chinese_short: def.chineseS,
        })),
      })),
      // 移除 wordFormats 字段，词形信息现在通过 wordForms 统一提供
      phonetic: {
        us: this.getUSPhonetic() || '',
        uk: this.getUKPhonetic() || '',
      },
    };
    // {{END MODIFICATIONS}}
  }

  /**
   * 转换为数据库格式
   */
  toPersistence(): VocabularyData {
    return { ...this.data };
  }

  /**
   * 比较两个词汇是否相等
   */
  equals(other: Vocabulary): boolean {
    return this.data.id === other.data.id && this.data.word === other.data.word;
  }

  /**
   * 克隆词汇实体
   */
  clone(): Vocabulary {
    return new Vocabulary({
      ...this.data,
      explains: this.data.explains.map((explain) => ({
        ...explain,
        definitions: explain.definitions.map((def) => ({ ...def })),
      })),
      formats: this.data.formats.map((format) => ({ ...format })),
    });
  }
}
