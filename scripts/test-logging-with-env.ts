#!/usr/bin/env tsx

/**
 * 带环境变量加载的日志测试脚本
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 手动加载 .env 文件
const envPath = resolve(process.cwd(), '.env');
config({ path: envPath });

async function main() {
  console.log('🔧 日志系统测试（带环境变量加载）\n');
  console.log('='.repeat(60));

  // 1. 检查环境变量
  console.log('📋 环境变量配置:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   LOG_LEVEL: ${process.env.LOG_LEVEL}`);
  console.log(`   LOG_SAMPLING_RATE: ${process.env.LOG_SAMPLING_RATE}`);
  console.log(`   ENABLE_PERFORMANCE_LOGS: ${process.env.ENABLE_PERFORMANCE_LOGS}`);
  console.log(`   ENABLE_REDIS_POOL_LOGS: ${process.env.ENABLE_REDIS_POOL_LOGS}`);
  console.log(`   ENABLE_CACHE_LOGS: ${process.env.ENABLE_CACHE_LOGS}`);
  console.log(`   ENABLE_DETAILED_CACHE_LOGS: ${process.env.ENABLE_DETAILED_CACHE_LOGS}`);
  console.log(`   DEBUG_MODE: ${process.env.DEBUG_MODE}`);
  console.log(`   HIGH_CONCURRENCY_MODE: ${process.env.HIGH_CONCURRENCY_MODE}`);

  // 2. 重新初始化Logger配置
  console.log('\n🔧 重新初始化Logger配置...');
  try {
    const { Logger } = await import('../app/lib/utils/logger');

    // 强制重新初始化配置
    Logger.reinitializeConfig();

    const config = Logger.getConfig();
    const envInfo = Logger.getEnvironmentInfo();

    console.log('   更新后的配置:', JSON.stringify(config, null, 2));
    console.log('   环境信息:', JSON.stringify(envInfo, null, 2));

    // 3. 测试各种日志级别
    console.log('\n🧪 测试日志输出:');
    console.log('   测试ERROR级别:');
    Logger.error('这是一个错误日志测试', { test: 'error' });

    console.log('   测试WARN级别:');
    Logger.warn('这是一个警告日志测试', { test: 'warn' });

    console.log('   测试INFO级别:');
    Logger.info('这是一个信息日志测试', { test: 'info' });

    console.log('   测试DEBUG级别:');
    Logger.debug('这是一个调试日志测试', { test: 'debug' });

    console.log('   测试性能日志:');
    Logger.performance('性能日志测试', { responseTime: 123, cacheHit: true });

    console.log('   测试缓存日志:');
    Logger.cache('缓存日志测试', { key: 'test', hitTime: 5.5 });

    console.log('   测试Redis连接池日志:');
    Logger.redisPool('Redis连接池日志测试', { borrowed: 1, available: 19 });
  } catch (error) {
    console.error('❌ 导入Logger失败:', error);
  }

  // 4. 测试缓存日志系统
  console.log('\n🎯 测试缓存日志系统:');
  try {
    const { CacheLogConfig, CacheLogger, CacheSource } = await import(
      '../app/lib/utils/cache-types'
    );

    console.log('   缓存日志配置:');
    console.log(`     shouldLog(): ${CacheLogConfig.shouldLog()}`);
    console.log(`     shouldLogDetailed(): ${CacheLogConfig.shouldLogDetailed()}`);

    console.log('\n   测试缓存日志输出:');

    CacheLogger.logHit(
      CacheSource.DATABASE,
      {
        key: 'test:word',
        hitTime: 25.5,
        layer: 'database',
      },
      'test'
    );

    CacheLogger.logHit(
      CacheSource.REDIS_CACHE,
      {
        key: 'test:word',
        hitTime: 2.1,
        layer: 'redis-distributed',
      },
      'test'
    );

    CacheLogger.logHit(
      CacheSource.MEMORY_CACHE,
      {
        key: 'test:word',
        hitTime: 0.05,
        layer: 'application-memory',
      },
      'test'
    );

    CacheLogger.logMiss('test', [CacheSource.MEMORY_CACHE]);

    CacheLogger.logSet(CacheSource.REDIS_CACHE, 'test:word', 3600);

    CacheLogger.logStats();
  } catch (error) {
    console.error('❌ 导入缓存日志系统失败:', error);
  }

  // 5. 测试API请求的日志输出
  console.log('\n🌐 测试API请求日志:');
  try {
    const response = await fetch('http://localhost:3000/api/dictionary/en/test', {
      headers: {
        'User-Agent': 'Log-Test/1.0',
      },
    });

    if (response.ok) {
      const data = await response.json();
      console.log('   API请求成功');
      console.log(`   缓存状态: ${response.headers.get('x-cache-status')}`);
      console.log(`   缓存来源: ${response.headers.get('x-cache-source')}`);
      console.log(`   缓存命中时间: ${response.headers.get('x-cache-hit-time')}`);
    } else {
      console.log(`   API请求失败: ${response.status}`);
    }
  } catch (error) {
    console.log(`   API请求错误: ${error}`);
  }

  console.log('\n✅ 测试完成');
}

main().catch(console.error);
