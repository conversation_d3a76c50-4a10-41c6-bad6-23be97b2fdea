#!/usr/bin/env tsx
/**
 * 数据库备份管理工具
 * 提供备份文件的管理、清理、验证等功能
 */

import { promises as fs } from 'fs';
import { join, basename } from 'path';
import { stat } from 'fs/promises';
import { createHash } from 'crypto';
import {
  BackupConfig,
  BackupInfo,
  BackupStats,
  CleanupOptions,
  CleanupResult,
  BackupType,
  BackupFormat,
} from './utils/backup-types.js';
import { getBackupConfig } from './utils/backup-config.js';

export class BackupManager {
  private config: BackupConfig;

  constructor(config?: Partial<BackupConfig>) {
    this.config = { ...getBackupConfig(), ...config };
  }

  /**
   * 列出所有备份文件
   */
  async listBackups(): Promise<BackupInfo[]> {
    try {
      const files = await fs.readdir(this.config.backup.outputDir);
      const backupFiles = files
        .filter((file) => file.startsWith(this.config.backup.filenamePrefix))
        .map((file) => join(this.config.backup.outputDir, file));

      const backups: BackupInfo[] = [];

      for (const file of backupFiles) {
        try {
          const info = await this.getBackupInfo(file);
          if (info) {
            backups.push(info);
          }
        } catch {
          // 跳过无效文件
        }
      }

      // 按创建时间倒序排列
      backups.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return backups;
    } catch (error: any) {
      throw new Error(`列出备份文件失败: ${error.message}`);
    }
  }

  /**
   * 获取备份文件信息
   */
  async getBackupInfo(filePath: string): Promise<BackupInfo | null> {
    try {
      const fileStats = await stat(filePath);
      const filename = basename(filePath);

      // 解析文件名获取信息
      const fileInfo = this.parseBackupFilename(filename);

      // 验证备份文件完整性
      const isValid = await this.validateBackupFile(filePath);

      return {
        filename,
        filePath,
        size: fileStats.size,
        sizeFormatted: this.formatFileSize(fileStats.size),
        createdAt: fileStats.mtime,
        type: fileInfo.type,
        format: fileInfo.format,
        compressed: fileInfo.compressed,
        checksum: await this.calculateChecksum(filePath),
        isValid,
      };
    } catch {
      return null;
    }
  }

  /**
   * 解析备份文件名
   */
  private parseBackupFilename(filename: string): {
    type: BackupType;
    format: BackupFormat;
    compressed: boolean;
  } {
    const compressed = filename.includes('.gz');

    // 检测备份类型
    let type: BackupType = 'full';
    if (filename.includes('-schema-')) {
      type = 'schema';
    } else if (filename.includes('-data-')) {
      type = 'data';
    }

    // 检测格式
    let format: BackupFormat = 'custom';
    if (filename.includes('.sql')) {
      format = 'sql';
    } else if (filename.includes('.tar')) {
      format = 'tar';
    } else if (filename.includes('.backup')) {
      format = 'custom';
    }

    return { type, format, compressed };
  }

  /**
   * 验证备份文件完整性
   */
  async validateBackupFile(filePath: string): Promise<boolean> {
    try {
      const fileStats = await stat(filePath);

      // 检查文件大小（至少应该有一些内容）
      if (fileStats.size < 100) {
        return false;
      }

      // 对于SQL文件，检查是否包含基本的SQL结构
      if (filePath.endsWith('.sql') || filePath.endsWith('.sql.gz')) {
        const content = await fs.readFile(filePath, 'utf8');
        return content.includes('--') || content.includes('CREATE') || content.includes('INSERT');
      }

      // 对于其他格式，只检查文件大小
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 计算文件校验和
   */
  async calculateChecksum(filePath: string): Promise<string> {
    try {
      const data = await fs.readFile(filePath);
      return createHash('sha256').update(data).digest('hex');
    } catch {
      return '';
    }
  }

  /**
   * 获取备份统计信息
   */
  async getBackupStats(): Promise<BackupStats> {
    const backups = await this.listBackups();

    const stats: BackupStats = {
      totalBackups: backups.length,
      totalSize: 0,
      totalSizeFormatted: '',
      backupsByType: { full: 0, schema: 0, data: 0 },
      backupsByFormat: { sql: 0, custom: 0, tar: 0, directory: 0 },
    };

    if (backups.length === 0) {
      stats.totalSizeFormatted = this.formatFileSize(0);
      return stats;
    }

    // 计算总大小
    stats.totalSize = backups.reduce((total, backup) => total + backup.size, 0);
    stats.totalSizeFormatted = this.formatFileSize(stats.totalSize);

    // 计算最旧和最新备份时间
    const dates = backups.map((b) => b.createdAt);
    stats.oldestBackup = new Date(Math.min(...dates.map((d) => d.getTime())));
    stats.newestBackup = new Date(Math.max(...dates.map((d) => d.getTime())));

    // 按类型统计
    backups.forEach((backup) => {
      stats.backupsByType[backup.type]++;
      stats.backupsByFormat[backup.format]++;
    });

    return stats;
  }

  /**
   * 清理旧备份文件
   */
  async cleanupOldBackups(options: CleanupOptions = {}): Promise<CleanupResult> {
    const retentionDays = options.retentionDays || this.config.backup.retentionDays;
    const maxBackups = options.maxBackups || this.config.backup.maxBackups;
    const dryRun = options.dryRun || false;
    const pattern = options.pattern;

    const backups = await this.listBackups();
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const toRemove: BackupInfo[] = [];

    // 按保留天数筛选
    const expiredBackups = backups.filter((backup) => backup.createdAt < cutoffDate);

    // 按最大数量筛选
    const excessBackups = backups.length > maxBackups ? backups.slice(maxBackups) : [];

    // 合并需要删除的文件（去重）
    const filesToRemove = new Set([
      ...expiredBackups.map((b) => b.filePath),
      ...excessBackups.map((b) => b.filePath),
    ]);

    // 如果有模式匹配，进一步筛选
    if (pattern) {
      const regex = new RegExp(pattern);
      Array.from(filesToRemove).forEach((file) => {
        if (!regex.test(basename(file))) {
          filesToRemove.delete(file);
        }
      });
    }

    toRemove.push(...backups.filter((b) => filesToRemove.has(b.filePath)));

    const result: CleanupResult = {
      removedFiles: [],
      removedCount: toRemove.length,
      freedSpace: 0,
      freedSpaceFormatted: '',
    };

    if (!dryRun) {
      // 实际删除文件
      for (const backup of toRemove) {
        try {
          await fs.unlink(backup.filePath);
          result.removedFiles.push(backup.filePath);
          result.freedSpace += backup.size;
        } catch (error: any) {
          console.warn(`⚠️ 删除文件失败: ${backup.filePath} - ${error.message}`);
        }
      }
    } else {
      // 模拟删除
      result.removedFiles = toRemove.map((b) => b.filePath);
      result.freedSpace = toRemove.reduce((total, backup) => total + backup.size, 0);
    }

    result.freedSpaceFormatted = this.formatFileSize(result.freedSpace);

    return result;
  }

  /**
   * 验证所有备份文件的完整性
   */
  async verifyAllBackups(): Promise<{
    total: number;
    valid: number;
    invalid: BackupInfo[];
  }> {
    const backups = await this.listBackups();
    const invalid: BackupInfo[] = [];

    for (const backup of backups) {
      if (!backup.isValid) {
        invalid.push(backup);
      }
    }

    return {
      total: backups.length,
      valid: backups.length - invalid.length,
      invalid,
    };
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 格式化备份信息
   */
  formatBackupInfo(backup: BackupInfo): string {
    const status = backup.isValid ? '✅' : '❌';
    const compressed = backup.compressed ? '🗜️' : '📄';

    return [
      `${status} ${backup.filename}`,
      `   📁 路径: ${backup.filePath}`,
      `   📊 大小: ${backup.sizeFormatted}`,
      `   📅 时间: ${backup.createdAt.toLocaleString()}`,
      `   🏷️ 类型: ${backup.type}`,
      `   ${compressed} 格式: ${backup.format}${backup.compressed ? ' (压缩)' : ''}`,
      backup.checksum ? `   🔒 校验: ${backup.checksum.slice(0, 16)}...` : '',
      '',
    ]
      .filter((line) => line !== '')
      .join('\n');
  }
}

/**
 * 命令行入口
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];

  if (!command || args.includes('--help')) {
    console.log(`
数据库备份管理工具

用法:
  tsx backup-manager.ts <命令> [选项]

命令:
  list                 列出所有备份文件
  stats               显示备份统计信息
  cleanup             清理旧备份文件
  verify              验证备份文件完整性
  info <文件>         显示指定备份文件的详细信息

清理选项:
  --retention=<天数>   保留天数 (默认: 30)
  --max-backups=<数量> 最大备份数量 (默认: 10)
  --pattern=<正则>     文件名模式匹配
  --dry-run           模拟运行，不实际删除

示例:
  tsx backup-manager.ts list
  tsx backup-manager.ts stats
  tsx backup-manager.ts cleanup --retention=7 --dry-run
  tsx backup-manager.ts info backups/lucid-bd-full-2024-01-01.backup
    `);
    return;
  }

  const manager = new BackupManager();

  try {
    switch (command) {
      case 'list':
        await handleListCommand(manager);
        break;

      case 'stats':
        await handleStatsCommand(manager);
        break;

      case 'cleanup':
        await handleCleanupCommand(manager, args);
        break;

      case 'verify':
        await handleVerifyCommand(manager);
        break;

      case 'info':
        await handleInfoCommand(manager, args[1]);
        break;

      default:
        console.error(`❌ 未知命令: ${command}`);
        console.error('使用 --help 查看可用命令');
        process.exit(1);
    }
  } catch (error: any) {
    console.error(`❌ 执行失败: ${error.message}`);
    process.exit(1);
  }
}

async function handleListCommand(manager: BackupManager) {
  const backups = await manager.listBackups();

  if (backups.length === 0) {
    console.log('📭 没有找到备份文件');
    return;
  }

  console.log(`📋 备份文件列表 (${backups.length}个):\n`);

  backups.forEach((backup, index) => {
    console.log(`${index + 1}. ${manager.formatBackupInfo(backup)}`);
  });
}

async function handleStatsCommand(manager: BackupManager) {
  const stats = await manager.getBackupStats();

  console.log('📊 备份统计信息:\n');
  console.log(`总备份数量: ${stats.totalBackups}`);
  console.log(`总占用空间: ${stats.totalSizeFormatted}`);

  if (stats.oldestBackup && stats.newestBackup) {
    console.log(`最旧备份: ${stats.oldestBackup.toLocaleString()}`);
    console.log(`最新备份: ${stats.newestBackup.toLocaleString()}`);
  }

  console.log('\n按类型统计:');
  Object.entries(stats.backupsByType).forEach(([type, count]) => {
    if (count > 0) {
      console.log(`  ${type}: ${count}个`);
    }
  });

  console.log('\n按格式统计:');
  Object.entries(stats.backupsByFormat).forEach(([format, count]) => {
    if (count > 0) {
      console.log(`  ${format}: ${count}个`);
    }
  });
}

async function handleCleanupCommand(manager: BackupManager, args: string[]) {
  const options: CleanupOptions = {
    retentionDays: parseInt(
      args.find((arg) => arg.startsWith('--retention='))?.split('=')[1] || '30'
    ),
    maxBackups: parseInt(
      args.find((arg) => arg.startsWith('--max-backups='))?.split('=')[1] || '10'
    ),
    pattern: args.find((arg) => arg.startsWith('--pattern='))?.split('=')[1],
    dryRun: args.includes('--dry-run'),
  };

  console.log(`🧹 清理备份文件${options.dryRun ? ' (模拟运行)' : ''}...`);
  console.log(`保留天数: ${options.retentionDays}`);
  console.log(`最大数量: ${options.maxBackups}`);

  const result = await manager.cleanupOldBackups(options);

  console.log(`\n清理结果:`);
  console.log(`${options.dryRun ? '将要' : '已'}删除文件: ${result.removedCount}个`);
  console.log(`${options.dryRun ? '将要' : '已'}释放空间: ${result.freedSpaceFormatted}`);

  if (result.removedFiles.length > 0) {
    console.log(`\n${options.dryRun ? '将要删除' : '已删除'}的文件:`);
    result.removedFiles.forEach((file) => {
      console.log(`  - ${file}`);
    });
  }
}

async function handleVerifyCommand(manager: BackupManager) {
  console.log('🔍 验证备份文件完整性...');

  const result = await manager.verifyAllBackups();

  console.log(`\n验证结果:`);
  console.log(`总文件数: ${result.total}`);
  console.log(`有效文件: ${result.valid}`);
  console.log(`无效文件: ${result.invalid.length}`);

  if (result.invalid.length > 0) {
    console.log(`\n❌ 无效备份文件:`);
    result.invalid.forEach((backup) => {
      console.log(`  - ${backup.filename} (${backup.filePath})`);
    });
  }
}

async function handleInfoCommand(manager: BackupManager, filePath?: string) {
  if (!filePath) {
    console.error('❌ 请指定备份文件路径');
    process.exit(1);
  }

  const info = await manager.getBackupInfo(filePath);

  if (!info) {
    console.error(`❌ 无法获取文件信息: ${filePath}`);
    process.exit(1);
  }

  console.log('📄 备份文件详细信息:\n');
  console.log(manager.formatBackupInfo(info));
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
