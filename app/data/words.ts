/**
 * 打字练习使用的常见英语单词列表
 * 这些单词按频率和难度精心挑选，适合打字练习
 */

export const COMMON_ENGLISH_WORDS = [
  'the',
  'be',
  'of',
  'and',
  'a',
  'to',
  'in',
  'he',
  'have',
  'it',
  'that',
  'for',
  'they',
  'I',
  'with',
  'as',
  'not',
  'on',
  'she',
  'at',
  'by',
  'this',
  'we',
  'you',
  'do',
  'but',
  'from',
  'or',
  'which',
  'one',
  'would',
  'all',
  'will',
  'there',
  'say',
  'who',
  'make',
  'when',
  'can',
  'more',
  'if',
  'no',
  'man',
  'out',
  'other',
  'so',
  'what',
  'time',
  'up',
  'go',
  'about',
  'than',
  'into',
  'could',
  'state',
  'only',
  'new',
  'year',
  'some',
  'take',
  'come',
  'these',
  'know',
  'see',
  'use',
  'get',
  'like',
  'then',
  'first',
  'any',
  'work',
  'now',
  'may',
  'such',
  'give',
  'over',
  'think',
  'most',
  'even',
  'find',
  'day',
  'also',
  'after',
  'way',
  'many',
  'must',
  'look',
  'before',
  'great',
  'back',
  'through',
  'long',
  'where',
  'much',
  'should',
  'well',
  'people',
  'down',
  'own',
  'just',
  'because',
  'good',
  'each',
  'those',
  'feel',
  'seem',
  'how',
  'high',
  'too',
  'place',
  'little',
  'world',
  'very',
  'still',
  'nation',
  'hand',
  'old',
  'life',
  'tell',
  'write',
  'become',
  'here',
  'show',
  'house',
  'both',
  'between',
  'need',
  'mean',
  'call',
  'develop',
  'under',
  'last',
  'right',
  'move',
  'thing',
  'general',
  'school',
  'never',
  'same',
  'another',
  'begin',
  'while',
  'number',
  'part',
  'turn',
  'real',
  'leave',
  'might',
  'want',
  'point',
  'form',
  'off',
  'child',
  'few',
  'small',
  'since',
  'against',
  'ask',
  'late',
  'home',
  'interest',
  'large',
  'person',
  'end',
  'open',
  'public',
  'follow',
  'during',
  'present',
  'without',
  'again',
  'hold',
  'govern',
  'around',
  'possible',
  'head',
  'consider',
  'word',
  'program',
  'problem',
  'however',
  'lead',
  'system',
  'set',
  'order',
  'eye',
  'plan',
  'run',
  'keep',
  'face',
  'fact',
  'group',
  'play',
  'stand',
  'increase',
  'early',
  'course',
  'change',
  'help',
  'line',
] as const;

/**
 * 生成指定数量的随机单词
 * @param count 需要生成的单词数量
 * @returns 随机单词数组
 */
export function generateRandomWords(count: number): string[] {
  return Array.from(
    { length: count },
    () => COMMON_ENGLISH_WORDS[Math.floor(Math.random() * COMMON_ENGLISH_WORDS.length)]
  );
}
