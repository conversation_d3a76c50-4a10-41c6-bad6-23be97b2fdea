/**
 * 词库功能端到端测试
 * 测试完整的用户使用场景和业务流程
 */
import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';

describe('词库功能端到端测试', () => {
  let practiceService: any;
  let testWordLists: any[] = [];
  let testUserId = 'e2e-test-user';

  beforeAll(async () => {
    practiceService = createPracticeService();
    
    // 清理测试数据
    await cleanupTestData();

    // 创建测试词库和数据
    await setupTestData();
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  async function cleanupTestData() {
    await prisma.userWordProficiency.deleteMany({
      where: { userId: testUserId }
    });
    await prisma.userWordStat.deleteMany({
      where: { userId: testUserId }
    });
    await prisma.wordListEntry.deleteMany({
      where: { 
        word: { 
          in: ['apple', 'banana', 'cherry', 'date', 'elderberry'] 
        } 
      }
    });
    await prisma.vocabulary.deleteMany({
      where: { 
        word: { 
          in: ['apple', 'banana', 'cherry', 'date', 'elderberry'] 
        } 
      }
    });
    await prisma.wordList.deleteMany({
      where: { 
        name: { 
          in: ['E2E测试词库-初级', 'E2E测试词库-中级'] 
        } 
      }
    });
  }

  async function setupTestData() {
    // 创建两个测试词库
    const beginnerWordList = await prisma.wordList.create({
      data: {
        name: 'E2E测试词库-初级',
        description: '端到端测试用初级词库',
        difficulty: 1,
        totalWords: 0,
        isActive: true
      }
    });

    const intermediateWordList = await prisma.wordList.create({
      data: {
        name: 'E2E测试词库-中级',
        description: '端到端测试用中级词库',
        difficulty: 3,
        totalWords: 0,
        isActive: true
      }
    });

    testWordLists = [beginnerWordList, intermediateWordList];

    // 创建测试词汇
    const vocabularies = [
      { word: 'apple', phonetics: ['/ˈæpl/'], freqRank: 100 },
      { word: 'banana', phonetics: ['/bəˈnænə/'], freqRank: 200 },
      { word: 'cherry', phonetics: ['/ˈtʃeri/'], freqRank: 300 },
      { word: 'date', phonetics: ['/deɪt/'], freqRank: 150 },
      { word: 'elderberry', phonetics: ['/ˈɛldərˌbɛri/'], freqRank: 500 }
    ];

    for (const vocab of vocabularies) {
      await prisma.vocabulary.create({
        data: vocab
      });

      // 将前3个单词分配给初级词库，后3个分配给中级词库，date同时属于两个词库
      const wordListIds = [];
      if (['apple', 'banana', 'cherry', 'date'].includes(vocab.word)) {
        wordListIds.push(beginnerWordList.id);
      }
      if (['cherry', 'date', 'elderberry'].includes(vocab.word)) {
        wordListIds.push(intermediateWordList.id);
      }

      for (const wordListId of wordListIds) {
        await prisma.wordListEntry.create({
          data: {
            word: vocab.word,
            wordListIds: [wordListId],
            wordListId: wordListId
          }
        });
      }
    }

    // 更新词库统计
    await prisma.wordList.update({
      where: { id: beginnerWordList.id },
      data: { totalWords: 4 }
    });

    await prisma.wordList.update({
      where: { id: intermediateWordList.id },
      data: { totalWords: 3 }
    });
  }

  describe('新用户学习场景', () => {
    it('新用户应该能够浏览可用词库', async () => {
      const wordLists = await practiceService.getAvailableWordLists();

      expect(wordLists).toBeInstanceOf(Array);
      expect(wordLists.length).toBeGreaterThanOrEqual(2);

      // 查找测试词库
      const beginnerList = wordLists.find((wl: any) => wl.name === 'E2E测试词库-初级');
      const intermediateList = wordLists.find((wl: any) => wl.name === 'E2E测试词库-中级');

      expect(beginnerList).toBeDefined();
      expect(beginnerList.difficulty).toBe(1);
      expect(beginnerList.totalWords).toBe(4);
      expect(beginnerList.userProgress).toBeNull(); // 新用户无进度

      expect(intermediateList).toBeDefined();
      expect(intermediateList.difficulty).toBe(3);
      expect(intermediateList.totalWords).toBe(3);
      expect(intermediateList.userProgress).toBeNull();
    });

    it('新用户应该能够开始初级词库练习', async () => {
      const beginnerWordList = testWordLists[0];
      
      // 获取练习会话
      const session = await practiceService.getWordListPracticeSession(
        beginnerWordList.id,
        testUserId,
        { limit: 2, offset: 0 }
      );

      expect(session.wordListId).toBe(beginnerWordList.id);
      expect(session.words).toBeInstanceOf(Array);
      expect(session.words.length).toBe(2);
      expect(session.sessionConfig.totalWords).toBe(4);

      // 验证单词数据结构
      const firstWord = session.words[0];
      expect(firstWord).toHaveProperty('id');
      expect(firstWord).toHaveProperty('word');
      expect(firstWord).toHaveProperty('phonetics');
      expect(firstWord).toHaveProperty('freqRank');
      expect(firstWord.userProficiency).toBeNull(); // 新用户无历史记录
    });
  });

  describe('练习进度记录场景', () => {
    it('用户应该能够记录正确的练习结果', async () => {
      const beginnerWordList = testWordLists[0];
      
      const result = await practiceService.recordWordListProgress(
        testUserId,
        beginnerWordList.id,
        'apple',
        {
          isCorrect: true,
          timeTaken: 2000,
          errorCount: 0,
          sessionType: 'typing'
        }
      );

      expect(result.proficiency.practiceCount).toBe(1);
      expect(result.proficiency.errorCount).toBe(0);
      expect(result.proficiency.averageTime).toBe(2000);
      expect(result.proficiency.proficiencyScore).toBeGreaterThan(0);
      expect(result.improvement).toBe(0); // 第一次练习
      expect(result.suggestion).toBe('needs_more_practice');
    });

    it('用户应该能够记录错误的练习结果', async () => {
      const beginnerWordList = testWordLists[0];
      
      const result = await practiceService.recordWordListProgress(
        testUserId,
        beginnerWordList.id,
        'banana',
        {
          isCorrect: false,
          timeTaken: 5000,
          errorCount: 3,
          sessionType: 'typing'
        }
      );

      expect(result.proficiency.practiceCount).toBe(1);
      expect(result.proficiency.errorCount).toBe(3);
      expect(result.proficiency.averageTime).toBe(5000);
      expect(result.proficiency.proficiencyScore).toBeLessThan(0.5);
      expect(result.suggestion).toBe('needs_more_practice');
    });

    it('多次练习应该提升熟练度分数', async () => {
      const beginnerWordList = testWordLists[0];
      
      // 第二次练习apple，表现更好
      const result = await practiceService.recordWordListProgress(
        testUserId,
        beginnerWordList.id,
        'apple',
        {
          isCorrect: true,
          timeTaken: 1200,
          errorCount: 0,
          sessionType: 'typing'
        }
      );

      expect(result.proficiency.practiceCount).toBe(2);
      expect(result.proficiency.averageTime).toBeLessThan(2000); // 平均时间减少
      expect(result.improvement).toBeGreaterThan(0); // 有改进
      expect(result.proficiency.proficiencyScore).toBeGreaterThan(0.2);
    });
  });

  describe('生词本管理场景', () => {
    it('用户应该能够将词库单词加入生词本', async () => {
      const beginnerWordList = testWordLists[0];
      
      const success = await practiceService.addWordListWordToUserWordBook(
        testUserId,
        beginnerWordList.id,
        'cherry'
      );

      expect(success).toBe(true);

      // 验证单词已被标记
      const userProficiency = await prisma.userWordProficiency.findFirst({
        where: { 
          userId: testUserId,
          vocabulary: { word: 'cherry' }
        },
        include: { vocabulary: true }
      });

      expect(userProficiency).not.toBeNull();
      expect(userProficiency!.isMarked).toBe(true);
    });

    it('用户应该能够查看生词本中的单词', async () => {
      const userWordsList = await practiceService.getUserWordList(testUserId, {
        page: 1,
        limit: 10,
        sortBy: 'time',
        order: 'desc'
      });

      expect(userWordsList.words).toBeInstanceOf(Array);
      expect(userWordsList.words.length).toBeGreaterThan(0);

      // 应该包含之前练习的单词和加入生词本的单词
      const words = userWordsList.words.map((w: any) => w.word);
      expect(words).toContain('apple');
      expect(words).toContain('banana');
      expect(words).toContain('cherry');

      // 验证cherry被标记为生词本单词
      const cherryWord = userWordsList.words.find((w: any) => w.word === 'cherry');
      expect(cherryWord.proficiency.isMarked).toBe(true);
    });
  });

  describe('跨词库学习场景', () => {
    it('用户应该能够在中级词库中练习已学单词', async () => {
      const intermediateWordList = testWordLists[1];
      
      // cherry既在初级词库也在中级词库中
      const result = await practiceService.recordWordListProgress(
        testUserId,
        intermediateWordList.id,
        'cherry',
        {
          isCorrect: true,
          timeTaken: 1500,
          errorCount: 0,
          sessionType: 'typing'
        }
      );

      expect(result.proficiency.practiceCount).toBeGreaterThan(1); // 之前在初级词库练习过
      expect(result.improvement).toBeGreaterThanOrEqual(0);
    });

    it('用户进度统计应该反映跨词库学习', async () => {
      const beginnerProgress = await practiceService.getUserWordListProgress(
        testUserId,
        testWordLists[0].id
      );

      const intermediateProgress = await practiceService.getUserWordListProgress(
        testUserId,
        testWordLists[1].id
      );

      // 初级词库进度
      expect(beginnerProgress.totalWords).toBe(4);
      expect(beginnerProgress.practicedWords).toBeGreaterThan(0);
      expect(beginnerProgress.progressPercentage).toBeGreaterThan(0);

      // 中级词库进度
      expect(intermediateProgress.totalWords).toBe(3);
      expect(intermediateProgress.practicedWords).toBeGreaterThan(0);
      expect(intermediateProgress.progressPercentage).toBeGreaterThan(0);
    });
  });

  describe('学习统计和推荐场景', () => {
    it('用户应该能够获取个人学习统计', async () => {
      const stats = await practiceService.getUserWordStats(testUserId);

      expect(stats).toHaveProperty('totalWords');
      expect(stats).toHaveProperty('avgProficiency');
      expect(stats).toHaveProperty('totalPracticeTime');
      expect(stats).toHaveProperty('markedWords');
      expect(stats).toHaveProperty('proficiencyLevel');

      expect(stats.totalWords).toBeGreaterThan(0);
      expect(stats.markedWords).toBeGreaterThan(0); // 有标记的单词
    });

    it('用户应该能够获取推荐练习的单词', async () => {
      const recommendedWords = await practiceService.getRecommendedWords(testUserId, 5);

      expect(recommendedWords).toBeInstanceOf(Array);
      expect(recommendedWords.length).toBeGreaterThan(0);
      expect(recommendedWords.length).toBeLessThanOrEqual(5);

      // 验证推荐单词的数据结构
      if (recommendedWords.length > 0) {
        const firstRecommendation = recommendedWords[0];
        expect(firstRecommendation).toHaveProperty('word');
        expect(firstRecommendation).toHaveProperty('proficiencyScore');
        expect(firstRecommendation).toHaveProperty('lastPracticed');
      }
    });

    it('用户应该能够获取需要复习的单词', async () => {
      const reviewWords = await practiceService.getWordsForReview(testUserId, 0); // 今天需要复习的

      expect(reviewWords).toBeInstanceOf(Array);
      // 由于测试中的单词都是刚练习的，可能没有需要复习的单词
      expect(reviewWords.length).toBeGreaterThanOrEqual(0);
    });
  });

  describe('完整学习工作流', () => {
    it('模拟用户完整学习一个词库的流程', async () => {
      const beginnerWordList = testWordLists[0];
      
      // 1. 获取词库信息
      const wordList = await practiceService.getWordListById(beginnerWordList.id);
      expect(wordList).not.toBeNull();
      expect(wordList.name).toBe('E2E测试词库-初级');

      // 2. 获取所有单词进行练习
      const allWords = await practiceService.getWordListWords(
        beginnerWordList.id,
        testUserId,
        { limit: 10, offset: 0 }
      );

      expect(allWords.length).toBe(4);

      // 3. 逐个练习所有单词
      for (const word of allWords) {
        // 模拟练习过程，有正确也有错误
        const isCorrect = Math.random() > 0.3; // 70%正确率
        const timeTaken = Math.floor(Math.random() * 3000) + 1000; // 1-4秒
        
        const result = await practiceService.recordWordListProgress(
          testUserId,
          beginnerWordList.id,
          word.word,
          {
            isCorrect,
            timeTaken,
            errorCount: isCorrect ? 0 : Math.floor(Math.random() * 3) + 1,
            sessionType: 'typing'
          }
        );

        expect(result.proficiency).toBeDefined();
        expect(result.suggestion).toMatch(/needs_more_practice|good_progress|mastered/);

        // 如果错误较多，加入生词本
        if (!isCorrect || result.proficiency.proficiencyScore < 0.5) {
          await practiceService.addWordListWordToUserWordBook(
            testUserId,
            beginnerWordList.id,
            word.word
          );
        }
      }

      // 4. 检查最终进度
      const finalProgress = await practiceService.getUserWordListProgress(
        testUserId,
        beginnerWordList.id
      );

      expect(finalProgress.totalWords).toBe(4);
      expect(finalProgress.practicedWords).toBe(4); // 所有单词都练习过
      expect(finalProgress.progressPercentage).toBe(100);
      expect(finalProgress.averageProficiency).toBeGreaterThan(0);

      // 5. 检查用户统计数据
      const finalStats = await practiceService.getUserWordStats(testUserId);
      expect(finalStats.totalWords).toBeGreaterThanOrEqual(4);
      expect(finalStats.markedWords).toBeGreaterThan(0);
    });
  });
});