import { EventEmitter } from 'events';
import { createHash } from 'crypto';

// 攻击类型
export enum AttackType {
  BRUTE_FORCE = 'brute_force',
  DDoS = 'ddos',
  SQL_INJECTION = 'sql_injection',
  XSS = 'xss',
  CSRF = 'csrf',
  DIRECTORY_TRAVERSAL = 'directory_traversal',
  COMMAND_INJECTION = 'command_injection',
  RATE_LIMIT_ABUSE = 'rate_limit_abuse',
  CREDENTIAL_STUFFING = 'credential_stuffing',
  ACCOUNT_TAKEOVER = 'account_takeover',
  SCRAPING = 'scraping',
  SPAM = 'spam',
}

// 保护动作
export enum ProtectionAction {
  BLOCK_IP = 'block_ip',
  BLOCK_USER = 'block_user',
  RATE_LIMIT = 'rate_limit',
  CAPTCHA = 'captcha',
  MONITOR = 'monitor',
  ALERT = 'alert',
  QUARANTINE = 'quarantine',
  THROTTLE = 'throttle',
}

// 威胁等级
export enum ThreatLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// 阻断规则
export interface BlockingRule {
  id: string;
  name: string;
  description: string;
  attackTypes: AttackType[];
  conditions: {
    threshold: number;
    timeWindow: number; // 秒
    aggregation: 'count' | 'rate' | 'score';
    criteria: {
      ip?: string;
      userId?: string;
      userAgent?: string;
      endpoint?: string;
      method?: string;
    };
  };
  actions: {
    type: ProtectionAction;
    duration: number; // 秒，0表示永久
    config: any;
  }[];
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
}

// 阻断实例
export interface BlockingInstance {
  id: string;
  ruleId: string;
  ruleName: string;
  target: {
    type: 'ip' | 'user' | 'session';
    value: string;
    metadata?: any;
  };
  actions: ProtectionAction[];
  reason: string;
  evidence: {
    attackType: AttackType;
    threatLevel: ThreatLevel;
    riskScore: number;
    events: any[];
    metadata: any;
  };
  startTime: number;
  endTime: number; // 0表示永久
  status: 'active' | 'expired' | 'manually_removed';
  createdBy: 'system' | 'admin';
  removedBy?: string;
  removedAt?: number;
  appealable: boolean;
}

// 攻击检测结果
export interface AttackDetectionResult {
  detected: boolean;
  attackType: AttackType;
  threatLevel: ThreatLevel;
  riskScore: number;
  confidence: number;
  evidence: {
    indicators: string[];
    patterns: string[];
    signatures: string[];
    anomalies: string[];
  };
  recommendations: string[];
  suggestedActions: ProtectionAction[];
}

// 防护配置
export interface AttackProtectionConfig {
  enabled: boolean;
  autoBlock: boolean;
  learningMode: boolean;
  adaptiveThresholds: boolean;
  globalRateLimit: {
    enabled: boolean;
    requestsPerSecond: number;
    burstSize: number;
  };
  detection: {
    behaviorAnalysis: boolean;
    signatureMatching: boolean;
    anomalyDetection: boolean;
    machineLearning: boolean;
  };
  response: {
    gracePeriod: number; // 秒
    escalationRules: boolean;
    whitelistBypass: boolean;
  };
  monitoring: {
    logBlocks: boolean;
    alertOnBlock: boolean;
    trackMetrics: boolean;
  };
}

// 攻击防护管理器
export class AttackProtectionManager extends EventEmitter {
  private config: AttackProtectionConfig;
  private blockingRules: Map<string, BlockingRule> = new Map();
  private activeBlocks: Map<string, BlockingInstance> = new Map();
  private whitelist: Set<string> = new Set();
  private blacklist: Set<string> = new Set();
  private rateLimit: Map<
    string,
    {
      count: number;
      windowStart: number;
      blocked: boolean;
    }
  > = new Map();
  private requestHistory: Map<
    string,
    {
      requests: number[];
      lastRequest: number;
      averageInterval: number;
    }
  > = new Map();
  private behaviorProfiles: Map<
    string,
    {
      normalPatterns: Set<string>;
      anomalyScore: number;
      lastUpdate: number;
    }
  > = new Map();
  private metrics = {
    totalBlocks: 0,
    blocksPerType: new Map<AttackType, number>(),
    blocksPerAction: new Map<ProtectionAction, number>(),
    falsePositives: 0,
    detectionAccuracy: 0,
  };

  constructor(config: AttackProtectionConfig) {
    super();
    this.config = config;
    this.initializeDefaultRules();
    this.startCleanupTimer();
    this.startMetricsCalculation();
  }

  // 检测攻击
  async detectAttack(request: {
    ip: string;
    userId?: string;
    userAgent?: string;
    endpoint: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
    timestamp: number;
  }): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.BRUTE_FORCE,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: {
        indicators: [],
        patterns: [],
        signatures: [],
        anomalies: [],
      },
      recommendations: [],
      suggestedActions: [],
    };

    // 检查白名单
    if (this.whitelist.has(request.ip)) {
      return result;
    }

    // 检查黑名单
    if (this.blacklist.has(request.ip)) {
      result.detected = true;
      result.attackType = AttackType.BRUTE_FORCE;
      result.threatLevel = ThreatLevel.HIGH;
      result.riskScore = 100;
      result.confidence = 1;
      result.evidence.indicators.push('IP在黑名单中');
      result.suggestedActions.push(ProtectionAction.BLOCK_IP);
      return result;
    }

    // 多重检测
    const detectionResults = await Promise.all([
      this.detectBruteForce(request),
      this.detectDDoS(request),
      this.detectSQLInjection(request),
      this.detectXSS(request),
      this.detectDirectoryTraversal(request),
      this.detectCommandInjection(request),
      this.detectRateLimitAbuse(request),
      this.detectBehaviorAnomalies(request),
      this.detectScraping(request),
    ]);

    // 合并检测结果
    const positiveResults = detectionResults.filter((r) => r.detected);
    if (positiveResults.length > 0) {
      result.detected = true;
      result.attackType = positiveResults[0].attackType;
      result.threatLevel = this.calculateThreatLevel(positiveResults);
      result.riskScore = Math.max(...positiveResults.map((r) => r.riskScore));
      result.confidence = Math.max(...positiveResults.map((r) => r.confidence));

      // 合并证据
      positiveResults.forEach((r) => {
        result.evidence.indicators.push(...r.evidence.indicators);
        result.evidence.patterns.push(...r.evidence.patterns);
        result.evidence.signatures.push(...r.evidence.signatures);
        result.evidence.anomalies.push(...r.evidence.anomalies);
      });

      // 生成建议
      result.recommendations = this.generateRecommendations(positiveResults);
      result.suggestedActions = this.generateSuggestedActions(
        result.threatLevel,
        result.attackType
      );
    }

    // 更新请求历史
    this.updateRequestHistory(request);

    return result;
  }

  // 应用保护措施
  async applyProtection(
    request: any,
    detection: AttackDetectionResult
  ): Promise<BlockingInstance | null> {
    if (!detection.detected || !this.config.autoBlock) {
      return null;
    }

    // 查找匹配的规则
    const matchingRule = this.findMatchingRule(request, detection);
    if (!matchingRule) {
      return null;
    }

    // 创建阻断实例
    const blockingInstance = this.createBlockingInstance(matchingRule, request, detection);

    // 应用保护动作
    await this.executeProtectionActions(blockingInstance);

    // 存储阻断实例
    this.activeBlocks.set(blockingInstance.id, blockingInstance);

    // 更新指标
    this.updateMetrics(blockingInstance);

    // 发出事件
    this.emit('attackBlocked', blockingInstance);

    return blockingInstance;
  }

  // 检查是否被阻断
  isBlocked(request: { ip: string; userId?: string; endpoint?: string }): BlockingInstance | null {
    // 检查IP阻断
    const ipBlock = Array.from(this.activeBlocks.values()).find(
      (block) =>
        block.target.type === 'ip' &&
        block.target.value === request.ip &&
        block.status === 'active' &&
        (block.endTime === 0 || block.endTime > Date.now())
    );

    if (ipBlock) return ipBlock;

    // 检查用户阻断
    if (request.userId) {
      const userBlock = Array.from(this.activeBlocks.values()).find(
        (block) =>
          block.target.type === 'user' &&
          block.target.value === request.userId &&
          block.status === 'active' &&
          (block.endTime === 0 || block.endTime > Date.now())
      );

      if (userBlock) return userBlock;
    }

    return null;
  }

  // 暴力破解检测
  private async detectBruteForce(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.BRUTE_FORCE,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    // 检查登录端点
    if (request.endpoint.includes('login') || request.endpoint.includes('auth')) {
      const history = this.requestHistory.get(request.ip);
      if (history) {
        const recentRequests = history.requests.filter(
          (timestamp) => Date.now() - timestamp < 60000 // 1分钟内
        );

        if (recentRequests.length > 5) {
          result.detected = true;
          result.threatLevel = ThreatLevel.HIGH;
          result.riskScore = Math.min(100, recentRequests.length * 10);
          result.confidence = 0.8;
          result.evidence.indicators.push(`1分钟内${recentRequests.length}次登录尝试`);
          result.evidence.patterns.push('频繁登录请求');
        }
      }
    }

    return result;
  }

  // DDoS检测
  private async detectDDoS(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.DDoS,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    const history = this.requestHistory.get(request.ip);
    if (history) {
      const recentRequests = history.requests.filter(
        (timestamp) => Date.now() - timestamp < 10000 // 10秒内
      );

      if (recentRequests.length > 50) {
        result.detected = true;
        result.threatLevel = ThreatLevel.CRITICAL;
        result.riskScore = 100;
        result.confidence = 0.9;
        result.evidence.indicators.push(`10秒内${recentRequests.length}次请求`);
        result.evidence.patterns.push('异常高频请求');
      }
    }

    return result;
  }

  // SQL注入检测
  private async detectSQLInjection(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.SQL_INJECTION,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    const sqlPatterns = [
      /(\%27)|(\')|(\-\-)|(\%23)|(#)/i,
      /((\%3D)|(=))[^\n]*((\%27)|(\')|(\-\-)|(\%3B)|(;))/i,
      /\w*((\%27)|(\'))((\%6F)|o|(\%4F))((\%72)|r|(\%52))/i,
      /((\%27)|(\'))union/i,
      /union\s+select/i,
      /select\s+.*\s+from/i,
      /insert\s+into/i,
      /delete\s+from/i,
      /update\s+.*\s+set/i,
      /drop\s+table/i,
    ];

    const testData = [
      request.endpoint,
      JSON.stringify(request.body || {}),
      Object.values(request.headers).join(' '),
    ].join(' ');

    let matchedPatterns = 0;
    const foundPatterns: string[] = [];

    for (const pattern of sqlPatterns) {
      if (pattern.test(testData)) {
        matchedPatterns++;
        foundPatterns.push(pattern.toString());
      }
    }

    if (matchedPatterns > 0) {
      result.detected = true;
      result.threatLevel = matchedPatterns > 2 ? ThreatLevel.HIGH : ThreatLevel.MEDIUM;
      result.riskScore = Math.min(100, matchedPatterns * 25);
      result.confidence = matchedPatterns > 2 ? 0.9 : 0.7;
      result.evidence.indicators.push(`匹配${matchedPatterns}个SQL注入模式`);
      result.evidence.signatures.push(...foundPatterns);
    }

    return result;
  }

  // XSS检测
  private async detectXSS(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.XSS,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    const xssPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /<iframe[^>]*>.*?<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /expression\s*\(/gi,
      /<[^>]*\s+on\w+\s*=/gi,
      /eval\s*\(/gi,
      /setTimeout\s*\(/gi,
      /setInterval\s*\(/gi,
    ];

    const testData = [
      request.endpoint,
      JSON.stringify(request.body || {}),
      Object.values(request.headers).join(' '),
    ].join(' ');

    let matchedPatterns = 0;
    const foundPatterns: string[] = [];

    for (const pattern of xssPatterns) {
      if (pattern.test(testData)) {
        matchedPatterns++;
        foundPatterns.push(pattern.toString());
      }
    }

    if (matchedPatterns > 0) {
      result.detected = true;
      result.threatLevel = matchedPatterns > 2 ? ThreatLevel.HIGH : ThreatLevel.MEDIUM;
      result.riskScore = Math.min(100, matchedPatterns * 30);
      result.confidence = matchedPatterns > 2 ? 0.85 : 0.65;
      result.evidence.indicators.push(`匹配${matchedPatterns}个XSS攻击模式`);
      result.evidence.signatures.push(...foundPatterns);
    }

    return result;
  }

  // 目录遍历检测
  private async detectDirectoryTraversal(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.DIRECTORY_TRAVERSAL,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    const traversalPatterns = [
      /\.\.[\/\\]/g,
      /\%2e\%2e[\/\\]/gi,
      /\%2f\%2e\%2e/gi,
      /\%5c\%2e\%2e/gi,
      /\.\.%2f/gi,
      /\.\.%5c/gi,
      /\.\.\//g,
      /\.\.\\/g,
    ];

    const testData = request.endpoint + JSON.stringify(request.body || {});

    let matchedPatterns = 0;
    const foundPatterns: string[] = [];

    for (const pattern of traversalPatterns) {
      if (pattern.test(testData)) {
        matchedPatterns++;
        foundPatterns.push(pattern.toString());
      }
    }

    if (matchedPatterns > 0) {
      result.detected = true;
      result.threatLevel = ThreatLevel.HIGH;
      result.riskScore = Math.min(100, matchedPatterns * 40);
      result.confidence = 0.8;
      result.evidence.indicators.push(`匹配${matchedPatterns}个目录遍历模式`);
      result.evidence.signatures.push(...foundPatterns);
    }

    return result;
  }

  // 命令注入检测
  private async detectCommandInjection(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.COMMAND_INJECTION,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    const commandPatterns = [
      /[;&|`$(){}[\]]/g,
      /\${[^}]*}/g,
      /`[^`]*`/g,
      /\b(rm|cat|ls|ps|kill|chmod|chown|wget|curl|nc|netcat)\b/gi,
      /;\s*(rm|cat|ls|ps|kill|chmod|chown|wget|curl|nc|netcat)/gi,
    ];

    const testData = request.endpoint + JSON.stringify(request.body || {});

    let matchedPatterns = 0;
    const foundPatterns: string[] = [];

    for (const pattern of commandPatterns) {
      if (pattern.test(testData)) {
        matchedPatterns++;
        foundPatterns.push(pattern.toString());
      }
    }

    if (matchedPatterns > 0) {
      result.detected = true;
      result.threatLevel = ThreatLevel.HIGH;
      result.riskScore = Math.min(100, matchedPatterns * 35);
      result.confidence = 0.75;
      result.evidence.indicators.push(`匹配${matchedPatterns}个命令注入模式`);
      result.evidence.signatures.push(...foundPatterns);
    }

    return result;
  }

  // 速率限制滥用检测
  private async detectRateLimitAbuse(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.RATE_LIMIT_ABUSE,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    const rateLimitData = this.rateLimit.get(request.ip);
    if (rateLimitData && rateLimitData.blocked) {
      result.detected = true;
      result.threatLevel = ThreatLevel.MEDIUM;
      result.riskScore = 60;
      result.confidence = 0.9;
      result.evidence.indicators.push('频繁超出速率限制');
      result.evidence.patterns.push('持续的限制违规');
    }

    return result;
  }

  // 行为异常检测
  private async detectBehaviorAnomalies(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.BRUTE_FORCE,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    if (!this.config.detection.behaviorAnalysis) {
      return result;
    }

    const profile = this.behaviorProfiles.get(request.ip);
    if (profile) {
      const currentPattern = `${request.method}:${request.endpoint}`;

      if (!profile.normalPatterns.has(currentPattern)) {
        profile.anomalyScore += 10;

        if (profile.anomalyScore > 50) {
          result.detected = true;
          result.threatLevel = ThreatLevel.MEDIUM;
          result.riskScore = profile.anomalyScore;
          result.confidence = 0.6;
          result.evidence.anomalies.push('异常行为模式');
          result.evidence.indicators.push(`异常评分: ${profile.anomalyScore}`);
        }
      } else {
        profile.anomalyScore = Math.max(0, profile.anomalyScore - 5);
      }
    }

    return result;
  }

  // 爬虫检测
  private async detectScraping(request: any): Promise<AttackDetectionResult> {
    const result: AttackDetectionResult = {
      detected: false,
      attackType: AttackType.SCRAPING,
      threatLevel: ThreatLevel.LOW,
      riskScore: 0,
      confidence: 0,
      evidence: { indicators: [], patterns: [], signatures: [], anomalies: [] },
      recommendations: [],
      suggestedActions: [],
    };

    // 检查User-Agent
    const userAgent = request.userAgent || '';
    const botPatterns = [/bot|crawler|spider|scraper/i, /curl|wget|python|java|go-http/i, /^$/];

    const isBotUserAgent = botPatterns.some((pattern) => pattern.test(userAgent));

    // 检查请求模式
    const history = this.requestHistory.get(request.ip);
    if (history) {
      const recentRequests = history.requests.filter(
        (timestamp) => Date.now() - timestamp < 300000 // 5分钟内
      );

      const isHighFrequency = recentRequests.length > 100;
      const isRegularInterval = history.averageInterval < 1000; // 少于1秒间隔

      if (isBotUserAgent || isHighFrequency || isRegularInterval) {
        result.detected = true;
        result.threatLevel = ThreatLevel.LOW;
        result.riskScore = 30;
        result.confidence = 0.7;

        if (isBotUserAgent) {
          result.evidence.indicators.push('机器人User-Agent');
        }
        if (isHighFrequency) {
          result.evidence.indicators.push('高频率请求');
        }
        if (isRegularInterval) {
          result.evidence.indicators.push('规律性请求间隔');
        }
      }
    }

    return result;
  }

  // 其他辅助方法...
  private calculateThreatLevel(results: AttackDetectionResult[]): ThreatLevel {
    const maxLevel = Math.max(...results.map((r) => this.getThreatLevelValue(r.threatLevel)));
    return this.getThreatLevelFromValue(maxLevel);
  }

  private getThreatLevelValue(level: ThreatLevel): number {
    switch (level) {
      case ThreatLevel.LOW:
        return 1;
      case ThreatLevel.MEDIUM:
        return 2;
      case ThreatLevel.HIGH:
        return 3;
      case ThreatLevel.CRITICAL:
        return 4;
      default:
        return 0;
    }
  }

  private getThreatLevelFromValue(value: number): ThreatLevel {
    switch (value) {
      case 1:
        return ThreatLevel.LOW;
      case 2:
        return ThreatLevel.MEDIUM;
      case 3:
        return ThreatLevel.HIGH;
      case 4:
        return ThreatLevel.CRITICAL;
      default:
        return ThreatLevel.LOW;
    }
  }

  private generateRecommendations(results: AttackDetectionResult[]): string[] {
    const recommendations: string[] = [];

    results.forEach((result) => {
      switch (result.attackType) {
        case AttackType.BRUTE_FORCE:
          recommendations.push('实施更强的密码策略');
          recommendations.push('启用账户锁定机制');
          break;
        case AttackType.DDoS:
          recommendations.push('配置负载均衡');
          recommendations.push('启用DDoS防护服务');
          break;
        case AttackType.SQL_INJECTION:
          recommendations.push('使用参数化查询');
          recommendations.push('输入验证和净化');
          break;
        case AttackType.XSS:
          recommendations.push('实施内容安全策略(CSP)');
          recommendations.push('输出编码');
          break;
      }
    });

    return [...new Set(recommendations)];
  }

  private generateSuggestedActions(
    threatLevel: ThreatLevel,
    attackType: AttackType
  ): ProtectionAction[] {
    const actions: ProtectionAction[] = [];

    switch (threatLevel) {
      case ThreatLevel.LOW:
        actions.push(ProtectionAction.MONITOR);
        break;
      case ThreatLevel.MEDIUM:
        actions.push(ProtectionAction.RATE_LIMIT, ProtectionAction.CAPTCHA);
        break;
      case ThreatLevel.HIGH:
        actions.push(ProtectionAction.BLOCK_IP, ProtectionAction.ALERT);
        break;
      case ThreatLevel.CRITICAL:
        actions.push(
          ProtectionAction.BLOCK_IP,
          ProtectionAction.QUARANTINE,
          ProtectionAction.ALERT
        );
        break;
    }

    return actions;
  }

  private findMatchingRule(request: any, detection: AttackDetectionResult): BlockingRule | null {
    const rules = Array.from(this.blockingRules.values())
      .filter((rule) => rule.enabled && rule.attackTypes.includes(detection.attackType))
      .sort((a, b) => b.priority - a.priority);

    for (const rule of rules) {
      if (this.ruleMatches(rule, request, detection)) {
        return rule;
      }
    }

    return null;
  }

  private ruleMatches(rule: BlockingRule, request: any, detection: AttackDetectionResult): boolean {
    const criteria = rule.conditions.criteria;

    if (criteria.ip && criteria.ip !== request.ip) return false;
    if (criteria.userId && criteria.userId !== request.userId) return false;
    if (criteria.userAgent && criteria.userAgent !== request.userAgent) return false;
    if (criteria.endpoint && !request.endpoint.includes(criteria.endpoint)) return false;
    if (criteria.method && criteria.method !== request.method) return false;

    return true;
  }

  private createBlockingInstance(
    rule: BlockingRule,
    request: any,
    detection: AttackDetectionResult
  ): BlockingInstance {
    const now = Date.now();

    return {
      id: this.generateBlockId(),
      ruleId: rule.id,
      ruleName: rule.name,
      target: {
        type: 'ip',
        value: request.ip,
        metadata: {
          userAgent: request.userAgent,
          endpoint: request.endpoint,
        },
      },
      actions: rule.actions.map((a) => a.type),
      reason: detection.evidence.indicators.join(', '),
      evidence: {
        attackType: detection.attackType,
        threatLevel: detection.threatLevel,
        riskScore: detection.riskScore,
        events: [request],
        metadata: detection.evidence,
      },
      startTime: now,
      endTime: rule.actions[0].duration > 0 ? now + rule.actions[0].duration * 1000 : 0,
      status: 'active',
      createdBy: 'system',
      appealable: true,
    };
  }

  private async executeProtectionActions(instance: BlockingInstance): Promise<void> {
    for (const action of instance.actions) {
      switch (action) {
        case ProtectionAction.BLOCK_IP:
          this.blacklist.add(instance.target.value);
          break;
        case ProtectionAction.RATE_LIMIT:
          this.applyRateLimit(instance.target.value);
          break;
        case ProtectionAction.ALERT:
          this.emit('protectionAlert', instance);
          break;
        case ProtectionAction.QUARANTINE:
          this.quarantineTarget(instance.target.value);
          break;
      }
    }
  }

  private applyRateLimit(ip: string): void {
    this.rateLimit.set(ip, {
      count: 0,
      windowStart: Date.now(),
      blocked: true,
    });
  }

  private quarantineTarget(ip: string): void {
    // 实施隔离措施
    console.log(`隔离目标: ${ip}`);
  }

  private updateRequestHistory(request: any): void {
    const history = this.requestHistory.get(request.ip) || {
      requests: [],
      lastRequest: 0,
      averageInterval: 0,
    };

    history.requests.push(request.timestamp);

    // 只保留最近1小时的记录
    const oneHourAgo = Date.now() - 3600000;
    history.requests = history.requests.filter((timestamp) => timestamp > oneHourAgo);

    // 计算平均间隔
    if (history.requests.length > 1) {
      const intervals = [];
      for (let i = 1; i < history.requests.length; i++) {
        intervals.push(history.requests[i] - history.requests[i - 1]);
      }
      history.averageInterval = intervals.reduce((a, b) => a + b, 0) / intervals.length;
    }

    history.lastRequest = request.timestamp;
    this.requestHistory.set(request.ip, history);
  }

  private updateMetrics(instance: BlockingInstance): void {
    this.metrics.totalBlocks++;
    this.metrics.blocksPerType.set(
      instance.evidence.attackType,
      (this.metrics.blocksPerType.get(instance.evidence.attackType) || 0) + 1
    );

    instance.actions.forEach((action) => {
      this.metrics.blocksPerAction.set(action, (this.metrics.blocksPerAction.get(action) || 0) + 1);
    });
  }

  private initializeDefaultRules(): void {
    // 默认规则会在这里初始化
    const defaultRules: Omit<BlockingRule, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: '暴力破解防护',
        description: '检测并阻止暴力破解攻击',
        attackTypes: [AttackType.BRUTE_FORCE],
        conditions: {
          threshold: 5,
          timeWindow: 300,
          aggregation: 'count',
          criteria: {},
        },
        actions: [
          {
            type: ProtectionAction.BLOCK_IP,
            duration: 3600,
            config: {},
          },
        ],
        enabled: true,
        priority: 100,
      },
      {
        name: 'DDoS防护',
        description: '检测并阻止DDoS攻击',
        attackTypes: [AttackType.DDoS],
        conditions: {
          threshold: 100,
          timeWindow: 60,
          aggregation: 'rate',
          criteria: {},
        },
        actions: [
          {
            type: ProtectionAction.BLOCK_IP,
            duration: 1800,
            config: {},
          },
        ],
        enabled: true,
        priority: 200,
      },
    ];

    defaultRules.forEach((rule) => {
      const fullRule: BlockingRule = {
        ...rule,
        id: this.generateRuleId(),
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
      this.blockingRules.set(fullRule.id, fullRule);
    });
  }

  private startCleanupTimer(): void {
    setInterval(() => {
      const now = Date.now();

      // 清理过期的阻断
      for (const [id, block] of this.activeBlocks.entries()) {
        if (block.endTime > 0 && block.endTime < now) {
          block.status = 'expired';
          this.activeBlocks.delete(id);
          this.emit('blockExpired', block);
        }
      }

      // 清理速率限制
      for (const [ip, data] of this.rateLimit.entries()) {
        if (now - data.windowStart > 60000) {
          this.rateLimit.delete(ip);
        }
      }

      // 清理旧的请求历史
      for (const [ip, history] of this.requestHistory.entries()) {
        if (now - history.lastRequest > 3600000) {
          this.requestHistory.delete(ip);
        }
      }
    }, 60000); // 每分钟清理一次
  }

  private startMetricsCalculation(): void {
    setInterval(() => {
      // 计算检测准确率等指标
      this.calculateDetectionAccuracy();
    }, 300000); // 每5分钟计算一次
  }

  private calculateDetectionAccuracy(): void {
    // 这里可以实现检测准确率的计算逻辑
    this.metrics.detectionAccuracy = 0.95; // 示例值
  }

  private generateBlockId(): string {
    return `block_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 公共方法
  getMetrics() {
    return {
      ...this.metrics,
      blocksPerType: Object.fromEntries(this.metrics.blocksPerType),
      blocksPerAction: Object.fromEntries(this.metrics.blocksPerAction),
      activeBlocks: this.activeBlocks.size,
      blacklistSize: this.blacklist.size,
      whitelistSize: this.whitelist.size,
    };
  }

  addToWhitelist(ip: string): void {
    this.whitelist.add(ip);
  }

  removeFromWhitelist(ip: string): void {
    this.whitelist.delete(ip);
  }

  addToBlacklist(ip: string): void {
    this.blacklist.add(ip);
  }

  removeFromBlacklist(ip: string): void {
    this.blacklist.delete(ip);
  }

  getActiveBlocks(): BlockingInstance[] {
    return Array.from(this.activeBlocks.values());
  }

  removeBlock(blockId: string, removedBy: string): boolean {
    const block = this.activeBlocks.get(blockId);
    if (!block) return false;

    block.status = 'manually_removed';
    block.removedBy = removedBy;
    block.removedAt = Date.now();

    this.activeBlocks.delete(blockId);
    this.emit('blockRemoved', block);

    return true;
  }
}

// 预设配置
export const protectionConfigs = {
  strict: {
    enabled: true,
    autoBlock: true,
    learningMode: false,
    adaptiveThresholds: true,
    globalRateLimit: {
      enabled: true,
      requestsPerSecond: 10,
      burstSize: 20,
    },
    detection: {
      behaviorAnalysis: true,
      signatureMatching: true,
      anomalyDetection: true,
      machineLearning: true,
    },
    response: {
      gracePeriod: 5,
      escalationRules: true,
      whitelistBypass: true,
    },
    monitoring: {
      logBlocks: true,
      alertOnBlock: true,
      trackMetrics: true,
    },
  } as AttackProtectionConfig,

  standard: {
    enabled: true,
    autoBlock: true,
    learningMode: false,
    adaptiveThresholds: false,
    globalRateLimit: {
      enabled: true,
      requestsPerSecond: 20,
      burstSize: 40,
    },
    detection: {
      behaviorAnalysis: false,
      signatureMatching: true,
      anomalyDetection: false,
      machineLearning: false,
    },
    response: {
      gracePeriod: 10,
      escalationRules: false,
      whitelistBypass: true,
    },
    monitoring: {
      logBlocks: true,
      alertOnBlock: false,
      trackMetrics: true,
    },
  } as AttackProtectionConfig,
};

// 工厂函数
export function createAttackProtectionManager(
  config: AttackProtectionConfig
): AttackProtectionManager {
  return new AttackProtectionManager(config);
}

export function createStrictProtectionManager(): AttackProtectionManager {
  return new AttackProtectionManager(protectionConfigs.strict);
}

export function createStandardProtectionManager(): AttackProtectionManager {
  return new AttackProtectionManager(protectionConfigs.standard);
}
