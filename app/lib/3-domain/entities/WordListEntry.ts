/**
 * WordListEntry Domain Entity
 * 代表词库条目实体，表示单词与词库的关联关系
 */
export interface WordListEntry {
  id: number;
  word: string; // 单词文本
  wordListIds: number[]; // 该单词所属的词库ID数组（支持一个单词属于多个词库）
  wordListId: number; // 主要关联的词库ID (用于关系约束)
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 创建词库条目的数据传输对象
 */
export interface CreateWordListEntryData {
  word: string;
  wordListIds: number[];
  wordListId: number; // 主要词库ID
}

/**
 * 更新词库条目的数据传输对象
 */
export interface UpdateWordListEntryData {
  word?: string;
  wordListIds?: number[];
  wordListId?: number;
}

/**
 * 词库条目查询参数
 */
export interface WordListEntryQueryOptions {
  wordListId?: number;
  wordListIds?: number[]; // 查询属于任意指定词库的单词
  word?: string; // 精确匹配单词
  wordPattern?: string; // 单词模式匹配
  limit?: number;
  offset?: number;
  random?: boolean; // 是否随机抽取
}

/**
 * 词库单词练习数据（包含用户数据）
 */
export interface WordListPracticeWord {
  id: number;
  word: string;
  wordListIds: number[];
  // 用户相关数据
  practiceCount?: number;
  errorCount?: number;
  averageTime?: number;
  proficiencyScore?: number;
  lastPracticed?: Date;
  isMarked?: boolean; // 是否被标记为困难单词
  // 词汇信息（从Vocabulary表获取）
  phonetics?: string[];
  definitions?: Array<{
    pos: string;
    definition: string;
    chinese: string;
  }>;
}

/**
 * 词库练习会话数据
 */
export interface WordListPracticeSession {
  wordListId: number;
  words: WordListPracticeWord[];
  sessionConfig: {
    pageSize: number;
    currentPage: number;
    totalWords: number;
    isRandomized: boolean;
    filterDifficult?: boolean; // 是否只显示困难单词
    skipMastered?: boolean; // 是否跳过已掌握的单词
  };
}