/**
 * 检查 pos_values.md 中的词性是否都在 pos-mappings.ts 中有映射
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { POS_TO_ABBREVIATION, POS_TO_CHINESE } from './pos-mappings.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function checkPosCoverage() {
  try {
    // 读取 pos_values.md 文件
    const posValuesPath = path.join(__dirname, 'pos_values.md');
    const posValuesContent = fs.readFileSync(posValuesPath, 'utf-8');
    const posValuesFromFile = posValuesContent
      .split('\n')
      .map((line) => line.trim())
      .filter((line) => line.length > 0);

    console.log(`📋 pos_values.md 中共有 ${posValuesFromFile.length} 个词性`);
    console.log(`🗂️  pos-mappings.ts 中共有 ${Object.keys(POS_TO_ABBREVIATION).length} 个映射`);

    // 检查缺失的词性
    const missingInAbbreviation = posValuesFromFile.filter((pos) => !(pos in POS_TO_ABBREVIATION));
    const missingInChinese = posValuesFromFile.filter((pos) => !(pos in POS_TO_CHINESE));

    console.log('\n🔍 检查结果:');

    if (missingInAbbreviation.length === 0) {
      console.log('✅ 所有词性都有缩写映射');
    } else {
      console.log(`❌ 缺少缩写映射的词性 (${missingInAbbreviation.length} 个):`);
      missingInAbbreviation.forEach((pos) => {
        console.log(`   - "${pos}"`);
      });
    }

    if (missingInChinese.length === 0) {
      console.log('✅ 所有词性都有中文映射');
    } else {
      console.log(`❌ 缺少中文映射的词性 (${missingInChinese.length} 个):`);
      missingInChinese.forEach((pos) => {
        console.log(`   - "${pos}"`);
      });
    }

    // 检查映射中多余的词性
    const extraInMappings = Object.keys(POS_TO_ABBREVIATION).filter(
      (pos) => !posValuesFromFile.includes(pos)
    );

    if (extraInMappings.length > 0) {
      console.log(`\n📝 映射中存在但不在 pos_values.md 中的词性 (${extraInMappings.length} 个):`);
      extraInMappings.forEach((pos) => {
        console.log(`   - "${pos}"`);
      });
    }

    // 生成缺失映射的代码
    if (missingInAbbreviation.length > 0 || missingInChinese.length > 0) {
      console.log('\n🛠️  建议添加的映射代码:');

      if (missingInAbbreviation.length > 0) {
        console.log('\n// 缺少的缩写映射:');
        missingInAbbreviation.forEach((pos) => {
          // 简单的缩写生成逻辑
          let abbr = pos;
          if (pos.includes('(')) {
            // 对于带括号的，取主要部分
            abbr = pos.split('(')[0].trim();
          }
          if (abbr === 'noun') abbr = 'n.';
          else if (abbr === 'verb') abbr = 'v.';
          else if (abbr === 'adjective') abbr = 'adj.';
          else if (abbr === 'adverb') abbr = 'adv.';
          else if (abbr.length > 4) abbr = abbr.substring(0, 4) + '.';

          console.log(`  '${pos}': '${abbr}',`);
        });
      }

      if (missingInChinese.length > 0) {
        console.log('\n// 缺少的中文映射:');
        missingInChinese.forEach((pos) => {
          // 简单的中文翻译生成逻辑
          let chinese = pos;
          if (pos.includes('(')) {
            // 对于带括号的，取主要部分
            chinese = pos.split('(')[0].trim();
          }
          if (chinese === 'noun') chinese = '名词';
          else if (chinese === 'verb') chinese = '动词';
          else if (chinese === 'adjective') chinese = '形容词';
          else if (chinese === 'adverb') chinese = '副词';

          console.log(`  '${pos}': '${chinese}',`);
        });
      }
    }

    return {
      totalPosValues: posValuesFromFile.length,
      totalMappings: Object.keys(POS_TO_ABBREVIATION).length,
      missingInAbbreviation,
      missingInChinese,
      extraInMappings,
    };
  } catch (error) {
    console.error('❌ 检查过程中出错:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  checkPosCoverage()
    .then((result) => {
      console.log('\n📊 检查完成');
      if (result.missingInAbbreviation.length === 0 && result.missingInChinese.length === 0) {
        console.log('🎉 所有词性都有完整的映射！');
      }
    })
    .catch((error) => {
      console.error('检查失败:', error);
      process.exit(1);
    });
}

export { checkPosCoverage };
