/**
 * 获取练习句子 API
 * GET /api/practice/sentence
 * 
 * 支持随机获取句子或根据条件筛选句子进行练习
 */

import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { authOptions } from '@/api/auth/[...nextauth]/route';
import { z } from 'zod';
import { SentenceWithContextOptions } from '@/lib/2-repositories/SentenceRepository';

// 请求参数验证 schema
const getSentenceQuerySchema = z.object({
  difficulty: z.coerce.number().min(1).max(10).optional()
    .or(z.string().transform(val => val.split(',').map(Number))).optional(),
  category: z.string().optional()
    .or(z.string().transform(val => val.split(','))).optional(),
  minLength: z.coerce.number().positive().optional(),
  maxLength: z.coerce.number().positive().optional(),
  search: z.string().optional(),
  excludeIds: z.string().transform(val => val.split(',').map(Number)).optional(),
  limit: z.coerce.number().min(1).max(50).default(1).optional(),
  sortBy: z.enum(['createdAt', 'difficulty', 'favoriteCount', 'length', 'random']).default('random').optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc').optional()
});

export async function GET(request: NextRequest) {
  try {
    // 用户认证
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);

    // 验证查询参数
    const queryResult = getSentenceQuerySchema.safeParse(
      Object.fromEntries(searchParams.entries())
    );

    if (!queryResult.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: queryResult.error.flatten().fieldErrors
        },
        { status: 400 }
      );
    }

    const options: SentenceWithContextOptions = {
      userId,
      ...queryResult.data,
      isActive: true
    };

    const practiceService = createPracticeService();

    // 如果 limit 为 1，使用 getRandomSentence；否则使用 getSentencesWithUserContext
    if (options.limit === 1) {
      const sentence = await practiceService.getPracticeSentence(userId, options);

      if (!sentence) {
        return NextResponse.json(
          { error: 'No sentences found matching the criteria' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          sentence,
          message: 'Practice sentence retrieved successfully'
        }
      });
    } else {
      const sentences = await practiceService.getSentencesWithUserContext(userId, options);

      return NextResponse.json({
        success: true,
        data: {
          sentences,
          count: sentences.length,
          message: 'Practice sentences retrieved successfully'
        }
      });
    }

  } catch (error) {
    console.error('Error fetching practice sentence:', error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'Unknown error');
    console.error('Error message:', error instanceof Error ? error.message : 'No message');

    return NextResponse.json(
      {
        error: 'Internal server error',
        message: 'Failed to retrieve practice sentence',
        debug: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}