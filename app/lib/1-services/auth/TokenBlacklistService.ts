/**
 * JWT令牌黑名单服务
 * 实现令牌吊销和黑名单管理功能
 */

import { prisma } from '@/lib/4-infrastructure/database/prisma';

export interface TokenBlacklistEntry {
  jti: string;
  userId?: string;
  tokenHash: string;
  reason: 'logout' | 'security' | 'expired' | 'revoked' | 'password_change' | 'refresh_rotation';
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
}

export class TokenBlacklistService {
  private static instance: TokenBlacklistService;

  public static getInstance(): TokenBlacklistService {
    if (!TokenBlacklistService.instance) {
      TokenBlacklistService.instance = new TokenBlacklistService();
    }
    return TokenBlacklistService.instance;
  }

  /**
   * 将JWT令牌加入黑名单
   */
  async blacklistToken(entry: TokenBlacklistEntry): Promise<void> {
    try {
      await prisma.tokenBlacklist.create({
        data: {
          jti: entry.jti,
          userId: entry.userId,
          tokenHash: entry.tokenHash,
          reason: entry.reason,
          expiresAt: entry.expiresAt,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
        },
      });
    } catch (error) {
      console.error('Failed to blacklist token:', error);
      throw new Error('Failed to blacklist token');
    }
  }

  /**
   * 检查令牌是否在黑名单中
   */
  async isBlacklisted(jti: string): Promise<boolean> {
    try {
      const entry = await prisma.tokenBlacklist.findUnique({
        where: { jti },
      });

      if (!entry) return false;

      // 检查是否过期
      if (entry.expiresAt <= new Date()) {
        // 清理过期的黑名单条目
        await this.cleanupExpiredTokens();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to check token blacklist:', error);
      // 安全起见，如果检查失败则认为令牌已被列入黑名单
      return true;
    }
  }

  /**
   * 通过令牌哈希检查是否在黑名单中
   */
  async isTokenHashBlacklisted(tokenHash: string): Promise<boolean> {
    try {
      const entry = await prisma.tokenBlacklist.findUnique({
        where: { tokenHash },
      });

      if (!entry) return false;

      if (entry.expiresAt <= new Date()) {
        await this.cleanupExpiredTokens();
        return false;
      }

      return true;
    } catch (error) {
      console.error('Failed to check token hash blacklist:', error);
      return true;
    }
  }

  /**
   * 将用户的所有令牌加入黑名单（用于密码修改等场景）
   */
  async blacklistAllUserTokens(
    userId: string,
    reason: 'password_change' | 'security',
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    try {
      // 获取该用户所有有效的refresh token
      const refreshTokens = await prisma.refreshToken.findMany({
        where: {
          userId,
          isRevoked: false,
          expiresAt: { gt: new Date() },
        },
      });

      // 将所有refresh token吊销
      await prisma.refreshToken.updateMany({
        where: { userId },
        data: {
          isRevoked: true,
          revokedAt: new Date(),
          revokedBy: reason,
        },
      });

      // 由于access token是无状态的，我们需要在黑名单中记录用户ID和时间戳
      // 这样可以在验证时检查token的签发时间是否在密码修改之前
      const blacklistEntry: TokenBlacklistEntry = {
        jti: `user_${userId}_${Date.now()}`, // 生成唯一的JTI
        userId,
        tokenHash: this.hashString(`user_revocation_${userId}_${Date.now()}`),
        reason,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后清理
        ipAddress,
        userAgent,
      };

      await this.blacklistToken(blacklistEntry);
    } catch (error) {
      console.error('Failed to blacklist all user tokens:', error);
      throw new Error('Failed to blacklist user tokens');
    }
  }

  /**
   * 批量将令牌加入黑名单
   */
  async blacklistTokens(entries: TokenBlacklistEntry[]): Promise<void> {
    try {
      await prisma.tokenBlacklist.createMany({
        data: entries.map((entry) => ({
          jti: entry.jti,
          userId: entry.userId,
          tokenHash: entry.tokenHash,
          reason: entry.reason,
          expiresAt: entry.expiresAt,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
        })),
        skipDuplicates: true,
      });
    } catch (error) {
      console.error('Failed to blacklist tokens:', error);
      throw new Error('Failed to blacklist tokens');
    }
  }

  /**
   * 清理过期的黑名单条目
   */
  async cleanupExpiredTokens(): Promise<number> {
    try {
      const result = await prisma.tokenBlacklist.deleteMany({
        where: {
          expiresAt: { lte: new Date() },
        },
      });

      return result.count;
    } catch (error) {
      console.error('Failed to cleanup expired tokens:', error);
      return 0;
    }
  }

  /**
   * 获取黑名单统计信息
   */
  async getBlacklistStats(): Promise<{
    total: number;
    byReason: Record<string, number>;
    expiredCount: number;
  }> {
    try {
      const [total, byReason, expiredCount] = await Promise.all([
        prisma.tokenBlacklist.count(),
        prisma.tokenBlacklist.groupBy({
          by: ['reason'],
          _count: { reason: true },
        }),
        prisma.tokenBlacklist.count({
          where: { expiresAt: { lte: new Date() } },
        }),
      ]);

      const reasonStats = byReason.reduce(
        (acc, item) => {
          acc[item.reason] = item._count.reason;
          return acc;
        },
        {} as Record<string, number>
      );

      return {
        total,
        byReason: reasonStats,
        expiredCount,
      };
    } catch (error) {
      console.error('Failed to get blacklist stats:', error);
      return { total: 0, byReason: {}, expiredCount: 0 };
    }
  }

  /**
   * 检查用户令牌是否需要强制过期（用于密码修改场景）
   */
  async shouldForceExpireUserTokens(userId: string, tokenIssuedAt: number): Promise<boolean> {
    try {
      // 查找该用户最近的安全相关黑名单条目
      const securityRevocation = await prisma.tokenBlacklist.findFirst({
        where: {
          userId,
          reason: { in: ['password_change', 'security'] },
          createdAt: { gt: new Date(tokenIssuedAt * 1000) },
        },
        orderBy: { createdAt: 'desc' },
      });

      return !!securityRevocation;
    } catch (error) {
      console.error('Failed to check force expiration:', error);
      return false;
    }
  }

  /**
   * 计算字符串哈希值
   */
  private hashString(input: string): string {
    const crypto = require('crypto');
    return crypto.createHash('sha256').update(input).digest('hex');
  }
}

// 单例导出
export const tokenBlacklistService = TokenBlacklistService.getInstance();