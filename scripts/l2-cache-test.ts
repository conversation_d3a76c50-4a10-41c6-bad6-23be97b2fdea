#!/usr/bin/env tsx

/**
 * L2缓存测试脚本
 * 测试流程：
 * 1. 冷启动测试 → 建立所有缓存层
 * 2. 清理API缓存（L1层：search:*）→ 保留L2缓存（vocab:*, forms:*等）
 * 3. 测试L2缓存性能 → 验证L2缓存层效果
 */

const baseUrl = 'http://localhost:3000';

interface TestResult {
  word: string;
  success: boolean;
  responseTime: number;
  cacheSource?: string;
  cacheStatus?: string;
  status?: number;
}

interface TestPhaseResult {
  phase: string;
  results: TestResult[];
  avgResponseTime: number;
  successRate: number;
  cacheSourceStats: Record<string, number>;
}

/**
 * 发起API请求
 */
async function makeRequest(
  word: string,
  index: number,
  fresh: boolean = false
): Promise<TestResult> {
  const startTime = performance.now();
  const url = `${baseUrl}/api/dictionary/en/${word}${fresh ? '?fresh=true' : ''}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'User-Agent': 'L2-Cache-Test/1.0',
      },
      signal: AbortSignal.timeout(15000),
    });

    const responseTime = performance.now() - startTime;
    const cacheSource = response.headers.get('x-cache-source');
    const cacheStatus = response.headers.get('x-cache-status');

    const freshFlag = fresh ? ' (fresh)' : '';
    console.log(
      `   ✅ 请求 ${index}: ${word}${freshFlag} (${Math.round(responseTime)}ms, source: ${cacheSource || 'unknown'})`
    );

    return {
      word,
      success: response.ok,
      responseTime,
      cacheSource,
      cacheStatus,
      status: response.status,
    };
  } catch (error) {
    const responseTime = performance.now() - startTime;
    console.log(`   ❌ 请求 ${index}: ${word} 失败 (${Math.round(responseTime)}ms) - ${error}`);

    return {
      word,
      success: false,
      responseTime,
      cacheSource: 'error',
      cacheStatus: 'error',
    };
  }
}

/**
 * 清理API级别缓存（L1层：search:*）
 */
async function clearApiCache(): Promise<void> {
  console.log('\n🧹 清理API级别缓存 (L1层: search:*)...');

  try {
    const response = await fetch(`${baseUrl}/api/cache/clear`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'pattern',
        pattern: 'search:*',
      }),
    });

    if (response.ok) {
      const result = await response.json();
      const deletedCount = result.result?.keysDeleted || 0;
      console.log(`✅ API缓存已清理: ${deletedCount} 个 search:* 键被删除`);
      console.log('💡 L2缓存层（vocab:*, forms:*等）保持不变');
    } else {
      console.log('⚠️ API缓存清理失败，但继续测试');
    }
  } catch (error) {
    console.log('⚠️ 无法清理API缓存，继续测试');
  }
}

/**
 * 分析测试结果
 */
function analyzeResults(results: TestResult[], phase: string): TestPhaseResult {
  const successfulRequests = results.filter((r) => r.success);
  const avgResponseTime =
    successfulRequests.length > 0
      ? successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length
      : 0;

  const successRate = results.length > 0 ? successfulRequests.length / results.length : 0;

  // 统计缓存来源
  const cacheSourceStats = results.reduce(
    (acc, result) => {
      const source = result.cacheSource || 'unknown';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    phase,
    results,
    avgResponseTime,
    successRate,
    cacheSourceStats,
  };
}

/**
 * 打印阶段结果
 */
function printPhaseResults(phaseResult: TestPhaseResult): void {
  console.log(`\n📊 ${phaseResult.phase} 结果分析:`);
  console.log('='.repeat(50));
  console.log(`   成功率: ${Math.round(phaseResult.successRate * 100)}%`);
  console.log(`   平均响应时间: ${Math.round(phaseResult.avgResponseTime)}ms`);

  console.log('\n🔍 数据来源分析:');
  Object.entries(phaseResult.cacheSourceStats).forEach(([source, count]) => {
    const percentage = Math.round((count / phaseResult.results.length) * 100);
    console.log(`   ${source}: ${count} (${percentage}%)`);
  });
}

/**
 * 执行L2缓存测试
 */
async function runL2CacheTest(concurrency: number = 10): Promise<void> {
  console.log('🚀 L2缓存测试');
  console.log('='.repeat(60));
  console.log('📋 测试流程:');
  console.log('   1. 冷启动测试 → 建立所有缓存层');
  console.log('   2. 清理API缓存（L1层）→ 保留L2缓存');
  console.log('   3. 测试L2缓存性能 → 验证L2缓存效果');
  console.log(`   🎯 并发数: ${concurrency}`);
  console.log('');

  // 测试单词
  const testWords = [
    'computer',
    'science',
    'technology',
    'database',
    'network',
    'algorithm',
    'programming',
    'software',
    'hardware',
    'internet',
  ];

  // 阶段1：冷启动测试（建立缓存）
  console.log('🔥 阶段1：冷启动测试（建立缓存）');
  console.log('💡 使用 ?fresh=true 确保从数据库获取，同时建立缓存');

  const coldStartPromises = Array.from({ length: concurrency }, (_, i) => {
    const word = testWords[i % testWords.length];
    return makeRequest(word, i + 1, true); // fresh=true
  });

  const coldStartResults = await Promise.all(coldStartPromises);
  const coldStartAnalysis = analyzeResults(coldStartResults, '冷启动测试');
  printPhaseResults(coldStartAnalysis);

  // 等待缓存写入完成
  console.log('\n⏳ 等待缓存写入完成...');
  await new Promise((resolve) => setTimeout(resolve, 3000)); // 增加等待时间

  // 阶段2：清理API缓存（L1层）
  await clearApiCache();

  // 等待清理完成
  await new Promise((resolve) => setTimeout(resolve, 1000));

  // 阶段3：测试L2缓存性能
  console.log('\n🎯 阶段3：测试L2缓存性能');
  console.log('💡 相同单词请求，应该命中L2缓存（vocab:*, forms:*等）');

  const l2CachePromises = Array.from({ length: concurrency }, (_, i) => {
    const word = testWords[i % testWords.length];
    return makeRequest(word, i + 1, false); // 不使用fresh参数
  });

  const l2CacheResults = await Promise.all(l2CachePromises);
  const l2CacheAnalysis = analyzeResults(l2CacheResults, 'L2缓存测试');
  printPhaseResults(l2CacheAnalysis);

  // 对比分析
  console.log('\n📈 对比分析:');
  console.log('='.repeat(60));

  const coldStartAvg = coldStartAnalysis.avgResponseTime;
  const l2CacheAvg = l2CacheAnalysis.avgResponseTime;
  const improvement = coldStartAvg > 0 ? ((coldStartAvg - l2CacheAvg) / coldStartAvg) * 100 : 0;

  console.log(`   冷启动平均响应时间: ${Math.round(coldStartAvg)}ms`);
  console.log(`   L2缓存平均响应时间: ${Math.round(l2CacheAvg)}ms`);
  console.log(`   性能提升: ${Math.round(improvement)}%`);

  // L2缓存效果验证
  const l2DatabaseHits = l2CacheAnalysis.cacheSourceStats['database'] || 0;
  const l2RedisHits = l2CacheAnalysis.cacheSourceStats['redis_cache'] || 0;
  const l2MemoryHits = l2CacheAnalysis.cacheSourceStats['memory_cache'] || 0;

  console.log('\n✅ L2缓存效果验证:');

  if (l2DatabaseHits === 0 && (l2RedisHits > 0 || l2MemoryHits > 0)) {
    console.log('🎉 完美！L2缓存完全生效');
    console.log('   ✅ 没有请求直接访问数据库');
    console.log('   ✅ 所有数据都来自L2缓存层');
  } else if (l2DatabaseHits < l2CacheResults.length / 2) {
    console.log('✅ 良好！L2缓存部分生效');
    console.log(`   🔍 数据库访问: ${l2DatabaseHits}/${l2CacheResults.length}`);
    console.log(`   🔍 缓存命中: ${l2RedisHits + l2MemoryHits}/${l2CacheResults.length}`);
  } else {
    console.log('⚠️ L2缓存效果不明显');
    console.log('   可能原因：');
    console.log('   1. L2缓存未正确建立');
    console.log('   2. 缓存TTL过短');
    console.log('   3. 缓存键策略问题');
  }

  // 性能评估
  console.log('\n📊 性能评估:');
  if (improvement > 50) {
    console.log('🎉 优秀！L2缓存显著提升性能');
  } else if (improvement > 20) {
    console.log('✅ 良好！L2缓存有效提升性能');
  } else if (improvement > 0) {
    console.log('🟡 一般，L2缓存有轻微提升');
  } else {
    console.log('❌ L2缓存未显示性能提升');
  }
}

/**
 * 主函数
 */
async function main() {
  const concurrency = parseInt(process.argv[2]) || 10;

  console.log('🔧 L2缓存性能测试');
  console.log('='.repeat(60));
  console.log('💡 测试原理:');
  console.log('   - 冷启动建立完整缓存');
  console.log('   - 清理API缓存（L1）保留词汇缓存（L2）');
  console.log('   - 验证L2缓存层的性能效果');
  console.log('');

  try {
    await runL2CacheTest(concurrency);

    console.log('\n✅ L2缓存测试完成');
    console.log('\n💡 使用说明:');
    console.log('   - 默认并发数: 10');
    console.log('   - 自定义并发数: npx tsx scripts/l2-cache-test.ts 20');
    console.log('   - 测试验证了多层缓存架构的有效性');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
