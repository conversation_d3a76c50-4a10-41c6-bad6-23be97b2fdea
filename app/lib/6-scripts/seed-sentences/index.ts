/**
 * 句子数据初始化脚本
 * 
 * 为开发和测试环境创建初始句子数据
 */

import { PrismaClient } from '@prisma/client';
import { SentenceEntity, SentenceCategory, SentenceSource } from '@/lib/3-domain/entities/Sentence';

const prisma = new PrismaClient();

// 示例句子数据
const sampleSentences = [
  // 商务类句子
  {
    content: "The quarterly revenue exceeded our expectations by fifteen percent.",
    category: SentenceCategory.BUSINESS,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Please schedule a meeting with the project stakeholders for next Tuesday.",
    category: SentenceCategory.BUSINESS,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Our marketing campaign successfully increased brand awareness in the target demographic.",
    category: SentenceCategory.BUSINESS,
    source: SentenceSource.SYSTEM
  },
  
  // 日常对话类句子
  {
    content: "Good morning! How was your weekend?",
    category: SentenceCategory.DAILY,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Thank you for your help with the project.",
    category: SentenceCategory.DAILY,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Would you like to join us for lunch today?",
    category: SentenceCategory.CONVERSATIONAL,
    source: SentenceSource.SYSTEM
  },
  
  // 技术类句子
  {
    content: "The database query optimization reduced response time significantly.",
    category: SentenceCategory.TECHNICAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Implementing microservices architecture improves system scalability and maintainability.",
    category: SentenceCategory.TECHNICAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "The new authentication system uses JWT tokens for secure user sessions.",
    category: SentenceCategory.TECHNICAL,
    source: SentenceSource.SYSTEM
  },
  
  // 学术类句子
  {
    content: "The research methodology employed quantitative analysis to evaluate the hypothesis.",
    category: SentenceCategory.ACADEMIC,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Statistical analysis revealed a significant correlation between the variables.",
    category: SentenceCategory.ACADEMIC,
    source: SentenceSource.SYSTEM
  },
  {
    content: "The experimental results support the theoretical framework proposed in the literature.",
    category: SentenceCategory.ACADEMIC,
    source: SentenceSource.SYSTEM
  },
  
  // 新闻类句子
  {
    content: "The economic indicators suggest steady growth in the technology sector.",
    category: SentenceCategory.NEWS,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Environmental protection policies are being implemented across multiple industries.",
    category: SentenceCategory.NEWS,
    source: SentenceSource.SYSTEM
  },
  {
    content: "The international conference addressed global challenges in sustainable development.",
    category: SentenceCategory.NEWS,
    source: SentenceSource.SYSTEM
  },
  
  // 文学类句子
  {
    content: "The golden sunset painted the sky with magnificent hues of orange and purple.",
    category: SentenceCategory.LITERATURE,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Her words carried the weight of unspoken emotions and hidden truths.",
    category: SentenceCategory.LITERATURE,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Time flowed like a gentle river, carrying memories of days long past.",
    category: SentenceCategory.LITERATURE,
    source: SentenceSource.SYSTEM
  },
  
  // 教育类句子
  {
    content: "Critical thinking skills are essential for academic and professional success.",
    category: SentenceCategory.EDUCATIONAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Interactive learning methods enhance student engagement and knowledge retention.",
    category: SentenceCategory.EDUCATIONAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Collaborative projects help students develop teamwork and communication skills.",
    category: SentenceCategory.EDUCATIONAL,
    source: SentenceSource.SYSTEM
  },
  
  // 正式场合句子
  {
    content: "We are pleased to announce the successful completion of the merger.",
    category: SentenceCategory.FORMAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "I would like to express my sincere gratitude for your valuable contribution.",
    category: SentenceCategory.FORMAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "The board of directors has approved the proposed strategic initiatives.",
    category: SentenceCategory.FORMAL,
    source: SentenceSource.SYSTEM
  },
  
  // 休闲聊天句子
  {
    content: "That movie was absolutely amazing! I loved every minute of it.",
    category: SentenceCategory.CASUAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "Let's grab some coffee and catch up on the latest news.",
    category: SentenceCategory.CASUAL,
    source: SentenceSource.SYSTEM
  },
  {
    content: "The weather has been perfect for outdoor activities this week.",
    category: SentenceCategory.CASUAL,
    source: SentenceSource.SYSTEM
  }
];

async function seedSentences() {
  console.log('🌱 Starting sentence data seeding...');
  
  try {
    // 删除现有的句子数据（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log('🗑️  Clearing existing sentence data...');
      await prisma.userSavedSentence.deleteMany();
      await prisma.sentence.deleteMany();
      console.log('✅ Existing data cleared');
    }
    
    // 创建句子
    console.log('📝 Creating sentences...');
    const createdSentences = [];
    
    for (const sentenceData of sampleSentences) {
      const metadata = SentenceEntity.generateMetadata(sentenceData.content);
      
      const sentence = await prisma.sentence.create({
        data: {
          content: sentenceData.content,
          category: sentenceData.category,
          source: sentenceData.source,
          difficulty: metadata.difficulty,
          length: metadata.length,
          favoriteCount: 0,
          isActive: true
        }
      });
      
      createdSentences.push(sentence);
      console.log(`✅ Created sentence: "${sentence.content.substring(0, 50)}..." (difficulty: ${sentence.difficulty})`);
    }
    
    console.log(`🎉 Successfully seeded ${createdSentences.length} sentences!`);
    
    // 显示统计信息
    const stats = await prisma.sentence.groupBy({
      by: ['category', 'difficulty'],
      _count: true,
      orderBy: [
        { category: 'asc' },
        { difficulty: 'asc' }
      ]
    });
    
    console.log('📊 Sentence statistics:');
    console.table(stats.map(stat => ({
      Category: stat.category || 'Unknown',
      Difficulty: stat.difficulty,
      Count: stat._count
    })));
    
    // 显示难度分布
    const difficultyStats = await prisma.sentence.groupBy({
      by: ['difficulty'],
      _count: true,
      _avg: { length: true },
      orderBy: { difficulty: 'asc' }
    });
    
    console.log('🎯 Difficulty distribution:');
    console.table(difficultyStats.map(stat => ({
      Difficulty: stat.difficulty,
      Count: stat._count,
      'Avg Length': Math.round(stat._avg.length || 0)
    })));
    
  } catch (error) {
    console.error('❌ Error seeding sentences:', error);
    throw error;
  }
}

async function main() {
  try {
    await seedSentences();
  } catch (error) {
    console.error('Fatal error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果脚本直接运行
if (require.main === module) {
  main();
}

export { seedSentences };