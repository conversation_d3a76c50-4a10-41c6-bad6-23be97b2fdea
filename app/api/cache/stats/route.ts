import { NextResponse } from 'next/server';
import { getRedisStats, CacheKeys, CacheTTL } from '../../../lib/4-infrastructure/cache/redis';
import { CacheRepository } from '../../../lib/2-repositories/CacheRepository';

/**
 * 缓存统计API
 * GET /api/cache/stats
 *
 * 返回详细的缓存统计信息：
 * - Redis 连接状态和性能指标
 * - 缓存命中率和内存使用情况
 * - 缓存键分布和TTL配置
 */
export async function GET(request: Request) {
  const startTime = Date.now();

  try {
    // 获取Redis统计信息
    const redisStats = await getRedisStats();

    // 获取缓存仓储统计信息
    const cacheRepo = new CacheRepository();
    const cacheRepoStats = await cacheRepo.getStats();

    const processingTime = Date.now() - startTime;

    const statsData = {
      success: true,
      timestamp: new Date().toISOString(),
      processingTime: `${processingTime}ms`,
      redis: {
        connected: redisStats.connected,
        memoryUsage: {
          bytes: redisStats.memoryUsage,
          humanReadable: formatBytes(redisStats.memoryUsage),
        },
        keys: {
          total: redisStats.keyCount,
          distribution: await getKeyDistribution(),
        },
        performance: {
          hitRate: redisStats.hitRate,
          hitRatePercentage: `${(redisStats.hitRate * 100).toFixed(2)}%`,
        },
      },
      repository: {
        hitRate: cacheRepoStats.hitRate,
        totalKeys: cacheRepoStats.totalKeys,
        memoryUsage: cacheRepoStats.memoryUsage,
      },
      configuration: {
        keyPrefix: process.env.REDIS_KEY_PREFIX || 'lucid:',
        ttlSettings: {
          vocabulary: `${CacheTTL.VOCABULARY}s (${CacheTTL.VOCABULARY / 3600}h)`,
          wordForms: `${CacheTTL.WORD_FORMS}s (${CacheTTL.WORD_FORMS / 3600}h)`,
          searchResult: `${CacheTTL.SEARCH_RESULT}s (${CacheTTL.SEARCH_RESULT / 3600}h)`,
          suggestions: `${CacheTTL.SUGGESTIONS}s (${CacheTTL.SUGGESTIONS / 60}m)`,
          stats: `${CacheTTL.STATS}s (${CacheTTL.STATS / 60}m)`,
        },
        keyPatterns: {
          vocabulary: 'vocab:*',
          wordForms: 'forms:*',
          searchResult: 'search:*',
          suggestions: 'suggest:*',
          stats: 'stats:*',
        },
      },
    };

    return NextResponse.json(statsData, { status: 200 });
  } catch (error) {
    const processingTime = Date.now() - startTime;

    console.error('Cache stats failed:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to retrieve cache statistics',
        timestamp: new Date().toISOString(),
        processingTime: `${processingTime}ms`,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * 获取缓存键分布统计
 */
async function getKeyDistribution(): Promise<Record<string, number>> {
  try {
    const cacheRepo = new CacheRepository();

    // 获取不同类型键的数量
    const patterns = {
      vocabulary: 'vocab:*',
      wordForms: 'forms:*',
      searchResult: 'search:*',
      suggestions: 'suggest:*',
      stats: 'stats:*',
    };

    const distribution: Record<string, number> = {};

    for (const [type, pattern] of Object.entries(patterns)) {
      try {
        // 这里简化处理，实际生产环境中可能需要更高效的方法
        distribution[type] = 0; // 占位符，实际实现可能需要Redis SCAN命令
      } catch (error) {
        distribution[type] = 0;
      }
    }

    return distribution;
  } catch (error) {
    console.error('Failed to get key distribution:', error);
    return {};
  }
}

/**
 * 格式化字节数为人类可读格式
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 处理不支持的HTTP方法
 */
export async function POST() {
  return NextResponse.json({ success: false, message: 'Method not allowed' }, { status: 405 });
}

export async function PUT() {
  return NextResponse.json({ success: false, message: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ success: false, message: 'Method not allowed' }, { status: 405 });
}
