#!/usr/bin/env node

/**
 * 灵活的数据库备份工具
 * 支持分类备份和schema变更兼容
 */

import { PrismaClient } from '@prisma/client';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';

const prisma = new PrismaClient();

// 备份配置
const BACKUP_CONFIG = {
  // 核心词典数据表（最重要，变更少）
  CORE_TABLES: ['Vocabulary', 'Explain', 'Definition', 'WordFormat'],
  
  // 用户相关表（可能经常变更schema）
  USER_TABLES: ['User', 'Account', 'Session', 'RefreshToken', 'TokenBlacklist', 'RateLimit', 'PasswordResetToken'],
  
  // 学习数据表（可能经常变更schema）
  LEARNING_TABLES: ['UserWordStat', 'UserWordProficiency', 'QueryLog'],
  
  // 辅助数据表（变更较少）
  AUXILIARY_TABLES: ['WordList', 'WordListEntry'],
  
  // 备份目录
  BACKUP_DIR: './backups'
};

/**
 * 生成JSON格式备份（兼容性最好）
 */
async function createJsonBackup(tableName, data, outputDir) {
  const filename = `${tableName.toLowerCase()}_${new Date().toISOString().slice(0, 10)}.json`;
  const filepath = join(outputDir, filename);
  
  await writeFile(filepath, JSON.stringify({
    table: tableName,
    timestamp: new Date().toISOString(),
    recordCount: data.length,
    data: data
  }, null, 2));
  
  console.log(`✅ ${tableName}: ${data.length.toLocaleString()} 条记录 -> ${filename}`);
  return filepath;
}

/**
 * 生成SQL格式备份（数据库原生）
 */
async function createSqlBackup(tableName, data, outputDir) {
  if (data.length === 0) return null;
  
  const filename = `${tableName.toLowerCase()}_${new Date().toISOString().slice(0, 10)}.sql`;
  const filepath = join(outputDir, filename);
  
  // 获取第一条记录的字段
  const fields = Object.keys(data[0]);
  const tableLowerCase = tableName === 'WordFormat' ? 'wordformat' : 
                         tableName.replace(/([A-Z])/g, '_$1').toLowerCase().slice(1);
  
  let sqlContent = `-- ${tableName} 备份数据\n`;
  sqlContent += `-- 生成时间: ${new Date().toISOString()}\n`;
  sqlContent += `-- 记录数量: ${data.length}\n\n`;
  
  // 分批插入，避免单条SQL过长
  const BATCH_SIZE = 1000;
  for (let i = 0; i < data.length; i += BATCH_SIZE) {
    const batch = data.slice(i, i + BATCH_SIZE);
    
    sqlContent += `INSERT INTO "${tableLowerCase}" (${fields.map(f => `"${f}"`).join(', ')}) VALUES\n`;
    
    const values = batch.map(record => {
      const vals = fields.map(field => {
        const value = record[field];
        if (value === null) return 'NULL';
        if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
        if (typeof value === 'boolean') return value ? 'true' : 'false';
        if (Array.isArray(value)) return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
        if (value instanceof Date) return `'${value.toISOString()}'`;
        return String(value);
      });
      return `  (${vals.join(', ')})`;
    }).join(',\n');
    
    sqlContent += values + ';\n\n';
  }
  
  await writeFile(filepath, sqlContent);
  console.log(`📄 ${tableName}: ${data.length.toLocaleString()} 条记录 -> ${filename}`);
  return filepath;
}

/**
 * 分类备份主函数
 */
async function performCategorizedBackup() {
  console.log('🚀 开始分类数据备份...\n');
  
  const timestamp = new Date().toISOString().slice(0, 16).replace(/[:.]/g, '-');
  const backupDir = join(BACKUP_CONFIG.BACKUP_DIR, `backup_${timestamp}`);
  
  // 创建备份目录
  await mkdir(backupDir, { recursive: true });
  console.log(`📁 备份目录: ${backupDir}\n`);
  
  const backupSummary = {
    timestamp: new Date().toISOString(),
    backupDir,
    categories: {}
  };
  
  // 定义表映射
  const tableModels = {
    'Vocabulary': () => prisma.vocabulary.findMany(),
    'Explain': () => prisma.explain.findMany(),
    'Definition': () => prisma.definition.findMany(),
    'WordFormat': () => prisma.wordFormat.findMany(),
    'User': () => prisma.user.findMany(),
    'Account': () => prisma.account.findMany(),
    'Session': () => prisma.session.findMany(),
    'RefreshToken': () => prisma.refreshToken.findMany(),
    'TokenBlacklist': () => prisma.tokenBlacklist.findMany(),
    'RateLimit': () => prisma.rateLimit.findMany(),
    'PasswordResetToken': () => prisma.passwordResetToken.findMany(),
    'UserWordStat': () => prisma.userWordStat.findMany(),
    'UserWordProficiency': () => prisma.userWordProficiency.findMany(),
    'QueryLog': () => prisma.queryLog.findMany(),
    'WordList': () => prisma.wordList.findMany(),
    'WordListEntry': () => prisma.wordListEntry.findMany(),
  };
  
  // 分类备份
  const categories = [
    { name: 'CORE', tables: BACKUP_CONFIG.CORE_TABLES, desc: '核心词典数据' },
    { name: 'USER', tables: BACKUP_CONFIG.USER_TABLES, desc: '用户账户数据' },
    { name: 'LEARNING', tables: BACKUP_CONFIG.LEARNING_TABLES, desc: '学习记录数据' },
    { name: 'AUXILIARY', tables: BACKUP_CONFIG.AUXILIARY_TABLES, desc: '辅助功能数据' }
  ];
  
  for (const category of categories) {
    console.log(`📂 ${category.desc} (${category.name}):`);
    
    const categoryDir = join(backupDir, category.name.toLowerCase());
    await mkdir(categoryDir, { recursive: true });
    
    const categoryStats = { tables: [], totalRecords: 0 };
    
    for (const tableName of category.tables) {
      try {
        const getData = tableModels[tableName];
        if (!getData) {
          console.log(`⚠️  ${tableName}: 未知表，跳过`);
          continue;
        }
        
        const data = await getData();
        
        if (data.length === 0) {
          console.log(`📝 ${tableName}: 0 条记录，跳过备份`);
          categoryStats.tables.push({ name: tableName, count: 0, files: [] });
          continue;
        }
        
        // 同时创建JSON和SQL备份
        const jsonFile = await createJsonBackup(tableName, data, categoryDir);
        const sqlFile = await createSqlBackup(tableName, data, categoryDir);
        
        categoryStats.tables.push({
          name: tableName,
          count: data.length,
          files: [jsonFile, sqlFile].filter(Boolean)
        });
        categoryStats.totalRecords += data.length;
        
      } catch (error) {
        console.error(`❌ ${tableName} 备份失败:`, error.message);
        categoryStats.tables.push({
          name: tableName,
          count: 0,
          files: [],
          error: error.message
        });
      }
    }
    
    backupSummary.categories[category.name] = categoryStats;
    console.log(`📊 ${category.desc}总计: ${categoryStats.totalRecords.toLocaleString()} 条记录\n`);
  }
  
  // 保存备份摘要
  const summaryFile = join(backupDir, 'backup_summary.json');
  await writeFile(summaryFile, JSON.stringify(backupSummary, null, 2));
  
  // 创建恢复脚本模板
  await createRestoreScriptTemplate(backupDir, backupSummary);
  
  console.log('🎉 分类备份完成!');
  console.log(`📁 备份位置: ${backupDir}`);
  console.log(`📋 备份摘要: backup_summary.json`);
  console.log(`🔧 恢复脚本: restore_template.js`);
  
  return backupSummary;
}

/**
 * 创建恢复脚本模板
 */
async function createRestoreScriptTemplate(backupDir, summary) {
  const template = `#!/usr/bin/env node

/**
 * 数据恢复脚本模板
 * 使用方法: node restore_template.js [category] [--dry-run]
 * 
 * 支持的分类:
 * - core: 核心词典数据 (${summary.categories.CORE?.totalRecords.toLocaleString() || 0}条)
 * - user: 用户账户数据 (${summary.categories.USER?.totalRecords.toLocaleString() || 0}条)
 * - learning: 学习记录数据 (${summary.categories.LEARNING?.totalRecords.toLocaleString() || 0}条)
 * - auxiliary: 辅助功能数据 (${summary.categories.AUXILIARY?.totalRecords.toLocaleString() || 0}条)
 * - all: 全部数据
 * 
 * 示例:
 * node restore_template.js core --dry-run    # 预览恢复核心数据
 * node restore_template.js core              # 实际恢复核心数据
 * node restore_template.js all               # 恢复全部数据
 */

import { PrismaClient } from '@prisma/client';
import { readFile } from 'fs/promises';
import { join } from 'path';

const prisma = new PrismaClient();

const BACKUP_DATA = ${JSON.stringify(summary, null, 2)};

async function restoreCategory(categoryName, dryRun = false) {
  console.log(\`🔄 \${dryRun ? '预览' : '开始'}恢复 \${categoryName} 数据...\`);
  
  const category = BACKUP_DATA.categories[categoryName.toUpperCase()];
  if (!category) {
    console.error(\`❌ 未找到分类: \${categoryName}\`);
    return;
  }
  
  for (const table of category.tables) {
    if (table.count === 0) {
      console.log(\`⏭️  跳过空表: \${table.name}\`);
      continue;
    }
    
    const jsonFile = table.files.find(f => f.endsWith('.json'));
    if (!jsonFile) {
      console.log(\`⚠️  \${table.name}: 未找到JSON备份文件\`);
      continue;
    }
    
    if (dryRun) {
      console.log(\`👀 预览: \${table.name} - \${table.count.toLocaleString()}条记录\`);
      continue;
    }
    
    try {
      console.log(\`📥 恢复 \${table.name}...\`);
      const backupContent = JSON.parse(await readFile(jsonFile, 'utf-8'));
      
      // 这里需要根据具体表实现恢复逻辑
      // 注意: schema变更时需要调整字段映射
      
      console.log(\`✅ \${table.name}: \${backupContent.data.length}条记录已恢复\`);
      
    } catch (error) {
      console.error(\`❌ \${table.name} 恢复失败:\`, error.message);
    }
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const category = args[0] || 'core';
const dryRun = args.includes('--dry-run');

if (category === 'all') {
  // 恢复所有分类
  ['CORE', 'AUXILIARY', 'USER', 'LEARNING'].forEach(async (cat) => {
    await restoreCategory(cat, dryRun);
  });
} else {
  restoreCategory(category, dryRun);
}
`;

  await writeFile(join(backupDir, 'restore_template.js'), template);
}

// 运行备份
performCategorizedBackup()
  .then(() => {
    console.log('\\n✅ 备份任务完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ 备份失败:', error);
    process.exit(1);
  })
  .finally(() => {
    prisma.$disconnect();
  });