import { WordList, CreateWordListData, UpdateWordListData, WordListQueryOptions, WordListStats } from '../3-domain/entities/WordList';

/**
 * WordList Repository Interface
 * 定义词库数据访问层的接口
 */
export interface IWordListRepository {
  /**
   * 获取所有词库
   */
  findAll(options?: WordListQueryOptions): Promise<WordList[]>;

  /**
   * 根据ID获取词库
   */
  findById(id: number): Promise<WordList | null>;

  /**
   * 根据名称获取词库
   */
  findByName(name: string): Promise<WordList | null>;

  /**
   * 创建词库
   */
  create(data: CreateWordListData): Promise<WordList>;

  /**
   * 更新词库
   */
  update(id: number, data: UpdateWordListData): Promise<WordList>;

  /**
   * 删除词库
   */
  delete(id: number): Promise<void>;

  /**
   * 获取词库统计信息（包含用户进度）
   */
  getStatsWithUserProgress(userId?: string): Promise<WordListStats[]>;

  /**
   * 批量创建词库
   */
  createMany(data: CreateWordListData[]): Promise<WordList[]>;
}

/**
 * WordList Repository Implementation
 * 使用Prisma实现词库数据访问
 */
import { prisma } from '../4-infrastructure/database/prisma';

export class WordListRepository implements IWordListRepository {
  
  async findAll(options: WordListQueryOptions = {}): Promise<WordList[]> {
    const where: any = {};

    if (options.isActive !== undefined) {
      where.isActive = options.isActive;
    }

    if (options.difficulty !== undefined) {
      where.difficulty = options.difficulty;
    }

    if (options.minDifficulty !== undefined || options.maxDifficulty !== undefined) {
      where.difficulty = {};
      if (options.minDifficulty !== undefined) {
        where.difficulty.gte = options.minDifficulty;
      }
      if (options.maxDifficulty !== undefined) {
        where.difficulty.lte = options.maxDifficulty;
      }
    }

    const wordLists = await prisma.wordList.findMany({
      where,
      orderBy: [
        { difficulty: 'asc' },
        { name: 'asc' }
      ]
    });

    return wordLists.map(this.mapToEntity);
  }

  async findById(id: number): Promise<WordList | null> {
    const wordList = await prisma.wordList.findUnique({
      where: { id }
    });

    return wordList ? this.mapToEntity(wordList) : null;
  }

  async findByName(name: string): Promise<WordList | null> {
    const wordList = await prisma.wordList.findUnique({
      where: { name }
    });

    return wordList ? this.mapToEntity(wordList) : null;
  }

  async create(data: CreateWordListData): Promise<WordList> {
    const wordList = await prisma.wordList.create({
      data: {
        name: data.name,
        description: data.description,
        difficulty: data.difficulty,
        totalWords: data.totalWords || 0,
        isActive: data.isActive ?? true
      }
    });

    return this.mapToEntity(wordList);
  }

  async update(id: number, data: UpdateWordListData): Promise<WordList> {
    const wordList = await prisma.wordList.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.description && { description: data.description }),
        ...(data.difficulty && { difficulty: data.difficulty }),
        ...(data.totalWords !== undefined && { totalWords: data.totalWords }),
        ...(data.isActive !== undefined && { isActive: data.isActive })
      }
    });

    return this.mapToEntity(wordList);
  }

  async delete(id: number): Promise<void> {
    await prisma.wordList.delete({
      where: { id }
    });
  }

  async getStatsWithUserProgress(userId?: string): Promise<WordListStats[]> {
    const wordLists = await prisma.wordList.findMany({
      where: { isActive: true },
      orderBy: [
        { difficulty: 'asc' },
        { name: 'asc' }
      ]
    });

    const stats: WordListStats[] = [];

    for (const wordList of wordLists) {
      const stat: WordListStats = {
        id: wordList.id,
        name: wordList.name,
        totalWords: wordList.totalWords
      };

      if (userId) {
        // 查询用户在此词库中的练习进度
        const userProgress = await this.getUserProgressForWordList(userId, wordList.id);
        stat.userPracticedWords = userProgress.practicedWords;
        stat.userProgress = userProgress.progressPercentage;
        stat.averageProficiency = userProgress.averageProficiency;
      }

      stats.push(stat);
    }

    return stats;
  }

  async createMany(data: CreateWordListData[]): Promise<WordList[]> {
    const createdWordLists = await prisma.$transaction(
      data.map(item => prisma.wordList.create({
        data: {
          name: item.name,
          description: item.description,
          difficulty: item.difficulty,
          totalWords: item.totalWords || 0,
          isActive: item.isActive ?? true
        }
      }))
    );

    return createdWordLists.map(this.mapToEntity);
  }

  /**
   * 获取用户在特定词库中的练习进度
   */
  private async getUserProgressForWordList(userId: string, wordListId: number) {
    // 获取此词库的所有单词
    const wordListEntries = await prisma.wordListEntry.findMany({
      where: {
        wordListIds: {
          has: wordListId
        }
      }
    });

    const totalWords = wordListEntries.length;
    let practicedWords = 0;
    let totalProficiency = 0;

    if (totalWords > 0) {
      // 查询用户对这些单词的熟练度数据
      const userProficiencies = await prisma.userWordProficiency.findMany({
        where: {
          userId,
          vocabulary: {
            word: {
              in: wordListEntries.map(entry => entry.word)
            }
          }
        },
        include: {
          vocabulary: true
        }
      });

      practicedWords = userProficiencies.length;
      totalProficiency = userProficiencies.reduce((sum, p) => sum + p.proficiencyScore, 0);
    }

    return {
      practicedWords,
      progressPercentage: totalWords > 0 ? Math.round((practicedWords / totalWords) * 100) : 0,
      averageProficiency: practicedWords > 0 ? totalProficiency / practicedWords : 0
    };
  }

  /**
   * 将Prisma模型映射为领域实体
   */
  private mapToEntity(model: any): WordList {
    return {
      id: model.id,
      name: model.name,
      description: model.description,
      difficulty: model.difficulty,
      totalWords: model.totalWords,
      isActive: model.isActive,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt
    };
  }
}