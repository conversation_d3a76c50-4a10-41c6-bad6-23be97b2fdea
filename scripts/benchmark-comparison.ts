/**
 * Benchmark Comparison Test
 *
 * Comprehensive performance comparison between the original heavy middleware
 * and the new lightweight security middleware
 *
 * Test Goals:
 * - Validate 70-80% performance improvement claim
 * - Compare response times across all security levels
 * - Analyze memory usage differences
 * - Measure throughput improvements
 * - Test concurrency handling improvements
 */

import { NextRequest } from 'next/server';
import {
  LightweightSecurityMiddleware,
  SecurityLevel,
} from '../app/lib/auth/lightweight-security-middleware';
import {
  EnhancedSecurityMiddleware,
  securityConfigs,
} from '../app/lib/auth/enhanced-security-middleware';

// ============================================================================
// Benchmark Configuration
// ============================================================================

interface BenchmarkConfig {
  iterations: number;
  warmupIterations: number;
  concurrencyLevels: number[];
  requestsPerLevel: number;
  testDuration: number;
  memoryMeasurementInterval: number;
}

const DEFAULT_BENCHMARK_CONFIG: BenchmarkConfig = {
  iterations: 5,
  warmupIterations: 100,
  concurrencyLevels: [1, 10, 25, 50, 100, 150],
  requestsPerLevel: 1000,
  testDuration: 30000, // 30 seconds
  memoryMeasurementInterval: 100, // 100ms
};

// ============================================================================
// Test Request Patterns
// ============================================================================

class BenchmarkRequestGenerator {
  private static patterns = [
    // Normal requests (60%)
    { weight: 0.6, generator: () => this.createNormalRequest() },
    // Potentially malicious requests (25%)
    { weight: 0.25, generator: () => this.createMaliciousRequest() },
    // High-load requests (10%)
    { weight: 0.1, generator: () => this.createHighLoadRequest() },
    // Edge case requests (5%)
    { weight: 0.05, generator: () => this.createEdgeCaseRequest() },
  ];

  static generateRequest(): NextRequest {
    const random = Math.random();
    let cumulativeWeight = 0;

    for (const pattern of this.patterns) {
      cumulativeWeight += pattern.weight;
      if (random <= cumulativeWeight) {
        return pattern.generator();
      }
    }

    return this.createNormalRequest();
  }

  private static createNormalRequest(): NextRequest {
    const paths = [
      '/api/dictionary/en/hello',
      '/api/user/profile',
      '/api/auth/login',
      '/api/dictionary/en/world',
      '/api/user/stats',
    ];

    const path = paths[Math.floor(Math.random() * paths.length)];
    const url = new URL(path, 'http://localhost:3000');

    return new NextRequest(url, {
      method: 'GET',
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-forwarded-for': this.generateRandomIP(),
        accept: 'application/json',
        'content-type': 'application/json',
      },
    });
  }

  private static createMaliciousRequest(): NextRequest {
    const maliciousPaths = [
      '/api/user/profile?id=1 OR 1=1',
      '/api/dictionary/en/<script>alert(1)</script>',
      '/api/auth/../../../etc/passwd',
      '/api/admin/config?cmd=ls',
      '/api/user/profile?search="; DROP TABLE users; --',
    ];

    const path = maliciousPaths[Math.floor(Math.random() * maliciousPaths.length)];
    const url = new URL(path, 'http://localhost:3000');

    return new NextRequest(url, {
      method: 'GET',
      headers: {
        'user-agent': 'sqlmap/1.0',
        'x-forwarded-for': this.generateRandomIP(),
        accept: 'application/json',
      },
    });
  }

  private static createHighLoadRequest(): NextRequest {
    const url = new URL('/api/dictionary/en/performance', 'http://localhost:3000');

    return new NextRequest(url, {
      method: 'POST',
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-forwarded-for': this.generateRandomIP(),
        accept: 'application/json',
        'content-type': 'application/json',
      },
      body: JSON.stringify({
        data: 'a'.repeat(10000), // Large payload
        nested: {
          level1: { level2: { level3: { data: 'deep nesting test' } } },
        },
      }),
    });
  }

  private static createEdgeCaseRequest(): NextRequest {
    const edgeCases = [
      '/api/user/profile?search=' + encodeURIComponent('测试中文'),
      '/api/dictionary/en/test?param=' + 'a'.repeat(5000),
      '/api/auth/login?redirect=' + encodeURIComponent('javascript:alert(1)'),
      '/api/user/stats?filter=',
      '/api/dictionary/en/null',
    ];

    const path = edgeCases[Math.floor(Math.random() * edgeCases.length)];
    const url = new URL(path, 'http://localhost:3000');

    return new NextRequest(url, {
      method: 'GET',
      headers: {
        'user-agent': '',
        'x-forwarded-for': this.generateRandomIP(),
        accept: '*/*',
      },
    });
  }

  private static generateRandomIP(): string {
    return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  }
}

// ============================================================================
// Performance Metrics
// ============================================================================

interface MiddlewareMetrics {
  name: string;
  responseTime: {
    avg: number;
    min: number;
    max: number;
    p50: number;
    p95: number;
    p99: number;
    stdDev: number;
  };
  throughput: number;
  memoryUsage: {
    initial: number;
    peak: number;
    final: number;
    average: number;
  };
  errorRate: number;
  concurrencyHandling: {
    maxConcurrency: number;
    avgConcurrency: number;
    droppedRequests: number;
  };
}

interface BenchmarkResults {
  lightweightMiddleware: MiddlewareMetrics;
  enhancedMiddleware: MiddlewareMetrics;
  improvement: {
    responseTime: number; // Percentage improvement
    throughput: number;
    memoryUsage: number;
    errorRate: number;
  };
  targetsMet: {
    responseTimeImprovement: boolean; // 70-80% improvement
    throughputImprovement: boolean;
    memoryImprovement: boolean;
    concurrencyImprovement: boolean;
  };
}

// ============================================================================
// Memory Monitor
// ============================================================================

class MemoryMonitor {
  private measurements: number[] = [];
  private interval: NodeJS.Timeout | null = null;
  private initialMemory: number = 0;

  start(): void {
    this.initialMemory = process.memoryUsage().heapUsed;
    this.measurements = [];

    this.interval = setInterval(() => {
      this.measurements.push(process.memoryUsage().heapUsed);
    }, 100);
  }

  stop(): void {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }

  getStats() {
    const final = process.memoryUsage().heapUsed;
    const peak = Math.max(...this.measurements);
    const average = this.measurements.reduce((a, b) => a + b, 0) / this.measurements.length;

    return {
      initial: this.initialMemory,
      peak,
      final,
      average,
    };
  }
}

// ============================================================================
// Benchmark Runner
// ============================================================================

class MiddlewareBenchmark {
  private config: BenchmarkConfig;
  private lightweightMiddleware: LightweightSecurityMiddleware;
  private enhancedMiddleware: EnhancedSecurityMiddleware;

  constructor(config: Partial<BenchmarkConfig> = {}) {
    this.config = { ...DEFAULT_BENCHMARK_CONFIG, ...config };
    this.initializeMiddlewares();
  }

  private initializeMiddlewares(): void {
    // Initialize lightweight middleware
    this.lightweightMiddleware = new LightweightSecurityMiddleware({
      enableCaching: true,
      enableThreatDetection: true,
      enableRateLimiting: true,
      enableFingerprinting: true,
      performanceMode: true,
      maxConcurrency: 150,
      cacheSize: 2000,
    });

    // Initialize enhanced middleware
    this.enhancedMiddleware = new EnhancedSecurityMiddleware(securityConfigs.standard);
  }

  async runComprehensiveBenchmark(): Promise<BenchmarkResults> {
    console.log('🚀 Starting Comprehensive Middleware Benchmark');
    console.log('='.repeat(60));
    console.log('');

    // Run benchmarks for both middlewares
    const lightweightMetrics = await this.benchmarkMiddleware(
      'Lightweight Security Middleware',
      async (request) => await this.lightweightMiddleware.checkSecurity(request)
    );

    const enhancedMetrics = await this.benchmarkMiddleware(
      'Enhanced Security Middleware',
      async (request) => await this.enhancedMiddleware.processRequest(request)
    );

    // Calculate improvements
    const improvement = this.calculateImprovements(lightweightMetrics, enhancedMetrics);
    const targetsMet = this.evaluateTargets(improvement);

    const results: BenchmarkResults = {
      lightweightMiddleware: lightweightMetrics,
      enhancedMiddleware: enhancedMetrics,
      improvement,
      targetsMet,
    };

    this.printBenchmarkResults(results);
    return results;
  }

  private async benchmarkMiddleware(
    name: string,
    processor: (request: NextRequest) => Promise<any>
  ): Promise<MiddlewareMetrics> {
    console.log(`📊 Benchmarking ${name}...`);

    const memoryMonitor = new MemoryMonitor();
    const responseTimes: number[] = [];
    const errors: number[] = [];
    let totalRequests = 0;
    let droppedRequests = 0;

    // Warmup
    console.log(`  🔥 Warming up (${this.config.warmupIterations} requests)...`);
    for (let i = 0; i < this.config.warmupIterations; i++) {
      const request = BenchmarkRequestGenerator.generateRequest();
      try {
        await processor(request);
      } catch (error) {
        // Ignore warmup errors
      }
    }

    // Main benchmark
    console.log(`  ⚡ Running main benchmark...`);
    memoryMonitor.start();

    const benchmarkStart = performance.now();

    // Test different concurrency levels
    for (const concurrency of this.config.concurrencyLevels) {
      console.log(`    🔄 Testing ${concurrency} concurrent requests...`);

      const promises: Promise<void>[] = [];

      for (let i = 0; i < concurrency; i++) {
        promises.push(this.processRequestBatch(processor, responseTimes, errors, 100));
      }

      await Promise.all(promises);
      totalRequests += concurrency * 100;
    }

    const benchmarkEnd = performance.now();
    const totalTime = benchmarkEnd - benchmarkStart;

    memoryMonitor.stop();
    const memoryStats = memoryMonitor.getStats();

    // Calculate metrics
    const responseTimeStats = this.calculateResponseTimeStats(responseTimes);
    const throughput = (totalRequests / totalTime) * 1000; // requests per second
    const errorRate = errors.length / totalRequests;

    console.log(`  ✅ ${name} benchmark completed`);
    console.log(`     - Total requests: ${totalRequests}`);
    console.log(`     - Average response time: ${responseTimeStats.avg.toFixed(2)}ms`);
    console.log(`     - Throughput: ${throughput.toFixed(2)} RPS`);
    console.log(`     - Error rate: ${(errorRate * 100).toFixed(2)}%`);
    console.log('');

    return {
      name,
      responseTime: responseTimeStats,
      throughput,
      memoryUsage: memoryStats,
      errorRate,
      concurrencyHandling: {
        maxConcurrency: Math.max(...this.config.concurrencyLevels),
        avgConcurrency:
          this.config.concurrencyLevels.reduce((a, b) => a + b, 0) /
          this.config.concurrencyLevels.length,
        droppedRequests,
      },
    };
  }

  private async processRequestBatch(
    processor: (request: NextRequest) => Promise<any>,
    responseTimes: number[],
    errors: number[],
    batchSize: number
  ): Promise<void> {
    const promises: Promise<void>[] = [];

    for (let i = 0; i < batchSize; i++) {
      promises.push(this.processRequest(processor, responseTimes, errors));
    }

    await Promise.all(promises);
  }

  private async processRequest(
    processor: (request: NextRequest) => Promise<any>,
    responseTimes: number[],
    errors: number[]
  ): Promise<void> {
    const request = BenchmarkRequestGenerator.generateRequest();
    const startTime = performance.now();

    try {
      await processor(request);
      const responseTime = performance.now() - startTime;
      responseTimes.push(responseTime);
    } catch (error) {
      const responseTime = performance.now() - startTime;
      responseTimes.push(responseTime);
      errors.push(1);
    }
  }

  private calculateResponseTimeStats(responseTimes: number[]) {
    const sorted = responseTimes.sort((a, b) => a - b);
    const length = sorted.length;

    const avg = sorted.reduce((a, b) => a + b, 0) / length;
    const min = sorted[0];
    const max = sorted[length - 1];
    const p50 = sorted[Math.floor(length * 0.5)];
    const p95 = sorted[Math.floor(length * 0.95)];
    const p99 = sorted[Math.floor(length * 0.99)];

    // Calculate standard deviation
    const variance = sorted.reduce((acc, val) => acc + Math.pow(val - avg, 2), 0) / length;
    const stdDev = Math.sqrt(variance);

    return { avg, min, max, p50, p95, p99, stdDev };
  }

  private calculateImprovements(lightweight: MiddlewareMetrics, enhanced: MiddlewareMetrics) {
    const responseTimeImprovement =
      ((enhanced.responseTime.avg - lightweight.responseTime.avg) / enhanced.responseTime.avg) *
      100;
    const throughputImprovement =
      ((lightweight.throughput - enhanced.throughput) / enhanced.throughput) * 100;
    const memoryImprovement =
      ((enhanced.memoryUsage.peak - lightweight.memoryUsage.peak) / enhanced.memoryUsage.peak) *
      100;
    const errorRateImprovement =
      ((enhanced.errorRate - lightweight.errorRate) / enhanced.errorRate) * 100;

    return {
      responseTime: responseTimeImprovement,
      throughput: throughputImprovement,
      memoryUsage: memoryImprovement,
      errorRate: errorRateImprovement,
    };
  }

  private evaluateTargets(improvement: BenchmarkResults['improvement']) {
    return {
      responseTimeImprovement: improvement.responseTime >= 70 && improvement.responseTime <= 80,
      throughputImprovement: improvement.throughput >= 70,
      memoryImprovement: improvement.memoryUsage >= 30,
      concurrencyImprovement: improvement.throughput >= 50,
    };
  }

  private printBenchmarkResults(results: BenchmarkResults): void {
    console.log('📊 BENCHMARK RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log('');

    // Performance comparison table
    console.log('📈 PERFORMANCE COMPARISON');
    console.log('-'.repeat(60));
    console.log('Metric                  | Lightweight | Enhanced   | Improvement');
    console.log('-'.repeat(60));
    console.log(
      `Avg Response Time (ms)  | ${results.lightweightMiddleware.responseTime.avg.toFixed(2).padStart(11)} | ${results.enhancedMiddleware.responseTime.avg.toFixed(2).padStart(10)} | ${results.improvement.responseTime.toFixed(2).padStart(10)}%`
    );
    console.log(
      `P95 Response Time (ms)  | ${results.lightweightMiddleware.responseTime.p95.toFixed(2).padStart(11)} | ${results.enhancedMiddleware.responseTime.p95.toFixed(2).padStart(10)} | -`
    );
    console.log(
      `P99 Response Time (ms)  | ${results.lightweightMiddleware.responseTime.p99.toFixed(2).padStart(11)} | ${results.enhancedMiddleware.responseTime.p99.toFixed(2).padStart(10)} | -`
    );
    console.log(
      `Throughput (RPS)        | ${results.lightweightMiddleware.throughput.toFixed(2).padStart(11)} | ${results.enhancedMiddleware.throughput.toFixed(2).padStart(10)} | ${results.improvement.throughput.toFixed(2).padStart(10)}%`
    );
    console.log(
      `Peak Memory (MB)        | ${(results.lightweightMiddleware.memoryUsage.peak / 1024 / 1024).toFixed(2).padStart(11)} | ${(results.enhancedMiddleware.memoryUsage.peak / 1024 / 1024).toFixed(2).padStart(10)} | ${results.improvement.memoryUsage.toFixed(2).padStart(10)}%`
    );
    console.log(
      `Error Rate (%)          | ${(results.lightweightMiddleware.errorRate * 100).toFixed(2).padStart(11)} | ${(results.enhancedMiddleware.errorRate * 100).toFixed(2).padStart(10)} | ${results.improvement.errorRate.toFixed(2).padStart(10)}%`
    );
    console.log('');

    // Target evaluation
    console.log('🎯 TARGET EVALUATION');
    console.log('-'.repeat(40));
    console.log(
      `Response Time (70-80%): ${results.targetsMet.responseTimeImprovement ? '✅ MET' : '❌ MISSED'} (${results.improvement.responseTime.toFixed(2)}%)`
    );
    console.log(
      `Throughput (>70%):      ${results.targetsMet.throughputImprovement ? '✅ MET' : '❌ MISSED'} (${results.improvement.throughput.toFixed(2)}%)`
    );
    console.log(
      `Memory Usage (>30%):    ${results.targetsMet.memoryImprovement ? '✅ MET' : '❌ MISSED'} (${results.improvement.memoryUsage.toFixed(2)}%)`
    );
    console.log(
      `Concurrency (>50%):     ${results.targetsMet.concurrencyImprovement ? '✅ MET' : '❌ MISSED'} (${results.improvement.throughput.toFixed(2)}%)`
    );
    console.log('');

    // Overall assessment
    const allTargetsMet = Object.values(results.targetsMet).every((met) => met);
    console.log('🏆 OVERALL ASSESSMENT');
    console.log('-'.repeat(40));
    console.log(
      `Performance Targets: ${allTargetsMet ? '✅ ALL TARGETS MET' : '❌ SOME TARGETS MISSED'}`
    );

    if (results.improvement.responseTime >= 70) {
      console.log(
        `✅ Significant performance improvement achieved (${results.improvement.responseTime.toFixed(2)}%)`
      );
    } else {
      console.log(
        `⚠️  Performance improvement below target (${results.improvement.responseTime.toFixed(2)}%)`
      );
    }

    if (results.improvement.throughput >= 100) {
      console.log(
        `✅ Throughput doubled or better (${results.improvement.throughput.toFixed(2)}%)`
      );
    } else if (results.improvement.throughput >= 50) {
      console.log(
        `✅ Significant throughput improvement (${results.improvement.throughput.toFixed(2)}%)`
      );
    } else {
      console.log(
        `⚠️  Throughput improvement below expectations (${results.improvement.throughput.toFixed(2)}%)`
      );
    }

    console.log('');
    console.log('📋 DETAILED METRICS');
    console.log('-'.repeat(40));

    console.log('Lightweight Middleware:');
    console.log(
      `  Response Time: ${results.lightweightMiddleware.responseTime.avg.toFixed(2)}ms ± ${results.lightweightMiddleware.responseTime.stdDev.toFixed(2)}ms`
    );
    console.log(
      `  Max Concurrency: ${results.lightweightMiddleware.concurrencyHandling.maxConcurrency}`
    );
    console.log(
      `  Memory Range: ${(results.lightweightMiddleware.memoryUsage.initial / 1024 / 1024).toFixed(2)}MB → ${(results.lightweightMiddleware.memoryUsage.peak / 1024 / 1024).toFixed(2)}MB`
    );

    console.log('');
    console.log('Enhanced Middleware:');
    console.log(
      `  Response Time: ${results.enhancedMiddleware.responseTime.avg.toFixed(2)}ms ± ${results.enhancedMiddleware.responseTime.stdDev.toFixed(2)}ms`
    );
    console.log(
      `  Max Concurrency: ${results.enhancedMiddleware.concurrencyHandling.maxConcurrency}`
    );
    console.log(
      `  Memory Range: ${(results.enhancedMiddleware.memoryUsage.initial / 1024 / 1024).toFixed(2)}MB → ${(results.enhancedMiddleware.memoryUsage.peak / 1024 / 1024).toFixed(2)}MB`
    );
  }
}

// ============================================================================
// Main Execution
// ============================================================================

export async function runBenchmarkComparison(): Promise<BenchmarkResults> {
  const benchmark = new MiddlewareBenchmark();
  return await benchmark.runComprehensiveBenchmark();
}

// Run benchmark if this file is executed directly
if (require.main === module) {
  runBenchmarkComparison()
    .then((results) => {
      console.log('\n🎉 Benchmark comparison completed successfully!');

      // Output summary for easy parsing
      console.log('\n📊 SUMMARY FOR AUTOMATION:');
      console.log(`RESPONSE_TIME_IMPROVEMENT: ${results.improvement.responseTime.toFixed(2)}%`);
      console.log(`THROUGHPUT_IMPROVEMENT: ${results.improvement.throughput.toFixed(2)}%`);
      console.log(`MEMORY_IMPROVEMENT: ${results.improvement.memoryUsage.toFixed(2)}%`);
      console.log(`TARGETS_MET: ${Object.values(results.targetsMet).every((met) => met)}`);

      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Benchmark comparison failed:', error);
      process.exit(1);
    });
}
