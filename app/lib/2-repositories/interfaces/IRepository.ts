/**
 * 仓储接口定义
 * 定义数据访问的标准契约
 */

import { Vocabulary, VocabularyData, WordFormatData } from '../../3-domain/entities/Vocabulary';

/**
 * 基础仓储接口
 */
export interface IBaseRepository<T, ID> {
  findById(id: ID): Promise<T | null>;
  save(entity: T): Promise<T>;
  delete(id: ID): Promise<boolean>;
  exists(id: ID): Promise<boolean>;
}

/**
 * 词汇仓储接口
 * 定义词汇相关的数据访问方法
 */
export interface IVocabularyRepository extends IBaseRepository<Vocabulary, number> {
  /**
   * 根据单词查找词汇
   */
  findByWord(word: string): Promise<Vocabulary | null>;

  /**
   * 根据单词查找词汇（优化版本）
   */
  findByWordOptimized(word: string): Promise<Vocabulary | null>;

  /**
   * 根据单词列表批量查找
   */
  findByWords(words: string[]): Promise<Vocabulary[]>;

  /**
   * 根据词频排名查找
   */
  findByFreqRank(minRank: number, maxRank: number): Promise<Vocabulary[]>;

  /**
   * 搜索相似词汇
   */
  findSimilar(word: string, limit?: number): Promise<Vocabulary[]>;

  /**
   * 获取高频词汇
   */
  findHighFrequency(limit?: number): Promise<Vocabulary[]>;

  /**
   * 根据词性查找词汇
   */
  findByPOS(pos: string, limit?: number): Promise<Vocabulary[]>;

  /**
   * 全文搜索
   */
  search(query: string, limit?: number): Promise<Vocabulary[]>;

  /**
   * 获取词汇统计信息
   */
  getStats(): Promise<{
    totalCount: number;
    avgComplexity: number;
    topPOS: Array<{ pos: string; count: number }>;
  }>;
}

/**
 * 词形仓储接口
 * 定义词形相关的数据访问方法
 */
export interface IWordFormatRepository extends IBaseRepository<WordFormatData, number> {
  /**
   * 根据词形查找
   */
  findByForm(form: string): Promise<WordFormatData[]>;

  /**
   * 根据词汇ID查找所有词形
   */
  findByVocabularyId(vocabularyId: number): Promise<WordFormatData[]>;

  /**
   * 根据基础形式ID查找派生形式
   */
  findByBaseFormId(baseFormId: number): Promise<WordFormatData[]>;

  /**
   * 查找主要形式（直接关联vocabulary的形式）
   */
  findMainFormsByForm(form: string): Promise<WordFormatData[]>;

  /**
   * 查找派生形式（通过baseFormId关联的形式）
   */
  findDerivedFormsByForm(form: string): Promise<WordFormatData[]>;

  /**
   * 获取所有相关形式（主要形式 + 派生形式）
   */
  findAllRelatedForms(vocabularyId: number): Promise<WordFormatData[]>;

  /**
   * 根据形式名称查找
   */
  findByFormName(name: string, limit?: number): Promise<WordFormatData[]>;

  /**
   * 批量创建词形
   */
  createMany(wordFormats: Omit<WordFormatData, 'id'>[]): Promise<WordFormatData[]>;
}

/**
 * 缓存仓储接口
 * 定义缓存相关的数据访问方法
 */
export interface ICacheRepository {
  /**
   * 获取缓存值
   */
  get<T>(key: string): Promise<T | null>;

  /**
   * 设置缓存值
   */
  set<T>(key: string, value: T, ttlSeconds?: number): Promise<boolean>;

  /**
   * 删除缓存
   */
  delete(key: string): Promise<boolean>;

  /**
   * 检查缓存是否存在
   */
  exists(key: string): Promise<boolean>;

  /**
   * 批量获取
   */
  mget<T>(keys: string[]): Promise<(T | null)[]>;

  /**
   * 批量设置
   */
  mset<T>(keyValues: Array<{ key: string; value: T; ttl?: number }>): Promise<boolean>;

  /**
   * 获取缓存统计
   */
  getStats(): Promise<{
    hitRate: number;
    totalKeys: number;
    memoryUsage: number;
  }>;

  /**
   * 清空所有缓存
   */
  clear(): Promise<boolean>;
}

/**
 * 搜索建议仓储接口
 */
export interface ISuggestionRepository {
  /**
   * 获取搜索建议
   */
  getSuggestions(query: string, limit?: number): Promise<string[]>;

  /**
   * 记录搜索查询
   */
  recordQuery(query: string, found: boolean): Promise<void>;

  /**
   * 获取热门搜索
   */
  getPopularQueries(limit?: number): Promise<Array<{ query: string; count: number }>>;

  /**
   * 获取相关词汇建议
   */
  getRelatedWords(word: string, limit?: number): Promise<string[]>;
}

/**
 * 工作单元接口
 * 用于管理事务和批量操作
 */
export interface IUnitOfWork {
  /**
   * 开始事务
   */
  begin(): Promise<void>;

  /**
   * 提交事务
   */
  commit(): Promise<void>;

  /**
   * 回滚事务
   */
  rollback(): Promise<void>;

  /**
   * 在事务中执行操作
   */
  execute<T>(operation: () => Promise<T>): Promise<T>;

  /**
   * 获取仓储实例
   */
  vocabularyRepository: IVocabularyRepository;
  wordFormatRepository: IWordFormatRepository;
  cacheRepository: ICacheRepository;
  suggestionRepository: ISuggestionRepository;
}

/**
 * 查询选项接口
 */
export interface QueryOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  includeRelations?: boolean;
  useCache?: boolean;
  cacheTTL?: number;
}

/**
 * 分页结果接口
 */
export interface PaginatedResult<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 仓储工厂接口
 */
export interface IRepositoryFactory {
  createVocabularyRepository(): IVocabularyRepository;
  createWordFormatRepository(): IWordFormatRepository;
  createCacheRepository(): ICacheRepository;
  createSuggestionRepository(): ISuggestionRepository;
  createUnitOfWork(): IUnitOfWork;
}
