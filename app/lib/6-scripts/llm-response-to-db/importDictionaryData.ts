// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-01-27 17:00:00 +08:00]
// Reason: [Completely redesigned WordFormat creation logic to support multiple WordFormat records per vocabulary (e.g., "dates" as both "复数" and "第三人称单数"). Fixed the core issue where words in wordList should have their own Vocabulary records with multiple associated WordFormat records for each form type they represent.]
// Principle_Applied: [Correctness, Data Integrity, Semantic Accuracy, DRY, Consistency, Idempotency, Flexibility]
// Architectural_Note (AR): [Implemented proper one-to-many relationship between Vocabulary and WordFormat. Each word in wordList gets its own Vocabulary record, and all its wordFormats that match the word itself become main WordFormat records (with vocabularyId). This allows proper representation of words like "dates" that serve multiple grammatical functions.]
// Documentation_Note (DW): [Redesigned import logic to correctly handle the relationship between vocabulary entries and their multiple word forms. Each vocabulary can now have multiple WordFormat records representing different grammatical functions of the same word form, while maintaining proper hierarchical relationships to base forms.]
// }}
// {{START MODIFICATIONS}}
import { PrismaClient } from '@prisma/client';
import type { DictionaryResponseWithWordCountType } from '../../utils/dict-types.js';

interface ImportOptions {
  forceOverwrite?: boolean;
  verbose?: boolean; // 控制详细日志输出
}

interface ImportResult {
  successCount: number;
  failureCount: number;
  suggestsSuccessCount: number; // 新增：成功导入的suggests数量
  errors: Array<{
    word: string;
    error: any;
  }>;
}

/**
 * 导入字典数据到数据库
 * @param dictionaryData 字典数据
 * @param prismaClient Prisma客户端实例
 * @param options 导入选项，如forceOverwrite强制覆盖已存在数据
 * @returns 导入结果统计
 */
export async function importFromDictionaryData(
  dictionaryData: DictionaryResponseWithWordCountType,
  prismaClient: PrismaClient,
  options: ImportOptions = {}
): Promise<ImportResult> {
  const { forceOverwrite = false, verbose = false } = options;
  const result: ImportResult = {
    successCount: 0,
    failureCount: 0,
    suggestsSuccessCount: 0, // 新增：成功导入的suggests数量
    errors: [],
  };

  if (!dictionaryData.words || !Array.isArray(dictionaryData.words)) {
    console.error('Invalid dictionary data: words array is missing or not an array');
    return result;
  }

  console.log(
    `开始导入 ${dictionaryData.words.length} 个单词到数据库${forceOverwrite ? ' (强制覆盖模式)' : ''}`
  );

  // 第一步：创建所有Vocabulary记录
  const wordFormatsMap = new Map(); // 用于存储单词形式关系

  // 处理主要单词列表
  await processWordsList(
    dictionaryData.words,
    prismaClient,
    wordFormatsMap,
    result,
    forceOverwrite,
    false,
    verbose
  );

  // 处理建议的相似词列表(如果存在)
  if (
    dictionaryData.suggests &&
    Array.isArray(dictionaryData.suggests) &&
    dictionaryData.suggests.length > 0
  ) {
    console.log(`开始导入 ${dictionaryData.suggests.length} 个相似词建议...`);
    await processWordsList(
      dictionaryData.suggests,
      prismaClient,
      wordFormatsMap,
      result,
      forceOverwrite,
      true,
      verbose
    );
    console.log(`相似词建议导入完成，成功导入 ${result.suggestsSuccessCount} 个相似词`);
  }

  // 第二步：为每个词条创建WordFormat记录
  const wordFormatRecords = new Map<string, number>(); // 存储主WordFormat记录ID（用于建立关系）

  console.log('🔍 创建WordFormat记录...');

  // 创建WordFormat记录
  for (const [word, data] of wordFormatsMap.entries()) {
    const { vocabularyId, wordFormats } = data;

    // 为当前词条创建所有相关的WordFormat记录
    // 注意：一个词条可能有多个WordFormat记录（如dates既是复数又是第三人称单数）

    let mainWordFormatId: number | null = null;

    // 处理当前词的所有wordFormats
    for (const format of wordFormats) {
      const { form, name } = format;

      // 如果这个form就是当前词条本身，创建主WordFormat记录
      if (form === word) {
        const wordFormat = await prismaClient.wordFormat.create({
          data: {
            name,
            form: word,
            vocabularyId, // 直接关联到vocabulary
          },
        });

        if (verbose) {
          console.log(`创建主WordFormat: "${word}" (${name})`);
        }

        // 记录第一个主WordFormat的ID（用于后续关系建立）
        if (!mainWordFormatId) {
          mainWordFormatId = wordFormat.id;
          wordFormatRecords.set(word, wordFormat.id);
        }
      }
    }

    // 处理其他形式（派生形式）
    for (const format of wordFormats) {
      const { form, name } = format;

      // 跳过与主词条相同的form，避免重复创建
      if (form === word) {
        if (verbose) {
          console.log(`跳过与主词条相同的形式: "${form}" (${name})`);
        }
        continue;
      }

      // 如果这个form也是主词条，跳过创建派生记录（避免重复）
      if (wordFormatsMap.has(form)) {
        if (verbose) {
          console.log(`跳过创建派生记录: "${form}" (${name})，因为它也是主词条`);
        }
        continue;
      }

      // 确保有主WordFormat记录ID
      if (!mainWordFormatId) {
        console.warn(
          `无法创建派生记录 "${form}" (${name})，因为主词条 "${word}" 没有主WordFormat记录`
        );
        continue;
      }

      try {
        // {{CHENGQI:
        // Action: Modified
        // Timestamp: [2025-01-27 19:00:00 +08:00]
        // Reason: [修复派生WordFormat创建逻辑，原型词不应该设置baseFormId，避免循环引用]
        // Principle_Applied: [Data Integrity, Correctness, Avoiding Circular References]
        // Optimization: [区分原型词和其他派生词的处理逻辑]
        // Architectural_Note (AR): [确保原型词WordFormat记录的独立性，避免循环依赖]
        // Documentation_Note (DW): [修复了原型词被错误设置baseFormId导致循环引用的问题]
        // }}
        // {{START MODIFICATIONS}}
        // 检查是否已经存在相同的派生形式记录
        const existingDerivedFormat = await prismaClient.wordFormat.findFirst({
          where: {
            form,
            name,
            vocabularyId: null,
          },
        });

        if (!existingDerivedFormat) {
          // 创建派生形式记录
          // 注意：如果是原型词，不设置baseFormId；如果是其他形式，需要找到真正的原型词
          const createData: any = {
            name,
            form,
          };

          // {{CHENGQI:
          // Action: Modified
          // Timestamp: [2025-01-27 19:45:00 +08:00]
          // Reason: [修复派生WordFormat的baseFormId设置，确保指向真正的原型词而不是当前主词]
          // Principle_Applied: [Data Integrity, Correct Relationships, SOLID]
          // Optimization: [派生词应该指向真正的原型词，而不是当前主词]
          // Architectural_Note (AR): [确保词形关系的正确性，避免错误的层级关系]
          // Documentation_Note (DW): [修复了done、doing等派生词错误指向did而不是do的问题]
          // }}
          // {{START MODIFICATIONS}}
          // 只有非原型词才设置baseFormId，且需要指向真正的原型词
          if (name !== '原型') {
            // 从当前词的wordFormats中找到原型词
            const baseFormFormat = data.wordFormats.find(
              (format: { name: string; form: string }) => format.name === '原型'
            );

            if (baseFormFormat) {
              const baseFormWord = baseFormFormat.form;

              // 查找或创建原型词的WordFormat记录
              let baseFormatId = wordFormatRecords.get(baseFormWord);
              if (!baseFormatId) {
                // 如果当前批次中没有原型词，查找数据库中的原型词
                const existingBaseFormat = await prismaClient.wordFormat.findFirst({
                  where: {
                    form: baseFormWord,
                    name: '原型',
                  },
                });

                if (existingBaseFormat) {
                  baseFormatId = existingBaseFormat.id;
                } else {
                  // 创建原型词WordFormat记录
                  const newBaseFormat = await prismaClient.wordFormat.create({
                    data: {
                      name: '原型',
                      form: baseFormWord,
                      vocabularyId: null,
                      baseFormId: null,
                    },
                  });
                  baseFormatId = newBaseFormat.id;
                  console.log(`创建原型词WordFormat: "${baseFormWord}" (ID: ${baseFormatId})`);
                }
              }

              createData.baseFormId = baseFormatId;
            }
          }
          // {{END MODIFICATIONS}}

          await prismaClient.wordFormat.create({
            data: createData,
          });
          if (verbose) {
            console.log(`创建派生WordFormat: "${form}" (${name}) <- "${word}"`);
          }
        } else {
          if (verbose) {
            console.log(`派生WordFormat已存在，跳过创建: "${form}" (${name}) <- "${word}"`);
          }
        }
        // {{END MODIFICATIONS}}
      } catch (error) {
        console.warn(
          `无法创建单词形式 "${form}" (${name}) for base "${word}": ${(error as Error).message}`
        );
      }
    }
  }

  // 第三步：建立变形词与原型词的关系
  console.log('🔗 建立词形关系...');
  for (const [word, data] of wordFormatsMap.entries()) {
    const { wordFormats } = data;

    // 找到原型形式
    const baseFormFormat = wordFormats.find(
      (format: { name: string; form: string }) => format.name === '原型'
    );
    if (!baseFormFormat) continue;

    const baseFormWord = baseFormFormat.form;

    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-01-27 18:30:00 +08:00]
    // Reason: [修复词形关系建立逻辑，支持跨批次原型词查找和创建，确保非原型词能正确建立baseFormId关系]
    // Principle_Applied: [SOLID (Single Responsibility), DRY, Error Handling, Data Integrity]
    // Optimization: [添加数据库查询逻辑，支持原型词不在当前批次的情况]
    // Architectural_Note (AR): [改进了词形关系建立的健壮性，支持更灵活的数据导入场景]
    // Documentation_Note (DW): [修复了"did"等非原型词无法正确建立baseFormId的问题]
    // }}
    // {{START MODIFICATIONS}}
    // 首先尝试从当前批次中查找原型词
    let baseFormatId: number | null = wordFormatRecords.get(baseFormWord) || null;

    // 如果当前批次中没有找到，查找或创建原型词的WordFormat记录
    if (!baseFormatId) {
      console.log(`当前批次中未找到原型词 "${baseFormWord}"，尝试从数据库查找或创建...`);
      baseFormatId = await findOrCreateBaseWordFormat(prismaClient, baseFormWord);
    }

    if (!baseFormatId) {
      console.warn(`无法找到或创建原型词 "${baseFormWord}" 的WordFormat记录`);
      continue;
    }
    // {{END MODIFICATIONS}}

    // 更新当前词条的所有主WordFormat记录，使其指向原型词
    const currentWordFormats = await prismaClient.wordFormat.findMany({
      where: {
        form: word,
        vocabularyId: { not: null }, // 只更新主WordFormat记录
      },
    });

    for (const wordFormat of currentWordFormats) {
      // 如果当前记录本身就是原型，不需要设置baseFormId
      if (wordFormat.form === baseFormWord && wordFormat.name === '原型') {
        continue;
      }

      try {
        await prismaClient.wordFormat.update({
          where: { id: wordFormat.id },
          data: {
            baseFormId: baseFormatId,
          },
        });
        if (verbose) {
          console.log(`建立关系: "${word}" (${wordFormat.name}) -> "${baseFormWord}" (原型)`);
        }
      } catch (error) {
        console.warn(
          `无法建立词形关系 "${word}" -> "${baseFormWord}": ${(error as Error).message}`
        );
      }
    }
  }

  return result;
}

/**
 * 查找或创建原型词的WordFormat记录
 * @param prismaClient Prisma客户端实例
 * @param baseFormWord 原型词的form
 * @returns 原型词WordFormat记录的ID，如果创建失败则返回null
 */
async function findOrCreateBaseWordFormat(
  prismaClient: PrismaClient,
  baseFormWord: string
): Promise<number | null> {
  try {
    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-01-27 18:32:00 +08:00]
    // Reason: [创建辅助函数处理原型词WordFormat的查找和创建逻辑，支持跨批次词形关系建立]
    // Principle_Applied: [SOLID (Single Responsibility), DRY, Error Handling]
    // Optimization: [将复杂逻辑提取到专门函数，提高代码可维护性和复用性]
    // Architectural_Note (AR): [遵循单一职责原则，将词形关系建立逻辑模块化]
    // Documentation_Note (DW): [新增函数用于处理原型词WordFormat记录的查找和创建]
    // }}
    // {{START MODIFICATIONS}}
    // 首先查找数据库中是否已存在该原型词的WordFormat记录
    const existingBaseFormat = await prismaClient.wordFormat.findFirst({
      where: {
        form: baseFormWord,
        name: '原型',
      },
    });

    if (existingBaseFormat) {
      return existingBaseFormat.id;
    }

    // 如果不存在，创建新的原型词WordFormat记录（不创建Vocabulary）
    const newBaseFormat = await prismaClient.wordFormat.create({
      data: {
        name: '原型',
        form: baseFormWord,
        // 注意：不设置vocabularyId和baseFormId，原型词是最顶层的，不应该有baseFormId
        vocabularyId: null,
        baseFormId: null,
      },
    });

    return newBaseFormat.id;
    // {{END MODIFICATIONS}}
  } catch (error) {
    console.error(`查找或创建原型词 "${baseFormWord}" 的WordFormat记录失败:`, error);
    return null;
  }
}

/**
 * 处理单词列表（通用函数，同时支持words和suggests）
 */
async function processWordsList(
  wordsList: any[],
  prismaClient: PrismaClient,
  wordFormatsMap: Map<string, any>,
  result: ImportResult,
  forceOverwrite: boolean,
  isSuggests: boolean = false,
  verbose: boolean = false
) {
  for (const wordData of wordsList) {
    try {
      const { word, explain, wordFormats, phonetic } = wordData;

      if (!word) {
        console.warn('跳过无效单词条目：单词为空');
        result.failureCount++;
        result.errors.push({ word: '[空单词]', error: new Error('单词为空') });
        continue;
      }

      // 查找是否已存在此单词
      let vocabularyRecord = await prismaClient.vocabulary.findFirst({
        where: { word },
        include: { explains: true },
      });

      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-05-27 00:06:00 +08:00]
      // Reason: 改为更新模式，保持现有数据的连续性，只更新必要字段
      // Principle_Applied: KISS (简化逻辑), Data Integrity (保持数据完整性), Performance (避免不必要的删除重建)
      // Optimization: 使用更新而不是删除重建，保持数据库关系的稳定性
      // Architectural_Note (AR): 改进数据导入策略，支持增量更新而不破坏现有关系
      // Documentation_Note (DW): 修改为更新模式，避免删除现有数据和关联关系
      // }}
      // 更新现有记录(如果存在)
      if (vocabularyRecord) {
        // 更新 phonetics 字段
        const phoneticArray: string[] = [];
        if (phonetic) {
          if (phonetic.us) phoneticArray.push(phonetic.us);
          if (phonetic.uk) phoneticArray.push(phonetic.uk);
        }

        vocabularyRecord = await prismaClient.vocabulary.update({
          where: { id: vocabularyRecord.id },
          data: { phonetics: phoneticArray },
          include: { explains: true },
        });

        // 处理现有记录的词性解释更新
        if (explain && explain.length > 0) {
          for (const explainItem of explain) {
            const explainRecord = await prismaClient.explain.upsert({
              where: {
                vocabularyId_pos: {
                  vocabularyId: vocabularyRecord.id,
                  pos: explainItem.pos,
                },
              },
              update: {
                // 更新时保持 pos 和 vocabularyId 不变
              },
              create: {
                pos: explainItem.pos,
                vocabularyId: vocabularyRecord.id,
              },
            });

            // 如果有新的 definitions，先清理旧的，再添加新的
            if (explainItem.definitions && explainItem.definitions.length > 0) {
              // 删除该 explain 下的所有旧 definitions
              await prismaClient.definition.deleteMany({
                where: { explainId: explainRecord.id },
              });

              // 添加新的 definitions
              await prismaClient.definition.createMany({
                data: explainItem.definitions.map((def: any) => ({
                  definition: def.definition,
                  chinese: def.chinese,
                  chineseS: def.chinese_short,
                  explainId: explainRecord.id,
                })),
              });

              if (verbose) {
                console.log(`更新现有单词 "${word}" 的 ${explainItem.pos} 词性释义`);
              }
            }
          }
        }

        // 处理现有记录的单词形式关系数据
        if (wordFormats && wordFormats.length > 0) {
          wordFormatsMap.set(word, {
            vocabularyId: vocabularyRecord.id,
            wordFormats,
          });
        }

        if (verbose) {
          console.log(`更新现有单词: "${word}"`);
        }
      }

      // 如果单词不存在或已被删除，创建新记录
      if (!vocabularyRecord) {
        const phoneticArray: string[] = [];
        if (phonetic) {
          if (phonetic.us) phoneticArray.push(phonetic.us);
          if (phonetic.uk) phoneticArray.push(phonetic.uk);
        }

        vocabularyRecord = await prismaClient.vocabulary.create({
          data: {
            word,
            phonetics: phoneticArray,
          },
          include: { explains: true },
        });

        // 处理词性解释 - 支持更新模式
        if (explain && explain.length > 0) {
          for (const explainItem of explain) {
            // {{CHENGQI:
            // Action: Modified
            // Timestamp: [2025-05-27 00:07:00 +08:00]
            // Reason: 改进 Explain 处理逻辑，支持更新模式下的 Definition 覆盖
            // Principle_Applied: KISS (简化逻辑), Data Integrity (保持数据完整性)
            // Optimization: 清理旧的 definitions，添加新的，实现真正的更新
            // Architectural_Note (AR): 支持 Explain 的增量更新，保持数据最新
            // Documentation_Note (DW): 改进 Explain 和 Definition 的更新逻辑
            // }}
            const explainRecord = await prismaClient.explain.upsert({
              where: {
                vocabularyId_pos: {
                  vocabularyId: vocabularyRecord.id,
                  pos: explainItem.pos,
                },
              },
              update: {
                // 更新时保持 pos 和 vocabularyId 不变
              },
              create: {
                pos: explainItem.pos,
                vocabularyId: vocabularyRecord.id,
              },
            });

            // 如果有新的 definitions，先清理旧的，再添加新的
            if (explainItem.definitions && explainItem.definitions.length > 0) {
              // 删除该 explain 下的所有旧 definitions
              await prismaClient.definition.deleteMany({
                where: { explainId: explainRecord.id },
              });

              // 添加新的 definitions
              await prismaClient.definition.createMany({
                data: explainItem.definitions.map((def: any) => ({
                  definition: def.definition,
                  chinese: def.chinese,
                  chineseS: def.chinese_short,
                  explainId: explainRecord.id,
                })),
              });

              if (verbose) {
                console.log(
                  `更新 ${explainItem.pos} 词性的 ${explainItem.definitions.length} 个释义`
                );
              }
            }
          }
        }

        // 保存单词形式关系数据
        if (wordFormats && wordFormats.length > 0) {
          wordFormatsMap.set(word, {
            vocabularyId: vocabularyRecord.id,
            wordFormats,
          });
        }

        if (isSuggests) {
          if (verbose) {
            console.log(`成功导入相似词建议: "${word}"`);
          }
          result.suggestsSuccessCount++;
        } else {
          if (verbose) {
            console.log(`成功导入单词: "${word}"`);
          }
          result.successCount++;
        }
      } else {
        if (verbose) {
          console.log(`单词 "${word}" 已存在，跳过`);
        }
        if (isSuggests) {
          result.suggestsSuccessCount++;
        } else {
          result.successCount++;
        }
      }
    } catch (error) {
      console.error(`导入${isSuggests ? '相似词' : '单词'} "${wordData.word}" 失败:`, error);
      result.failureCount++;
      result.errors.push({
        word: wordData.word || '[未知单词]',
        error,
      });
    }
  }
}

// {{END MODIFICATIONS}}
