// src/utils/poemService.ts
import { OpenAI } from 'openai';
import 'dotenv/config';
import { z } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';

// 1. 定义返回结构的 TypeScript 接口
interface Poem {
  title: string;
  author: string;
  firstLine: string;
}
interface ReturnPoemsArgs {
  poems: Poem[];
}
// 使用 DOI9 作为默认 API 服务器
// const SERVER = 'DOI9' 654600_BASE_URL
const SERVER = '654600';

// 2. 实例化 OpenAI 客户端
const openai = new OpenAI({
  apiKey: process.env[`${SERVER}_API_KEY`],
  baseURL: process.env[`${SERVER}_BASE_URL`],
});

async function listAllModels() {
  // 调用 models.list() 获取模型列表
  const response = await openai.models.list();
  return response.data; // 数组，每项包含 id、owned_by 等字段
}

(async () => {
  try {
    const models = await listAllModels();
    console.log('共找到模型：', models.length);
    models.forEach((m) => console.log(`- ${m.id} (${m.owned_by})`));
  } catch (err) {
    console.error('获取模型失败：', err);
  }
})();

export async function chatWithOpenAI(
  prompt: string,
  model: string = 'gemini-2.5-flash-preview-05-20'
): Promise<string> {
  const resp = await openai.chat.completions.create({
    model: model,
    messages: [{ role: 'user', content: prompt }],
  });
  // 兼容第三方返回结构，直接取第一个 choice 的 message 内容
  return resp.choices?.[0]?.message?.content || '';
}

// (async () => {
//     try {
//         const reply = await chatWithOpenAI("你好，介绍一下你自己");
//         console.log("Gemini 回复:", reply);
//     } catch (err) {
//         console.error("error", err);
//     }
// })();

// FIXME 第三方的结构化会报错

export async function structuredOutput(
  prompt: string,
  model: string = 'gemini-2.5-flash-preview-05-20'
): Promise<any> {
  const Step = z.object({
    explanation: z.string(),
    output: z.string(),
  });

  const MathResponse = z.object({
    steps: z.array(Step),
    final_answer: z.string(),
  });

  const completion = await openai.beta.chat.completions.parse({
    model: model,
    messages: [
      {
        role: 'system',
        content: 'You are a helpful math tutor. Only use the schema for math responses.',
      },
      { role: 'user', content: prompt },
    ],
    response_format: zodResponseFormat(MathResponse, 'mathResponse'),
  });

  const message = completion.choices[0]?.message;
  if (message?.parsed) {
    console.log(message.parsed.steps);
    console.log(message.parsed.final_answer);
  } else {
    console.log(message.refusal);
  }
}

// (async () => {
//     await structuredOutput("solve 8x + 3 = 21", "gemini-2.5-flash-preview-05-20");
// })();
