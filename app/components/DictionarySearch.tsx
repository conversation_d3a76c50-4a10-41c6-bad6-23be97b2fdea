'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  useWordLookup,
  useWordCache,
  useSearchSuggestions,
  useQueryStats,
} from '@/hooks/useWordLookup';
import WordLookupResult from './WordLookupResult';

export default function DictionarySearch() {
  const [query, setQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const { state, lookupWord, clearResults, goBack, canGoBack } = useWordLookup();
  const { getCachedWord, setCachedWord, cacheSize } = useWordCache();
  const { suggestions, getSuggestions, clearSuggestions } = useSearchSuggestions();
  const { stats, updateStats } = useQueryStats();

  // 处理搜索
  const handleSearch = async (searchTerm: string = query) => {
    if (!searchTerm.trim()) return;

    // 检查缓存
    const cached = getCachedWord(searchTerm);
    if (cached) {
      console.log('Using cached result for:', searchTerm);
      // 这里可以直接使用缓存结果
      return;
    }

    await lookupWord(searchTerm, { includeRelationships: true });

    // 更新统计
    if (state.data) {
      updateStats(state.data);
      setCachedWord(searchTerm, state.data);
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    if (value.length >= 2) {
      getSuggestions(value);
      setShowSuggestions(true);
    } else {
      clearSuggestions();
      setShowSuggestions(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
      setShowSuggestions(false);
    } else if (e.key === 'Escape') {
      setShowSuggestions(false);
    }
  };

  // 处理词汇点击
  const handleWordClick = (word: string) => {
    setQuery(word);
    handleSearch(word);
    setShowSuggestions(false);
  };

  // 处理建议选择
  const handleSuggestionClick = (suggestion: string) => {
    setQuery(suggestion);
    handleSearch(suggestion);
    setShowSuggestions(false);
  };

  // 点击外部关闭建议
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* 搜索头部 */}
        <div className="mb-8">
          <div className="text-center mb-6">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">智能词典</h1>
            <p className="text-gray-600">支持词形变化查找的英语词典</p>
          </div>

          {/* 搜索框 */}
          <div className="relative max-w-2xl mx-auto">
            <div className="relative">
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder="输入英语单词..."
                className="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
                disabled={state.loading}
              />
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
                {state.loading && (
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
                )}
                <button
                  onClick={() => handleSearch()}
                  disabled={state.loading || !query.trim()}
                  className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  搜索
                </button>
              </div>
            </div>

            {/* 搜索建议 */}
            {showSuggestions && suggestions.length > 0 && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10 max-h-60 overflow-y-auto">
                {suggestions.map((suggestion, index) => (
                  <button
                    key={index}
                    onClick={() => handleSuggestionClick(suggestion)}
                    className="w-full px-4 py-2 text-left hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* 工具栏 */}
          <div className="flex justify-center items-center gap-4 mt-4">
            {canGoBack && (
              <button
                onClick={goBack}
                className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              >
                ← 返回
              </button>
            )}
            {state.data && (
              <button
                onClick={clearResults}
                className="px-3 py-1 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              >
                清除结果
              </button>
            )}
            <div className="text-sm text-gray-500">
              缓存: {cacheSize} | 查询: {stats.totalQueries} | 成功: {stats.successfulQueries}
            </div>
          </div>
        </div>

        {/* 错误提示 */}
        {state.error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <div className="text-red-600">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">查询出错</h3>
                <p className="text-sm text-red-700 mt-1">{state.error}</p>
              </div>
            </div>
          </div>
        )}

        {/* 查询结果 */}
        {state.data && <WordLookupResult result={state.data} onWordClick={handleWordClick} />}

        {/* 使用说明 */}
        {!state.data && !state.loading && !state.error && (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">功能特色</h2>
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">🔍 智能查找</h3>
                  <p className="text-gray-600 text-sm">
                    支持通过任意词形查找，如输入"cats"会自动找到"cat"的完整信息
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">📝 词形变化</h3>
                  <p className="text-gray-600 text-sm">
                    显示完整的词形变化关系，包括复数、过去式、现在分词等
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">⚡ 快速响应</h3>
                  <p className="text-gray-600 text-sm">
                    优化的查询策略和缓存机制，提供毫秒级的查询体验
                  </p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-2">🎯 相关建议</h3>
                  <p className="text-gray-600 text-sm">
                    基于词频和相似度的智能推荐，帮助发现相关词汇
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
