import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import crypto from 'crypto';
import { z } from 'zod';
import { sendPasswordResetEmail } from '../../../lib/5-providers/email';

// 使用简单的Prisma客户端
const simplePrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export async function POST(request: NextRequest) {
  try {
    console.log('🔑 Password reset request received');
    const body = await request.json();

    // 验证请求数据
    const validation = forgotPasswordSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          message: 'Invalid email address',
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { email } = validation.data;
    console.log('📧 Processing password reset for:', email);

    // 检查用户是否存在
    const user = await simplePrisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, name: true, isActive: true },
    });

    // 安全考虑：无论用户是否存在都返回成功消息
    if (!user || !user.isActive) {
      console.log('⚠️ User not found or inactive, but returning success for security');
      return NextResponse.json(
        {
          message: 'If this email is registered, you will receive a password reset link shortly.',
          type: 'success',
        },
        { status: 200 }
      );
    }

    // 生成安全的重置令牌
    const resetToken = crypto.randomBytes(32).toString('hex');
    const tokenExpiry = new Date(Date.now() + 1 * 60 * 60 * 1000); // 1小时后过期

    console.log('🔐 Generated reset token for user:', user.id);

    // 清除用户之前的重置令牌
    await simplePrisma.passwordResetToken.deleteMany({
      where: { userId: user.id },
    });

    // 创建新的重置令牌
    await simplePrisma.passwordResetToken.create({
      data: {
        token: resetToken,
        userId: user.id,
        expires: tokenExpiry,
      },
    });

    console.log('✅ Password reset token created successfully');

    // 发送密码重置邮件
    console.log('📮 Sending password reset email...');
    const emailResult = await sendPasswordResetEmail(email, resetToken, user.name || undefined);

    if (!emailResult.success) {
      console.error('❌ Failed to send reset email:', emailResult.error);
      // 即使邮件发送失败，也不向用户暴露错误信息（安全考虑）
    } else {
      console.log('✅ Password reset email sent successfully');
    }

    return NextResponse.json(
      {
        message: 'If this email is registered, you will receive a password reset link shortly.',
        type: 'success',
        // 开发环境临时返回令牌，生产环境需要删除
        ...(process.env.NODE_ENV === 'development' && {
          debug: {
            resetToken,
            resetLink: `http://localhost:4000/auth/reset-password?token=${resetToken}`,
          },
        }),
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('💥 Forgot password error:', error);
    return NextResponse.json(
      {
        message: 'Internal server error. Please try again later.',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
