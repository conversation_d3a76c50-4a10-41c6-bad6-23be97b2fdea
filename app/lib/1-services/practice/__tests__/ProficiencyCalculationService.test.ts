import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ProficiencyCalculationService, PracticeSession } from '../ProficiencyCalculationService';
import { UserWordProficiencyRepository } from '@/lib/2-repositories/UserWordProficiencyRepository';

// Mock the repository
const mockRepository = {
  findByUserAndWord: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
  toggleMarked: vi.fn(),
  findLowestProficiencyWords: vi.fn(),
  findMarkedWords: vi.fn(),
  findWordsForReview: vi.fn(),
  getUserStats: vi.fn(),
  findManyByUser: vi.fn(),
} as any;

describe('ProficiencyCalculationService', () => {
  let service: ProficiencyCalculationService;

  beforeEach(() => {
    service = new ProficiencyCalculationService(mockRepository);
    vi.clearAllMocks();
  });

  describe('calculateProficiencyScore', () => {
    it('should return 0 for no practice', () => {
      const result = service.calculateProficiencyScore({
        practiceCount: 0,
        errorCount: 0,
        averageTime: 0,
        isMarked: false,
      });

      expect(result).toBe(0.0);
    });

    it('should calculate perfect score for ideal performance', () => {
      const result = service.calculateProficiencyScore({
        practiceCount: 20, // Maximum for full practice score
        errorCount: 0,     // No errors
        averageTime: 1000, // Ideal time
        isMarked: false,   // Not marked as difficult
      });

      expect(result).toBe(1.0);
    });

    it('should penalize high error rate', () => {
      const perfectScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 0,
        averageTime: 1000,
        isMarked: false,
      });

      const highErrorScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 10, // 50% error rate
        averageTime: 1000,
        isMarked: false,
      });

      expect(highErrorScore).toBeLessThan(perfectScore);
      expect(highErrorScore).toBeCloseTo(0.8, 1); // Roughly 80% due to 50% accuracy
    });

    it('should penalize slow typing speed', () => {
      const perfectScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 0,
        averageTime: 1000, // Ideal time
        isMarked: false,
      });

      const slowScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 0,
        averageTime: 3000, // 3x slower
        isMarked: false,
      });

      expect(slowScore).toBeLessThan(perfectScore);
    });

    it('should reward more practice sessions', () => {
      const lowPracticeScore = service.calculateProficiencyScore({
        practiceCount: 5,
        errorCount: 0,
        averageTime: 1000,
        isMarked: false,
      });

      const highPracticeScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 0,
        averageTime: 1000,
        isMarked: false,
      });

      expect(highPracticeScore).toBeGreaterThan(lowPracticeScore);
    });

    it('should apply penalty for marked words', () => {
      const unmarkedScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 0,
        averageTime: 1000,
        isMarked: false,
      });

      const markedScore = service.calculateProficiencyScore({
        practiceCount: 20,
        errorCount: 0,
        averageTime: 1000,
        isMarked: true,
      });

      expect(markedScore).toBe(unmarkedScore * 0.7); // 30% penalty
    });

    it('should handle edge cases and return valid range', () => {
      // Test extremely slow typing
      const extremelySlowScore = service.calculateProficiencyScore({
        practiceCount: 1,
        errorCount: 0,
        averageTime: 10000, // 10 seconds
        isMarked: false,
      });

      expect(extremelySlowScore).toBeGreaterThanOrEqual(0.0);
      expect(extremelySlowScore).toBeLessThanOrEqual(1.0);

      // Test all errors
      const allErrorsScore = service.calculateProficiencyScore({
        practiceCount: 10,
        errorCount: 10, // 100% error rate
        averageTime: 1000,
        isMarked: false,
      });

      expect(allErrorsScore).toBeGreaterThanOrEqual(0.0);
      expect(allErrorsScore).toBeLessThanOrEqual(1.0);
    });
  });

  describe('updateProficiencyFromSession', () => {
    it('should create new proficiency record for first session', async () => {
      const session: PracticeSession = {
        userId: 'user-1',
        wordId: 123,
        timeTaken: 1200,
        hasError: false,
      };

      mockRepository.findByUserAndWord.mockResolvedValue(null);
      mockRepository.create.mockResolvedValue({
        userId: 'user-1',
        wordId: 123,
        practiceCount: 1,
        errorCount: 0,
        averageTime: 1200,
        isMarked: false,
        proficiencyScore: 0.0,
        lastPracticed: new Date(),
      });
      mockRepository.update.mockResolvedValue({});

      await service.updateProficiencyFromSession(session);

      expect(mockRepository.create).toHaveBeenCalledWith({
        userId: 'user-1',
        wordId: 123,
        practiceCount: 1,
        errorCount: 0,
        averageTime: 1200,
        lastPracticed: expect.any(Date),
      });

      // Should update with calculated proficiency score
      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 123, {
        proficiencyScore: expect.any(Number),
      });
    });

    it('should update existing proficiency record', async () => {
      const session: PracticeSession = {
        userId: 'user-1',
        wordId: 123,
        timeTaken: 800,
        hasError: true,
      };

      const existingProficiency = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 5,
        errorCount: 1,
        averageTime: 1000,
        isMarked: false,
        proficiencyScore: 0.7,
      };

      mockRepository.findByUserAndWord.mockResolvedValue(existingProficiency);
      mockRepository.update.mockResolvedValue({
        ...existingProficiency,
        practiceCount: 6,
        errorCount: 2,
        averageTime: 967, // Weighted average: (1000*5 + 800)/6 ≈ 967
        lastPracticed: new Date(),
      });

      await service.updateProficiencyFromSession(session);

      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 123, {
        practiceCount: 6,
        errorCount: 2,
        averageTime: 967,
        lastPracticed: expect.any(Date),
      });

      // Should call update twice - once for session data, once for recalculated score
      expect(mockRepository.update).toHaveBeenCalledTimes(2);
    });

    it('should correctly calculate weighted average time', async () => {
      const session: PracticeSession = {
        userId: 'user-1',
        wordId: 123,
        timeTaken: 500, // Much faster than existing average
        hasError: false,
      };

      const existingProficiency = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 4,
        errorCount: 0,
        averageTime: 1000,
        isMarked: false,
        proficiencyScore: 0.8,
      };

      mockRepository.findByUserAndWord.mockResolvedValue(existingProficiency);
      mockRepository.update.mockResolvedValue({
        ...existingProficiency,
        practiceCount: 5,
        averageTime: 900, // (1000*4 + 500)/5 = 900
      });

      await service.updateProficiencyFromSession(session);

      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 123, {
        practiceCount: 5,
        errorCount: 0,
        averageTime: 900,
        lastPracticed: expect.any(Date),
      });
    });
  });

  describe('updateProficienciesFromSessions', () => {
    it('should process multiple sessions sequentially', async () => {
      const sessions: PracticeSession[] = [
        { userId: 'user-1', wordId: 123, timeTaken: 1000, hasError: false },
        { userId: 'user-1', wordId: 124, timeTaken: 1200, hasError: true },
        { userId: 'user-1', wordId: 125, timeTaken: 800, hasError: false },
      ];

      mockRepository.findByUserAndWord.mockResolvedValue(null);
      mockRepository.create.mockResolvedValue({});
      mockRepository.update.mockResolvedValue({});

      await service.updateProficienciesFromSessions(sessions);

      expect(mockRepository.create).toHaveBeenCalledTimes(3);
      expect(mockRepository.update).toHaveBeenCalledTimes(3); // Once per session for score update
    });
  });

  describe('toggleWordMarking', () => {
    it('should mark word as difficult and recalculate score', async () => {
      const existingProficiency = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 10,
        errorCount: 2,
        averageTime: 1100,
        isMarked: false,
        proficiencyScore: 0.8,
      };

      mockRepository.toggleMarked.mockResolvedValue({
        ...existingProficiency,
        isMarked: true,
      });
      mockRepository.update.mockResolvedValue({});

      await service.toggleWordMarking('user-1', 123, true);

      expect(mockRepository.toggleMarked).toHaveBeenCalledWith('user-1', 123, true);
      
      // Should recalculate score with penalty for marked word
      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 123, {
        proficiencyScore: expect.any(Number),
      });
    });

    it('should unmark word and recalculate score without penalty', async () => {
      const existingProficiency = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 10,
        errorCount: 2,
        averageTime: 1100,
        isMarked: true,
        proficiencyScore: 0.56, // With penalty
      };

      mockRepository.toggleMarked.mockResolvedValue({
        ...existingProficiency,
        isMarked: false,
      });
      mockRepository.update.mockResolvedValue({});

      await service.toggleWordMarking('user-1', 123, false);

      expect(mockRepository.toggleMarked).toHaveBeenCalledWith('user-1', 123, false);
      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 123, {
        proficiencyScore: expect.any(Number),
      });
    });
  });

  describe('getRecommendedWordsForPractice', () => {
    it('should return lowest proficiency words by default', async () => {
      const mockWords = [
        { userId: 'user-1', wordId: 123, proficiencyScore: 0.2 },
        { userId: 'user-1', wordId: 124, proficiencyScore: 0.3 },
      ];

      mockRepository.findLowestProficiencyWords.mockResolvedValue(mockWords);

      const result = await service.getRecommendedWordsForPractice('user-1', 10);

      expect(mockRepository.findLowestProficiencyWords).toHaveBeenCalledWith('user-1', 10);
      expect(result).toEqual(mockWords);
    });

    it('should return marked words when specified', async () => {
      const mockMarkedWords = [
        { userId: 'user-1', wordId: 125, proficiencyScore: 0.4, isMarked: true },
      ];

      mockRepository.findMarkedWords.mockResolvedValue(mockMarkedWords);

      const result = await service.getRecommendedWordsForPractice('user-1', 10, true);

      expect(mockRepository.findMarkedWords).toHaveBeenCalledWith('user-1');
      expect(result).toEqual(mockMarkedWords);
    });
  });

  describe('getUserProficiencyStats', () => {
    it('should return user stats with proficiency level', async () => {
      const mockStats = {
        totalWords: 50,
        markedWords: 5,
        averageProficiency: 0.75,
        totalPracticeCount: 200,
      };

      mockRepository.getUserStats.mockResolvedValue(mockStats);

      const result = await service.getUserProficiencyStats('user-1');

      expect(result).toEqual({
        ...mockStats,
        proficiencyLevel: 'Advanced',
      });
    });

    it('should correctly classify proficiency levels', async () => {
      // Test different proficiency levels
      const testCases = [
        { avg: 0.9, expected: 'Expert' },
        { avg: 0.8, expected: 'Expert' },
        { avg: 0.7, expected: 'Advanced' },
        { avg: 0.6, expected: 'Advanced' },
        { avg: 0.5, expected: 'Intermediate' },
        { avg: 0.4, expected: 'Intermediate' },
        { avg: 0.3, expected: 'Beginner' },
        { avg: 0.1, expected: 'Beginner' },
      ];

      for (const testCase of testCases) {
        mockRepository.getUserStats.mockResolvedValue({
          totalWords: 10,
          markedWords: 2,
          averageProficiency: testCase.avg,
          totalPracticeCount: 50,
        });

        const result = await service.getUserProficiencyStats('user-1');
        expect(result.proficiencyLevel).toBe(testCase.expected);
      }
    });
  });

  describe('recalculateAllProficiencyScores', () => {
    it('should recalculate scores for all user words', async () => {
      const mockProficiencies = [
        {
          userId: 'user-1',
          wordId: 123,
          practiceCount: 5,
          errorCount: 1,
          averageTime: 1200,
          isMarked: false,
        },
        {
          userId: 'user-1',
          wordId: 124,
          practiceCount: 10,
          errorCount: 0,
          averageTime: 900,
          isMarked: true,
        },
      ];

      mockRepository.findManyByUser.mockResolvedValue(mockProficiencies);
      mockRepository.update.mockResolvedValue({});

      await service.recalculateAllProficiencyScores('user-1');

      expect(mockRepository.findManyByUser).toHaveBeenCalledWith('user-1');
      expect(mockRepository.update).toHaveBeenCalledTimes(2);

      // Verify each word's score was recalculated
      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 123, {
        proficiencyScore: expect.any(Number),
      });
      expect(mockRepository.update).toHaveBeenCalledWith('user-1', 124, {
        proficiencyScore: expect.any(Number),
      });
    });
  });
});