#!/usr/bin/env tsx

/**
 * 调试数据库查询脚本
 * 验证是否真的执行了数据库查询
 */

const baseUrl = 'http://localhost:3000';

/**
 * 清空所有缓存
 */
async function clearAllCaches(): Promise<void> {
  console.log('🧹 清空所有缓存...');

  try {
    // 1. 清空Redis缓存
    const { redis } = await import('../app/lib/4-infrastructure/cache/redis');
    const keys = await redis.keys('lucid:*');
    if (keys.length > 0) {
      const deletedCount = await redis.del(...keys);
      console.log(`✅ 删除 ${deletedCount} 个Redis缓存键`);
    } else {
      console.log('✅ Redis缓存为空');
    }

    // 2. 清空应用级缓存
    try {
      const { OptimizedVocabularyRepository } = await import(
        '../app/lib/2-repositories/OptimizedVocabularyRepository'
      );
      const tempRepo = new OptimizedVocabularyRepository();
      tempRepo.clearCache();
      console.log('✅ 应用级Repository缓存已清空');
    } catch (error) {
      console.log(`⚠️ 清空Repository缓存失败: ${error}`);
    }

    try {
      const { vocabularyCache, queryResultCache, relationshipCache } = await import(
        '../app/lib/4-infrastructure/cache/ApplicationCache'
      );
      vocabularyCache.clear();
      queryResultCache.clear();
      relationshipCache.clear();
      console.log('✅ 全局应用缓存已清空');
    } catch (error) {
      console.log(`⚠️ 清空全局缓存失败: ${error}`);
    }
  } catch (error) {
    console.error('❌ 清空缓存失败:', error);
    throw error;
  }
}

/**
 * 直接测试数据库查询
 */
async function testDirectDatabaseQuery(): Promise<void> {
  console.log('\n🔍 直接测试数据库查询...');

  try {
    const { prisma } = await import('../app/lib/4-infrastructure/database/prisma');

    // 启用查询日志
    console.log('📝 启用Prisma查询日志...');

    // 执行5个不同的查询
    const testWords = ['test', 'hello', 'world', 'example', 'sample'];

    console.log('🚀 执行5个并发数据库查询...');
    const startTime = performance.now();

    const promises = testWords.map(async (word, index) => {
      console.log(`   🔍 查询 ${index + 1}: ${word}`);
      const result = await prisma.vocabulary.findFirst({
        where: { word: word.toLowerCase() },
        include: {
          explains: {
            include: { definitions: true },
            orderBy: { id: 'asc' },
          },
        },
      });
      console.log(`   ✅ 查询 ${index + 1} 完成: ${word} (${result ? '找到' : '未找到'})`);
      return { word, found: !!result };
    });

    const results = await Promise.all(promises);
    const endTime = performance.now();

    console.log(`\n📊 直接数据库查询结果:`);
    console.log(`   总耗时: ${Math.round(endTime - startTime)}ms`);
    results.forEach((result, index) => {
      console.log(
        `   查询 ${index + 1}: ${result.word} - ${result.found ? '✅ 找到' : '❌ 未找到'}`
      );
    });
  } catch (error) {
    console.error('❌ 直接数据库查询失败:', error);
  }
}

/**
 * 检查数据库连接池状态
 */
async function checkDbPoolStatus(): Promise<any> {
  try {
    const { prisma } = await import('../app/lib/4-infrastructure/database/prisma');
    const prismaWithMetrics = prisma as any;

    if (!prismaWithMetrics.$metrics) {
      console.log('⚠️ Prisma metrics 未启用');
      return null;
    }

    const metrics = await prismaWithMetrics.$metrics.json();
    const gauges = metrics.gauges;
    const counters = metrics.counters;

    const getGauge = (name: string) => gauges.find((g: any) => g.key === name)?.value ?? 0;
    const getCounter = (name: string) => counters.find((c: any) => c.key === name)?.value ?? 0;

    const stats = {
      open: getGauge('prisma_pool_connections_open'),
      idle: getGauge('prisma_pool_connections_idle'),
      busy: getGauge('prisma_pool_connections_busy'),
      waitedCount: getCounter('prisma_pool_wait_count'),
    };

    console.log('📊 数据库连接池状态:');
    console.log(
      `   总连接: ${stats.open} | 空闲: ${stats.idle} | 繁忙: ${stats.busy} | 等待数: ${stats.waitedCount}`
    );

    return stats;
  } catch (error) {
    console.error('❌ 检查数据库连接池失败:', error);
    return null;
  }
}

/**
 * 测试API请求
 */
async function testApiRequests(): Promise<void> {
  console.log('\n🌐 测试API请求...');

  const testWords = ['test', 'hello', 'world', 'example', 'sample'];

  console.log('🚀 执行5个并发API请求...');
  const startTime = performance.now();

  const promises = testWords.map(async (word, index) => {
    console.log(`   🌐 API请求 ${index + 1}: ${word}`);
    try {
      const response = await fetch(`${baseUrl}/api/dictionary/en/${word}`, {
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'User-Agent': 'Debug-Test/1.0',
        },
        signal: AbortSignal.timeout(10000),
      });

      const responseTime = performance.now() - startTime;
      const cacheStatus = response.headers.get('x-cache-status');

      console.log(
        `   ✅ API请求 ${index + 1} 完成: ${word} (${response.status}, ${Math.round(responseTime)}ms, cache: ${cacheStatus || 'unknown'})`
      );

      return {
        word,
        success: response.ok,
        status: response.status,
        cacheStatus,
        responseTime,
      };
    } catch (error) {
      console.log(`   ❌ API请求 ${index + 1} 失败: ${word} - ${error}`);
      return { word, success: false, error };
    }
  });

  const results = await Promise.all(promises);
  const endTime = performance.now();

  console.log(`\n📊 API请求结果:`);
  console.log(`   总耗时: ${Math.round(endTime - startTime)}ms`);
  results.forEach((result, index) => {
    if (result.success) {
      console.log(
        `   请求 ${index + 1}: ${result.word} - ✅ ${result.status} (cache: ${result.cacheStatus})`
      );
    } else {
      console.log(`   请求 ${index + 1}: ${result.word} - ❌ 失败`);
    }
  });
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🔧 数据库查询调试测试\n');

  try {
    // 1. 检查初始状态
    console.log('📋 初始状态:');
    await checkDbPoolStatus();

    // 2. 清空所有缓存
    await clearAllCaches();

    // 3. 等待缓存清空完成
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 4. 检查清空后状态
    console.log('\n📋 清空缓存后状态:');
    await checkDbPoolStatus();

    // 5. 直接测试数据库查询
    await testDirectDatabaseQuery();

    // 6. 检查直接查询后的连接池状态
    console.log('\n📋 直接查询后连接池状态:');
    await checkDbPoolStatus();

    // 7. 测试API请求
    await testApiRequests();

    // 8. 检查API请求后的连接池状态
    console.log('\n📋 API请求后连接池状态:');
    await checkDbPoolStatus();

    console.log('\n✅ 调试测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
