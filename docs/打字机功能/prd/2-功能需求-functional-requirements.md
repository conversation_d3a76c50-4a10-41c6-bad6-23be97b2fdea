# 2. 功能需求 (Functional Requirements)

## Epic: 打字练习功能 2.0 (Typer Feature 2.0)

- **FR1: 系统化词库练习**

  - **FR1.1:** 系统应引入标准化的词库，至少包括大学四级 (CET-4)、六级 (CET-6) 和 GRE 词汇。
  - **FR1.2:** 用户应能够从可用的词库列表中选择一个进行打字练习。
  - **FR1.3:** “随机单词练习”模式应改造为从用户选择的词库中随机抽词。
  - **FR1.4:** 在进行标准词库练习时，用户应能够将当前单词一键“加入生词本”。

- **FR2: 智能化生词复习**

  - **FR2.1:** 系统需要为每个用户和每个单词建立一个“熟练度”追踪模型。 _(依赖于核心技术故事：用户-单词关联)_
  - **FR2.2:** 在“生词练习”中，系统需要记录用户的详细练习指标，包括但不限于：练习次数、具体错误、平均卡顿时间、总错误次数。
  - **FR2.3:** 系统应提供一个智能筛选算法，根据 FR2.2 中记录的指标，优先为用户推送他们最不熟练的单词。
  - **FR2.4:** 用户应有功能可以手动“标注”单词，被标注的单词其复习优先级应显著高于其他单词。

- **FR3: 个性化句子练习**

  - **FR3.1:** 用户应能够在“句子练习”模式中收藏（Favorite/Save）他们喜欢的句子。 _(依赖于核心技术故事：用户-句子关联)_
  - **FR3.2:** 系统需要持久化存储用户和他们收藏的句子之间的关联关系。
  - **FR3.3:** 系统应提供一种练习模式，允许用户只练习自己收藏过的句子。

- **FR4: 核心技术故事 (Dependencies)**
  - **FR4.1:** **[前置任务]** 必须先实现用户与“单词熟练度”数据表的关联逻辑。
  - **FR4.2:** **[前置任务]** 必须先实现用户与“句子收藏”数据表的关联逻辑。
  - **FR4.3:** **[前置任务]** 必须编写并执行CET-4, CET-6, GRE等核心词库的数据初始化脚本。
