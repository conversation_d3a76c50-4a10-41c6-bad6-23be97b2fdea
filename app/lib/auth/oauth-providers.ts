/**
 * OAuth Providers Configuration
 * Enhanced OAuth provider setup with Phase 2 security features
 */

import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';

/**
 * Enhanced Google OAuth Provider
 * Includes security enhancements and audit logging
 */
export const enhancedGoogleProvider = GoogleProvider({
  clientId: process.env.GOOGLE_CLIENT_ID!,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
  authorization: {
    params: {
      prompt: 'consent',
      access_type: 'offline',
      response_type: 'code',
      // Enhanced security parameters
      scope: 'openid email profile',
      include_granted_scopes: 'true',
      // PKCE for enhanced security
      code_challenge_method: 'S256',
    },
  },
  checks: ['state', 'pkce'],
  profile(profile) {
    return {
      id: profile.sub,
      name: profile.name,
      email: profile.email,
      image: profile.picture,
      // Enhanced profile fields
      emailVerified: profile.email_verified,
      locale: profile.locale,
      provider: 'google',
      providerId: profile.sub,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  },
});

/**
 * Enhanced GitHub OAuth Provider
 * Includes security enhancements and audit logging
 */
export const enhancedGitHubProvider = GitHubProvider({
  clientId: process.env.GITHUB_ID!,
  clientSecret: process.env.GITHUB_SECRET!,
  authorization: {
    params: {
      scope: 'read:user user:email',
      // Enhanced security parameters
      allow_signup: 'true',
    },
  },
  profile(profile) {
    return {
      id: profile.id.toString(),
      name: profile.name || profile.login,
      email: profile.email || '',
      image: profile.avatar_url,
      // Enhanced profile fields
      username: profile.login,
      provider: 'github',
      providerId: profile.id.toString(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  },
});

/**
 * LinkedIn OAuth Provider (Optional)
 * For professional networking integration
 */
export function createLinkedInProvider() {
  return {
    id: 'linkedin',
    name: 'LinkedIn',
    type: 'oauth' as const,
    authorization: {
      url: 'https://www.linkedin.com/oauth/v2/authorization',
      params: {
        scope: 'r_liteprofile r_emailaddress',
        response_type: 'code',
      },
    },
    token: 'https://www.linkedin.com/oauth/v2/accessToken',
    userinfo: {
      url: 'https://api.linkedin.com/v2/me',
      params: {
        projection: '(id,firstName,lastName,profilePicture(displayImage~:playableStreams))',
      },
    },
    profile(profile: any) {
      return {
        id: profile.id,
        name: `${profile.firstName.localized.en_US} ${profile.lastName.localized.en_US}`,
        email: profile.emailAddress,
        image: profile.profilePicture?.displayImage?.elements?.[0]?.identifiers?.[0]?.identifier,
        provider: 'linkedin',
        providerId: profile.id,
      };
    },
    clientId: process.env.LINKEDIN_CLIENT_ID,
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
  };
}

/**
 * Microsoft OAuth Provider (Optional)
 * For enterprise integration
 */
export function createMicrosoftProvider() {
  return {
    id: 'microsoft',
    name: 'Microsoft',
    type: 'oauth' as const,
    authorization: {
      url: 'https://login.microsoftonline.com/common/oauth2/v2.0/authorize',
      params: {
        scope: 'openid email profile User.Read',
        response_type: 'code',
      },
    },
    token: 'https://login.microsoftonline.com/common/oauth2/v2.0/token',
    userinfo: 'https://graph.microsoft.com/v1.0/me',
    profile(profile: any) {
      return {
        id: profile.id,
        name: profile.displayName,
        email: profile.mail || profile.userPrincipalName,
        image: profile.photo?.value || null,
        provider: 'microsoft',
        providerId: profile.id,
      };
    },
    clientId: process.env.MICROSOFT_CLIENT_ID,
    clientSecret: process.env.MICROSOFT_CLIENT_SECRET,
  };
}

/**
 * Apple OAuth Provider (Optional)
 * For iOS/macOS integration
 */
export function createAppleProvider() {
  return {
    id: 'apple',
    name: 'Apple',
    type: 'oauth' as const,
    authorization: {
      url: 'https://appleid.apple.com/auth/authorize',
      params: {
        scope: 'name email',
        response_type: 'code',
        response_mode: 'form_post',
      },
    },
    token: 'https://appleid.apple.com/auth/token',
    profile(profile: any) {
      return {
        id: profile.sub,
        name: profile.name ? `${profile.name.firstName} ${profile.name.lastName}` : profile.email,
        email: profile.email,
        image: null,
        provider: 'apple',
        providerId: profile.sub,
      };
    },
    clientId: process.env.APPLE_CLIENT_ID,
    clientSecret: process.env.APPLE_CLIENT_SECRET,
  };
}

/**
 * Enhanced OAuth Event Handlers
 * Integrated with Phase 2 security features
 */
export const oauthEventHandlers = {
  async signIn({ user, account, profile, isNewUser }: any) {
    // Security checks for OAuth providers
    if (account?.provider === 'google') {
      // Verify Google email is verified
      if (profile?.email_verified !== true) {
        console.warn('OAuth signin rejected: Google email not verified', { userId: user.id });
        return false;
      }
    }

    if (account?.provider === 'github') {
      // Additional GitHub security checks
      if (profile?.login && profile.login.length < 3) {
        console.warn('OAuth signin rejected: Suspicious GitHub username', { userId: user.id });
        return false;
      }
    }

    return true;
  },

  async signOut({ token }: any) {
    // OAuth sign-out handling
    console.log('OAuth signout completed', { provider: token?.provider });
  },

  async createUser({ user }: any) {
    // New OAuth user creation
    console.log('OAuth user created', { provider: user.provider, email: user.email });
  },

  async linkAccount({ user, account, profile }: any) {
    // Account linking
    console.log('OAuth account linked', { provider: account?.provider, userId: user.id });
  },
};

/**
 * OAuth Provider Security Configuration
 * Enhanced security settings for OAuth providers
 */
export const oauthSecurityConfig = {
  // Rate limiting for OAuth requests
  rateLimits: {
    signIn: { max: 5, windowMs: 60 * 1000 }, // 5 attempts per minute
    signUp: { max: 3, windowMs: 60 * 60 * 1000 }, // 3 attempts per hour
    linkAccount: { max: 3, windowMs: 60 * 60 * 1000 }, // 3 attempts per hour
  },

  // Security headers for OAuth redirects
  securityHeaders: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy':
      "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'",
  },

  // OAuth state parameter security
  stateConfig: {
    length: 32,
    secure: true,
    httpOnly: true,
    sameSite: 'lax' as const,
  },

  // PKCE configuration for enhanced security
  pkceConfig: {
    codeChallenge: true,
    codeChallengeMethod: 'S256',
  },
};

/**
 * Get all available OAuth providers
 * Returns configured providers based on environment variables
 */
export function getAvailableOAuthProviders(): any[] {
  const providers: any[] = [];

  // Always include Google and GitHub if configured
  if (process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
    providers.push(enhancedGoogleProvider);
  }

  if (process.env.GITHUB_ID && process.env.GITHUB_SECRET) {
    providers.push(enhancedGitHubProvider);
  }

  // Optional providers
  if (process.env.LINKEDIN_CLIENT_ID && process.env.LINKEDIN_CLIENT_SECRET) {
    providers.push(createLinkedInProvider() as any);
  }

  if (process.env.MICROSOFT_CLIENT_ID && process.env.MICROSOFT_CLIENT_SECRET) {
    providers.push(createMicrosoftProvider() as any);
  }

  if (process.env.APPLE_CLIENT_ID && process.env.APPLE_CLIENT_SECRET) {
    providers.push(createAppleProvider() as any);
  }

  return providers;
}

/**
 * OAuth Provider Utilities
 */
export const oauthUtils = {
  /**
   * Get provider icon class
   */
  getProviderIcon(provider: string): string {
    const icons: Record<string, string> = {
      google: 'fab fa-google',
      github: 'fab fa-github',
      linkedin: 'fab fa-linkedin',
      microsoft: 'fab fa-microsoft',
      apple: 'fab fa-apple',
    };
    return icons[provider] || 'fas fa-sign-in-alt';
  },

  /**
   * Get provider display name
   */
  getProviderDisplayName(provider: string): string {
    const names: Record<string, string> = {
      google: 'Google',
      github: 'GitHub',
      linkedin: 'LinkedIn',
      microsoft: 'Microsoft',
      apple: 'Apple',
    };
    return names[provider] || provider;
  },

  /**
   * Get provider theme color
   */
  getProviderColor(provider: string): string {
    const colors: Record<string, string> = {
      google: '#4285f4',
      github: '#333333',
      linkedin: '#0077b5',
      microsoft: '#0078d4',
      apple: '#000000',
    };
    return colors[provider] || '#6b7280';
  },
};

export default {
  enhancedGoogleProvider,
  enhancedGitHubProvider,
  createLinkedInProvider,
  createMicrosoftProvider,
  createAppleProvider,
  oauthEventHandlers,
  oauthSecurityConfig,
  getAvailableOAuthProviders,
  oauthUtils,
};
