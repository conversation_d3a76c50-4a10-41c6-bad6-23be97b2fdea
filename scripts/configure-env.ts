#!/usr/bin/env tsx
/**
 * 环境配置管理工具
 * 用于快速切换不同的环境配置模式
 */

import { writeFileSync, readFileSync, existsSync } from 'fs';
import { join } from 'path';

interface EnvConfig {
  [key: string]: string | boolean | number;
}

const ENV_PRESETS = {
  development: {
    NODE_ENV: 'development',
    DEBUG_MODE: false,
    HIGH_CONCURRENCY_MODE: false,
    LOG_LEVEL: 2,
    LOG_SAMPLING_RATE: 0.1,
    ENABLE_PERFORMANCE_LOGS: false,
    ENABLE_REDIS_POOL_LOGS: false,
    ENABLE_CACHE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ERROR_REPORTING: true,
  },
  debug: {
    NODE_ENV: 'development',
    DEBUG_MODE: true,
    HIGH_CONCURRENCY_MODE: false,
    LOG_LEVEL: 3,
    LOG_SAMPLING_RATE: 1.0,
    ENABLE_PERFORMANCE_LOGS: true,
    ENABLE_REDIS_POOL_LOGS: true,
    ENABLE_CACHE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ERROR_REPORTING: true,
  },
  'high-concurrency': {
    NODE_ENV: 'development',
    DEBUG_MODE: false,
    HIGH_CONCURRENCY_MODE: true,
    LOG_LEVEL: 1,
    LOG_SAMPLING_RATE: 0.01,
    ENABLE_PERFORMANCE_LOGS: false,
    ENABLE_REDIS_POOL_LOGS: false,
    ENABLE_CACHE_LOGS: false,
    ENABLE_PERFORMANCE_MONITORING: false,
    ENABLE_ERROR_REPORTING: true,
  },
  production: {
    NODE_ENV: 'production',
    DEBUG_MODE: false,
    HIGH_CONCURRENCY_MODE: false,
    LOG_LEVEL: 1,
    LOG_SAMPLING_RATE: 0.01,
    ENABLE_PERFORMANCE_LOGS: false,
    ENABLE_REDIS_POOL_LOGS: false,
    ENABLE_CACHE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ERROR_REPORTING: true,
    PRODUCTION_LOG_LEVEL: 1,
    PRODUCTION_LOG_SAMPLING_RATE: 0.01,
    PRODUCTION_ENABLE_PERFORMANCE_LOGS: false,
  },
  'performance-test': {
    NODE_ENV: 'development',
    DEBUG_MODE: false,
    HIGH_CONCURRENCY_MODE: false,
    LOG_LEVEL: 2,
    LOG_SAMPLING_RATE: 0.05,
    ENABLE_PERFORMANCE_LOGS: false,
    ENABLE_REDIS_POOL_LOGS: false,
    ENABLE_CACHE_LOGS: true,
    ENABLE_PERFORMANCE_MONITORING: true,
    ENABLE_ERROR_REPORTING: true,
  },
};

function loadCurrentEnv(): Record<string, string> {
  const envPath = join(process.cwd(), '.env');
  if (!existsSync(envPath)) {
    return {};
  }

  const envContent = readFileSync(envPath, 'utf-8');
  const env: Record<string, string> = {};

  envContent.split('\n').forEach((line) => {
    const trimmed = line.trim();
    if (trimmed && !trimmed.startsWith('#')) {
      const [key, ...valueParts] = trimmed.split('=');
      if (key && valueParts.length > 0) {
        env[key] = valueParts.join('=').replace(/^["']|["']$/g, '');
      }
    }
  });

  return env;
}

function updateEnvFile(updates: EnvConfig): void {
  const envPath = join(process.cwd(), '.env');
  const currentEnv = loadCurrentEnv();

  // 合并配置
  const newEnv = { ...currentEnv };
  Object.entries(updates).forEach(([key, value]) => {
    newEnv[key] = String(value);
  });

  // 生成新的.env内容
  const lines: string[] = [];

  if (existsSync(envPath)) {
    const currentContent = readFileSync(envPath, 'utf-8');
    const currentLines = currentContent.split('\n');

    currentLines.forEach((line) => {
      const trimmed = line.trim();
      if (trimmed.startsWith('#') || !trimmed) {
        // 保留注释和空行
        lines.push(line);
      } else {
        const [key] = trimmed.split('=');
        if (key && newEnv.hasOwnProperty(key)) {
          // 更新现有配置
          lines.push(`${key}=${newEnv[key]}`);
          delete newEnv[key];
        } else {
          // 保留其他配置
          lines.push(line);
        }
      }
    });
  }

  // 添加新配置
  Object.entries(newEnv).forEach(([key, value]) => {
    if (!currentEnv.hasOwnProperty(key)) {
      lines.push(`${key}=${value}`);
    }
  });

  writeFileSync(envPath, lines.join('\n'));
}

function showCurrentConfig(): void {
  const currentEnv = loadCurrentEnv();
  const relevantKeys = [
    'NODE_ENV',
    'DEBUG_MODE',
    'HIGH_CONCURRENCY_MODE',
    'LOG_LEVEL',
    'LOG_SAMPLING_RATE',
    'ENABLE_PERFORMANCE_LOGS',
    'ENABLE_REDIS_POOL_LOGS',
    'ENABLE_CACHE_LOGS',
    'ENABLE_PERFORMANCE_MONITORING',
    'ENABLE_ERROR_REPORTING',
  ];

  console.log('📋 当前环境配置:');
  console.log('================');
  relevantKeys.forEach((key) => {
    const value = currentEnv[key] || '未设置';
    console.log(`${key.padEnd(30)} = ${value}`);
  });
  console.log('');
}

function showAvailablePresets(): void {
  console.log('🎛️  可用的配置预设:');
  console.log('==================');
  Object.keys(ENV_PRESETS).forEach((preset) => {
    console.log(`- ${preset}`);
  });
  console.log('');
}

function applyPreset(presetName: string): void {
  if (!ENV_PRESETS[presetName as keyof typeof ENV_PRESETS]) {
    console.error(`❌ 未知的预设: ${presetName}`);
    showAvailablePresets();
    process.exit(1);
  }

  const preset = ENV_PRESETS[presetName as keyof typeof ENV_PRESETS];
  console.log(`🔧 应用配置预设: ${presetName}`);

  updateEnvFile(preset);

  console.log('✅ 配置已更新');
  console.log('');
  showCurrentConfig();
}

function main(): void {
  const args = process.argv.slice(2);
  const command = args[0];

  console.log('🛠️  环境配置管理工具');
  console.log('====================\n');

  switch (command) {
    case 'show':
    case 'current':
      showCurrentConfig();
      break;

    case 'list':
    case 'presets':
      showAvailablePresets();
      break;

    case 'apply':
      const preset = args[1];
      if (!preset) {
        console.error('❌ 请指定要应用的预设名称');
        showAvailablePresets();
        process.exit(1);
      }
      applyPreset(preset);
      break;

    case 'help':
    default:
      console.log('用法:');
      console.log('  tsx scripts/configure-env.ts show          # 显示当前配置');
      console.log('  tsx scripts/configure-env.ts list          # 显示可用预设');
      console.log('  tsx scripts/configure-env.ts apply <preset> # 应用指定预设');
      console.log('');
      console.log('示例:');
      console.log('  tsx scripts/configure-env.ts apply debug            # 启用调试模式');
      console.log('  tsx scripts/configure-env.ts apply high-concurrency # 启用高并发模式');
      console.log('  tsx scripts/configure-env.ts apply production       # 启用生产模式');
      console.log('');
      showAvailablePresets();
      break;
  }
}

// 检查是否为直接执行
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
