import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { WordListRepository } from '@/lib/2-repositories/WordListRepository';
import { requireAuth, AuthContext } from '@/lib/auth/route-protection';

/**
 * 获取词库列表API
 * GET /api/practice/wordlists
 */

// Query参数验证schema
const QuerySchema = z.object({
  difficulty: z.string().transform(val => parseInt(val)).optional(),
  minDifficulty: z.string().transform(val => parseInt(val)).optional(),
  maxDifficulty: z.string().transform(val => parseInt(val)).optional(),
  isActive: z.string().transform(val => val === 'true').optional(),
});

const protectedHandler = async (request: NextRequest, context: AuthContext) => {
  try {
    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validatedParams = QuerySchema.parse(queryParams);

    // 创建Repository实例
    const wordListRepo = new WordListRepository();

    // 获取词库列表和用户进度（使用认证的用户ID）
    const wordListStats = await wordListRepo.getStatsWithUserProgress(context.user.id);

    // 应用过滤条件
    let filteredStats = wordListStats;

    if (validatedParams.difficulty !== undefined) {
      filteredStats = filteredStats.filter(stat => {
        // 需要根据统计数据找到对应的词库难度
        // 这里简化处理，实际需要从WordList中获取难度信息
        return true; // 暂时不过滤
      });
    }

    if (validatedParams.isActive !== undefined) {
      // 只返回激活的词库（默认行为）
      filteredStats = filteredStats.filter(() => true);
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: {
        wordLists: filteredStats,
        total: filteredStats.length
      },
      message: 'Word lists retrieved successfully'
    });

  } catch (error) {
    console.error('获取词库列表失败:', error);

    // Zod验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid query parameters',
        details: error.errors
      }, { status: 400 });
    }

    // 通用错误处理
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve word lists',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

export const GET = requireAuth()(protectedHandler);