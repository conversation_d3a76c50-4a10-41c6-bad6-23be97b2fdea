/**
 * 持久化安全存储实现
 * 提供 Token 黑名单和速率限制的持久化存储
 */

import { PrismaClient } from '@prisma/client';
import { createHash } from 'crypto';
import { redis } from '../4-infrastructure/cache/redis';

const prisma = new PrismaClient();

// Redis 缓存前缀
const CACHE_PREFIXES = {
  BLACKLIST: 'auth:blacklist:',
  RATE_LIMIT: 'auth:rate:',
  TOKEN_HASH: 'auth:token:',
} as const;

// 缓存过期时间 (秒)
const CACHE_TTL = {
  BLACKLIST: 3600, // 1小时
  RATE_LIMIT: 900, // 15分钟
  TOKEN_HASH: 1800, // 30分钟
} as const;

/**
 * Token 黑名单持久化存储
 */
export class TokenBlacklistStorage {
  /**
   * 将 token 加入黑名单
   */
  static async addToBlacklist(params: {
    jti: string;
    tokenHash: string;
    userId?: string;
    reason: 'logout' | 'security' | 'expired' | 'revoked';
    expiresAt: Date;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<void> {
    const { jti, tokenHash, userId, reason, expiresAt, ipAddress, userAgent } = params;

    // 1. 存储到数据库
    await prisma.tokenBlacklist.create({
      data: {
        jti,
        tokenHash,
        userId,
        reason,
        expiresAt,
        ipAddress,
        userAgent,
      },
    });

    // 2. 缓存到 Redis
    const cacheKey = `${CACHE_PREFIXES.BLACKLIST}${jti}`;
    await redis.setex(
      cacheKey,
      CACHE_TTL.BLACKLIST,
      JSON.stringify({
        jti,
        tokenHash,
        reason,
        expiresAt: expiresAt.toISOString(),
        blacklistedAt: new Date().toISOString(),
      })
    );

    // 3. 按 token hash 索引
    const hashKey = `${CACHE_PREFIXES.TOKEN_HASH}${tokenHash}`;
    await redis.setex(hashKey, CACHE_TTL.TOKEN_HASH, jti);
  }

  /**
   * 检查 token 是否在黑名单中
   */
  static async isBlacklisted(jti: string): Promise<boolean> {
    // 1. 先检查 Redis 缓存
    const cacheKey = `${CACHE_PREFIXES.BLACKLIST}${jti}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      const data = JSON.parse(cached);
      // 检查是否过期
      if (new Date(data.expiresAt) > new Date()) {
        return true;
      }
    }

    // 2. 检查数据库
    const blacklisted = await prisma.tokenBlacklist.findUnique({
      where: { jti },
      select: { expiresAt: true },
    });

    if (blacklisted) {
      // 如果在数据库中找到且未过期，更新缓存
      if (blacklisted.expiresAt > new Date()) {
        await redis.setex(
          cacheKey,
          CACHE_TTL.BLACKLIST,
          JSON.stringify({
            jti,
            expiresAt: blacklisted.expiresAt.toISOString(),
            blacklistedAt: new Date().toISOString(),
          })
        );
        return true;
      }
    }

    return false;
  }

  /**
   * 通过 token hash 检查是否被黑名单
   */
  static async isBlacklistedByHash(tokenHash: string): Promise<boolean> {
    // 1. 先通过 hash 找到 jti
    const hashKey = `${CACHE_PREFIXES.TOKEN_HASH}${tokenHash}`;
    let jti = await redis.get(hashKey);

    if (!jti) {
      // 从数据库查找
      const blacklisted = await prisma.tokenBlacklist.findUnique({
        where: { tokenHash },
        select: { jti: true, expiresAt: true },
      });

      if (blacklisted && blacklisted.expiresAt > new Date()) {
        jti = blacklisted.jti;
        // 更新缓存
        await redis.setex(hashKey, CACHE_TTL.TOKEN_HASH, jti);
      }
    }

    // 2. 检查 jti 是否在黑名单中
    return jti ? await this.isBlacklisted(jti) : false;
  }

  /**
   * 清理过期的黑名单记录
   */
  static async cleanupExpired(): Promise<number> {
    const deleted = await prisma.tokenBlacklist.deleteMany({
      where: {
        expiresAt: {
          lt: new Date(),
        },
      },
    });

    return deleted.count;
  }

  /**
   * 获取用户的黑名单记录
   */
  static async getUserBlacklistRecords(userId: string, limit = 50) {
    return await prisma.tokenBlacklist.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      select: {
        id: true,
        jti: true,
        reason: true,
        expiresAt: true,
        createdAt: true,
        ipAddress: true,
        userAgent: true,
      },
    });
  }
}

/**
 * 速率限制持久化存储
 */
export class RateLimitStorage {
  /**
   * 创建散列 token
   */
  private static createTokenHash(token: string): string {
    return createHash('sha256').update(token).digest('hex');
  }

  /**
   * 记录认证尝试
   */
  static async recordAttempt(params: {
    ipAddress: string;
    endpoint: string;
    userId?: string;
    success: boolean;
  }): Promise<{
    attempts: number;
    blocked: boolean;
    blockUntil?: Date;
  }> {
    const { ipAddress, endpoint, userId, success } = params;
    const now = new Date();

    // 1. 检查 Redis 缓存
    const cacheKey = `${CACHE_PREFIXES.RATE_LIMIT}${ipAddress}:${endpoint}`;
    const cached = await redis.get(cacheKey);

    let rateLimitRecord;

    if (cached) {
      const data = JSON.parse(cached);
      // 检查是否在同一时间窗口内
      const windowStart = new Date(data.windowStart);
      const windowDuration = 15 * 60 * 1000; // 15分钟窗口

      if (now.getTime() - windowStart.getTime() < windowDuration) {
        // 在同一窗口内，增加尝试次数
        rateLimitRecord = {
          attempts: data.attempts + 1,
          windowStart,
          lastAttempt: now,
        };
      } else {
        // 新窗口，重置计数
        rateLimitRecord = {
          attempts: 1,
          windowStart: now,
          lastAttempt: now,
        };
      }
    } else {
      // 新记录
      rateLimitRecord = {
        attempts: 1,
        windowStart: now,
        lastAttempt: now,
      };
    }

    // 2. 判断是否需要阻止
    const maxAttempts = 5;
    const blockDuration = 30 * 60 * 1000; // 30分钟阻止
    const blocked = rateLimitRecord.attempts > maxAttempts;
    const blockUntil = blocked ? new Date(now.getTime() + blockDuration) : undefined;

    // 3. 更新数据库
    await prisma.rateLimit.upsert({
      where: {
        ipAddress_endpoint: {
          ipAddress,
          endpoint,
        },
      },
      update: {
        attempts: rateLimitRecord.attempts,
        lastAttempt: now,
        blocked,
        blockUntil,
        userId,
      },
      create: {
        ipAddress,
        endpoint,
        attempts: rateLimitRecord.attempts,
        lastAttempt: now,
        windowStart: rateLimitRecord.windowStart,
        blocked,
        blockUntil,
        userId,
      },
    });

    // 4. 更新 Redis 缓存
    const cacheData = {
      attempts: rateLimitRecord.attempts,
      windowStart: rateLimitRecord.windowStart.toISOString(),
      lastAttempt: now.toISOString(),
      blocked,
      blockUntil: blockUntil?.toISOString(),
    };

    await redis.setex(cacheKey, CACHE_TTL.RATE_LIMIT, JSON.stringify(cacheData));

    return {
      attempts: rateLimitRecord.attempts,
      blocked,
      blockUntil,
    };
  }

  /**
   * 检查是否被速率限制
   */
  static async isRateLimited(
    ipAddress: string,
    endpoint: string
  ): Promise<{
    limited: boolean;
    attempts: number;
    blockUntil?: Date;
  }> {
    const now = new Date();

    // 1. 检查 Redis 缓存
    const cacheKey = `${CACHE_PREFIXES.RATE_LIMIT}${ipAddress}:${endpoint}`;
    const cached = await redis.get(cacheKey);

    if (cached) {
      const data = JSON.parse(cached);
      if (data.blocked && data.blockUntil) {
        const blockUntil = new Date(data.blockUntil);
        if (blockUntil > now) {
          return {
            limited: true,
            attempts: data.attempts,
            blockUntil,
          };
        }
      }
    }

    // 2. 检查数据库
    const record = await prisma.rateLimit.findUnique({
      where: {
        ipAddress_endpoint: {
          ipAddress,
          endpoint,
        },
      },
      select: {
        attempts: true,
        blocked: true,
        blockUntil: true,
      },
    });

    if (record?.blocked && record.blockUntil && record.blockUntil > now) {
      return {
        limited: true,
        attempts: record.attempts,
        blockUntil: record.blockUntil,
      };
    }

    return {
      limited: false,
      attempts: record?.attempts || 0,
    };
  }

  /**
   * 重置速率限制
   */
  static async resetRateLimit(ipAddress: string, endpoint: string): Promise<void> {
    // 1. 删除数据库记录
    await prisma.rateLimit.deleteMany({
      where: {
        ipAddress,
        endpoint,
      },
    });

    // 2. 删除 Redis 缓存
    const cacheKey = `${CACHE_PREFIXES.RATE_LIMIT}${ipAddress}:${endpoint}`;
    await redis.del(cacheKey);
  }

  /**
   * 清理过期的速率限制记录
   */
  static async cleanupExpired(): Promise<number> {
    const deleted = await prisma.rateLimit.deleteMany({
      where: {
        blockUntil: {
          lt: new Date(),
        },
      },
    });

    return deleted.count;
  }
}

/**
 * 定期清理过期记录
 */
export class StorageCleanupService {
  private static cleanupInterval: NodeJS.Timeout | null = null;

  /**
   * 启动清理服务
   */
  static start(intervalMs = 3600000): void {
    // 默认1小时
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(async () => {
      try {
        const [blacklistDeleted, rateLimitDeleted] = await Promise.all([
          TokenBlacklistStorage.cleanupExpired(),
          RateLimitStorage.cleanupExpired(),
        ]);

        console.log(`清理过期记录: 黑名单 ${blacklistDeleted} 条, 速率限制 ${rateLimitDeleted} 条`);
      } catch (error) {
        console.error('清理过期记录失败:', error);
      }
    }, intervalMs);
  }

  /**
   * 停止清理服务
   */
  static stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }
}
