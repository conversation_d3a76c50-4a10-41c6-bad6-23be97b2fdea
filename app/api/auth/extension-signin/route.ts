/**
 * 浏览器扩展专用登录 API
 * 绕过 NextAuth.js 的重定向机制，直接返回 JSON 响应
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

// 请求体类型定义
interface ExtensionSignInRequest {
  email: string;
  password: string;
  clientType?: string;
}

// 响应体类型定义
interface ExtensionSignInResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string | null;
    avatar: string | null;
  };
  accessToken?: string;
  refreshToken?: string;
  expiresIn?: number;
  error?: string;
  message?: string;
  code?: string;
}

/**
 * 浏览器扩展登录 API
 * POST /api/auth/extension-signin
 */
export async function POST(request: NextRequest): Promise<NextResponse<ExtensionSignInResponse>> {
  try {
    // 解析请求体
    const body: ExtensionSignInRequest = await request.json();
    const { email, password, clientType } = body;

    // 输入验证
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必填字段',
          message: 'Missing required fields: email, password',
          code: 'MISSING_FIELDS',
        },
        { status: 400 }
      );
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱格式不正确',
          message: 'Invalid email format',
          code: 'INVALID_EMAIL_FORMAT',
        },
        { status: 422 }
      );
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        passwordHash: true,
        isActive: true,
        provider: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在',
          message: 'User not found',
          code: 'USER_NOT_FOUND',
        },
        { status: 401 }
      );
    }

    // 检查用户是否被禁用
    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: '账户已被禁用',
          message: 'Account is disabled',
          code: 'ACCOUNT_DISABLED',
        },
        { status: 401 }
      );
    }

    // 检查密码是否设置（OAuth 用户可能没有密码）
    if (!user.passwordHash) {
      return NextResponse.json(
        {
          success: false,
          error: '该账户需要通过第三方登录',
          message: 'This account requires OAuth sign-in',
          code: 'NO_PASSWORD_SET',
        },
        { status: 401 }
      );
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);

    if (!isValidPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名或密码错误',
          message: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS',
        },
        { status: 401 }
      );
    }

    // 获取 JWT 密钥
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not configured');
      return NextResponse.json(
        {
          success: false,
          error: '服务器配置错误',
          message: 'Server configuration error',
          code: 'SERVER_CONFIG_ERROR',
        },
        { status: 500 }
      );
    }

    // 生成 JWT 访问令牌 (1小时有效期)
    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        iat: Math.floor(Date.now() / 1000),
        type: 'access',
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    // 生成刷新令牌 (30天有效期)
    const refreshToken = jwt.sign(
      {
        sub: user.id,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000),
      },
      jwtSecret,
      { expiresIn: '30d' }
    );

    // 更新用户最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        provider: 'credentials',
      },
    });

    // 记录日志
    console.log(`Extension login successful: ${user.email} (${user.id})`);

    // 返回成功响应
    return NextResponse.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
        },
        accessToken,
        refreshToken,
        expiresIn: 3600, // 1 hour in seconds
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Extension signin error:', error);

    // 返回通用错误响应
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * 不支持 GET 请求
 */
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    {
      error: '此端点仅支持 POST 请求',
      message: 'This endpoint only supports POST requests',
    },
    { status: 405 }
  );
}

/**
 * 不支持其他 HTTP 方法
 */
export async function PUT(): Promise<NextResponse> {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
