#!/usr/bin/env node --loader ts-node/esm

/**
 * 小规模测试脚本 - 只处理前5个单词来验证整个流程
 */

import { join } from 'path';
import XLSX from 'xlsx';
import { PrismaClient } from '@prisma/client';
import { fetchDictionary } from '../../5-providers/gemini.js';
import { importFromDictionaryData } from '../llm-response-to-db/importDictionaryData.js';

const prismaClient = new PrismaClient();

async function testSmallSync() {
  console.log('🧪 开始小规模测试...\n');

  try {
    // 1. 读取Excel文件，只取前5个单词
    console.log('📖 步骤1: 读取Excel文件（前5个单词）');
    const excelPath = join(process.cwd(), './SUBTLEXusfrequencyabove1.xls');
    const workbook = XLSX.readFile(excelPath);
    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    const words: string[] = [];
    let extractedCount = 0;

    for (let i = 1; i < jsonData.length && extractedCount < 10; i++) {
      const row = jsonData[i] as any[];
      if (row && row[0] && typeof row[0] === 'string') {
        const word = row[0].trim().toLowerCase();
        if (word && /^[a-zA-Z]+$/.test(word)) {
          words.push(word);
          extractedCount++;
        }
      }
    }

    console.log(`✅ 提取的单词: ${words.join(', ')}\n`);

    // 2. 调用词典API（测试新的并发方式：2个请求，每个5个单词）
    console.log('🌐 步骤2: 测试并发API调用');

    const startTime = Date.now();

    // 分成两组，每组5个单词
    const words1 = words.slice(0, 5);
    const words2 = words.slice(5, 10);

    console.log(`📤 请求1: ${words1.join(',')}`);
    console.log(`📤 请求2: ${words2.join(',')}`);

    // 使用Promise.all并发调用
    const [result1, result2] = await Promise.all([
      fetchDictionary(words1.join(',')),
      fetchDictionary(words2.join(',')),
    ]);

    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;

    console.log(`⏱️  API调用耗时: ${executionTime.toFixed(2)}秒`);

    // 验证两个请求的结果
    if (result1.statusCode !== 200 || !result1.data) {
      throw new Error(`请求1失败: ${JSON.stringify(result1.error)}`);
    }
    if (result2.statusCode !== 200 || !result2.data) {
      throw new Error(`请求2失败: ${JSON.stringify(result2.error)}`);
    }

    console.log(`✅ 请求1成功: ${result1.data.wordCount} 个单词`);
    console.log(`✅ 请求2成功: ${result2.data.wordCount} 个单词\n`);

    // 合并两个结果
    const mergedData = {
      words: [...result1.data.words, ...result2.data.words],
      wordList: [...result1.data.wordList, ...result2.data.wordList],
      wordCount: result1.data.wordCount + result2.data.wordCount,
      suggests: [...(result1.data.suggests || []), ...(result2.data.suggests || [])],
      illegalWords: [...(result1.data.illegalWords || []), ...(result2.data.illegalWords || [])],
    };

    console.log(`🔄 合并结果:`);
    console.log(`  ✅ 有效单词: ${mergedData.wordCount} 个`);
    console.log(`  💡 建议单词: ${mergedData.suggests?.length || 0} 个`);
    console.log(`  ❌ 不合法单词: ${mergedData.illegalWords?.length || 0} 个\n`);

    // 3. 导入数据到数据库
    console.log('💾 步骤3: 导入数据到数据库');
    const importResult = await importFromDictionaryData(mergedData, prismaClient, {
      forceOverwrite: true,
      verbose: false, // 关闭详细日志
    });

    console.log(`📈 导入结果:`);
    console.log(`  ✅ 主要单词成功: ${importResult.successCount} 个`);
    console.log(`  💡 建议单词成功: ${importResult.suggestsSuccessCount} 个`);
    console.log(`  ❌ 失败: ${importResult.failureCount} 个`);
    console.log(
      `  📊 总成功导入: ${importResult.successCount + importResult.suggestsSuccessCount} 个单词`
    );

    if (importResult.errors.length > 0) {
      console.log('  🔍 错误详情:');
      importResult.errors.forEach((error, index) => {
        console.log(`    ${index + 1}. ${error.word}: ${error.error.message}`);
      });
    }

    // 4. 设置频率排名
    console.log('\n🏆 步骤4: 设置频率排名');
    for (let i = 0; i < words.length; i++) {
      const word = words[i];
      const rank = i + 1;

      try {
        const result = await prismaClient.vocabulary.updateMany({
          where: { word: word },
          data: { freqRank: rank },
        });

        if (result.count > 0) {
          // 测试脚本中显示所有排名设置（数量少）
          console.log(`📊 设置排名: "${word}" -> ${rank}`);
        } else {
          console.warn(`⚠️  未找到单词: "${word}"`);
        }
      } catch (error) {
        console.error(`❌ 设置排名失败 "${word}":`, error);
      }
    }

    // 5. 验证结果
    console.log('\n🔍 步骤5: 验证结果');
    for (const word of words) {
      const vocabulary = await prismaClient.vocabulary.findFirst({
        where: { word: word },
        include: {
          explains: {
            include: {
              definitions: true,
            },
          },
        },
      });

      if (vocabulary) {
        console.log(
          `✅ "${word}": 排名=${vocabulary.freqRank}, 词性数=${vocabulary.explains.length}`
        );
      } else {
        console.log(`❌ "${word}": 未找到`);
      }
    }

    console.log('\n🎉 小规模测试完成！');
  } catch (error) {
    console.error('\n💥 测试失败:', error);
    throw error;
  } finally {
    await prismaClient.$disconnect();
  }
}

// 运行测试
testSmallSync()
  .then(() => {
    console.log('\n✅ 测试成功完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 测试失败:', error);
    process.exit(1);
  });
