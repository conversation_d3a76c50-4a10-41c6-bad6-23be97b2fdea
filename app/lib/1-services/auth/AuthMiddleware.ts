/**
 * 双认证策略中间件
 * 支持Session（Web）和JWT（移动端/扩展）认证
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import jwt from 'jsonwebtoken';
// Note: authOptions is now defined inline in the NextAuth handler
// We'll pass authOptions as a parameter or create a separate config file
import type { NextAuthOptions } from 'next-auth';
import { tokenBlacklistService } from '@/lib/1-services/auth/TokenBlacklistService';
import { jwtValidationCache } from '@/lib/1-services/auth/JWTValidationCache';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { getJWTSecret } from '@/lib/utils/env';
import { redisRateLimiter } from '@/lib/utils/redis';
import { Logger } from '@/lib/utils/logger';
import {
  AuthContext,
  AuthenticatedUser,
  JWTPayload,
  UnauthorizedError,
  ForbiddenError,
  RateLimitError,
} from '@/lib/types/api-types';

interface AuthMiddlewareOptions {
  methods?: ('session' | 'jwt')[];
  clientTypes?: ('web' | 'mobile' | 'extension')[];
  scopes?: string[];
  rateLimit?: {
    limit: number;
    window: number; // in milliseconds
  };
}

interface AuthenticatedHandler {
  (request: NextRequest, context: AuthContext): Promise<NextResponse>;
}

export function requireAuth(options: AuthMiddlewareOptions = {}) {
  const {
    methods = ['session', 'jwt'],
    clientTypes = ['web', 'mobile', 'extension'],
    scopes = [],
    rateLimit,
  } = options;

  return function (handler: AuthenticatedHandler) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        // 1. 检查速率限制
        if (rateLimit) {
          await checkRateLimit(request, rateLimit);
        }

        // 2. 执行认证
        const authContext = await authenticateRequest(request, methods);

        // 3. 检查客户端类型权限
        if (!clientTypes.includes(authContext.clientType)) {
          throw new ForbiddenError(`Client type '${authContext.clientType}' not allowed`);
        }

        // 4. 检查权限作用域
        if (scopes.length > 0) {
          const hasRequiredScope = scopes.some((scope) =>
            authContext.scopes.includes(scope)
          );
          if (!hasRequiredScope) {
            throw new ForbiddenError(`Required scopes: ${scopes.join(', ')}`);
          }
        }

        // 5. 调用处理器
        return await handler(request, authContext);
      } catch (error) {
        return handleAuthError(error);
      }
    };
  };
}

/**
 * 主认证函数 - 优先检查Authorization头，后备Session认证
 */
async function authenticateRequest(
  request: NextRequest,
  allowedMethods: ('session' | 'jwt')[]
): Promise<AuthContext> {
  // 1. 优先检查Authorization头中的Bearer Token
  const authHeader = request.headers.get('authorization');
  if (authHeader?.startsWith('Bearer ') && allowedMethods.includes('jwt')) {
    const jwtAuth = await tryJWTAuth(request);
    if (jwtAuth) return jwtAuth;
  }

  // 2. 如果没有Bearer Token，检查Session Cookie
  if (allowedMethods.includes('session')) {
    const sessionAuth = await trySessionAuth(request);
    if (sessionAuth) return sessionAuth;
  }

  throw new UnauthorizedError('No valid authentication found');
}

/**
 * 尝试JWT认证
 */
async function tryJWTAuth(request: NextRequest): Promise<AuthContext | null> {
  try {
    const token = extractBearerToken(request);
    if (!token) return null;

    // 1. 验证JWT签名和基本结构
    const decoded = jwt.verify(token, getJWTSecret()) as JWTPayload;
    if (!decoded || !decoded.sub || !decoded.jti) return null;

    // 2. 检查缓存中的验证结果
    const cachedValidation = await jwtValidationCache.getCachedValidation(decoded.jti);
    if (cachedValidation) {
      if (!cachedValidation.isValid) {
        throw new UnauthorizedError('Token validation failed (cached)');
      }
      
      // 从缓存恢复认证上下文
      return createAuthContext(
        {
          id: cachedValidation.userId,
          name: null, // 缓存中没有存储name
          email: cachedValidation.email,
        },
        'jwt',
        cachedValidation.clientType,
        cachedValidation.scopes,
        request
      );
    }

    // 3. 检查令牌是否在黑名单中
    const isBlacklisted = await tokenBlacklistService.isBlacklisted(decoded.jti);
    if (isBlacklisted) {
      // 缓存失败结果
      await jwtValidationCache.cacheValidation(decoded.jti, decoded, false);
      throw new UnauthorizedError('Token has been revoked');
    }

    // 4. 检查令牌过期时间
    if (decoded.exp < Date.now() / 1000) {
      // 缓存失败结果
      await jwtValidationCache.cacheValidation(decoded.jti, decoded, false);
      throw new UnauthorizedError('Token has expired');
    }

    // 5. 检查用户强制过期（密码修改等）
    const shouldForceExpire = await tokenBlacklistService.shouldForceExpireUserTokens(
      decoded.sub,
      decoded.iat
    );
    if (shouldForceExpire) {
      // 缓存失败结果
      await jwtValidationCache.cacheValidation(decoded.jti, decoded, false);
      throw new UnauthorizedError('Token has been force expired due to security changes');
    }

    // 6. 获取用户信息
    const user = await getUserById(decoded.sub);
    if (!user || !user.isActive) {
      // 缓存失败结果
      await jwtValidationCache.cacheValidation(decoded.jti, decoded, false);
      throw new UnauthorizedError('User not found or inactive');
    }

    // 7. 缓存成功的验证结果
    await jwtValidationCache.cacheValidation(decoded.jti, decoded, true);

    // 8. 创建认证上下文
    return createAuthContext(
      {
        id: user.id,
        name: user.name,
        email: user.email,
      },
      'jwt',
      decoded.clientType,
      decoded.scopes || [],
      request
    );
  } catch (error) {
    if (error instanceof UnauthorizedError) {
      throw error;
    }
    // JWT解析失败，返回null让后续方法尝试
    return null;
  }
}

/**
 * 尝试Session认证
 */
async function trySessionAuth(request: NextRequest): Promise<AuthContext | null> {
  try {
    // Use getServerSession without authOptions for now
    // This will use the default NextAuth configuration
    const session = await getServerSession();
    if (!session?.user?.id) return null;

    const user = await getUserById(session.user.id);
    if (!user || !user.isActive) return null;

    return createAuthContext(
      {
        id: user.id,
        name: user.name,
        email: user.email,
      },
      'session',
      'web',
      ['word:read', 'word:write'], // Web用户默认权限
      request
    );
  } catch (error) {
    return null;
  }
}

/**
 * 从请求头提取Bearer Token
 */
function extractBearerToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader?.startsWith('Bearer ')) return null;
  return authHeader.substring(7);
}

/**
 * 获取用户信息
 */
async function getUserById(userId: string) {
  return await prisma.user.findUnique({
    where: { id: userId },
    select: {
      id: true,
      name: true,
      email: true,
      isActive: true,
    },
  });
}

/**
 * 创建认证上下文
 */
function createAuthContext(
  user: AuthenticatedUser,
  authMethod: 'session' | 'jwt',
  clientType: 'web' | 'mobile' | 'extension',
  scopes: string[],
  request: NextRequest
): AuthContext {
  return {
    user,
    authMethod,
    clientType,
    permissions: [], // 可以根据用户角色扩展
    scopes,
    ipAddress: getClientIP(request),
    userAgent: request.headers.get('user-agent') || '',
  };
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = request.headers.get('x-client-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || clientIP || 'unknown';
}

/**
 * Redis速率限制检查
 */
async function checkRateLimit(
  request: NextRequest,
  rateLimit: { limit: number; window: number }
): Promise<void> {
  const clientIP = getClientIP(request);
  const endpoint = request.nextUrl.pathname;
  const key = `rate_limit:${clientIP}:${endpoint}`;
  
  try {
    const result = await redisRateLimiter.checkRateLimit(
      key,
      rateLimit.limit,
      Math.floor(rateLimit.window / 1000) // 转换为秒
    );
    
    if (!result.allowed) {
      const waitMinutes = Math.ceil((result.resetTime - Date.now() / 1000) / 60);
      
      // 记录速率限制事件
      Logger.rateLimit('exceeded', {
        key,
        ipAddress: clientIP,
        endpoint,
        current: result.total,
        limit: rateLimit.limit,
        resetTime: result.resetTime,
      });
      
      throw new RateLimitError(`Rate limit exceeded. Try again in ${waitMinutes} minutes.`);
    }
    
    // 记录成功的速率限制检查
    Logger.rateLimit('check', {
      key,
      ipAddress: clientIP,
      endpoint,
      current: result.total,
      limit: rateLimit.limit,
      resetTime: result.resetTime,
    });
  } catch (error) {
    if (error instanceof RateLimitError) {
      throw error;
    }
    // Redis不可用时的降级处理：记录日志但不阻止请求
    console.warn('Redis速率限制检查失败，使用降级策略:', error);
  }
}

/**
 * 处理认证错误
 */
function handleAuthError(error: any): NextResponse {
  if (error instanceof UnauthorizedError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
      },
      { status: 401 }
    );
  }
  
  if (error instanceof ForbiddenError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
      },
      { status: 403 }
    );
  }
  
  if (error instanceof RateLimitError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
      },
      { status: 429 }
    );
  }
  
  // 未知错误
  console.error('Authentication middleware error:', error);
  return NextResponse.json(
    {
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
    },
    { status: 500 }
  );
}