#!/usr/bin/env tsx

/**
 * 测试Excel文件读取功能
 */

import { join } from 'path';
import XLSX from 'xlsx';

async function testExcelRead() {
  try {
    const excelPath = join(process.cwd(), 'app/lib/6-scripts/sync6w/SUBTLEXusfrequencyabove1.xls');
    console.log(`📁 读取Excel文件: ${excelPath}`);

    // 读取Excel文件
    const workbook = XLSX.readFile(excelPath);
    console.log(`📊 工作表数量: ${workbook.SheetNames.length}`);
    console.log(`📋 工作表名称: ${workbook.SheetNames.join(', ')}`);

    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // 将工作表转换为JSON数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    console.log(`📋 总行数: ${jsonData.length}`);
    console.log(`🔍 前10行数据:`);

    for (let i = 0; i < Math.min(10, jsonData.length); i++) {
      const row = jsonData[i] as any[];
      console.log(`  行 ${i + 1}: ${JSON.stringify(row)}`);
    }

    // 尝试提取前20个单词
    console.log(`\n🔤 尝试提取前20个单词:`);
    const words: string[] = [];
    let extractedCount = 0;

    for (let i = 1; i < jsonData.length && extractedCount < 20; i++) {
      const row = jsonData[i] as any[];
      if (row && row[0] && typeof row[0] === 'string') {
        const word = row[0].trim().toLowerCase();
        if (word && /^[a-zA-Z]+$/.test(word)) {
          words.push(word);
          extractedCount++;
          console.log(`  ${extractedCount}. ${word}`);
        }
      }
    }

    console.log(`\n✅ 成功提取 ${words.length} 个单词`);
  } catch (error) {
    console.error('❌ 测试失败:', error);
  }
}

testExcelRead();
