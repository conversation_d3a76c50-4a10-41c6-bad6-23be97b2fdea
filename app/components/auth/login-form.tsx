'use client';

import { useState, useRef, useEffect, useReducer } from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/lib/hooks/useAuth';
import { SignupConfirmationDialog } from '@/components/auth/signup-confirmation-dialog';
import { validateEmailStrict } from '@/lib/utils/email-validation';
import { Loader2 } from 'lucide-react';

// 表单状态类型定义
interface FormState {
  formData: {
    email: string;
    password: string;
  };
  formErrors: {
    email?: string;
    password?: string;
  };
  showSignupDialog: boolean;
  isCheckingUser: boolean;
}

type FormAction =
  | { type: 'UPDATE_FIELD'; field: string; value: string }
  | { type: 'SET_ERROR'; field: string; error: string }
  | { type: 'CLEAR_ERROR'; field: string }
  | { type: 'SET_CHECKING_USER'; checking: boolean }
  | { type: 'SHOW_SIGNUP_DIALOG'; show: boolean }
  | { type: 'CLEAR_ALL_ERRORS' };

// 表单状态管理reducer
function formReducer(state: FormState, action: FormAction): FormState {
  switch (action.type) {
    case 'UPDATE_FIELD':
      return {
        ...state,
        formData: { ...state.formData, [action.field]: action.value },
      };
    case 'SET_ERROR':
      return {
        ...state,
        formErrors: { ...state.formErrors, [action.field]: action.error },
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        formErrors: { ...state.formErrors, [action.field]: undefined },
      };
    case 'SET_CHECKING_USER':
      return { ...state, isCheckingUser: action.checking };
    case 'SHOW_SIGNUP_DIALOG':
      return { ...state, showSignupDialog: action.show };
    case 'CLEAR_ALL_ERRORS':
      return { ...state, formErrors: {} };
    default:
      return state;
  }
}

export function LoginForm({ className, ...props }: React.ComponentProps<'div'>) {
  const { login, loginWithOAuth, checkUserExists, registerAndLogin, loading } = useAuth();

  // 使用 useReducer 管理复杂状态
  const [state, dispatch] = useReducer(formReducer, {
    formData: { email: '', password: '' },
    formErrors: {},
    showSignupDialog: false,
    isCheckingUser: false,
  });

  // 用于 debounce 邮箱验证
  const emailValidationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器防止内存泄漏
  useEffect(() => {
    return () => {
      if (emailValidationTimeoutRef.current) {
        clearTimeout(emailValidationTimeoutRef.current);
      }
    };
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    dispatch({ type: 'UPDATE_FIELD', field: name, value });

    // Clear error when user starts typing
    if (state.formErrors[name as keyof typeof state.formErrors]) {
      dispatch({ type: 'CLEAR_ERROR', field: name });
    }

    // 实时邮箱验证（debounced）
    if (name === 'email') {
      // 清除之前的定时器
      if (emailValidationTimeoutRef.current) {
        clearTimeout(emailValidationTimeoutRef.current);
      }

      // 如果邮箱不为空，设置新的验证定时器
      if (value.trim()) {
        emailValidationTimeoutRef.current = setTimeout(() => {
          const emailValidation = validateEmailStrict(value.trim());
          if (!emailValidation.isValid) {
            dispatch({ type: 'SET_ERROR', field: 'email', error: emailValidation.error! });
          }
        }, 500); // 优化为 500ms 延迟
      }
    }
  };

  const validateForm = () => {
    dispatch({ type: 'CLEAR_ALL_ERRORS' });

    if (!state.formData.email) {
      dispatch({ type: 'SET_ERROR', field: 'email', error: '邮箱地址不能为空' });
      return false;
    } else {
      const emailValidation = validateEmailStrict(state.formData.email);
      if (!emailValidation.isValid) {
        dispatch({ type: 'SET_ERROR', field: 'email', error: emailValidation.error! });
        return false;
      }
    }

    if (!state.formData.password) {
      dispatch({ type: 'SET_ERROR', field: 'password', error: '密码不能为空' });
      return false;
    } else if (state.formData.password.length < 6) {
      dispatch({ type: 'SET_ERROR', field: 'password', error: '密码至少需要6位字符' });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      dispatch({ type: 'SET_CHECKING_USER', checking: true });

      // 先检查用户是否存在
      const userExists = await checkUserExists(state.formData.email);

      if (userExists) {
        // 用户存在，执行正常登录
        await login(state.formData);
      } else {
        // 用户不存在，显示确认对话框
        dispatch({ type: 'SHOW_SIGNUP_DIALOG', show: true });
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      dispatch({ type: 'SET_CHECKING_USER', checking: false });
    }
  };

  const handleConfirmSignup = async () => {
    try {
      // 执行注册并登录
      await registerAndLogin({
        email: state.formData.email,
        password: state.formData.password,
      });
      dispatch({ type: 'SHOW_SIGNUP_DIALOG', show: false });
    } catch (error) {
      console.error('Signup error:', error);
      // 不关闭对话框，让用户看到错误
    }
  };

  const handleOAuthLogin = async (provider: 'google' | 'github') => {
    try {
      await loginWithOAuth(provider);
    } catch (error) {
      console.error(`${provider} login error:`, error);
    }
  };

  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Welcome back</CardTitle>
          <CardDescription>Login with your Google or GitHub account</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-6">
              <div className="flex flex-col gap-4">
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleOAuthLogin('google')}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="size-4 animate-spin" />
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="size-4">
                      <path
                        d="M12.48 10.92v3.28h7.84c-.24 1.84-.853 3.187-1.787 4.133-1.147 1.147-2.933 2.4-6.053 2.4-4.827 0-8.6-3.893-8.6-8.72s3.773-8.72 8.6-8.72c2.6 0 4.507 1.027 5.907 2.347l2.307-2.307C18.747 1.44 16.133 0 12.48 0 5.867 0 .307 5.387.307 12s5.56 12 12.173 12c3.573 0 6.267-1.173 8.373-3.36 2.16-2.16 2.84-5.213 2.84-7.667 0-.76-.053-1.467-.173-2.053H12.48z"
                        fill="currentColor"
                      />
                    </svg>
                  )}
                  Login with Google
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => handleOAuthLogin('github')}
                  disabled={loading}
                >
                  {loading ? (
                    <Loader2 className="size-4 animate-spin" />
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" className="size-4">
                      <path
                        d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"
                        fill="currentColor"
                      />
                    </svg>
                  )}
                  Login with GitHub
                </Button>
              </div>
              <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                <span className="bg-card text-muted-foreground relative z-10 px-2">
                  Or continue with
                </span>
              </div>
              <div className="grid gap-6">
                <div className="grid gap-3">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={state.formData.email}
                    onChange={handleInputChange}
                    className={state.formErrors.email ? 'border-red-500' : ''}
                    autoComplete="email"
                    required
                  />
                  {state.formErrors.email && (
                    <p className="text-sm text-red-500">{state.formErrors.email}</p>
                  )}
                </div>
                <div className="grid gap-3">
                  <div className="flex items-center">
                    <Label htmlFor="password">Password</Label>
                    <Link
                      href="/forgot-password"
                      className="ml-auto text-sm underline-offset-4 hover:underline"
                    >
                      Forgot your password?
                    </Link>
                  </div>
                  <Input
                    id="password"
                    name="password"
                    type="password"
                    value={state.formData.password}
                    onChange={handleInputChange}
                    className={state.formErrors.password ? 'border-red-500' : ''}
                    autoComplete="current-password"
                    required
                  />
                  {state.formErrors.password && (
                    <p className="text-sm text-red-500">{state.formErrors.password}</p>
                  )}
                </div>
                <Button type="submit" className="w-full" disabled={loading || state.isCheckingUser}>
                  {state.isCheckingUser ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      检查用户中...
                    </>
                  ) : loading ? (
                    <>
                      <Loader2 className="mr-2 size-4 animate-spin" />
                      登录中...
                    </>
                  ) : (
                    '登录'
                  )}
                </Button>
              </div>
              <div className="text-center text-sm">
                Don&apos;t have an account?{' '}
                <Link href="/signup" className="underline underline-offset-4">
                  Sign up
                </Link>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>
      <div className="text-muted-foreground text-center text-xs text-balance">
        By clicking continue, you agree to our{' '}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          Terms of Service
        </a>{' '}
        and{' '}
        <a href="#" className="underline underline-offset-4 hover:text-primary">
          Privacy Policy
        </a>
        .
      </div>

      <SignupConfirmationDialog
        open={state.showSignupDialog}
        onOpenChange={(open) => dispatch({ type: 'SHOW_SIGNUP_DIALOG', show: open })}
        email={state.formData.email}
        onConfirm={handleConfirmSignup}
        loading={loading}
      />
    </div>
  );
}
