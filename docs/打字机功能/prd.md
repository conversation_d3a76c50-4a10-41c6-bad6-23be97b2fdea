# PRD: lucid-bd 项目 - 打字练习功能 2.0 增强

## 1. 目标与背景 (Goals & Context)

**目标:** 将 `lucid-bd` 项目中现有的“打字机”功能从一个基础MVP（Demo）提升为一个功能完善、可供用户正式使用的“正式MVP”。

**背景:** 当前的打字功能仅支持随机简单单词练习。本次增强旨在引入结构化的词库、智能化的生词复习机制和个性化的句子练习，以提供更全面、更有效的学习体验。

## 2. 功能需求 (Functional Requirements)

### Epic: 打字练习功能 2.0 (Typer Feature 2.0)

- **FR1: 系统化词库练习**

  - **FR1.1:** 系统应引入标准化的词库，至少包括大学四级 (CET-4)、六级 (CET-6) 和 GRE 词汇。
  - **FR1.2:** 用户应能够从可用的词库列表中选择一个进行打字练习。
  - **FR1.3:** “随机单词练习”模式应改造为从用户选择的词库中随机抽词。
  - **FR1.4:** 在进行标准词库练习时，用户应能够将当前单词一键“加入生词本”。

- **FR2: 智能化生词复习**

  - **FR2.1:** 系统需要为每个用户和每个单词建立一个“熟练度”追踪模型。 _(依赖于核心技术故事：用户-单词关联)_
  - **FR2.2:** 在“生词练习”中，系统需要记录用户的详细练习指标，包括但不限于：练习次数、具体错误、平均卡顿时间、总错误次数。
  - **FR2.3:** 系统应提供一个智能筛选算法，根据 FR2.2 中记录的指标，优先为用户推送他们最不熟练的单词。
  - **FR2.4:** 用户应有功能可以手动“标注”单词，被标注的单词其复习优先级应显著高于其他单词。

- **FR3: 个性化句子练习**

  - **FR3.1:** 用户应能够在“句子练习”模式中收藏（Favorite/Save）他们喜欢的句子。 _(依赖于核心技术故事：用户-句子关联)_
  - **FR3.2:** 系统需要持久化存储用户和他们收藏的句子之间的关联关系。
  - **FR3.3:** 系统应提供一种练习模式，允许用户只练习自己收藏过的句子。

- **FR4: 核心技术故事 (Dependencies)**
  - **FR4.1:** **[前置任务]** 必须先实现用户与“单词熟练度”数据表的关联逻辑。
  - **FR4.2:** **[前置任务]** 必须先实现用户与“句子收藏”数据表的关联逻辑。
  - **FR4.3:** **[前置任务]** 必须编写并执行CET-4, CET-6, GRE等核心词库的数据初始化脚本。

## 3. 非功能性需求 (Non-Functional Requirements)

- **NFR1: 性能 (Performance)**

  - **NFR1.1:** 对于大型词库（如GRE），系统必须采用分页或分块加载，确保用户选择词库后的初始加载时间在2秒以内。
  - **NFR1.2:** 打字过程中的按键响应必须是即时的（<100ms延迟）。

- **NFR2: 用户体验 (User Experience)**

  - **NFR2.1:** 在“词汇练习”和“生词练习”的每一轮结束后，系统应展示一个总结/成功页面。
  - **NFR2.2:** 总结页面必须包含以下指标：整体打字速度 (WPM)、出错的单词列表及各单词的出错次数、练习过程中每个单词所花费的时间。
  - **NFR2.3:** 系统需要提供一个历史图表，可视化用户打字速度（WPM）随时间的变化趋势。
  - **NFR2.4:** “生词练习”模式的单词列表，**只应展示**用户手动“标注”过或在练习中出过错的单词。
  - **NFR2.5:** “句子练习”模式在完成一句后，应直接过渡到下一句，**不显示**中间的总结页面。

- **NFR3: 兼容性与依赖逻辑 (Compatibility & Dependencies)**
  - **NFR3.1:** 整个打字练习功能的用户界面必须是**响应式**的，能在桌面和移动设备上良好运行。
  - **NFR3.2:** 用户**必须登录**后才能访问所有打字练习页面。
  - **NFR3.3:** 如果用户的生词本为空，“生词练习”的入口或页面应被隐藏或禁用。
  - **NFR3.4:** 如果用户没有收藏任何句子，“句子练习”模式应从一个默认的公共句库中随机提供句子。

## 4. 对现有功能的影响

- **定义:** 本次开发旨在将项目从Demo提升至正式MVP，需对现有功能进行评估。
- **要求:** 在后续的故事拆分中，每个与现有功能相关的部分都需明确其状态：
  - **保留并兼容 (Retain & Compatible)**
  - **重构升级 (Refactor & Upgrade)**
  - **废弃取代 (Deprecate & Replace)**
