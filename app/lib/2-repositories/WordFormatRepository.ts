/**
 * 词形仓储实现
 * 负责词形相关的数据访问操作
 */

import { prisma } from '../4-infrastructure/database/prisma';
import { WordFormatData } from '../3-domain/entities/Vocabulary';
import { IWordFormatRepository } from './interfaces/IRepository';

export class WordFormatRepository implements IWordFormatRepository {
  /**
   * 根据ID查找词形
   */
  async findById(id: number): Promise<WordFormatData | null> {
    const data = await prisma.wordFormat.findUnique({
      where: { id },
    });

    return data ? this.mapToData(data) : null;
  }

  /**
   * 根据词形查找
   */
  async findByForm(form: string): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.findMany({
      where: { form: form.toLowerCase() },
      orderBy: [{ name: 'asc' }, { id: 'asc' }],
    });

    return data.map((item) => this.mapToData(item));
  }

  /**
   * 根据词汇ID查找所有词形
   */
  async findByVocabularyId(vocabularyId: number): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.findMany({
      where: { vocabularyId },
      orderBy: [{ name: 'asc' }, { id: 'asc' }],
    });

    return data.map((item) => this.mapToData(item));
  }

  /**
   * 根据基础形式ID查找派生形式
   */
  async findByBaseFormId(baseFormId: number): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.findMany({
      where: { baseFormId },
      orderBy: [{ name: 'asc' }, { id: 'asc' }],
    });

    return data.map((item) => this.mapToData(item));
  }

  /**
   * 查找主要形式（直接关联vocabulary的形式）
   */
  async findMainFormsByForm(form: string): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.findMany({
      where: {
        form: form.toLowerCase(),
        vocabularyId: { not: null },
      },
      orderBy: [{ name: 'asc' }, { id: 'asc' }],
    });

    return data.map((item) => this.mapToData(item));
  }

  /**
   * 查找派生形式（通过baseFormId关联的形式）
   */
  async findDerivedFormsByForm(form: string): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.findMany({
      where: {
        form: form.toLowerCase(),
        vocabularyId: null,
        baseFormId: { not: null },
      },
      orderBy: [{ name: 'asc' }, { id: 'asc' }],
    });

    return data.map((item) => this.mapToData(item));
  }

  /**
   * 获取所有相关形式（主要形式 + 派生形式）
   */
  async findAllRelatedForms(vocabularyId: number): Promise<WordFormatData[]> {
    // 首先获取主要形式
    const mainForms = await this.findByVocabularyId(vocabularyId);
    const mainFormIds = mainForms.map((f) => f.id);

    // 然后获取派生形式
    const derivedForms = await prisma.wordFormat.findMany({
      where: {
        baseFormId: { in: mainFormIds },
        vocabularyId: null,
      },
      orderBy: [{ name: 'asc' }, { id: 'asc' }],
    });

    // 合并结果
    return [...mainForms, ...derivedForms.map((item) => this.mapToData(item))];
  }

  /**
   * 根据形式名称查找
   */
  async findByFormName(name: string, limit: number = 50): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.findMany({
      where: { name },
      orderBy: [{ form: 'asc' }, { id: 'asc' }],
      take: limit,
    });

    return data.map((item) => this.mapToData(item));
  }

  /**
   * 批量创建词形
   */
  async createMany(wordFormats: Omit<WordFormatData, 'id'>[]): Promise<WordFormatData[]> {
    const data = await prisma.wordFormat.createMany({
      data: wordFormats.map((wf) => ({
        name: wf.name,
        form: wf.form.toLowerCase(),
        vocabularyId: wf.vocabularyId,
        baseFormId: wf.baseFormId,
      })),
    });

    // 由于createMany不返回创建的记录，我们需要重新查询
    // 这里简化处理，实际项目中可能需要更精确的查询
    const createdIds = await prisma.wordFormat.findMany({
      where: {
        form: { in: wordFormats.map((wf) => wf.form.toLowerCase()) },
      },
      orderBy: { id: 'desc' },
      take: wordFormats.length,
    });

    return createdIds.map((item) => this.mapToData(item));
  }

  /**
   * 保存词形
   */
  async save(wordFormat: WordFormatData): Promise<WordFormatData> {
    const data = await prisma.wordFormat.upsert({
      where: { id: wordFormat.id },
      update: {
        name: wordFormat.name,
        form: wordFormat.form.toLowerCase(),
        vocabularyId: wordFormat.vocabularyId,
        baseFormId: wordFormat.baseFormId,
      },
      create: {
        name: wordFormat.name,
        form: wordFormat.form.toLowerCase(),
        vocabularyId: wordFormat.vocabularyId,
        baseFormId: wordFormat.baseFormId,
      },
    });

    return this.mapToData(data);
  }

  /**
   * 删除词形
   */
  async delete(id: number): Promise<boolean> {
    try {
      await prisma.wordFormat.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查词形是否存在
   */
  async exists(id: number): Promise<boolean> {
    const count = await prisma.wordFormat.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * 将数据库数据映射为领域数据
   */
  private mapToData(data: any): WordFormatData {
    return {
      id: data.id,
      name: data.name,
      form: data.form,
      vocabularyId: data.vocabularyId,
      baseFormId: data.baseFormId,
    };
  }
}
