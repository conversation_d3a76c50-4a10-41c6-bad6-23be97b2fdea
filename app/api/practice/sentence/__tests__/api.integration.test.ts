/**
 * 句子练习 API 集成测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { NextRequest } from 'next/server';
import { PrismaClient } from '@prisma/client';
import { GET as getSentence } from '@/api/practice/sentence/route';
import { POST as saveFavorite, DELETE as removeFavorite } from '@/api/practice/sentence/[sentenceId]/favorite/route';
import { POST as recordPractice } from '@/api/practice/sentence/[sentenceId]/practice/route';
import { GET as getFavorites } from '@/api/user/favorites/sentences/route';

// Mock next-auth
vi.mock('next-auth/next', () => ({
  getServerSession: vi.fn()
}));

// Mock authOptions
vi.mock('@/api/auth/[...nextauth]/route', () => ({
  authOptions: {}
}));

// Import mocked function
import { getServerSession } from 'next-auth/next';

const prisma = new PrismaClient();
const mockUserId = 'test-user-123';

describe('Sentence Practice API', () => {
  let testSentenceId: number;

  beforeEach(async () => {
    // Mock认证
    (getServerSession as any).mockResolvedValue({
      user: { id: mockUserId }
    });

    // 清理测试数据
    await prisma.userSavedSentence.deleteMany();
    await prisma.sentence.deleteMany();
    await prisma.user.deleteMany();

    // 创建测试用户
    await prisma.user.create({
      data: {
        id: mockUserId,
        email: '<EMAIL>',
        name: 'Test User',
        isActive: true
      }
    });

    // 创建测试句子
    const sentence = await prisma.sentence.create({
      data: {
        content: 'This is a test sentence for API testing.',
        difficulty: 5,
        category: 'technical',
        source: 'system',
        length: 42,
        favoriteCount: 0,
        isActive: true
      }
    });
    testSentenceId = sentence.id;
  });

  afterEach(async () => {
    // 清理测试数据
    await prisma.userSavedSentence.deleteMany();
    await prisma.sentence.deleteMany();
    await prisma.user.deleteMany();
  });

  describe('GET /api/practice/sentence', () => {
    it('should return a random practice sentence', async () => {
      // Debug: Check if we can query sentence with relations
      const sentenceWithRelations = await prisma.sentence.findMany({
        where: { isActive: true },
        include: {
          savedByUsers: {
            where: { userId: mockUserId },
            select: {
              practiceCount: true,
              lastPracticedAt: true
            }
          }
        },
        take: 1
      });
      console.log('Sentence with relations query result:', JSON.stringify(sentenceWithRelations, null, 2));

      const request = new NextRequest('http://localhost:3000/api/practice/sentence');
      const response = await getSentence(request);
      const data = await response.json();

      // Debug: log the actual response for debugging
      console.log('Response status:', response.status);
      console.log('Response data:', JSON.stringify(data, null, 2));

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.sentences).toBeDefined();
      expect(data.data.sentences[0]).toBeDefined();
      expect(data.data.sentences[0].content).toBe('This is a test sentence for API testing.');
      expect(data.data.sentences[0].isSavedByUser).toBe(false);
    });

    it('should filter sentences by difficulty', async () => {
      const request = new NextRequest('http://localhost:3000/api/practice/sentence?difficulty=5');
      const response = await getSentence(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.sentences[0].difficulty).toBe(5);
    });

    it('should return 401 for unauthenticated requests', async () => {
      (getServerSession as any).mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/practice/sentence');
      const response = await getSentence(request);

      expect(response.status).toBe(401);
    });
  });

  describe('POST /api/practice/sentence/[sentenceId]/favorite', () => {
    it('should save sentence to favorites', async () => {
      const request = new NextRequest(`http://localhost:3000/api/practice/sentence/${testSentenceId}/favorite`, {
        method: 'POST'
      });

      const response = await saveFavorite(request, {
        params: { sentenceId: testSentenceId.toString() }
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.userSavedSentence).toBeDefined();
      expect(data.data.userSavedSentence.sentenceId).toBe(testSentenceId);

      // 验证数据库中的数据
      const saved = await prisma.userSavedSentence.findFirst({
        where: { userId: mockUserId, sentenceId: testSentenceId }
      });
      expect(saved).toBeDefined();
    });

    it('should return 409 for already saved sentence', async () => {
      // 先保存一次
      await prisma.userSavedSentence.create({
        data: {
          userId: mockUserId,
          sentenceId: testSentenceId
        }
      });

      const request = new NextRequest(`http://localhost:3000/api/practice/sentence/${testSentenceId}/favorite`, {
        method: 'POST'
      });

      const response = await saveFavorite(request, {
        params: { sentenceId: testSentenceId.toString() }
      });

      expect(response.status).toBe(409);
    });
  });

  describe('DELETE /api/practice/sentence/[sentenceId]/favorite', () => {
    beforeEach(async () => {
      // 预先保存句子到收藏
      await prisma.userSavedSentence.create({
        data: {
          userId: mockUserId,
          sentenceId: testSentenceId
        }
      });
    });

    it('should remove sentence from favorites', async () => {
      const request = new NextRequest(`http://localhost:3000/api/practice/sentence/${testSentenceId}/favorite`, {
        method: 'DELETE'
      });

      const response = await removeFavorite(request, {
        params: { sentenceId: testSentenceId.toString() }
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);

      // 验证数据库中的数据已删除
      const saved = await prisma.userSavedSentence.findFirst({
        where: { userId: mockUserId, sentenceId: testSentenceId }
      });
      expect(saved).toBeNull();
    });

    it('should return 404 for non-saved sentence', async () => {
      // 先删除收藏
      await prisma.userSavedSentence.deleteMany({
        where: { userId: mockUserId, sentenceId: testSentenceId }
      });

      const request = new NextRequest(`http://localhost:3000/api/practice/sentence/${testSentenceId}/favorite`, {
        method: 'DELETE'
      });

      const response = await removeFavorite(request, {
        params: { sentenceId: testSentenceId.toString() }
      });

      expect(response.status).toBe(404);
    });
  });

  describe('POST /api/practice/sentence/[sentenceId]/practice', () => {
    beforeEach(async () => {
      // 预先保存句子到收藏
      await prisma.userSavedSentence.create({
        data: {
          userId: mockUserId,
          sentenceId: testSentenceId
        }
      });
    });

    it('should record sentence practice', async () => {
      const request = new NextRequest(`http://localhost:3000/api/practice/sentence/${testSentenceId}/practice`, {
        method: 'POST'
      });

      const response = await recordPractice(request, {
        params: { sentenceId: testSentenceId.toString() }
      });
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.userSavedSentence.practiceCount).toBe(1);

      // 验证数据库中的数据
      const saved = await prisma.userSavedSentence.findFirst({
        where: { userId: mockUserId, sentenceId: testSentenceId }
      });
      expect(saved?.practiceCount).toBe(1);
      expect(saved?.lastPracticedAt).toBeDefined();
    });

    it('should return 400 for non-saved sentence', async () => {
      // 删除收藏
      await prisma.userSavedSentence.deleteMany({
        where: { userId: mockUserId, sentenceId: testSentenceId }
      });

      const request = new NextRequest(`http://localhost:3000/api/practice/sentence/${testSentenceId}/practice`, {
        method: 'POST'
      });

      const response = await recordPractice(request, {
        params: { sentenceId: testSentenceId.toString() }
      });

      expect(response.status).toBe(400);
    });
  });

  describe('GET /api/user/favorites/sentences', () => {
    beforeEach(async () => {
      // 创建多个收藏句子
      for (let i = 0; i < 3; i++) {
        const sentence = await prisma.sentence.create({
          data: {
            content: `Favorite test sentence ${i + 1}.`,
            difficulty: i + 1,
            category: 'daily',
            source: 'system',
            length: 25,
            favoriteCount: 0,
            isActive: true
          }
        });

        await prisma.userSavedSentence.create({
          data: {
            userId: mockUserId,
            sentenceId: sentence.id,
            practiceCount: i
          }
        });
      }
    });

    it('should return user favorite sentences', async () => {
      const request = new NextRequest('http://localhost:3000/api/user/favorites/sentences');
      const response = await getFavorites(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.sentences).toHaveLength(3);
      expect(data.data.pagination).toBeDefined();
      expect(data.data.pagination.total).toBe(3);
    });

    it('should filter favorites by difficulty', async () => {
      const request = new NextRequest('http://localhost:3000/api/user/favorites/sentences?difficulty=1');
      const response = await getFavorites(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.sentences).toHaveLength(1);
      expect(data.data.sentences[0].sentence.difficulty).toBe(1);
    });

    it('should paginate favorite sentences', async () => {
      const request = new NextRequest('http://localhost:3000/api/user/favorites/sentences?page=1&limit=2');
      const response = await getFavorites(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.data.sentences).toHaveLength(2);
      expect(data.data.pagination.page).toBe(1);
      expect(data.data.pagination.limit).toBe(2);
      expect(data.data.pagination.hasMore).toBe(true);
    });
  });
});