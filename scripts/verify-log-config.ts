#!/usr/bin/env tsx

/**
 * 日志配置验证脚本
 * 帮助用户验证和调试日志级别配置
 */

import { config } from 'dotenv';
import { resolve } from 'path';

// 加载环境变量
const envPath = resolve(process.cwd(), '.env');
config({ path: envPath });

async function main() {
  console.log('🔧 日志配置验证工具\n');
  console.log('='.repeat(60));

  // 1. 显示关键环境变量
  console.log('📋 关键环境变量:');
  console.log(`   LOG_LEVEL: ${process.env.LOG_LEVEL} (期望: 1)`);
  console.log(`   DEBUG_MODE: ${process.env.DEBUG_MODE} (建议: false)`);
  console.log(`   HIGH_CONCURRENCY_MODE: ${process.env.HIGH_CONCURRENCY_MODE}`);
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log('');

  console.log('📋 日志开关状态:');
  console.log(`   ENABLE_CACHE_LOGS: ${process.env.ENABLE_CACHE_LOGS}`);
  console.log(`   ENABLE_DETAILED_CACHE_LOGS: ${process.env.ENABLE_DETAILED_CACHE_LOGS}`);
  console.log(`   ENABLE_REDIS_POOL_LOGS: ${process.env.ENABLE_REDIS_POOL_LOGS}`);
  console.log(`   ENABLE_PERFORMANCE_LOGS: ${process.env.ENABLE_PERFORMANCE_LOGS}`);

  // 2. 导入并检查Logger配置
  console.log('\n🔧 Logger实际配置:');
  try {
    const { Logger } = await import('../app/lib/utils/logger');

    // 重新初始化配置以确保使用最新的环境变量
    Logger.reinitializeConfig();

    const config = Logger.getConfig();
    const envInfo = Logger.getEnvironmentInfo();

    console.log('   实际配置:');
    console.log(`     level: ${config.level} (0=ERROR, 1=WARN, 2=INFO, 3=DEBUG, 4=TRACE)`);
    console.log(`     samplingRate: ${config.samplingRate}`);
    console.log(`     enablePerformanceLogs: ${config.enablePerformanceLogs}`);
    console.log(`     enableRedisPoolLogs: ${config.enableRedisPoolLogs}`);
    console.log(`     enableCacheLogs: ${config.enableCacheLogs}`);
    console.log(`     debugMode: ${config.debugMode}`);
    console.log(`     highConcurrencyMode: ${config.highConcurrencyMode}`);

    console.log('\n   环境信息:');
    console.log(`     nodeEnv: ${envInfo.nodeEnv}`);
    console.log(`     debugMode: ${envInfo.debugMode}`);
    console.log(`     highConcurrencyMode: ${envInfo.highConcurrencyMode}`);
    console.log(`     isProduction: ${envInfo.isProduction}`);

    // 3. 分析配置问题
    console.log('\n🔍 配置分析:');

    const expectedLevel = parseInt(process.env.LOG_LEVEL || '2');
    if (config.level !== expectedLevel) {
      console.log(`   ⚠️  日志级别被覆盖: 期望 ${expectedLevel}, 实际 ${config.level}`);

      if (config.debugMode) {
        console.log(`   🐛 原因: DEBUG_MODE=true 强制设置为 DEBUG 级别 (3)`);
        console.log(`   💡 解决: 设置 DEBUG_MODE=false`);
      }

      if (config.highConcurrencyMode) {
        console.log(`   🚀 原因: HIGH_CONCURRENCY_MODE=true 强制设置为 WARN 级别 (1)`);
      }
    } else {
      console.log(`   ✅ 日志级别配置正确: ${config.level}`);
    }

    // 4. 测试日志输出
    console.log('\n🧪 测试日志输出:');
    console.log('   (只有符合当前配置的日志会显示)');
    console.log('');

    console.log('   测试 ERROR 级别:');
    Logger.error('这是一个错误日志测试');

    console.log('   测试 WARN 级别:');
    Logger.warn('这是一个警告日志测试');

    console.log('   测试 INFO 级别:');
    Logger.info('这是一个信息日志测试');

    console.log('   测试 DEBUG 级别:');
    Logger.debug('这是一个调试日志测试');

    console.log('   测试性能日志:');
    Logger.performance('性能日志测试', { responseTime: 123 });

    console.log('   测试缓存日志:');
    Logger.cache('缓存日志测试', { key: 'test', hitTime: 5.5 });

    console.log('   测试Redis连接池日志:');
    Logger.redisPool('Redis连接池日志测试', { borrowed: 1 });

    // 5. 提供建议
    console.log('\n💡 配置建议:');

    if (config.debugMode && expectedLevel <= 1) {
      console.log('   🔧 要减少日志输出，建议设置:');
      console.log('      DEBUG_MODE=false');
      console.log('      ENABLE_CACHE_LOGS=false');
      console.log('      ENABLE_REDIS_POOL_LOGS=false');
      console.log('      ENABLE_DETAILED_CACHE_LOGS=false');
    }

    if (config.level > 1) {
      console.log('   📝 当前配置会输出较多日志');
      console.log('   🚀 如需最小日志输出，可设置 HIGH_CONCURRENCY_MODE=true');
    }

    if (config.level <= 1) {
      console.log('   ✅ 当前配置适合生产环境，日志输出较少');
    }
  } catch (error) {
    console.error('❌ 导入Logger失败:', error);
  }

  console.log('\n✅ 验证完成');
  console.log('\n📖 说明:');
  console.log('   - 如果看到意外的日志输出，请检查上述分析中的建议');
  console.log('   - 修改 .env 文件后需要重启应用才能生效');
  console.log('   - 可以重复运行此脚本来验证配置更改');
}

main().catch(console.error);
