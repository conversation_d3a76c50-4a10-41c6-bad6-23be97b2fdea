/**
 * 应用层并发控制器
 * 基于性能测试结果，限制同时处理的请求数量以优化性能
 */

interface QueuedRequest<T> {
  id: string;
  resolve: (value: T) => void;
  reject: (error: Error) => void;
  operation: () => Promise<T>;
  timestamp: number;
}

export class ConcurrencyLimiter {
  private static instance: ConcurrencyLimiter;
  private activeRequests = 0;
  private requestQueue: QueuedRequest<any>[] = [];
  private readonly maxConcurrency: number;
  private readonly queueTimeout: number;
  private readonly maxQueueSize: number;

  private constructor(
    maxConcurrency: number = 25, // 基于测试数据，25个并发是最佳性能点
    queueTimeout: number = 10000, // 10秒队列超时
    maxQueueSize: number = 100 // 最大队列长度
  ) {
    this.maxConcurrency = maxConcurrency;
    this.queueTimeout = queueTimeout;
    this.maxQueueSize = maxQueueSize;
  }

  static getInstance(): ConcurrencyLimiter {
    if (!ConcurrencyLimiter.instance) {
      ConcurrencyLimiter.instance = new ConcurrencyLimiter();
    }
    return ConcurrencyLimiter.instance;
  }

  /**
   * 执行受并发限制的操作
   */
  async execute<T>(operation: () => Promise<T>, operationId?: string): Promise<T> {
    const requestId = operationId || Math.random().toString(36).substring(2, 10);

    // 检查队列是否已满
    if (this.requestQueue.length >= this.maxQueueSize) {
      throw new Error(`Request queue is full (${this.maxQueueSize}). Please try again later.`);
    }

    // 如果当前并发数未达到限制，直接执行
    if (this.activeRequests < this.maxConcurrency) {
      return this.executeImmediately(operation, requestId);
    }

    // 否则加入队列等待
    return this.enqueueRequest(operation, requestId);
  }

  /**
   * 立即执行操作
   */
  private async executeImmediately<T>(operation: () => Promise<T>, requestId: string): Promise<T> {
    this.activeRequests++;

    try {
      const startTime = performance.now();
      const result = await operation();
      const executionTime = performance.now() - startTime;

      // 记录性能指标
      this.logPerformanceMetrics(requestId, executionTime, 'immediate');

      return result;
    } finally {
      this.activeRequests--;
      this.processQueue(); // 处理队列中的下一个请求
    }
  }

  /**
   * 将请求加入队列
   */
  private enqueueRequest<T>(operation: () => Promise<T>, requestId: string): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const queuedRequest: QueuedRequest<T> = {
        id: requestId,
        resolve,
        reject,
        operation,
        timestamp: Date.now(),
      };

      this.requestQueue.push(queuedRequest);

      // 设置队列超时
      setTimeout(() => {
        this.timeoutRequest(requestId);
      }, this.queueTimeout);

      this.logQueueStatus(requestId, 'enqueued');
    });
  }

  /**
   * 处理队列中的请求
   */
  private processQueue(): void {
    if (this.requestQueue.length === 0 || this.activeRequests >= this.maxConcurrency) {
      return;
    }

    const queuedRequest = this.requestQueue.shift();
    if (!queuedRequest) return;

    const queueWaitTime = Date.now() - queuedRequest.timestamp;

    // 检查请求是否已超时
    if (queueWaitTime > this.queueTimeout) {
      queuedRequest.reject(
        new Error(`Request ${queuedRequest.id} timed out in queue after ${queueWaitTime}ms`)
      );
      this.processQueue(); // 继续处理下一个请求
      return;
    }

    this.activeRequests++;

    // 执行队列中的请求
    queuedRequest
      .operation()
      .then((result) => {
        const totalTime = Date.now() - queuedRequest.timestamp;
        this.logPerformanceMetrics(queuedRequest.id, totalTime, 'queued', queueWaitTime);
        queuedRequest.resolve(result);
      })
      .catch((error) => {
        queuedRequest.reject(error);
      })
      .finally(() => {
        this.activeRequests--;
        this.processQueue(); // 处理队列中的下一个请求
      });
  }

  /**
   * 超时处理
   */
  private timeoutRequest(requestId: string): void {
    const index = this.requestQueue.findIndex((req) => req.id === requestId);
    if (index !== -1) {
      const queuedRequest = this.requestQueue.splice(index, 1)[0];
      queuedRequest.reject(new Error(`Request ${requestId} timed out in queue`));
    }
  }

  /**
   * 记录性能指标
   */
  private logPerformanceMetrics(
    requestId: string,
    executionTime: number,
    type: 'immediate' | 'queued',
    queueWaitTime?: number
  ): void {
    const metrics = {
      requestId,
      type,
      executionTime: Math.round(executionTime),
      queueWaitTime: queueWaitTime ? Math.round(queueWaitTime) : 0,
      activeRequests: this.activeRequests,
      queueLength: this.requestQueue.length,
      timestamp: new Date().toISOString(),
    };

    // 只在DEBUG模式下记录详细日志
    if (process.env.LOG_LEVEL === '3') {
      console.log(`[CONCURRENCY-${requestId}] 📊 性能指标:`, metrics);
    }
  }

  /**
   * 记录队列状态
   */
  private logQueueStatus(requestId: string, action: 'enqueued' | 'processed'): void {
    if (process.env.LOG_LEVEL === '3') {
      console.log(`[CONCURRENCY-${requestId}] 🔄 队列${action}:`, {
        queueLength: this.requestQueue.length,
        activeRequests: this.activeRequests,
        maxConcurrency: this.maxConcurrency,
      });
    }
  }

  /**
   * 获取当前状态
   */
  getStatus(): {
    activeRequests: number;
    queueLength: number;
    maxConcurrency: number;
    utilizationRate: number;
  } {
    return {
      activeRequests: this.activeRequests,
      queueLength: this.requestQueue.length,
      maxConcurrency: this.maxConcurrency,
      utilizationRate: (this.activeRequests / this.maxConcurrency) * 100,
    };
  }

  /**
   * 动态调整并发限制
   */
  adjustConcurrency(newLimit: number): void {
    if (newLimit > 0 && newLimit <= 100) {
      const oldLimit = this.maxConcurrency;
      (this as any).maxConcurrency = newLimit;

      console.log(`🔧 并发限制已调整: ${oldLimit} -> ${newLimit}`);

      // 如果新限制更高，立即处理队列
      if (newLimit > oldLimit) {
        this.processQueue();
      }
    }
  }

  /**
   * 清空队列（紧急情况使用）
   */
  clearQueue(): void {
    const queuedRequests = this.requestQueue.splice(0);
    queuedRequests.forEach((req) => {
      req.reject(new Error('Request cancelled due to queue clear'));
    });

    console.log(`🧹 已清空队列，取消了 ${queuedRequests.length} 个等待请求`);
  }
}

/**
 * 全局并发限制器实例
 */
export const concurrencyLimiter = ConcurrencyLimiter.getInstance();

/**
 * 并发控制装饰器
 */
export function withConcurrencyLimit<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  operationName?: string
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const operationId = operationName || fn.name || 'unknown';
    return concurrencyLimiter.execute(() => fn(...args), operationId);
  };
}
