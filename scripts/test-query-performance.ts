/**
 * 测试不同查询策略的性能差异
 * 对比简单查询 vs 复杂JOIN查询的性能
 */

import { prisma } from '../app/lib/4-infrastructure/database/prisma';

interface QueryPerformanceResult {
  queryType: string;
  averageTime: number;
  minTime: number;
  maxTime: number;
  totalTime: number;
  iterations: number;
}

// 简单查询 - 只查询基础数据
async function simpleQuery(word: string) {
  const startTime = performance.now();

  const result = await prisma.vocabulary.findFirst({
    where: { word: word.toLowerCase() },
    select: {
      id: true,
      word: true,
      phonetics: true,
      freqRank: true,
    },
  });

  const endTime = performance.now();
  return { result, time: endTime - startTime };
}

// 中等复杂查询 - 包含基本关联
async function mediumQuery(word: string) {
  const startTime = performance.now();

  const result = await prisma.vocabulary.findFirst({
    where: { word: word.toLowerCase() },
    include: {
      explains: {
        include: {
          definitions: true,
        },
        orderBy: { id: 'asc' },
      },
    },
  });

  const endTime = performance.now();
  return { result, time: endTime - startTime };
}

// 复杂查询 - 当前的完整查询
async function complexQuery(word: string) {
  const startTime = performance.now();

  const result = await prisma.vocabulary.findFirst({
    where: { word: word.toLowerCase() },
    include: {
      explains: {
        include: {
          definitions: {
            orderBy: { id: 'asc' },
          },
        },
        orderBy: { id: 'asc' },
      },
      formats: {
        include: {
          derivedForms: true,
          baseForm: true,
        },
        orderBy: { id: 'asc' },
      },
    },
  });

  const endTime = performance.now();
  return { result, time: endTime - startTime };
}

// 优化查询 - 分离查询策略
async function optimizedQuery(word: string) {
  const startTime = performance.now();

  // 第一步：查询基础数据和释义
  const baseResult = await prisma.vocabulary.findFirst({
    where: { word: word.toLowerCase() },
    include: {
      explains: {
        include: {
          definitions: true,
        },
        orderBy: { id: 'asc' },
      },
    },
  });

  // 第二步：如果需要，单独查询格式（可选）
  let formats = null;
  if (baseResult) {
    formats = await prisma.wordFormat.findMany({
      where: { vocabularyId: baseResult.id },
      select: {
        id: true,
        name: true,
        form: true,
      },
    });
  }

  const endTime = performance.now();
  return {
    result: baseResult ? { ...baseResult, formats } : null,
    time: endTime - startTime,
  };
}

async function testQueryPerformance(
  queryFunction: Function,
  queryType: string,
  words: string[],
  iterations: number = 10
): Promise<QueryPerformanceResult> {
  console.log(`🔍 测试 ${queryType} 查询性能...`);

  const times: number[] = [];

  for (let i = 0; i < iterations; i++) {
    const word = words[i % words.length];
    try {
      const { time } = await queryFunction(word);
      times.push(time);
    } catch (error) {
      console.error(`查询失败: ${word}`, error);
      times.push(0);
    }
  }

  const totalTime = times.reduce((sum, time) => sum + time, 0);
  const averageTime = totalTime / times.length;
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);

  return {
    queryType,
    averageTime,
    minTime,
    maxTime,
    totalTime,
    iterations,
  };
}

async function testConcurrentQueries(
  queryFunction: Function,
  queryType: string,
  words: string[],
  concurrency: number
): Promise<QueryPerformanceResult> {
  console.log(`🚀 测试 ${queryType} 并发查询性能 (${concurrency} 并发)...`);

  const startTime = performance.now();
  const promises = [];

  for (let i = 0; i < concurrency; i++) {
    const word = words[i % words.length];
    promises.push(queryFunction(word));
  }

  const results = await Promise.all(promises);
  const totalTime = performance.now() - startTime;

  const queryTimes = results.map((r) => r.time);
  const averageTime = queryTimes.reduce((sum, time) => sum + time, 0) / queryTimes.length;
  const minTime = Math.min(...queryTimes);
  const maxTime = Math.max(...queryTimes);

  return {
    queryType: `${queryType} (${concurrency} 并发)`,
    averageTime,
    minTime,
    maxTime,
    totalTime,
    iterations: concurrency,
  };
}

async function runQueryPerformanceTests(): Promise<void> {
  console.log('🔍 数据库查询性能对比测试\n');

  // 测试用词汇
  const testWords = [
    'apple',
    'banana',
    'cherry',
    'dog',
    'elephant',
    'fish',
    'grape',
    'house',
    'ice',
    'juice',
    'key',
    'lemon',
    'mouse',
    'night',
    'orange',
    'paper',
    'queen',
    'rain',
    'sun',
    'tree',
  ];

  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功\n');

    // 1. 串行性能测试
    console.log('📊 串行查询性能测试:');
    const serialResults: QueryPerformanceResult[] = [];

    serialResults.push(await testQueryPerformance(simpleQuery, '简单查询', testWords, 20));
    serialResults.push(await testQueryPerformance(mediumQuery, '中等查询', testWords, 20));
    serialResults.push(await testQueryPerformance(complexQuery, '复杂查询', testWords, 20));
    serialResults.push(await testQueryPerformance(optimizedQuery, '优化查询', testWords, 20));

    console.log('\n串行查询结果:');
    console.log('查询类型     | 平均时间 | 最小时间 | 最大时间 | 总时间');
    console.log('-------------|----------|----------|----------|--------');
    serialResults.forEach((result) => {
      console.log(
        `${result.queryType.padEnd(12)} | ${Math.round(result.averageTime).toString().padStart(8)}ms | ${Math.round(result.minTime).toString().padStart(8)}ms | ${Math.round(result.maxTime).toString().padStart(8)}ms | ${Math.round(result.totalTime).toString().padStart(6)}ms`
      );
    });

    // 2. 并发性能测试
    console.log('\n📊 并发查询性能测试:');
    const concurrentResults: QueryPerformanceResult[] = [];

    const concurrencyLevels = [10, 20, 50];

    for (const concurrency of concurrencyLevels) {
      console.log(`\n测试 ${concurrency} 并发:`);
      concurrentResults.push(
        await testConcurrentQueries(simpleQuery, '简单查询', testWords, concurrency)
      );
      concurrentResults.push(
        await testConcurrentQueries(mediumQuery, '中等查询', testWords, concurrency)
      );
      concurrentResults.push(
        await testConcurrentQueries(complexQuery, '复杂查询', testWords, concurrency)
      );
      concurrentResults.push(
        await testConcurrentQueries(optimizedQuery, '优化查询', testWords, concurrency)
      );
    }

    console.log('\n并发查询结果:');
    console.log('查询类型                    | 平均时间 | 最小时间 | 最大时间 | 总时间');
    console.log('----------------------------|----------|----------|----------|--------');
    concurrentResults.forEach((result) => {
      console.log(
        `${result.queryType.padEnd(27)} | ${Math.round(result.averageTime).toString().padStart(8)}ms | ${Math.round(result.minTime).toString().padStart(8)}ms | ${Math.round(result.maxTime).toString().padStart(8)}ms | ${Math.round(result.totalTime).toString().padStart(6)}ms`
      );
    });

    // 3. 性能分析
    console.log('\n🔍 性能分析:');

    const simpleSerial = serialResults.find((r) => r.queryType === '简单查询');
    const complexSerial = serialResults.find((r) => r.queryType === '复杂查询');

    if (simpleSerial && complexSerial) {
      const complexityOverhead = complexSerial.averageTime / simpleSerial.averageTime;
      console.log(`复杂查询开销: ${complexityOverhead.toFixed(2)}x 简单查询`);

      if (complexityOverhead > 3) {
        console.log('🔴 复杂查询开销过大，建议优化');
      } else if (complexityOverhead > 2) {
        console.log('⚠️ 复杂查询有一定开销，可考虑优化');
      } else {
        console.log('✅ 复杂查询开销可接受');
      }
    }

    // 找出最佳查询策略
    const best50Concurrent = concurrentResults
      .filter((r) => r.queryType.includes('50 并发'))
      .reduce((best, current) => (current.averageTime < best.averageTime ? current : best));

    console.log(`\n🎯 50并发最佳策略: ${best50Concurrent.queryType}`);
    console.log(`   平均响应时间: ${Math.round(best50Concurrent.averageTime)}ms`);

    if (best50Concurrent.averageTime < 200) {
      console.log('✅ 性能优秀');
    } else if (best50Concurrent.averageTime < 500) {
      console.log('⚠️ 性能可接受，但有优化空间');
    } else {
      console.log('🔴 性能需要优化');
    }
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 运行测试
runQueryPerformanceTests().catch(console.error);
