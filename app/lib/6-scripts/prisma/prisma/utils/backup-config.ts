/**
 * 数据库备份配置管理
 */

import { BackupConfig } from './backup-types.js';
import { resolve } from 'path';

/**
 * 默认备份配置
 */
export const defaultBackupConfig: BackupConfig = {
  database: {
    containerName: 'lucid-bd-postgres',
    host: 'localhost',
    port: 5432,
    username: 'postgres',
    password: 'postgres',
    database: 'dictionary',
  },

  backup: {
    outputDir: resolve(process.cwd(), 'backups'),
    compression: true,
    format: 'custom',
    filenamePrefix: 'lucid-bd',
    retentionDays: 30,
    maxBackups: 10,
  },

  docker: {
    useDocker: true,
    containerName: 'lucid-bd-postgres',
  },
};

/**
 * 生产环境配置
 */
export const productionBackupConfig: BackupConfig = {
  ...defaultBackupConfig,
  backup: {
    ...defaultBackupConfig.backup,
    retentionDays: 90,
    maxBackups: 30,
    compression: true,
  },
};

/**
 * 开发环境配置
 */
export const developmentBackupConfig: BackupConfig = {
  ...defaultBackupConfig,
  backup: {
    ...defaultBackupConfig.backup,
    retentionDays: 7,
    maxBackups: 5,
    compression: false,
  },
};

/**
 * 根据环境获取备份配置
 */
export function getBackupConfig(env?: string): BackupConfig {
  switch (env?.toLowerCase()) {
    case 'production':
    case 'prod':
      return productionBackupConfig;

    case 'development':
    case 'dev':
      return developmentBackupConfig;

    default:
      return defaultBackupConfig;
  }
}

/**
 * 验证备份配置
 */
export function validateBackupConfig(config: BackupConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 数据库配置验证
  if (!config.database.containerName) {
    errors.push('数据库容器名称不能为空');
  }

  if (!config.database.username) {
    errors.push('数据库用户名不能为空');
  }

  if (!config.database.database) {
    errors.push('数据库名称不能为空');
  }

  // 备份配置验证
  if (!config.backup.outputDir) {
    errors.push('备份输出目录不能为空');
  }

  if (config.backup.retentionDays < 1) {
    errors.push('保留天数必须大于0');
  }

  if (config.backup.maxBackups < 1) {
    errors.push('最大备份数量必须大于0');
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}
