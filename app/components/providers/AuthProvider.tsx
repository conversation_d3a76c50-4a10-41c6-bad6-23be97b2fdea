'use client';

import { createContext, useContext, ReactNode } from 'react';
import { SessionProvider, useSession, signIn, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (credentials: { email: string; password: string }) => Promise<void>;
  checkUserExists: (email: string) => Promise<boolean>;
  registerAndLogin: (credentials: {
    email: string;
    password: string;
    name?: string;
  }) => Promise<void>;
  signup: (credentials: {
    email: string;
    password: string;
    confirmPassword: string;
    name?: string;
  }) => Promise<void>;
  loginWithOAuth: (provider: 'google' | 'github') => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<string>;
  isAuthenticated: boolean;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: ReactNode;
}

// Internal AuthProvider using NextAuth session
function InternalAuthProvider({ children }: AuthProviderProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  // Map NextAuth session to our AuthContextType
  const user: User | null = session?.user
    ? {
        id: session.user.id,
        email: session.user.email,
        name: session.user.name,
        avatar: session.user.image || session.user.avatar,
        role: 'user', // Default role
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    : null;

  const loading = status === 'loading';
  const error = null; // NextAuth handles errors internally
  const isAuthenticated = !!session;
  const isLoading = loading;

  // NextAuth handles initialization automatically

  // Check if user exists by email
  const checkUserExists = async (email: string): Promise<boolean> => {
    try {
      const response = await fetch('/api/auth/check-user', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to check user existence');
      }

      const data = await response.json();
      return data.exists;
    } catch (error) {
      console.error('Check user exists error:', error);
      throw error;
    }
  };

  // Register and login in one step
  const registerAndLogin = async (credentials: {
    email: string;
    password: string;
    name?: string;
  }) => {
    try {
      // First create the user account
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password,
          name: credentials.name,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Registration failed');
      }

      // Then sign in the user
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        redirect: false,
      });

      if (result?.error) {
        throw new Error('Account created but login failed');
      }

      if (result?.ok) {
        toast.success('账户创建成功，已自动登录！');
        router.push('/home');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Registration and login failed';
      toast.error(errorMessage);
      throw error;
    }
  };

  // Login function using NextAuth
  const login = async (credentials: { email: string; password: string }) => {
    try {
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        redirect: false,
      });

      if (result?.error) {
        const errorMessage =
          result.error === 'CredentialsSignin' ? 'Invalid email or password' : 'Login failed';
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }

      if (result?.ok) {
        toast.success('Successfully logged in!');
        router.push('/home');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Login failed';
      toast.error(errorMessage);
      throw error;
    }
  };

  // Signup function
  const signup = async (credentials: {
    email: string;
    password: string;
    confirmPassword: string;
    name?: string;
  }) => {
    if (credentials.password !== credentials.confirmPassword) {
      const error = 'Passwords do not match';
      toast.error(error);
      throw new Error(error);
    }

    try {
      // First create the user account
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password,
          name: credentials.name,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Signup failed');
      }

      // Then sign in the user
      const result = await signIn('credentials', {
        email: credentials.email,
        password: credentials.password,
        redirect: false,
      });

      if (result?.error) {
        throw new Error('Account created but login failed');
      }

      if (result?.ok) {
        toast.success('Account created successfully!');
        router.push('/home');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Signup failed';
      toast.error(errorMessage);
      throw error;
    }
  };

  // OAuth login function using NextAuth
  const loginWithOAuth = async (provider: 'google' | 'github') => {
    try {
      const result = await signIn(provider, {
        redirect: false,
        callbackUrl: '/home',
      });

      if (result?.error) {
        const errorMessage = `${provider} login failed`;
        toast.error(errorMessage);
        throw new Error(errorMessage);
      }

      if (result?.ok) {
        toast.success(`Successfully logged in with ${provider}!`);
        // NextAuth handles redirect automatically
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `${provider} login failed`;
      toast.error(errorMessage);
      throw error;
    }
  };

  // Logout function using NextAuth
  const logout = async () => {
    try {
      await signOut({
        redirect: false,
      });

      toast.success('Successfully logged out');
      router.push('/login');
    } catch (error) {
      console.error('Logout error:', error);
      toast.error('Logout failed');
    }
  };

  // Refresh token function (NextAuth handles this automatically)
  const refreshToken = async (): Promise<string> => {
    // NextAuth handles token refresh automatically
    // This is kept for interface compatibility
    return 'refreshed';
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    login,
    checkUserExists,
    registerAndLogin,
    signup,
    loginWithOAuth,
    logout,
    refreshToken,
    isAuthenticated,
    isLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Main AuthProvider that wraps NextAuth SessionProvider
export function AuthProvider({ children }: AuthProviderProps) {
  return (
    <SessionProvider>
      <InternalAuthProvider>{children}</InternalAuthProvider>
    </SessionProvider>
  );
}
