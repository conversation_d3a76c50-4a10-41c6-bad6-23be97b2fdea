/**
 * 句子搜索 API
 * GET /api/practice/sentence/search
 * 
 * 搜索句子库中的句子，支持关键词搜索和高级筛选
 */

import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { z } from 'zod';
import { SentenceWithContextOptions } from '@/lib/2-repositories/SentenceRepository';

// 请求参数验证 schema
const searchSentencesQuerySchema = z.object({
  q: z.string().min(1, 'Search query is required'),
  difficulty: z.coerce.number().min(1).max(10).optional()
    .or(z.string().transform(val => val.split(',').map(Number))).optional(),
  category: z.string().optional()
    .or(z.string().transform(val => val.split(','))).optional(),
  minLength: z.coerce.number().positive().optional(),
  maxLength: z.coerce.number().positive().optional(),
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(50).default(10).optional(),
  sortBy: z.enum(['createdAt', 'difficulty', 'favoriteCount', 'length', 'random']).default('createdAt').optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc').optional()
});

export async function GET(request: NextRequest) {
  try {
    // 用户认证
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    
    // 验证查询参数
    const queryResult = searchSentencesQuerySchema.safeParse(
      Object.fromEntries(searchParams.entries())
    );

    if (!queryResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters',
          details: queryResult.error.flatten().fieldErrors
        },
        { status: 400 }
      );
    }

    const { q: searchTerm, ...filterOptions } = queryResult.data;

    const searchOptions: SentenceWithContextOptions = {
      userId,
      search: searchTerm,
      ...filterOptions,
      isActive: true
    };

    const practiceService = createPracticeService();

    // 搜索句子
    const sentences = await practiceService.searchSentences(userId, searchTerm, searchOptions);
    
    return NextResponse.json({
      success: true,
      data: {
        sentences,
        searchTerm,
        count: sentences.length,
        filters: filterOptions,
        message: 'Sentence search completed successfully'
      }
    });

  } catch (error) {
    console.error('Error searching sentences:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to search sentences'
      },
      { status: 500 }
    );
  }
}