import { describe, it, expect, beforeEach, beforeAll, afterAll, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { UserWordProficiencyRepository, CreateUserWordProficiencyInput } from '../UserWordProficiencyRepository';

// Mock PrismaClient for unit tests
const mockPrisma = {
  userWordProficiency: {
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
    findMany: vi.fn(),
    upsert: vi.fn(),
    aggregate: vi.fn(),
    count: vi.fn(),
  },
  $transaction: vi.fn(),
} as any;

describe('UserWordProficiencyRepository', () => {
  let repository: UserWordProficiencyRepository;

  beforeEach(() => {
    repository = new UserWordProficiencyRepository(mockPrisma);
    vi.clearAllMocks();
  });

  describe('create', () => {
    it('should create a new user word proficiency record with default values', async () => {
      const input: CreateUserWordProficiencyInput = {
        userId: 'user-1',
        wordId: 123,
      };

      const expectedResult = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 0,
        errorCount: 0,
        averageTime: 0,
        isMarked: false,
        proficiencyScore: 0.0,
        lastPracticed: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.userWordProficiency.create.mockResolvedValue(expectedResult);

      const result = await repository.create(input);

      expect(mockPrisma.userWordProficiency.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          wordId: 123,
          practiceCount: 0,
          errorCount: 0,
          averageTime: 0,
          isMarked: false,
          proficiencyScore: 0.0,
          lastPracticed: undefined,
        },
      });
      expect(result).toEqual(expectedResult);
    });

    it('should create a new user word proficiency record with provided values', async () => {
      const lastPracticed = new Date();
      const input: CreateUserWordProficiencyInput = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 5,
        errorCount: 2,
        averageTime: 1500,
        isMarked: true,
        proficiencyScore: 0.7,
        lastPracticed,
      };

      const expectedResult = { ...input, createdAt: new Date(), updatedAt: new Date() };
      mockPrisma.userWordProficiency.create.mockResolvedValue(expectedResult);

      const result = await repository.create(input);

      expect(mockPrisma.userWordProficiency.create).toHaveBeenCalledWith({
        data: {
          userId: 'user-1',
          wordId: 123,
          practiceCount: 5,
          errorCount: 2,
          averageTime: 1500,
          isMarked: true,
          proficiencyScore: 0.7,
          lastPracticed,
        },
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('findByUserAndWord', () => {
    it('should find user word proficiency by user ID and word ID', async () => {
      const expectedResult = {
        userId: 'user-1',
        wordId: 123,
        practiceCount: 5,
        errorCount: 1,
        averageTime: 1200,
        isMarked: false,
        proficiencyScore: 0.8,
        lastPracticed: new Date(),
      };

      mockPrisma.userWordProficiency.findUnique.mockResolvedValue(expectedResult);

      const result = await repository.findByUserAndWord('user-1', 123);

      expect(mockPrisma.userWordProficiency.findUnique).toHaveBeenCalledWith({
        where: {
          userId_wordId: {
            userId: 'user-1',
            wordId: 123,
          },
        },
      });
      expect(result).toEqual(expectedResult);
    });

    it('should return null if record not found', async () => {
      mockPrisma.userWordProficiency.findUnique.mockResolvedValue(null);

      const result = await repository.findByUserAndWord('user-1', 999);

      expect(result).toBeNull();
    });
  });

  describe('update', () => {
    it('should update user word proficiency record', async () => {
      const updateData = {
        practiceCount: 10,
        errorCount: 3,
        averageTime: 1100,
        proficiencyScore: 0.85,
      };

      const expectedResult = {
        userId: 'user-1',
        wordId: 123,
        ...updateData,
        isMarked: false,
        lastPracticed: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.userWordProficiency.update.mockResolvedValue(expectedResult);

      const result = await repository.update('user-1', 123, updateData);

      expect(mockPrisma.userWordProficiency.update).toHaveBeenCalledWith({
        where: {
          userId_wordId: {
            userId: 'user-1',
            wordId: 123,
          },
        },
        data: {
          ...updateData,
          updatedAt: expect.any(Date),
        },
      });
      expect(result).toEqual(expectedResult);
    });
  });

  describe('delete', () => {
    it('should delete user word proficiency record', async () => {
      mockPrisma.userWordProficiency.delete.mockResolvedValue({});

      await repository.delete('user-1', 123);

      expect(mockPrisma.userWordProficiency.delete).toHaveBeenCalledWith({
        where: {
          userId_wordId: {
            userId: 'user-1',
            wordId: 123,
          },
        },
      });
    });
  });

  describe('findManyByUser', () => {
    it('should find user proficiencies with default options', async () => {
      const expectedResults = [
        {
          userId: 'user-1',
          wordId: 123,
          practiceCount: 5,
          proficiencyScore: 0.6,
          vocabulary: { id: 123, word: 'test', phonetics: [], freqRank: 1000 },
        },
      ];

      mockPrisma.userWordProficiency.findMany.mockResolvedValue(expectedResults);

      const result = await repository.findManyByUser('user-1');

      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        orderBy: { proficiencyScore: 'asc' },
        take: undefined,
        skip: undefined,
        include: {
          vocabulary: {
            select: {
              id: true,
              word: true,
              phonetics: true,
              freqRank: true,
            },
          },
        },
      });
      expect(result).toEqual(expectedResults);
    });

    it('should find marked words only when specified', async () => {
      await repository.findManyByUser('user-1', { markedOnly: true });

      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1', isMarked: true },
        orderBy: { proficiencyScore: 'asc' },
        take: undefined,
        skip: undefined,
        include: {
          vocabulary: {
            select: {
              id: true,
              word: true,
              phonetics: true,
              freqRank: true,
            },
          },
        },
      });
    });

    it('should apply pagination and sorting options', async () => {
      await repository.findManyByUser('user-1', {
        sortBy: 'lastPracticed',
        sortOrder: 'desc',
        limit: 10,
        offset: 20,
      });

      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        orderBy: { lastPracticed: 'desc' },
        take: 10,
        skip: 20,
        include: {
          vocabulary: {
            select: {
              id: true,
              word: true,
              phonetics: true,
              freqRank: true,
            },
          },
        },
      });
    });
  });

  describe('findLowestProficiencyWords', () => {
    it('should find words with lowest proficiency scores', async () => {
      const expectedResults = [
        { userId: 'user-1', wordId: 123, proficiencyScore: 0.2 },
        { userId: 'user-1', wordId: 124, proficiencyScore: 0.3 },
      ];

      mockPrisma.userWordProficiency.findMany.mockResolvedValue(expectedResults);

      const result = await repository.findLowestProficiencyWords('user-1', 10);

      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        orderBy: { proficiencyScore: 'asc' },
        take: 10,
        skip: undefined,
        include: {
          vocabulary: {
            select: {
              id: true,
              word: true,
              phonetics: true,
              freqRank: true,
            },
          },
        },
      });
      expect(result).toEqual(expectedResults);
    });
  });

  describe('upsertMany', () => {
    it('should perform bulk upsert operations', async () => {
      const records: CreateUserWordProficiencyInput[] = [
        { userId: 'user-1', wordId: 123, practiceCount: 1 },
        { userId: 'user-1', wordId: 124, practiceCount: 2 },
      ];

      mockPrisma.$transaction.mockResolvedValue([]);
      mockPrisma.userWordProficiency.upsert.mockResolvedValue({});

      await repository.upsertMany(records);

      expect(mockPrisma.$transaction).toHaveBeenCalled();
      expect(mockPrisma.userWordProficiency.upsert).toHaveBeenCalledTimes(2);
    });
  });

  describe('getUserStats', () => {
    it('should return user proficiency statistics', async () => {
      mockPrisma.userWordProficiency.aggregate.mockResolvedValue({
        _count: { _all: 50 },
        _avg: { proficiencyScore: 0.7 },
        _sum: { practiceCount: 200 },
      });

      mockPrisma.userWordProficiency.count.mockResolvedValue(10);

      const result = await repository.getUserStats('user-1');

      expect(result).toEqual({
        totalWords: 50,
        markedWords: 10,
        averageProficiency: 0.7,
        totalPracticeCount: 200,
      });

      expect(mockPrisma.userWordProficiency.aggregate).toHaveBeenCalledWith({
        where: { userId: 'user-1' },
        _count: { _all: true },
        _avg: { proficiencyScore: true },
        _sum: { practiceCount: true },
      });

      expect(mockPrisma.userWordProficiency.count).toHaveBeenCalledWith({
        where: { userId: 'user-1', isMarked: true },
      });
    });

    it('should handle null/undefined values in aggregations', async () => {
      mockPrisma.userWordProficiency.aggregate.mockResolvedValue({
        _count: { _all: 0 },
        _avg: { proficiencyScore: null },
        _sum: { practiceCount: null },
      });

      mockPrisma.userWordProficiency.count.mockResolvedValue(0);

      const result = await repository.getUserStats('user-1');

      expect(result).toEqual({
        totalWords: 0,
        markedWords: 0,
        averageProficiency: 0,
        totalPracticeCount: 0,
      });
    });
  });

  describe('findWordsForReview', () => {
    it('should find words that need review', async () => {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - 7);

      const expectedResults = [
        { userId: 'user-1', wordId: 123, lastPracticed: null },
        { userId: 'user-1', wordId: 124, lastPracticed: new Date('2023-01-01') },
      ];

      mockPrisma.userWordProficiency.findMany.mockResolvedValue(expectedResults);

      const result = await repository.findWordsForReview('user-1', 7);

      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-1',
          OR: [
            { lastPracticed: null },
            { lastPracticed: { lt: expect.any(Date) } },
          ],
        },
        orderBy: [
          { proficiencyScore: 'asc' },
          { lastPracticed: 'asc' },
        ],
        take: 20,
        include: {
          vocabulary: {
            select: {
              id: true,
              word: true,
              phonetics: true,
            },
          },
        },
      });

      expect(result).toEqual(expectedResults);
    });
  });
});

// Integration tests would go here if we had a test database setup
describe('UserWordProficiencyRepository Integration Tests', () => {
  // These would run against a real test database
  // For now, we'll skip them as they require database setup
  it.skip('should perform actual database operations', async () => {
    // Integration test implementation
  });
});