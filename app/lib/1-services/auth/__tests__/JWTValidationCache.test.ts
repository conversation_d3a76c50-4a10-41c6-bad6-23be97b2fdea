import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { JWTValidationCache, CachedJWTValidation } from '../JWTValidationCache';
import { JWTPayload } from '@/lib/types/auth-types';

// Mock Redis utils
vi.mock('@/lib/utils/redis', () => ({
  redisCacheManager: {
    set: vi.fn(),
    get: vi.fn(),
    del: vi.fn(),
  },
}));

// 获取mock实例
import { redisCacheManager } from '@/lib/utils/redis';
const mockCacheManager = redisCacheManager as any;

describe('JWTValidationCache', () => {
  let jwtValidationCache: JWTValidationCache;
  let mockJWTPayload: JWTPayload;

  beforeEach(() => {
    vi.clearAllMocks();
    jwtValidationCache = new JWTValidationCache();
    
    mockJWTPayload = {
      sub: 'user-123',
      jti: 'jwt-abc-123',
      email: '<EMAIL>',
      clientType: 'mobile',
      scopes: ['word:read', 'word:write'],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600,
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('缓存键生成', () => {
    it('应该为每个JTI生成唯一的缓存键', () => {
      // Arrange
      const jti1 = 'jwt-123';
      const jti2 = 'jwt-456';

      // Act
      const key1 = (jwtValidationCache as any).generateCacheKey(jti1);
      const key2 = (jwtValidationCache as any).generateCacheKey(jti2);

      // Assert
      expect(key1).toBe('jwt_validation:jwt-123');
      expect(key2).toBe('jwt_validation:jwt-456');
      expect(key1).not.toBe(key2);
    });

    it('应该使用正确的前缀格式', () => {
      // Arrange
      const jti = 'test-jti-123';

      // Act
      const key = (jwtValidationCache as any).generateCacheKey(jti);

      // Assert
      expect(key).toMatch(/^jwt_validation:/);
      expect(key).toBe('jwt_validation:test-jti-123');
    });

    it('应该处理特殊字符的JTI', () => {
      // Arrange
      const specialJti = 'jwt-abc:123_def@456';

      // Act
      const key = (jwtValidationCache as any).generateCacheKey(specialJti);

      // Assert
      expect(key).toBe('jwt_validation:jwt-abc:123_def@456');
    });
  });

  describe('验证结果缓存', () => {
    it('应该缓存成功的验证结果', async () => {
      // Arrange
      const jti = 'jwt-success-123';
      const isValid = true;

      // Act
      await jwtValidationCache.cacheValidation(jti, mockJWTPayload, isValid);

      // Assert
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        'jwt_validation:jwt-success-123',
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          clientType: 'mobile',
          scopes: ['word:read', 'word:write'],
          isValid: true,
          cachedAt: expect.any(Number),
        }),
        300 // 5分钟TTL
      );
    });

    it('应该缓存失败的验证结果', async () => {
      // Arrange
      const jti = 'jwt-failed-123';
      const isValid = false;

      // Act
      await jwtValidationCache.cacheValidation(jti, mockJWTPayload, isValid);

      // Assert
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        'jwt_validation:jwt-failed-123',
        expect.objectContaining({
          userId: 'user-123',
          email: '<EMAIL>',
          clientType: 'mobile',
          scopes: ['word:read', 'word:write'],
          isValid: false,
          cachedAt: expect.any(Number),
        }),
        60 // 1分钟TTL
      );
    });

    it('应该为成功和失败结果使用不同的TTL', async () => {
      // Arrange
      const jtiSuccess = 'jwt-success-123';
      const jtiFailed = 'jwt-failed-123';

      // Act
      await jwtValidationCache.cacheValidation(jtiSuccess, mockJWTPayload, true);
      await jwtValidationCache.cacheValidation(jtiFailed, mockJWTPayload, false);

      // Assert
      expect(mockCacheManager.set).toHaveBeenNthCalledWith(
        1,
        expect.any(String),
        expect.any(Object),
        300 // 成功结果5分钟
      );
      expect(mockCacheManager.set).toHaveBeenNthCalledWith(
        2,
        expect.any(String),
        expect.any(Object),
        60 // 失败结果1分钟
      );
    });

    it('应该正确序列化缓存数据', async () => {
      // Arrange
      const jti = 'jwt-serialize-test';
      const complexPayload: JWTPayload = {
        ...mockJWTPayload,
        scopes: ['word:read', 'word:write', 'admin:read'],
        custom: 'custom-data',
      };

      // Act
      await jwtValidationCache.cacheValidation(jti, complexPayload, true);

      // Assert
      const cachedData = mockCacheManager.set.mock.calls[0][1];
      expect(cachedData).toEqual({
        userId: complexPayload.sub,
        email: complexPayload.email,
        clientType: complexPayload.clientType,
        scopes: complexPayload.scopes,
        isValid: true,
        cachedAt: expect.any(Number),
      });
    });

    it('应该处理空的scopes数组', async () => {
      // Arrange
      const jti = 'jwt-empty-scopes';
      const payloadWithEmptyScopes: JWTPayload = {
        ...mockJWTPayload,
        scopes: [],
      };

      // Act
      await jwtValidationCache.cacheValidation(jti, payloadWithEmptyScopes, true);

      // Assert
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          scopes: [],
        }),
        expect.any(Number)
      );
    });

    it('应该处理未定义的scopes', async () => {
      // Arrange
      const jti = 'jwt-undefined-scopes';
      const payloadWithUndefinedScopes: JWTPayload = {
        ...mockJWTPayload,
        scopes: undefined,
      };

      // Act
      await jwtValidationCache.cacheValidation(jti, payloadWithUndefinedScopes, true);

      // Assert
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          scopes: [],
        }),
        expect.any(Number)
      );
    });
  });

  describe('缓存检索', () => {
    it('应该返回有效的缓存验证结果', async () => {
      // Arrange
      const jti = 'jwt-cached-123';
      const cachedValidation: CachedJWTValidation = {
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read', 'word:write'],
        isValid: true,
        cachedAt: Date.now(),
      };
      mockCacheManager.get.mockResolvedValue(cachedValidation);

      // Act
      const result = await jwtValidationCache.getCachedValidation(jti);

      // Assert
      expect(result).toEqual(cachedValidation);
      expect(mockCacheManager.get).toHaveBeenCalledWith('jwt_validation:jwt-cached-123');
    });

    it('应该对不存在的JTI返回null', async () => {
      // Arrange
      const jti = 'jwt-not-found';
      mockCacheManager.get.mockResolvedValue(null);

      // Act
      const result = await jwtValidationCache.getCachedValidation(jti);

      // Assert
      expect(result).toBeNull();
      expect(mockCacheManager.get).toHaveBeenCalledWith('jwt_validation:jwt-not-found');
    });

    it('应该正确反序列化缓存数据', async () => {
      // Arrange
      const jti = 'jwt-deserialize-test';
      const complexCachedData: CachedJWTValidation = {
        userId: 'user-456',
        email: '<EMAIL>',
        clientType: 'extension',
        scopes: ['word:read', 'word:write', 'user:read', 'admin:read'],
        isValid: false,
        cachedAt: Date.now() - 30000, // 30秒前
      };
      mockCacheManager.get.mockResolvedValue(complexCachedData);

      // Act
      const result = await jwtValidationCache.getCachedValidation(jti);

      // Assert
      expect(result).toEqual(complexCachedData);
      expect(result?.userId).toBe('user-456');
      expect(result?.scopes).toHaveLength(4);
      expect(result?.isValid).toBe(false);
    });

    it('应该处理缓存获取错误', async () => {
      // Arrange
      const jti = 'jwt-error-test';
      mockCacheManager.get.mockRejectedValue(new Error('Cache connection failed'));

      // Act & Assert
      await expect(jwtValidationCache.getCachedValidation(jti)).rejects.toThrow(
        'Cache connection failed'
      );
    });
  });

  describe('缓存失效操作', () => {
    it('应该能够失效特定JWT的缓存', async () => {
      // Arrange
      const jti = 'jwt-invalidate-123';

      // Act
      await jwtValidationCache.invalidateJWT(jti);

      // Assert
      expect(mockCacheManager.del).toHaveBeenCalledWith('jwt_validation:jwt-invalidate-123');
    });

    it('应该能够批量失效用户的所有JWT缓存', async () => {
      // Arrange
      const userId = 'user-456';
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      await jwtValidationCache.invalidateUserJWTs(userId);

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('清除用户 user-456 的JWT缓存');

      consoleSpy.mockRestore();
    });

    it('应该处理缓存失效操作的错误', async () => {
      // Arrange
      const jti = 'jwt-delete-error';
      mockCacheManager.del.mockRejectedValue(new Error('Delete operation failed'));

      // Act & Assert
      await expect(jwtValidationCache.invalidateJWT(jti)).rejects.toThrow(
        'Delete operation failed'
      );
    });

    it('应该处理批量失效操作', async () => {
      // Arrange
      const jtiList = ['jwt-1', 'jwt-2', 'jwt-3'];

      // Act
      for (const jti of jtiList) {
        await jwtValidationCache.invalidateJWT(jti);
      }

      // Assert
      expect(mockCacheManager.del).toHaveBeenCalledTimes(3);
      expect(mockCacheManager.del).toHaveBeenNthCalledWith(1, 'jwt_validation:jwt-1');
      expect(mockCacheManager.del).toHaveBeenNthCalledWith(2, 'jwt_validation:jwt-2');
      expect(mockCacheManager.del).toHaveBeenNthCalledWith(3, 'jwt_validation:jwt-3');
    });
  });

  describe('批量操作', () => {
    it('应该支持JWT缓存预热', async () => {
      // Arrange
      const tokens = [
        { jti: 'jwt-1', payload: { ...mockJWTPayload, jti: 'jwt-1' } },
        { jti: 'jwt-2', payload: { ...mockJWTPayload, jti: 'jwt-2' } },
        { jti: 'jwt-3', payload: { ...mockJWTPayload, jti: 'jwt-3' } },
      ];

      // Act
      await jwtValidationCache.warmupJWTCache(tokens);

      // Assert
      expect(mockCacheManager.set).toHaveBeenCalledTimes(3);
      expect(mockCacheManager.set).toHaveBeenNthCalledWith(
        1,
        'jwt_validation:jwt-1',
        expect.any(Object),
        300
      );
      expect(mockCacheManager.set).toHaveBeenNthCalledWith(
        2,
        'jwt_validation:jwt-2',
        expect.any(Object),
        300
      );
      expect(mockCacheManager.set).toHaveBeenNthCalledWith(
        3,
        'jwt_validation:jwt-3',
        expect.any(Object),
        300
      );
    });

    it('应该正确处理批量预热中的部分失败', async () => {
      // Arrange
      const tokens = [
        { jti: 'jwt-success', payload: { ...mockJWTPayload, jti: 'jwt-success' } },
        { jti: 'jwt-fail', payload: { ...mockJWTPayload, jti: 'jwt-fail' } },
      ];

      mockCacheManager.set
        .mockResolvedValueOnce(undefined) // 第一个成功
        .mockRejectedValueOnce(new Error('Cache write failed')); // 第二个失败

      // Act & Assert
      await expect(jwtValidationCache.warmupJWTCache(tokens)).rejects.toThrow();
      expect(mockCacheManager.set).toHaveBeenCalledTimes(2);
    });

    it('应该处理空的预热令牌数组', async () => {
      // Arrange
      const emptyTokens: Array<{ jti: string; payload: JWTPayload }> = [];

      // Act
      await jwtValidationCache.warmupJWTCache(emptyTokens);

      // Assert
      expect(mockCacheManager.set).not.toHaveBeenCalled();
    });

    it('应该在预热时使用正确的TTL', async () => {
      // Arrange
      const tokens = [
        { jti: 'jwt-warmup', payload: mockJWTPayload },
      ];

      // Act
      await jwtValidationCache.warmupJWTCache(tokens);

      // Assert
      expect(mockCacheManager.set).toHaveBeenCalledWith(
        expect.any(String),
        expect.any(Object),
        300 // 预热使用成功验证的TTL
      );
    });
  });

  describe('缓存状态检查', () => {
    it('应该正确检查JWT是否在缓存中且有效', async () => {
      // Arrange
      const jti = 'jwt-valid-cached';
      const validCachedResult: CachedJWTValidation = {
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read'],
        isValid: true,
        cachedAt: Date.now(),
      };
      mockCacheManager.get.mockResolvedValue(validCachedResult);

      // Act
      const result = await jwtValidationCache.isJWTCachedAndValid(jti);

      // Assert
      expect(result).toBe(true);
    });

    it('应该对无效的缓存JWT返回false', async () => {
      // Arrange
      const jti = 'jwt-invalid-cached';
      const invalidCachedResult: CachedJWTValidation = {
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read'],
        isValid: false,
        cachedAt: Date.now(),
      };
      mockCacheManager.get.mockResolvedValue(invalidCachedResult);

      // Act
      const result = await jwtValidationCache.isJWTCachedAndValid(jti);

      // Assert
      expect(result).toBe(false);
    });

    it('应该对不存在的JWT返回false', async () => {
      // Arrange
      const jti = 'jwt-not-found';
      mockCacheManager.get.mockResolvedValue(null);

      // Act
      const result = await jwtValidationCache.isJWTCachedAndValid(jti);

      // Assert
      expect(result).toBe(false);
    });
  });

  describe('缓存统计功能', () => {
    it('应该返回基础的缓存统计信息结构', async () => {
      // Act
      const stats = await jwtValidationCache.getCacheStats();

      // Assert
      expect(stats).toEqual({
        totalKeys: 0,
        validTokens: 0,
        invalidTokens: 0,
      });
    });

    it('应该返回正确的统计信息类型', async () => {
      // Act
      const stats = await jwtValidationCache.getCacheStats();

      // Assert
      expect(typeof stats.totalKeys).toBe('number');
      expect(typeof stats.validTokens).toBe('number');
      expect(typeof stats.invalidTokens).toBe('number');
    });
  });

  describe('边界条件和错误处理', () => {
    it('应该处理极长的JTI', async () => {
      // Arrange
      const longJti = 'jwt-' + 'a'.repeat(1000);

      // Act
      const key = (jwtValidationCache as any).generateCacheKey(longJti);

      // Assert
      expect(key).toBe(`jwt_validation:${longJti}`);
    });

    it('应该处理包含Unicode字符的JTI', async () => {
      // Arrange
      const unicodeJti = 'jwt-测试-🔑-token';

      // Act
      const key = (jwtValidationCache as any).generateCacheKey(unicodeJti);

      // Assert
      expect(key).toBe('jwt_validation:jwt-测试-🔑-token');
    });

    it('应该处理缓存操作的网络错误', async () => {
      // Arrange
      const jti = 'jwt-network-error';
      mockCacheManager.set.mockRejectedValue(new Error('Network timeout'));

      // Act & Assert
      await expect(
        jwtValidationCache.cacheValidation(jti, mockJWTPayload, true)
      ).rejects.toThrow('Network timeout');
    });

    it('应该处理缓存数据损坏的情况', async () => {
      // Arrange
      const jti = 'jwt-corrupted';
      mockCacheManager.get.mockResolvedValue('corrupted-data');

      // Act
      const result = await jwtValidationCache.getCachedValidation(jti);

      // Assert
      expect(result).toBe('corrupted-data'); // 应该返回原始数据，让调用者处理
    });
  });
});