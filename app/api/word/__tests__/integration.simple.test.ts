/**
 * @fileoverview 生词本管理API集成测试（修复版）
 * 
 * 使用简化的Mock策略避免复杂的中间件模拟
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';

// 使用vi.hoisted来避免变量hoisting问题
const mockPracticeService = vi.hoisted(() => ({
  getUserWordList: vi.fn(),
  addWordsToUserList: vi.fn(), 
  removeWordsFromUserList: vi.fn(),
}));

// Mock modules
vi.mock('@/lib/1-services/practice/PracticeService', () => ({
  createPracticeService: vi.fn(() => mockPracticeService),
}));

// Mock AuthMiddleware - 支持动态配置认证结果
let shouldAuthenticationSucceed = true;
let authError: any = null;
let mockAuthContext: any = null;

vi.mock('@/lib/1-services/auth/AuthMiddleware', () => ({
  requireAuth: vi.fn((options = {}) => 
    vi.fn((handler: any) => 
      vi.fn(async (request: NextRequest) => {
        if (!shouldAuthenticationSucceed) {
          // 返回认证失败的响应
          return new Response(JSON.stringify({
            success: false,
            error: authError?.message || 'Unauthorized',
            code: authError?.code || 'UNAUTHORIZED_ERROR',
          }), {
            status: authError?.status || 401,
            headers: { 'Content-Type': 'application/json' },
          });
        }
        
        // 认证成功，调用处理器
        const context = mockAuthContext || {
          user: { 
            id: 'user-123', 
            name: 'Test User', 
            email: '<EMAIL>',
            isActive: true,
          },
          authMethod: 'session' as const,
          clientType: 'web' as const,
          permissions: ['word:read', 'word:write'],
          scopes: ['word:read', 'word:write'],
          ipAddress: '127.0.0.1',
          userAgent: 'test-agent',
        };
        
        return handler(request, context);
      })
    )
  ),
}));

// Mock types module
vi.mock('@/lib/types/api-types', () => {
  class ApiError extends Error {
    constructor(
      public statusCode: number,
      message: string,
      public code?: string
    ) {
      super(message);
      this.name = 'ApiError';
    }
  }

  class ValidationError extends ApiError {
    constructor(message: string, public details?: any) {
      super(400, message, 'VALIDATION_ERROR');
      this.name = 'ValidationError';
    }
  }

  class NotFoundError extends ApiError {
    constructor(message: string = 'Not found') {
      super(404, message, 'NOT_FOUND');
      this.name = 'NotFoundError';
    }
  }

  class ConflictError extends ApiError {
    constructor(message: string = 'Conflict') {
      super(409, message, 'CONFLICT_ERROR');
      this.name = 'ConflictError';
    }
  }

  return {
    GetWordsQuerySchema: {
      safeParse: vi.fn(),
    },
    AddWordsBodySchema: {
      safeParse: vi.fn(),
    },
    DeleteWordsBodySchema: {
      safeParse: vi.fn(),
    },
    ApiError,
    ValidationError,
    NotFoundError,
    ConflictError,
  };
});

// Import mocked modules
import { requireAuth } from '@/lib/1-services/auth/AuthMiddleware';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { 
  GetWordsQuerySchema, 
  AddWordsBodySchema, 
  DeleteWordsBodySchema,
  ValidationError,
  NotFoundError,
  ConflictError
} from '@/lib/types/api-types';

// Import route handlers after mocks are set up
import { GET, POST, DELETE } from '@/api/word/route';

const mockRequireAuth = requireAuth as any;
const mockCreatePracticeService = createPracticeService as any;
const mockGetWordsQuerySchema = GetWordsQuerySchema as any;
const mockAddWordsBodySchema = AddWordsBodySchema as any;
const mockDeleteWordsBodySchema = DeleteWordsBodySchema as any;

describe('生词本管理API集成测试（简化版）', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // 重置认证状态
    shouldAuthenticationSucceed = true;
    authError = null;
    mockAuthContext = null;
    
    // 默认schema验证通过
    mockGetWordsQuerySchema.safeParse.mockReturnValue({
      success: true,
      data: {
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        order: 'desc',
      },
    });
    
    mockAddWordsBodySchema.safeParse.mockReturnValue({
      success: true,
      data: {
        words: ['test', 'word'],
        wordIds: [],
      },
    });
    
    mockDeleteWordsBodySchema.safeParse.mockReturnValue({
      success: true,
      data: {
        wordIds: [1, 2, 3],
      },
    });
    
    // 默认PracticeService Mock返回值
    mockPracticeService.getUserWordList.mockResolvedValue({
      words: [
        { id: 1, word: 'test', definition: 'A trial or experiment', createdAt: new Date() },
        { id: 2, word: 'word', definition: 'A unit of language', createdAt: new Date() },
      ],
      pagination: {
        page: 1,
        limit: 20,
        total: 2,
        totalPages: 1,
        hasMore: false,
      },
    });
    
    mockPracticeService.addWordsToUserList.mockResolvedValue({
      added: 2,
      skipped: 0,
      errors: [],
    });
    
    mockPracticeService.removeWordsFromUserList.mockResolvedValue({
      deleted: 3,
      notFound: 0,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('GET /api/word - 生词列表测试', () => {
    it('应该成功获取用户生词列表', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word?page=1&limit=20');

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data).toEqual({
        success: true,
        data: [
          { id: 1, word: 'test', definition: 'A trial or experiment', createdAt: expect.any(String) },
          { id: 2, word: 'word', definition: 'A unit of language', createdAt: expect.any(String) },
        ],
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          totalPages: 1,
          hasMore: false,
        },
        message: 'Successfully retrieved 2 words',
      });
      
      expect(mockPracticeService.getUserWordList).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        order: 'desc',
      });
    });

    it('应该验证查询参数并返回400错误', async () => {
      // Arrange
      mockGetWordsQuerySchema.safeParse.mockReturnValue({
        success: false,
        error: {
          issues: [{ message: 'Invalid page number', path: ['page'] }],
        },
      });
      
      const request = new NextRequest('http://localhost:3000/api/word?page=-1');

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data).toEqual({
        success: false,
        error: 'Invalid query parameters',
        code: 'VALIDATION_ERROR',
        details: [{ message: 'Invalid page number', path: ['page'] }],
      });
    });

    it('应该处理服务层错误', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockRejectedValue(new Error('Database connection failed'));
      
      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(data).toEqual({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      });
    });

    it('应该处理未授权访问', async () => {
      // Arrange
      shouldAuthenticationSucceed = false;
      authError = { message: 'No valid authentication found', code: 'UNAUTHORIZED_ERROR' };
      
      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'No valid authentication found',
        code: 'UNAUTHORIZED_ERROR',
      });
    });
  });

  describe('POST /api/word - 添加生词测试', () => {
    it('应该成功添加生词到用户生词本', async () => {
      // Arrange
      mockAddWordsBodySchema.safeParse.mockReturnValue({
        success: true,
        data: {
          words: ['new', 'vocabulary'],
          wordIds: [],
        },
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          words: ['new', 'vocabulary'],
        }),
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data).toEqual({
        success: true,
        data: {
          added: 2,
          skipped: 0,
          errors: [],
        },
        message: 'Successfully added 2 words to your vocabulary list',
      });
      
      expect(mockPracticeService.addWordsToUserList).toHaveBeenCalledWith('user-123', ['new', 'vocabulary'], []);
    });

    it('应该验证请求体并返回400错误', async () => {
      // Arrange
      mockAddWordsBodySchema.safeParse.mockReturnValue({
        success: false,
        error: {
          issues: [{ message: 'Invalid word format', path: ['words'] }],
        },
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          words: null,
        }),
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data).toEqual({
        success: false,
        error: 'Invalid request body',
        code: 'VALIDATION_ERROR',
        details: [{ message: 'Invalid word format', path: ['words'] }],
      });
    });

    it('应该要求正确的权限作用域', async () => {
      // Arrange
      shouldAuthenticationSucceed = false;
      authError = { 
        message: 'Required scopes: word:write', 
        code: 'FORBIDDEN_ERROR',
        status: 403 
      };
      
      mockAuthContext = {
        user: { id: 'user-123', name: 'Test User', email: '<EMAIL>', isActive: true },
        authMethod: 'jwt',
        clientType: 'mobile',
        permissions: ['word:read'],
        scopes: ['word:read'], // 缺少 word:write
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      };
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          words: ['test'],
        }),
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(data).toEqual({
        success: false,
        error: 'Required scopes: word:write',
        code: 'FORBIDDEN_ERROR',
      });
    });
  });

  describe('DELETE /api/word - 删除生词测试', () => {
    it('应该成功从用户生词本删除单词', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          wordIds: [1, 2, 3],
        }),
      });

      // Act
      const response = await DELETE(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data).toEqual({
        success: true,
        data: {
          deleted: 3,
          notFound: 0,
        },
        message: 'Successfully removed 3 words from your vocabulary list',
      });
      
      expect(mockPracticeService.removeWordsFromUserList).toHaveBeenCalledWith('user-123', [1, 2, 3]);
    });

    it('应该处理部分删除的情况', async () => {
      // Arrange
      mockPracticeService.removeWordsFromUserList.mockResolvedValue({
        deleted: 2,
        notFound: 1,
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          wordIds: [1, 2, 999],
        }),
      });

      // Act
      const response = await DELETE(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data).toEqual({
        success: true,
        data: {
          deleted: 2,
          notFound: 1,
        },
        message: 'Successfully removed 2 words from your vocabulary list, 1 words were not found in your list',
      });
    });

    it('应该验证请求体格式', async () => {
      // Arrange
      mockDeleteWordsBodySchema.safeParse.mockReturnValue({
        success: false,
        error: {
          issues: [{ message: 'Invalid wordIds format', path: ['wordIds'] }],
        },
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          wordIds: 'invalid',
        }),
      });

      // Act
      const response = await DELETE(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data).toEqual({
        success: false,
        error: 'Invalid request body',
        code: 'VALIDATION_ERROR',
        details: [{ message: 'Invalid wordIds format', path: ['wordIds'] }],
      });
    });
  });

  describe('认证与授权测试', () => {
    it('应该拒绝无效的认证令牌', async () => {
      // Arrange
      shouldAuthenticationSucceed = false;
      authError = { message: 'Invalid token', code: 'UNAUTHORIZED_ERROR' };
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer invalid-token',
        },
      });

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Invalid token',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该检查用户权限范围', async () => {
      // Arrange
      shouldAuthenticationSucceed = false;
      authError = { 
        message: 'Required scopes: word:read', 
        code: 'FORBIDDEN_ERROR',
        status: 403 
      };
      
      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(data).toEqual({
        success: false,
        error: 'Required scopes: word:read',
        code: 'FORBIDDEN_ERROR',
      });
    });
  });

  describe('错误处理测试', () => {
    it('应该处理JSON解析错误', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: 'invalid json',
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(data).toEqual({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      });
    });

    it('应该处理服务层抛出的业务错误', async () => {
      // Arrange
      mockPracticeService.addWordsToUserList.mockRejectedValue(
        new ConflictError('Word already exists in user list')
      );
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        headers: {
          'content-type': 'application/json',
        },
        body: JSON.stringify({
          words: ['duplicate'],
        }),
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(409);
      expect(data).toEqual({
        success: false,
        error: 'Word already exists in user list',
        code: 'CONFLICT_ERROR',
      });
    });
  });
});