/**
 * 数据库备份系统类型定义
 */

export interface BackupConfig {
  /** 数据库配置 */
  database: {
    containerName: string;
    host: string;
    port: number;
    username: string;
    password: string;
    database: string;
  };

  /** 备份配置 */
  backup: {
    outputDir: string;
    compression: boolean;
    format: BackupFormat;
    filenamePrefix: string;
    retentionDays: number;
    maxBackups: number;
  };

  /** Docker配置 */
  docker: {
    useDocker: boolean;
    containerName: string;
  };
}

export type BackupFormat = 'sql' | 'custom' | 'tar' | 'directory';

export type BackupType = 'full' | 'schema' | 'data';

export interface BackupOptions {
  type: BackupType;
  format: BackupFormat;
  compress: boolean;
  verbose: boolean;
  outputPath?: string;
  excludeTables?: string[];
  includeTables?: string[];
}

export interface BackupResult {
  success: boolean;
  filePath?: string;
  fileSize?: number;
  duration: number;
  backupType: BackupType;
  timestamp: string;
  checksum?: string;
  error?: string;
}

export interface BackupInfo {
  filename: string;
  filePath: string;
  size: number;
  sizeFormatted: string;
  createdAt: Date;
  type: BackupType;
  format: BackupFormat;
  compressed: boolean;
  checksum?: string;
  isValid: boolean;
}

export interface RestoreOptions {
  backupFile: string;
  dropDatabase?: boolean;
  createDatabase?: boolean;
  verbose?: boolean;
  skipData?: boolean;
  skipSchema?: boolean;
  targetDatabase?: string;
}

export interface RestoreResult {
  success: boolean;
  duration: number;
  restoredTables: number;
  restoredRows?: number;
  warnings: string[];
  error?: string;
}

export interface CleanupOptions {
  retentionDays?: number;
  maxBackups?: number;
  dryRun?: boolean;
  pattern?: string;
}

export interface CleanupResult {
  removedFiles: string[];
  removedCount: number;
  freedSpace: number;
  freedSpaceFormatted: string;
}

export interface BackupStats {
  totalBackups: number;
  totalSize: number;
  totalSizeFormatted: string;
  oldestBackup?: Date;
  newestBackup?: Date;
  backupsByType: Record<BackupType, number>;
  backupsByFormat: Record<BackupFormat, number>;
}

export interface DockerContainerInfo {
  id: string;
  name: string;
  image: string;
  state: string;
  status: string;
  ports: string[];
  isHealthy: boolean;
}

export interface DatabaseConnection {
  isConnected: boolean;
  version?: string;
  size?: string;
  tables?: number;
  error?: string;
}
