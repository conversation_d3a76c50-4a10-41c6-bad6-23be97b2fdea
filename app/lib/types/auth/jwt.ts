/**
 * JWT Token Type Definitions
 * 用于定义JWT令牌的TypeScript类型
 */

export interface JWTPayload {
  /** 用户ID */
  sub: string;
  /** 用户邮箱 */
  email: string;
  /** 用户名 */
  name?: string;
  /** 用户头像 */
  avatar?: string;
  /** 认证提供商 */
  provider?: string;
  /** 令牌签发时间 */
  iat: number;
  /** 令牌过期时间 */
  exp: number;
  /** 令牌颁发者 */
  iss?: string;
  /** 令牌受众 */
  aud?: string;
}

export interface AccessTokenPayload extends JWTPayload {
  /** 访问令牌类型 */
  type: 'access';
  /** 用户权限 */
  permissions?: string[];
  /** 会话ID */
  sessionId?: string;
  /** JWT ID for tracking */
  jti?: string;
  /** IP hash for binding */
  ipHash?: string;
}

export interface RefreshTokenPayload extends JWTPayload {
  /** 刷新令牌类型 */
  type: 'refresh';
  /** 刷新令牌ID */
  tokenId: string;
  /** JWT ID for tracking */
  jti?: string;
  /** Previous token hash for rotation tracking */
  rotatedFrom?: string;
}

export interface TokenResponse {
  /** 访问令牌 */
  accessToken: string;
  /** 刷新令牌 */
  refreshToken: string;
  /** 过期时间(秒) */
  expiresIn: number;
  /** 令牌类型 */
  tokenType: 'Bearer';
  /** 用户信息 */
  user: UserInfo;
}

export interface UserInfo {
  /** 用户ID */
  id: string;
  /** 用户邮箱 */
  email: string;
  /** 用户名 */
  name?: string;
  /** 用户头像 */
  avatar?: string;
  /** 认证提供商 */
  provider?: string;
  /** 账户激活状态 */
  isActive: boolean;
  /** 最后登录时间 */
  lastLoginAt?: Date;
}

export interface LoginRequest {
  /** 邮箱 */
  email: string;
  /** 密码 */
  password: string;
  /** 记住我 */
  remember?: boolean;
}

export interface LoginResponse extends TokenResponse {
  /** 登录成功状态 */
  success: true;
  /** 消息 */
  message: string;
}

export interface RefreshTokenRequest {
  /** 刷新令牌 */
  refreshToken: string;
}

export interface RefreshTokenResponse {
  /** 新的访问令牌 */
  accessToken: string;
  /** 新的刷新令牌 */
  refreshToken: string;
  /** 过期时间(秒) */
  expiresIn: number;
  /** 令牌类型 */
  tokenType: 'Bearer';
}

export interface LogoutRequest {
  /** 刷新令牌 (可选) */
  refreshToken?: string;
  /** 是否撤销所有令牌 */
  all?: boolean;
}

export interface LogoutResponse {
  /** 操作成功状态 */
  success: boolean;
  /** 消息 */
  message: string;
}
