import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';
import { z } from 'zod';
import { sendPasswordResetSuccessEmail } from '../../../lib/5-providers/email';

// 使用简单的Prisma客户端
const simplePrisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Reset token is required'),
  newPassword: z.string().min(6, 'Password must be at least 6 characters'),
});

export async function POST(request: NextRequest) {
  try {
    console.log('🔐 Password reset confirmation received');
    const body = await request.json();

    // 验证请求数据
    const validation = resetPasswordSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        {
          message: 'Invalid input',
          errors: validation.error.errors,
        },
        { status: 400 }
      );
    }

    const { token, newPassword } = validation.data;
    console.log('🔍 Processing password reset with token');

    // 查找重置令牌
    const resetToken = await simplePrisma.passwordResetToken.findUnique({
      where: { token },
      include: { user: true },
    });

    if (!resetToken) {
      console.log('❌ Invalid reset token');
      return NextResponse.json(
        {
          message: 'Invalid or expired reset token',
          type: 'invalid_token',
        },
        { status: 400 }
      );
    }

    // 检查令牌是否过期
    if (resetToken.expires < new Date()) {
      console.log('⏰ Reset token expired');
      // 删除过期令牌
      await simplePrisma.passwordResetToken.delete({
        where: { id: resetToken.id },
      });

      return NextResponse.json(
        {
          message: 'Reset token has expired. Please request a new password reset.',
          type: 'token_expired',
        },
        { status: 400 }
      );
    }

    // 检查令牌是否已使用
    if (resetToken.used) {
      console.log('🔒 Reset token already used');
      return NextResponse.json(
        {
          message: 'This reset token has already been used',
          type: 'token_used',
        },
        { status: 400 }
      );
    }

    // 检查用户是否仍然活跃
    if (!resetToken.user.isActive) {
      console.log('⚠️ User account is inactive');
      return NextResponse.json(
        {
          message: 'User account is not active',
          type: 'account_inactive',
        },
        { status: 400 }
      );
    }

    console.log('✅ Reset token valid, updating password for user:', resetToken.user.id);

    // 哈希新密码
    const passwordHash = await bcrypt.hash(newPassword, 12);

    // 使用事务更新密码并标记令牌为已使用
    await simplePrisma.$transaction(async (tx) => {
      // 更新用户密码
      await tx.user.update({
        where: { id: resetToken.user.id },
        data: {
          passwordHash,
          updatedAt: new Date(),
        },
      });

      // 标记令牌为已使用
      await tx.passwordResetToken.update({
        where: { id: resetToken.id },
        data: { used: true },
      });

      // 删除用户的其他所有重置令牌
      await tx.passwordResetToken.deleteMany({
        where: {
          userId: resetToken.user.id,
          id: { not: resetToken.id },
        },
      });
    });

    console.log('🎉 Password reset successful');

    // 发送密码重置成功通知邮件
    try {
      const emailResult = await sendPasswordResetSuccessEmail(
        resetToken.user.email,
        resetToken.user.name || undefined
      );

      if (emailResult.success) {
        console.log('✅ Password reset success email sent');
      } else {
        console.error('❌ Failed to send success notification email:', emailResult.error);
      }
    } catch (emailError) {
      console.error('❌ Error sending success notification:', emailError);
    }

    return NextResponse.json(
      {
        message: 'Password has been reset successfully. You can now login with your new password.',
        type: 'success',
        user: {
          id: resetToken.user.id,
          email: resetToken.user.email,
          name: resetToken.user.name,
        },
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('💥 Reset password error:', error);
    return NextResponse.json(
      {
        message: 'Internal server error. Please try again later.',
        details: error.message,
      },
      { status: 500 }
    );
  }
}

// GET方法用于验证令牌有效性（可选）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        {
          message: 'Token is required',
          valid: false,
        },
        { status: 400 }
      );
    }

    const resetToken = await simplePrisma.passwordResetToken.findUnique({
      where: { token },
      include: { user: { select: { email: true, name: true } } },
    });

    if (!resetToken || resetToken.expires < new Date() || resetToken.used) {
      return NextResponse.json(
        {
          message: 'Invalid or expired token',
          valid: false,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        message: 'Token is valid',
        valid: true,
        email: resetToken.user.email,
      },
      { status: 200 }
    );
  } catch (error: any) {
    console.error('Token validation error:', error);
    return NextResponse.json(
      {
        message: 'Error validating token',
        valid: false,
      },
      { status: 500 }
    );
  }
}
