#!/usr/bin/env tsx
/**
 * 密码重置功能测试脚本
 * 测试完整的密码重置流程：请求重置 -> 验证令牌 -> 重置密码
 */

import { PrismaClient } from '@prisma/client';

const BASE_URL = 'http://localhost:4000';

// 直接使用数据库客户端
const testDb = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

/**
 * HTTP请求辅助函数
 */
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    let data;
    try {
      data = await response.json();
    } catch {
      data = {};
    }

    return {
      response,
      data,
      status: response.status,
      ok: response.ok,
    };
  } catch (error) {
    console.error(`${colors.red}请求失败:${colors.reset}`, error.message);
    return {
      response: null,
      data: { message: error.message },
      status: 500,
      ok: false,
    };
  }
}

/**
 * 数据库清理函数
 */
async function cleanupUser(email: string) {
  try {
    const user = await testDb.user.findUnique({ where: { email } });
    if (user) {
      await testDb.$transaction(async (tx) => {
        await tx.passwordResetToken.deleteMany({ where: { userId: user.id } });
        await tx.account.deleteMany({ where: { userId: user.id } });
        await tx.session.deleteMany({ where: { userId: user.id } });
        await tx.refreshToken.deleteMany({ where: { userId: user.id } });
        await tx.rateLimit.deleteMany({ where: { userId: user.id } });
        await tx.userWordStat.deleteMany({ where: { userId: user.id } });
        await tx.user.delete({ where: { id: user.id } });
      });
      return true;
    }
    return false;
  } catch (error) {
    console.log(`清理用户失败 ${email}:`, error.message);
    return false;
  }
}

/**
 * 测试结果统计
 */
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

/**
 * 断言函数
 */
function expect(actual: any, expected: any, message: string) {
  totalTests++;
  if (actual === expected) {
    console.log(`  ${colors.green}✅ ${message}${colors.reset}`);
    passedTests++;
    return true;
  } else {
    console.log(`  ${colors.red}❌ ${message}${colors.reset}`);
    console.log(`     期望: ${expected}, 实际: ${actual}`);
    failedTests++;
    return false;
  }
}

/**
 * 密码重置功能测试
 */
async function runPasswordResetTests() {
  console.log(`${colors.bold}${colors.blue}🔑 密码重置功能测试开始${colors.reset}\\n`);

  const testEmail = `password-reset-test-${Date.now()}@example.com`;
  const testPassword = 'original-password-123';
  const newPassword = 'new-password-456';

  try {
    // 1. 准备测试用户
    console.log(`${colors.yellow}👤 创建测试用户${colors.reset}`);

    const registerResult = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        name: 'Password Reset Test User',
      }),
    });

    expect(registerResult.status, 201, '成功创建测试用户');

    // 2. 测试密码重置请求
    console.log(`\\n${colors.yellow}📧 测试密码重置请求${colors.reset}`);

    const forgotPasswordResult = await apiRequest('/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
      }),
    });

    expect(forgotPasswordResult.status, 200, '密码重置请求成功');
    expect(forgotPasswordResult.data.type, 'success', '返回成功类型');

    // 获取生成的重置令牌（开发环境）
    let resetToken = null;
    if (forgotPasswordResult.data.debug && forgotPasswordResult.data.debug.resetToken) {
      resetToken = forgotPasswordResult.data.debug.resetToken;
      console.log(
        `  ${colors.blue}🔗 重置链接: ${forgotPasswordResult.data.debug.resetLink}${colors.reset}`
      );
    } else {
      // 从数据库获取令牌
      const user = await testDb.user.findUnique({ where: { email: testEmail } });
      if (user) {
        const tokenRecord = await testDb.passwordResetToken.findFirst({
          where: { userId: user.id, used: false },
          orderBy: { createdAt: 'desc' },
        });
        if (tokenRecord) {
          resetToken = tokenRecord.token;
        }
      }
    }

    expect(resetToken ? true : false, true, '生成了重置令牌');

    if (!resetToken) {
      throw new Error('无法获取重置令牌');
    }

    // 3. 测试令牌验证
    console.log(`\\n${colors.yellow}🔍 测试令牌验证${colors.reset}`);

    const tokenValidationResult = await apiRequest(`/api/auth/reset-password?token=${resetToken}`, {
      method: 'GET',
    });

    expect(tokenValidationResult.status, 200, '令牌验证成功');
    expect(tokenValidationResult.data.valid, true, '令牌有效');
    expect(tokenValidationResult.data.email, testEmail, '返回正确的邮箱');

    // 4. 测试密码重置
    console.log(`\\n${colors.yellow}🔐 测试密码重置${colors.reset}`);

    const resetPasswordResult = await apiRequest('/api/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({
        token: resetToken,
        newPassword: newPassword,
      }),
    });

    expect(resetPasswordResult.status, 200, '密码重置成功');
    expect(resetPasswordResult.data.type, 'success', '返回成功类型');
    expect(resetPasswordResult.data.user.email, testEmail, '返回正确的用户信息');

    // 5. 验证新密码可以登录
    console.log(`\\n${colors.yellow}✅ 验证新密码登录${colors.reset}`);

    const loginResult = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
        password: newPassword,
      }),
    });

    expect(loginResult.status, 200, '使用新密码登录成功');
    expect(loginResult.data.type, 'login', '智能登录识别成功');

    // 6. 验证旧密码无法登录
    console.log(`\\n${colors.yellow}❌ 验证旧密码无法登录${colors.reset}`);

    const oldPasswordResult = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
      }),
    });

    expect(oldPasswordResult.status, 400, '旧密码无法登录');
    expect(oldPasswordResult.data.type, 'user_exists', '返回用户已存在错误');

    // 7. 测试令牌重复使用
    console.log(`\\n${colors.yellow}🚫 测试令牌重复使用${colors.reset}`);

    const reusedTokenResult = await apiRequest('/api/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({
        token: resetToken,
        newPassword: 'another-password',
      }),
    });

    expect(reusedTokenResult.status, 400, '拒绝重复使用令牌');
    expect(reusedTokenResult.data.type, 'token_used', '返回令牌已使用错误');

    // 8. 测试不存在用户的重置请求
    console.log(`\\n${colors.yellow}👻 测试不存在用户的重置请求${colors.reset}`);

    const nonExistentUserResult = await apiRequest('/api/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
      }),
    });

    expect(nonExistentUserResult.status, 200, '不存在用户也返回成功（安全考虑）');
    expect(nonExistentUserResult.data.type, 'success', '返回成功类型');

    // 9. 测试无效令牌
    console.log(`\\n${colors.yellow}🔒 测试无效令牌${colors.reset}`);

    const invalidTokenResult = await apiRequest('/api/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({
        token: 'invalid-token-12345',
        newPassword: 'some-password',
      }),
    });

    expect(invalidTokenResult.status, 400, '拒绝无效令牌');
    expect(invalidTokenResult.data.type, 'invalid_token', '返回无效令牌错误');
  } catch (error) {
    console.error(`${colors.red}测试执行失败:${colors.reset}`, error);
    failedTests++;
  } finally {
    // 清理测试数据
    await cleanupUser(testEmail);
    await testDb.$disconnect();
  }

  // 输出测试结果
  console.log(`\\n${colors.bold}📊 测试结果统计${colors.reset}`);
  console.log(`总测试数: ${totalTests}`);
  console.log(`${colors.green}通过: ${passedTests}${colors.reset}`);
  console.log(`${colors.red}失败: ${failedTests}${colors.reset}`);

  if (failedTests === 0) {
    console.log(
      `\\n${colors.green}${colors.bold}🎉 所有测试通过！密码重置功能工作正常${colors.reset}`
    );
    process.exit(0);
  } else {
    console.log(`\\n${colors.red}${colors.bold}💥 有测试失败，请检查密码重置功能${colors.reset}`);
    process.exit(1);
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await fetch(`${BASE_URL}/api/auth/register`);
    return true;
  } catch (error) {
    console.error(`${colors.red}无法连接到服务器 ${BASE_URL}${colors.reset}`);
    console.error('请确保开发服务器正在运行: pnpm dev');
    return false;
  }
}

// 主函数
async function main() {
  console.log(`${colors.blue}检查服务器连接...${colors.reset}`);

  if (await checkServer()) {
    console.log(`${colors.green}服务器连接正常${colors.reset}\\n`);
    await runPasswordResetTests();
  } else {
    process.exit(1);
  }
}

main().catch(console.error);
