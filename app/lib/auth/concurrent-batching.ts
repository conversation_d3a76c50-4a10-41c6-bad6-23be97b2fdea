// Concurrent request batching for optimized processing
export interface BatchRequest {
  id: string;
  data: any;
  priority: 'low' | 'medium' | 'high';
  timestamp: number;
  timeout: number;
  resolve: (result: any) => void;
  reject: (error: Error) => void;
}

export interface BatchProcessingOptions {
  maxBatchSize: number;
  maxWaitTime: number;
  maxConcurrency: number;
  priorityLevels: number;
}

export class ConcurrentBatcher<T, R> {
  private pendingRequests: Map<string, BatchRequest> = new Map();
  private priorityQueues: Map<string, BatchRequest[]> = new Map();
  private processingQueue: BatchRequest[] = [];
  private activeProcessors = 0;
  private batchProcessor: (items: T[]) => Promise<R[]>;
  private options: BatchProcessingOptions;
  private batchTimer: NodeJS.Timeout | null = null;

  constructor(
    batchProcessor: (items: T[]) => Promise<R[]>,
    options: Partial<BatchProcessingOptions> = {}
  ) {
    this.batchProcessor = batchProcessor;
    this.options = {
      maxBatchSize: 50,
      maxWaitTime: 10, // 10ms
      maxConcurrency: 10,
      priorityLevels: 3,
      ...options,
    };

    // Initialize priority queues
    this.priorityQueues.set('high', []);
    this.priorityQueues.set('medium', []);
    this.priorityQueues.set('low', []);
  }

  async process(
    data: T,
    priority: 'low' | 'medium' | 'high' = 'medium',
    timeout = 5000
  ): Promise<R> {
    return new Promise((resolve, reject) => {
      const request: BatchRequest = {
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        data,
        priority,
        timestamp: Date.now(),
        timeout,
        resolve,
        reject,
      };

      // Add to pending requests
      this.pendingRequests.set(request.id, request);

      // Add to priority queue
      const queue = this.priorityQueues.get(priority)!;
      queue.push(request);

      // Set timeout for individual request
      setTimeout(() => {
        if (this.pendingRequests.has(request.id)) {
          this.pendingRequests.delete(request.id);
          this.removeFromQueue(request);
          reject(new Error('Request timeout'));
        }
      }, timeout);

      // Trigger batch processing
      this.scheduleBatchProcessing();
    });
  }

  private removeFromQueue(request: BatchRequest): void {
    const queue = this.priorityQueues.get(request.priority);
    if (queue) {
      const index = queue.findIndex((r) => r.id === request.id);
      if (index !== -1) {
        queue.splice(index, 1);
      }
    }
  }

  private scheduleBatchProcessing(): void {
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }

    // Process immediately if batch is full or we have high priority items
    const highPriorityQueue = this.priorityQueues.get('high')!;
    const totalPending = this.getTotalPendingCount();

    if (totalPending >= this.options.maxBatchSize || highPriorityQueue.length > 0) {
      this.processBatch();
    } else {
      // Schedule batch processing after max wait time
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, this.options.maxWaitTime);
    }
  }

  private getTotalPendingCount(): number {
    return Array.from(this.priorityQueues.values()).reduce(
      (total, queue) => total + queue.length,
      0
    );
  }

  private async processBatch(): Promise<void> {
    if (this.activeProcessors >= this.options.maxConcurrency) {
      return;
    }

    const batch = this.createBatch();
    if (batch.length === 0) {
      return;
    }

    this.activeProcessors++;

    try {
      const results = await this.batchProcessor(batch.map((r) => r.data));

      // Resolve all requests in the batch
      batch.forEach((request, index) => {
        if (this.pendingRequests.has(request.id)) {
          this.pendingRequests.delete(request.id);
          request.resolve(results[index]);
        }
      });
    } catch (error) {
      // Reject all requests in the batch
      batch.forEach((request) => {
        if (this.pendingRequests.has(request.id)) {
          this.pendingRequests.delete(request.id);
          request.reject(error as Error);
        }
      });
    } finally {
      this.activeProcessors--;

      // Schedule next batch if there are more pending requests
      if (this.getTotalPendingCount() > 0) {
        this.scheduleBatchProcessing();
      }
    }
  }

  private createBatch(): BatchRequest[] {
    const batch: BatchRequest[] = [];
    const now = Date.now();

    // Process high priority first
    const highQueue = this.priorityQueues.get('high')!;
    while (highQueue.length > 0 && batch.length < this.options.maxBatchSize) {
      const request = highQueue.shift()!;
      if (now - request.timestamp < request.timeout) {
        batch.push(request);
      } else {
        // Request timed out
        this.pendingRequests.delete(request.id);
        request.reject(new Error('Request timeout'));
      }
    }

    // Fill remaining slots with medium priority
    const mediumQueue = this.priorityQueues.get('medium')!;
    while (mediumQueue.length > 0 && batch.length < this.options.maxBatchSize) {
      const request = mediumQueue.shift()!;
      if (now - request.timestamp < request.timeout) {
        batch.push(request);
      } else {
        this.pendingRequests.delete(request.id);
        request.reject(new Error('Request timeout'));
      }
    }

    // Fill remaining slots with low priority
    const lowQueue = this.priorityQueues.get('low')!;
    while (lowQueue.length > 0 && batch.length < this.options.maxBatchSize) {
      const request = lowQueue.shift()!;
      if (now - request.timestamp < request.timeout) {
        batch.push(request);
      } else {
        this.pendingRequests.delete(request.id);
        request.reject(new Error('Request timeout'));
      }
    }

    return batch;
  }

  getStats() {
    return {
      pendingRequests: this.pendingRequests.size,
      activeProcessors: this.activeProcessors,
      queueSizes: {
        high: this.priorityQueues.get('high')!.length,
        medium: this.priorityQueues.get('medium')!.length,
        low: this.priorityQueues.get('low')!.length,
      },
      totalPending: this.getTotalPendingCount(),
    };
  }
}

// Security-specific batch processor
export class SecurityBatchProcessor {
  private threatDetectionBatcher: ConcurrentBatcher<string, { threats: string[]; score: number }>;
  private validationBatcher: ConcurrentBatcher<any, { valid: boolean; errors: string[] }>;
  private ipCheckBatcher: ConcurrentBatcher<string, { blocked: boolean; reason?: string }>;

  constructor() {
    // Threat detection batcher
    this.threatDetectionBatcher = new ConcurrentBatcher(
      async (inputs: string[]) => {
        return this.batchThreatDetection(inputs);
      },
      {
        maxBatchSize: 100,
        maxWaitTime: 5,
        maxConcurrency: 5,
      }
    );

    // Validation batcher
    this.validationBatcher = new ConcurrentBatcher(
      async (inputs: any[]) => {
        return this.batchValidation(inputs);
      },
      {
        maxBatchSize: 50,
        maxWaitTime: 3,
        maxConcurrency: 8,
      }
    );

    // IP check batcher
    this.ipCheckBatcher = new ConcurrentBatcher(
      async (ips: string[]) => {
        return this.batchIPCheck(ips);
      },
      {
        maxBatchSize: 200,
        maxWaitTime: 2,
        maxConcurrency: 3,
      }
    );
  }

  async detectThreats(
    input: string,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<{ threats: string[]; score: number }> {
    return this.threatDetectionBatcher.process(input, priority);
  }

  async validateInput(
    input: any,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<{ valid: boolean; errors: string[] }> {
    return this.validationBatcher.process(input, priority);
  }

  async checkIP(
    ip: string,
    priority: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<{ blocked: boolean; reason?: string }> {
    return this.ipCheckBatcher.process(ip, priority);
  }

  private async batchThreatDetection(
    inputs: string[]
  ): Promise<{ threats: string[]; score: number }[]> {
    // Simplified threat detection patterns
    const patterns = [
      { pattern: /('.*'|;|--|\bunion\b|\bselect\b)/i, threat: 'sql_injection', weight: 10 },
      { pattern: /<script/i, threat: 'xss', weight: 8 },
      { pattern: /\.\./i, threat: 'path_traversal', weight: 6 },
      { pattern: /(cmd|eval|exec)\s*\(/i, threat: 'code_injection', weight: 9 },
    ];

    return inputs.map((input) => {
      const threats: string[] = [];
      let score = 0;

      for (const { pattern, threat, weight } of patterns) {
        if (pattern.test(input)) {
          threats.push(threat);
          score += weight;
        }
      }

      return { threats, score };
    });
  }

  private async batchValidation(inputs: any[]): Promise<{ valid: boolean; errors: string[] }[]> {
    return inputs.map((input) => {
      const errors: string[] = [];

      if (typeof input === 'string') {
        if (input.length > 10000) {
          errors.push('Input too long');
        }
        if (input.includes('\x00')) {
          errors.push('Null byte detected');
        }
      }

      return {
        valid: errors.length === 0,
        errors,
      };
    });
  }

  private async batchIPCheck(ips: string[]): Promise<{ blocked: boolean; reason?: string }[]> {
    // Simplified IP blocking logic
    const blockedIPs = new Set(['127.0.0.1', '*************']);

    return ips.map((ip) => {
      if (blockedIPs.has(ip)) {
        return { blocked: true, reason: 'IP in blocklist' };
      }

      // Check for private IP ranges in production
      if (ip.startsWith('10.') || ip.startsWith('192.168.') || ip.startsWith('172.')) {
        return { blocked: false, reason: 'Private IP' };
      }

      return { blocked: false };
    });
  }

  getStats() {
    return {
      threatDetection: this.threatDetectionBatcher.getStats(),
      validation: this.validationBatcher.getStats(),
      ipCheck: this.ipCheckBatcher.getStats(),
    };
  }
}

// Adaptive batching that adjusts based on load
export class AdaptiveBatchProcessor {
  private batcher: ConcurrentBatcher<any, any>;
  private loadMetrics = {
    avgProcessingTime: 0,
    throughput: 0,
    queueDepth: 0,
  };
  private adaptiveOptions: BatchProcessingOptions;

  constructor(
    batchProcessor: (items: any[]) => Promise<any[]>,
    baseOptions: Partial<BatchProcessingOptions> = {}
  ) {
    this.adaptiveOptions = {
      maxBatchSize: 50,
      maxWaitTime: 10,
      maxConcurrency: 5,
      priorityLevels: 3,
      ...baseOptions,
    };

    this.batcher = new ConcurrentBatcher(async (items: any[]) => {
      const startTime = Date.now();
      const results = await batchProcessor(items);
      const processingTime = Date.now() - startTime;

      this.updateMetrics(processingTime, items.length);
      this.adaptSettings();

      return results;
    }, this.adaptiveOptions);
  }

  private updateMetrics(processingTime: number, batchSize: number): void {
    // Update average processing time
    this.loadMetrics.avgProcessingTime =
      this.loadMetrics.avgProcessingTime * 0.9 + processingTime * 0.1;

    // Update throughput
    const throughput = batchSize / (processingTime / 1000);
    this.loadMetrics.throughput = this.loadMetrics.throughput * 0.9 + throughput * 0.1;

    // Update queue depth
    const stats = this.batcher.getStats();
    this.loadMetrics.queueDepth = stats.totalPending;
  }

  private adaptSettings(): void {
    // Increase concurrency if queue is growing
    if (this.loadMetrics.queueDepth > 100) {
      this.adaptiveOptions.maxConcurrency = Math.min(20, this.adaptiveOptions.maxConcurrency + 1);
    } else if (this.loadMetrics.queueDepth < 10) {
      this.adaptiveOptions.maxConcurrency = Math.max(1, this.adaptiveOptions.maxConcurrency - 1);
    }

    // Adjust batch size based on processing time
    if (this.loadMetrics.avgProcessingTime > 100) {
      this.adaptiveOptions.maxBatchSize = Math.max(10, this.adaptiveOptions.maxBatchSize - 5);
    } else if (this.loadMetrics.avgProcessingTime < 50) {
      this.adaptiveOptions.maxBatchSize = Math.min(200, this.adaptiveOptions.maxBatchSize + 5);
    }

    // Adjust wait time based on throughput
    if (this.loadMetrics.throughput > 1000) {
      this.adaptiveOptions.maxWaitTime = Math.max(1, this.adaptiveOptions.maxWaitTime - 1);
    } else if (this.loadMetrics.throughput < 100) {
      this.adaptiveOptions.maxWaitTime = Math.min(50, this.adaptiveOptions.maxWaitTime + 1);
    }
  }

  async process(
    data: any,
    priority: 'low' | 'medium' | 'high' = 'medium',
    timeout = 5000
  ): Promise<any> {
    return this.batcher.process(data, priority, timeout);
  }

  getMetrics() {
    return {
      batcherStats: this.batcher.getStats(),
      loadMetrics: this.loadMetrics,
      adaptiveOptions: this.adaptiveOptions,
    };
  }
}

// Factory function for creating optimized batch processors
export function createSecurityBatchProcessor(): SecurityBatchProcessor {
  return new SecurityBatchProcessor();
}

export function createAdaptiveBatchProcessor(
  batchProcessor: (items: any[]) => Promise<any[]>,
  options?: Partial<BatchProcessingOptions>
): AdaptiveBatchProcessor {
  return new AdaptiveBatchProcessor(batchProcessor, options);
}
