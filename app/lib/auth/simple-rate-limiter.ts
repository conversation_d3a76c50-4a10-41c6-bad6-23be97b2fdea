/**
 * 简化版基础速率限制器
 * 使用内存存储，满足基础安全需求
 */

interface RateLimitEntry {
  count: number;
  resetTime: number;
}

interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
}

class SimpleRateLimiter {
  private store = new Map<string, RateLimitEntry>();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;

    // 每5分钟清理一次过期记录
    setInterval(() => this.cleanup(), 5 * 60 * 1000);
  }

  check(key: string): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const entry = this.store.get(key);

    if (!entry || now > entry.resetTime) {
      // 新建或重置窗口
      const newEntry: RateLimitEntry = {
        count: 1,
        resetTime: now + this.config.windowMs,
      };
      this.store.set(key, newEntry);

      return {
        allowed: true,
        remaining: this.config.maxRequests - 1,
        resetTime: newEntry.resetTime,
      };
    }

    // 更新计数
    entry.count++;

    const allowed = entry.count <= this.config.maxRequests;
    const remaining = Math.max(0, this.config.maxRequests - entry.count);

    return {
      allowed,
      remaining,
      resetTime: entry.resetTime,
    };
  }

  private cleanup() {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

// 预定义的速率限制配置
export const rateLimitConfigs = {
  login: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 5, // 5次
  },
  tokenRefresh: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 10, // 10次
  },
  general: {
    windowMs: 60 * 1000, // 1分钟
    maxRequests: 30, // 30次
  },
};

// 全局速率限制器实例
export const loginRateLimiter = new SimpleRateLimiter(rateLimitConfigs.login);
export const tokenRefreshRateLimiter = new SimpleRateLimiter(rateLimitConfigs.tokenRefresh);
export const generalRateLimiter = new SimpleRateLimiter(rateLimitConfigs.general);

// 便捷的检查函数
export function checkRateLimit(ip: string, type: 'login' | 'tokenRefresh' | 'general' = 'general') {
  const key = `${ip}:${type}`;

  switch (type) {
    case 'login':
      return loginRateLimiter.check(key);
    case 'tokenRefresh':
      return tokenRefreshRateLimiter.check(key);
    case 'general':
    default:
      return generalRateLimiter.check(key);
  }
}
