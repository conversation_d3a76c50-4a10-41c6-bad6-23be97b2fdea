'use client';

// Platform-specific storage interface
export interface StorageAdapter {
  get(key: string): Promise<string | null>;
  set(key: string, value: string): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
}

// Web storage adapter (localStorage)
export class WebStorageAdapter implements StorageAdapter {
  async get(key: string): Promise<string | null> {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(key);
  }

  async set(key: string, value: string): Promise<void> {
    if (typeof window === 'undefined') return;
    localStorage.setItem(key, value);
  }

  async remove(key: string): Promise<void> {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(key);
  }

  async clear(): Promise<void> {
    if (typeof window === 'undefined') return;
    localStorage.clear();
  }
}

// Browser extension storage adapter
export class ExtensionStorageAdapter implements StorageAdapter {
  private isAvailable(): boolean {
    return (
      typeof window !== 'undefined' &&
      (window as any).chrome &&
      (window as any).chrome.storage &&
      (window as any).chrome.storage.local
    );
  }

  async get(key: string): Promise<string | null> {
    if (!this.isAvailable()) return null;

    return new Promise((resolve) => {
      (window as any).chrome.storage.local.get([key], (result: any) => {
        resolve(result[key] || null);
      });
    });
  }

  async set(key: string, value: string): Promise<void> {
    if (!this.isAvailable()) return;

    return new Promise((resolve) => {
      (window as any).chrome.storage.local.set({ [key]: value }, () => {
        resolve();
      });
    });
  }

  async remove(key: string): Promise<void> {
    if (!this.isAvailable()) return;

    return new Promise((resolve) => {
      (window as any).chrome.storage.local.remove([key], () => {
        resolve();
      });
    });
  }

  async clear(): Promise<void> {
    if (!this.isAvailable()) return;

    return new Promise((resolve) => {
      (window as any).chrome.storage.local.clear(() => {
        resolve();
      });
    });
  }
}

// Mobile storage adapter (simulated for web, would use AsyncStorage in React Native)
export class MobileStorageAdapter implements StorageAdapter {
  private prefix = 'lucid_mobile_';

  async get(key: string): Promise<string | null> {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.prefix + key);
  }

  async set(key: string, value: string): Promise<void> {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.prefix + key, value);
  }

  async remove(key: string): Promise<void> {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.prefix + key);
  }

  async clear(): Promise<void> {
    if (typeof window === 'undefined') return;
    const keys = Object.keys(localStorage).filter((key) => key.startsWith(this.prefix));
    keys.forEach((key) => localStorage.removeItem(key));
  }
}

// Memory storage adapter (fallback for unsupported environments)
export class MemoryStorageAdapter implements StorageAdapter {
  private storage = new Map<string, string>();

  async get(key: string): Promise<string | null> {
    return this.storage.get(key) || null;
  }

  async set(key: string, value: string): Promise<void> {
    this.storage.set(key, value);
  }

  async remove(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }
}

// Platform detection utilities
export class PlatformDetector {
  static get isWeb(): boolean {
    return typeof window !== 'undefined' && !this.isExtension && !this.isMobile;
  }

  static get isExtension(): boolean {
    return (
      typeof window !== 'undefined' &&
      (window as any).chrome &&
      (window as any).chrome.runtime &&
      (window as any).chrome.runtime.id !== undefined
    );
  }

  static get isMobile(): boolean {
    return (
      typeof window !== 'undefined' &&
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    );
  }

  static get isIOS(): boolean {
    return typeof window !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  static get isAndroid(): boolean {
    return typeof window !== 'undefined' && /Android/.test(navigator.userAgent);
  }

  static get platform(): 'web' | 'extension' | 'mobile' | 'unknown' {
    if (this.isExtension) return 'extension';
    if (this.isMobile) return 'mobile';
    if (this.isWeb) return 'web';
    return 'unknown';
  }
}

// Storage factory
export class StorageFactory {
  static createAdapter(): StorageAdapter {
    const platform = PlatformDetector.platform;

    switch (platform) {
      case 'extension':
        return new ExtensionStorageAdapter();
      case 'mobile':
        return new MobileStorageAdapter();
      case 'web':
        return new WebStorageAdapter();
      default:
        console.warn('Unknown platform, falling back to memory storage');
        return new MemoryStorageAdapter();
    }
  }

  static createSecureAdapter(): StorageAdapter {
    // For secure storage, we might want to use different strategies
    // on different platforms (e.g., keychain on iOS, encrypted storage on Android)
    const platform = PlatformDetector.platform;

    switch (platform) {
      case 'extension':
        // Chrome extension storage is already sandboxed per extension
        return new ExtensionStorageAdapter();
      case 'mobile':
        // In a real mobile app, this would use encrypted storage
        return new MobileStorageAdapter();
      case 'web':
        // Web storage is not encrypted, but it's what we have
        return new WebStorageAdapter();
      default:
        return new MemoryStorageAdapter();
    }
  }
}

// Enhanced token storage with platform-specific adapters
export class PlatformTokenStorage {
  private static adapter: StorageAdapter = StorageFactory.createSecureAdapter();

  private static readonly ACCESS_TOKEN_KEY = 'access_token';
  private static readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private static readonly USER_KEY = 'user_data';

  static async getAccessToken(): Promise<string | null> {
    try {
      return await this.adapter.get(this.ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get access token:', error);
      return null;
    }
  }

  static async setAccessToken(token: string): Promise<void> {
    try {
      await this.adapter.set(this.ACCESS_TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to set access token:', error);
    }
  }

  static async getRefreshToken(): Promise<string | null> {
    try {
      return await this.adapter.get(this.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get refresh token:', error);
      return null;
    }
  }

  static async setRefreshToken(token: string): Promise<void> {
    try {
      await this.adapter.set(this.REFRESH_TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to set refresh token:', error);
    }
  }

  static async getUserData(): Promise<any | null> {
    try {
      const data = await this.adapter.get(this.USER_KEY);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.error('Failed to get user data:', error);
      return null;
    }
  }

  static async setUserData(user: any): Promise<void> {
    try {
      await this.adapter.set(this.USER_KEY, JSON.stringify(user));
    } catch (error) {
      console.error('Failed to set user data:', error);
    }
  }

  static async clearAll(): Promise<void> {
    try {
      await Promise.all([
        this.adapter.remove(this.ACCESS_TOKEN_KEY),
        this.adapter.remove(this.REFRESH_TOKEN_KEY),
        this.adapter.remove(this.USER_KEY),
      ]);
    } catch (error) {
      console.error('Failed to clear auth data:', error);
    }
  }

  static async isTokenValid(): Promise<boolean> {
    try {
      const token = await this.getAccessToken();
      if (!token) return false;

      // Basic JWT validation
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp > currentTime;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  // Migration helper for existing localStorage users
  static async migrateFromLocalStorage(): Promise<void> {
    if (typeof window === 'undefined') return;

    try {
      const accessToken = localStorage.getItem('accessToken');
      const refreshToken = localStorage.getItem('refreshToken');
      const userData = localStorage.getItem('user');

      if (accessToken) {
        await this.setAccessToken(accessToken);
        localStorage.removeItem('accessToken');
      }

      if (refreshToken) {
        await this.setRefreshToken(refreshToken);
        localStorage.removeItem('refreshToken');
      }

      if (userData) {
        await this.setUserData(JSON.parse(userData));
        localStorage.removeItem('user');
      }

      console.log('Successfully migrated auth data to platform storage');
    } catch (error) {
      console.error('Failed to migrate from localStorage:', error);
    }
  }
}
