/**
 * NextAuth.js API Route Handler
 * Enhanced with Phase 2 Authentication System integration
 */

import NextAuth from 'next-auth';
import { PrismaAdapter } from '@next-auth/prisma-adapter';
import GoogleProvider from 'next-auth/providers/google';
import GitHubProvider from 'next-auth/providers/github';
import CredentialsProvider from 'next-auth/providers/credentials';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import type { NextAuthOptions } from 'next-auth';
import type { NextAuthConfig } from '@/lib/types/auth';
import { NextRequest, NextResponse } from 'next/server';

/**
 * Enhanced NextAuth Configuration
 * Integrated with Phase 2 JWT Token System
 */
const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),

  providers: [
    // Google OAuth Provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      authorization: {
        params: {
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),

    // GitHub OAuth Provider
    GitHubProvider({
      clientId: process.env.GITHUB_ID!,
      clientSecret: process.env.GITHUB_SECRET!,
    }),

    // Credentials Provider (Email/Password)
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
            select: {
              id: true,
              email: true,
              name: true,
              avatar: true,
              passwordHash: true,
              createdAt: true,
              updatedAt: true,
            },
          });

          if (!user || !user.passwordHash) {
            return null;
          }

          const isValidPassword = await bcrypt.compare(credentials.password, user.passwordHash);

          if (!isValidPassword) {
            return null;
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name || undefined,
            image: user.avatar || undefined,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      },
    }),
  ],

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },

  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  pages: {
    signIn: '/login',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/signup',
  },

  callbacks: {
    async jwt({ token, user, account, profile }) {
      // Initial sign in
      if (account && user) {
        token.accessToken = account.access_token;
        token.refreshToken = account.refresh_token;
        token.provider = account.provider;

        // Update user's last login
        await prisma.user.update({
          where: { id: user.id },
          data: {
            lastLoginAt: new Date(),
            provider: account.provider,
          },
        });
      }

      // Return previous token if the access token has not expired yet
      if (Date.now() < (token.accessTokenExpires as number)) {
        return token;
      }

      // Access token has expired, try to update it
      return await refreshAccessToken(token);
    },

    async session({ session, token }) {
      // Get user from database with latest info
      const user = await prisma.user.findUnique({
        where: { email: token.email as string },
        select: {
          id: true,
          email: true,
          name: true,
          avatar: true,
          isActive: true,
          lastLoginAt: true,
          provider: true,
        },
      });

      if (user) {
        session.user = {
          ...session.user,
          id: user.id,
          isActive: user.isActive,
          lastLoginAt: user.lastLoginAt || undefined,
          provider: user.provider || undefined,
        };
      }

      session.accessToken = token.accessToken as string;
      session.error = token.error;

      return session;
    },

    async signIn({ user, account, profile }) {
      // Check if user is active
      if (user.id) {
        const dbUser = await prisma.user.findUnique({
          where: { id: user.id },
          select: { isActive: true },
        });

        if (!dbUser?.isActive) {
          return false;
        }
      }

      // Allow OAuth sign ins
      if (account?.provider === 'google' || account?.provider === 'github') {
        return true;
      }

      // Allow credentials sign ins
      if (account?.provider === 'credentials') {
        return true;
      }

      return false;
    },

    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith('/')) return `${baseUrl}${url}`;

      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) return url;

      return baseUrl;
    },
  },

  events: {
    async signIn({ user, account, profile, isNewUser }) {
      console.log('Sign in event:', {
        userId: user.id,
        provider: account?.provider,
        isNewUser,
      });
    },

    async signOut({ token }) {
      console.log('Sign out event:', { userId: token?.sub });
    },
  },

  debug: process.env.NODE_ENV === 'development',
};

/**
 * Refresh access token using refresh token
 */
async function refreshAccessToken(token: any) {
  try {
    const url = 'https://oauth2.googleapis.com/token';

    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID!,
        client_secret: process.env.GOOGLE_CLIENT_SECRET!,
        grant_type: 'refresh_token',
        refresh_token: token.refreshToken,
      }),
      method: 'POST',
    });

    const refreshedTokens = await response.json();

    if (!response.ok) {
      throw refreshedTokens;
    }

    return {
      ...token,
      accessToken: refreshedTokens.access_token,
      accessTokenExpires: Date.now() + refreshedTokens.expires_in * 1000,
      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,
    };
  } catch (error) {
    console.error('Error refreshing access token:', error);

    return {
      ...token,
      error: 'RefreshAccessTokenError',
    };
  }
}

/**
 * 检测是否为 API 请求（浏览器扩展或其他程序化客户端）
 */
function isApiRequest(request: NextRequest): boolean {
  const contentType = request.headers.get('content-type') || '';
  const userAgent = request.headers.get('user-agent') || '';
  const clientType = request.headers.get('x-client-type') || '';
  const acceptHeader = request.headers.get('accept') || '';

  // 检测API请求的特征
  return (
    // 明确标识为扩展客户端
    clientType === 'extension' ||
    // Content-Type 为 JSON 且 Accept 不明确要求 HTML
    (contentType.includes('application/json') && !acceptHeader.includes('text/html')) ||
    // Accept header 明确偏好 JSON 而不是 HTML
    (acceptHeader.includes('application/json') && !acceptHeader.includes('text/html')) ||
    // User-Agent 是已知的 API 工具 (curl, postman等)
    userAgent.includes('curl') ||
    userAgent.includes('Postman') ||
    userAgent.includes('HTTPie')
  );
}

/**
 * 处理扩展登录的 API 请求
 */
async function handleExtensionSignIn(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { email, password } = body;

    // 输入验证
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必填字段',
          message: 'Missing required fields: email, password',
          code: 'MISSING_FIELDS',
        },
        { status: 400 }
      );
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        passwordHash: true,
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在',
          message: 'User not found',
          code: 'USER_NOT_FOUND',
        },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: '账户已被禁用',
          message: 'Account is disabled',
          code: 'ACCOUNT_DISABLED',
        },
        { status: 401 }
      );
    }

    if (!user.passwordHash) {
      return NextResponse.json(
        {
          success: false,
          error: '该账户需要通过第三方登录',
          message: 'This account requires OAuth sign-in',
          code: 'NO_PASSWORD_SET',
        },
        { status: 401 }
      );
    }

    // 验证密码
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);

    if (!isValidPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名或密码错误',
          message: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS',
        },
        { status: 401 }
      );
    }

    // 生成 JWT 令牌
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not configured');
      return NextResponse.json(
        {
          success: false,
          error: '服务器配置错误',
          message: 'Server configuration error',
          code: 'SERVER_CONFIG_ERROR',
        },
        { status: 500 }
      );
    }

    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        iat: Math.floor(Date.now() / 1000),
        type: 'access',
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    const refreshToken = jwt.sign(
      {
        sub: user.id,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000),
      },
      jwtSecret,
      { expiresIn: '30d' }
    );

    // 更新最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        provider: 'credentials',
      },
    });

    // 返回成功响应
    return NextResponse.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
        },
        accessToken,
        refreshToken,
        expiresIn: 3600,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Extension signin error:', error);
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * 处理扩展注册的 API 请求
 */
async function handleExtensionSignUp(request: NextRequest): Promise<NextResponse> {
  try {
    const body = await request.json();
    const { email, password, name } = body;

    // 输入验证
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必填字段',
          message: 'Missing required fields: email, password',
          code: 'MISSING_FIELDS',
        },
        { status: 400 }
      );
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        {
          success: false,
          error: '邮箱格式不正确',
          message: 'Invalid email format',
          code: 'INVALID_EMAIL_FORMAT',
        },
        { status: 422 }
      );
    }

    // 密码长度验证
    if (password.length < 6) {
      return NextResponse.json(
        {
          success: false,
          error: '密码长度至少6位',
          message: 'Password must be at least 6 characters',
          code: 'PASSWORD_TOO_SHORT',
        },
        { status: 422 }
      );
    }

    // 检查用户是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
      select: { id: true, email: true, passwordHash: true },
    });

    if (existingUser) {
      // 如果用户存在且有密码，可能是想登录
      if (existingUser.passwordHash) {
        const isValidPassword = await bcrypt.compare(password, existingUser.passwordHash);
        if (isValidPassword) {
          return NextResponse.json(
            {
              success: false,
              error: '用户已存在，请直接登录',
              message: 'User already exists, please sign in instead',
              code: 'USER_EXISTS_LOGIN',
            },
            { status: 409 }
          );
        }
      }

      return NextResponse.json(
        {
          success: false,
          error: '该邮箱已被注册',
          message: 'Email already registered',
          code: 'EMAIL_ALREADY_EXISTS',
        },
        { status: 409 }
      );
    }

    // 创建新用户
    const passwordHash = await bcrypt.hash(password, 12);

    const user = await prisma.user.create({
      data: {
        email,
        passwordHash,
        name: name || null,
        provider: 'credentials',
        isActive: true,
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 生成 JWT 令牌
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not configured');
      return NextResponse.json(
        {
          success: false,
          error: '服务器配置错误',
          message: 'Server configuration error',
          code: 'SERVER_CONFIG_ERROR',
        },
        { status: 500 }
      );
    }

    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        iat: Math.floor(Date.now() / 1000),
        type: 'access',
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    const refreshToken = jwt.sign(
      {
        sub: user.id,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000),
      },
      jwtSecret,
      { expiresIn: '7d' }
    );

    return NextResponse.json(
      {
        success: true,
        message: '注册成功',
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
        accessToken,
        refreshToken,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Extension signup error:', error);

    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * 添加CORS头部到响应
 */
function addCORSHeaders(response: NextResponse, request: NextRequest): void {
  const origin = request.headers.get('origin');

  const allowedOrigins = ['chrome-extension://*', 'https://*', 'http://localhost:*'];

  const isOriginAllowed = (origin: string | null, allowedOrigins: string[]): boolean => {
    if (!origin) return false;

    return allowedOrigins.some((allowed) => {
      if (allowed === '*') return true;
      if (allowed.endsWith('*')) {
        const prefix = allowed.slice(0, -1);
        return origin.startsWith(prefix);
      }
      return origin === allowed;
    });
  };

  if (isOriginAllowed(origin, allowedOrigins)) {
    response.headers.set('Access-Control-Allow-Origin', origin || '*');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set(
      'Access-Control-Allow-Headers',
      'Content-Type, Authorization, X-Requested-With, X-CSRF-Token'
    );
    response.headers.set('Access-Control-Expose-Headers', 'X-Total-Count, X-Request-ID');
  }
}

/**
 * 自定义 POST 处理器 - 拦截 API 请求
 */
async function POST(request: NextRequest) {
  const url = new URL(request.url);
  const path = url.pathname;

  // 如果是 signin 请求且为 API 请求
  if (path.includes('/signin') && isApiRequest(request)) {
    console.log('Detected API signin request, handling with JSON response');
    const response = await handleExtensionSignIn(request);
    addCORSHeaders(response, request);
    return response;
  }

  // 如果是 signup 请求且为 API 请求
  if (path.includes('/signup') && isApiRequest(request)) {
    console.log('Detected API signup request, handling with JSON response');
    const response = await handleExtensionSignUp(request);
    addCORSHeaders(response, request);
    return response;
  }

  // 检查是否是非NextAuth路由（如signup）
  const validNextAuthRoutes = [
    'signin',
    'signout',
    'callback',
    'csrf',
    'session',
    'providers',
    'signup',
  ];
  const pathSegments = path.split('/');
  const lastSegment = pathSegments[pathSegments.length - 1];

  if (!validNextAuthRoutes.includes(lastSegment)) {
    console.log(`❌ Invalid NextAuth route: ${path}`);
    const errorResponse = NextResponse.json(
      {
        error: 'Not Found',
        message: `Route ${path} not found. Available routes: ${validNextAuthRoutes.join(', ')}`,
        validRoutes: validNextAuthRoutes.map((route) => `/api/auth/${route}`),
      },
      { status: 404 }
    );
    addCORSHeaders(errorResponse, request);
    return errorResponse;
  }

  // 使用 NextAuth 的默认处理
  try {
    const handler = NextAuth(authOptions);
    const response = await handler(request);
    addCORSHeaders(response, request);
    return response;
  } catch (error) {
    console.error('NextAuth POST handler error:', error);
    const errorResponse = NextResponse.json(
      { error: 'Authentication service error' },
      { status: 500 }
    );
    addCORSHeaders(errorResponse, request);
    return errorResponse;
  }
}

/**
 * OPTIONS 请求处理器 - 处理CORS预检请求
 */
async function OPTIONS(request: NextRequest) {
  console.log('🔧 Handling OPTIONS request for NextAuth:', request.url);

  const response = new NextResponse(null, { status: 200 });

  // 获取请求的origin
  const origin = request.headers.get('origin');
  console.log('🔧 Request origin:', origin);

  // 检查origin是否被允许
  const allowedOrigins = ['chrome-extension://*', 'https://*', 'http://localhost:*'];

  const isOriginAllowed = (origin: string | null, allowedOrigins: string[]): boolean => {
    if (!origin) return false;

    return allowedOrigins.some((allowed) => {
      if (allowed === '*') return true;
      if (allowed.endsWith('*')) {
        const prefix = allowed.slice(0, -1);
        return origin.startsWith(prefix);
      }
      return origin === allowed;
    });
  };

  if (isOriginAllowed(origin, allowedOrigins)) {
    console.log('✅ Origin allowed, setting CORS headers');
    response.headers.set('Access-Control-Allow-Origin', origin || '*');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set(
      'Access-Control-Allow-Headers',
      'Content-Type, Authorization, X-Requested-With, X-CSRF-Token'
    );
    response.headers.set('Access-Control-Max-Age', '86400');
  } else {
    console.log('❌ Origin not allowed:', origin);
  }

  // 添加其他安全头部
  response.headers.set('X-Content-Type-Options', 'nosniff');
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-XSS-Protection', '1; mode=block');

  return response;
}

/**
 * NextAuth 处理器 - 包装以添加CORS支持
 */
const nextAuthHandler = NextAuth(authOptions);

/**
 * GET 请求处理器 - 添加CORS支持
 */
async function GET(request: NextRequest, context?: any) {
  try {
    const response = await nextAuthHandler(request, context);
    addCORSHeaders(response, request);
    return response;
  } catch (error) {
    console.error('NextAuth GET handler error:', error);
    const errorResponse = NextResponse.json(
      { error: 'Authentication service error' },
      { status: 500 }
    );
    addCORSHeaders(errorResponse, request);
    return errorResponse;
  }
}

export { GET, POST, OPTIONS, authOptions };
