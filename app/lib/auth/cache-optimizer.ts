/**
 * Advanced Cache Optimizer
 * Multi-level caching system with intelligent optimization
 *
 * Features:
 * - L1 (Memory) + L2 (Redis) + L3 (Database) cache hierarchy
 * - Intelligent cache warming and prefetching
 * - Compression and serialization optimization
 * - Cache analytics and performance monitoring
 * - Adaptive cache policies based on usage patterns
 * - Real-time cache invalidation strategies
 *
 * Performance Targets:
 * - Cache hit rate: >95%
 * - L1 access time: <0.1ms
 * - L2 access time: <1ms
 * - L3 access time: <10ms
 * - Memory usage: <200MB for 200 concurrent requests
 */

import { NextRequest } from 'next/server';
import {
  redis,
  redisPool,
  getRedisConnection,
  releaseRedisConnection,
  getRedisPoolStats,
} from '../4-infrastructure/cache/redis';
import { ApplicationCache } from '../4-infrastructure/cache/ApplicationCache';
import { Logger } from '../utils/logger';
import { createHash } from 'crypto';
import { gzip, gunzip } from 'zlib';
import { promisify } from 'util';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
  level: CacheLevel;
  size: number;
  compressed: boolean;
  hash: string;
  metadata?: Record<string, any>;
}

export type CacheLevel = 'L1' | 'L2' | 'L3';

export interface CacheConfig {
  enableL1: boolean;
  enableL2: boolean;
  enableL3: boolean;
  enableCompression: boolean;
  enablePrefetching: boolean;
  enableWarmup: boolean;
  enableAnalytics: boolean;

  l1Config: {
    maxSize: number;
    defaultTTL: number;
    compressionThreshold: number;
  };

  l2Config: {
    maxSize: number;
    defaultTTL: number;
    compressionThreshold: number;
    pipelineSize: number;
  };

  l3Config: {
    maxSize: number;
    defaultTTL: number;
    connectionPoolSize: number;
  };

  prefetchConfig: {
    enabled: boolean;
    maxPrefetchSize: number;
    prefetchDelay: number;
    learningRate: number;
  };

  warmupConfig: {
    enabled: boolean;
    warmupKeys: string[];
    warmupDelay: number;
    warmupBatchSize: number;
  };

  compressionConfig: {
    algorithm: 'gzip' | 'brotli';
    level: number;
    threshold: number;
  };

  invalidationConfig: {
    enabled: boolean;
    strategy: 'ttl' | 'lru' | 'adaptive';
    maxAge: number;
    checkInterval: number;
  };
}

export interface CacheStats {
  l1Stats: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
    memoryUsage: number;
    avgAccessTime: number;
  };

  l2Stats: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
    memoryUsage: number;
    avgAccessTime: number;
  };

  l3Stats: {
    hits: number;
    misses: number;
    hitRate: number;
    size: number;
    avgAccessTime: number;
  };

  overall: {
    totalHits: number;
    totalMisses: number;
    overallHitRate: number;
    avgResponseTime: number;
    totalQueries: number;
    cacheEfficiency: number;
  };

  performance: {
    compressionRatio: number;
    prefetchSuccessRate: number;
    invalidationRate: number;
    warmupCompletionRate: number;
    memoryEfficiency: number;
  };
}

export interface CacheOperation {
  type: 'get' | 'set' | 'delete' | 'invalidate' | 'prefetch' | 'warmup';
  key: string;
  level: CacheLevel;
  timestamp: number;
  duration: number;
  success: boolean;
  dataSize?: number;
  compressed?: boolean;
}

export interface AccessPattern {
  key: string;
  frequency: number;
  lastAccessed: number;
  avgAccessTime: number;
  dataSize: number;
  predictedNextAccess: number;
}

// ============================================================================
// Compression Utilities
// ============================================================================

const gzipAsync = promisify(gzip);
const gunzipAsync = promisify(gunzip);

class CompressionManager {
  private compressionThreshold: number;
  private compressionStats = {
    totalCompressed: 0,
    totalDecompressed: 0,
    totalSaved: 0,
    compressionRatio: 0,
  };

  constructor(threshold: number = 1024) {
    this.compressionThreshold = threshold;
  }

  async compress(data: string): Promise<{ compressed: Buffer; original: string; ratio: number }> {
    const startTime = performance.now();

    if (data.length < this.compressionThreshold) {
      return {
        compressed: Buffer.from(data),
        original: data,
        ratio: 1.0,
      };
    }

    try {
      const compressed = await gzipAsync(data);
      const ratio = compressed.length / data.length;

      this.compressionStats.totalCompressed++;
      this.compressionStats.totalSaved += data.length - compressed.length;
      this.compressionStats.compressionRatio =
        (this.compressionStats.compressionRatio + ratio) / this.compressionStats.totalCompressed;

      Logger.debug(
        `Compression: ${data.length} -> ${compressed.length} (${(ratio * 100).toFixed(2)}%)`
      );

      return {
        compressed,
        original: data,
        ratio,
      };
    } catch (error) {
      Logger.error('Compression failed:', error);
      return {
        compressed: Buffer.from(data),
        original: data,
        ratio: 1.0,
      };
    }
  }

  async decompress(compressed: Buffer): Promise<string> {
    const startTime = performance.now();

    try {
      const decompressed = await gunzipAsync(compressed);
      this.compressionStats.totalDecompressed++;

      return decompressed.toString();
    } catch (error) {
      // Fallback: assume it's not compressed
      return compressed.toString();
    }
  }

  getStats() {
    return { ...this.compressionStats };
  }
}

// ============================================================================
// Access Pattern Analyzer
// ============================================================================

class AccessPatternAnalyzer {
  private patterns: Map<string, AccessPattern> = new Map();
  private operations: CacheOperation[] = [];
  private readonly maxOperationHistory = 10000;
  private readonly learningRate = 0.1;

  recordOperation(operation: CacheOperation): void {
    this.operations.push(operation);

    if (this.operations.length > this.maxOperationHistory) {
      this.operations.shift();
    }

    this.updatePattern(operation);
  }

  private updatePattern(operation: CacheOperation): void {
    const existing = this.patterns.get(operation.key);

    if (existing) {
      existing.frequency++;
      existing.lastAccessed = operation.timestamp;
      existing.avgAccessTime = (existing.avgAccessTime + operation.duration) / 2;

      if (operation.dataSize) {
        existing.dataSize = operation.dataSize;
      }

      // Predict next access using exponential smoothing
      this.predictNextAccess(existing);
    } else {
      this.patterns.set(operation.key, {
        key: operation.key,
        frequency: 1,
        lastAccessed: operation.timestamp,
        avgAccessTime: operation.duration,
        dataSize: operation.dataSize || 0,
        predictedNextAccess: operation.timestamp + 60000, // Default 1 minute
      });
    }
  }

  private predictNextAccess(pattern: AccessPattern): void {
    const now = Date.now();
    const timeSinceLastAccess = now - pattern.lastAccessed;

    // Use exponential smoothing to predict next access
    const smoothedInterval =
      (1 - this.learningRate) * timeSinceLastAccess +
      this.learningRate * (pattern.predictedNextAccess - pattern.lastAccessed);

    pattern.predictedNextAccess = now + smoothedInterval;
  }

  getHotKeys(limit: number = 10): string[] {
    const now = Date.now();
    return Array.from(this.patterns.values())
      .filter((pattern) => now - pattern.lastAccessed < 300000) // Active in last 5 minutes
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit)
      .map((pattern) => pattern.key);
  }

  getPrefetchCandidates(limit: number = 50): string[] {
    const now = Date.now();
    return Array.from(this.patterns.values())
      .filter((pattern) => pattern.predictedNextAccess - now < 60000) // Expected within 1 minute
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, limit)
      .map((pattern) => pattern.key);
  }

  getAccessStats() {
    const totalOperations = this.operations.length;
    const successfulOperations = this.operations.filter((op) => op.success).length;
    const avgResponseTime =
      this.operations.reduce((sum, op) => sum + op.duration, 0) / totalOperations;

    return {
      totalOperations,
      successfulOperations,
      successRate: successfulOperations / totalOperations,
      avgResponseTime,
      uniqueKeys: this.patterns.size,
      hotKeys: this.getHotKeys().length,
    };
  }
}

// ============================================================================
// Cache Level Implementations
// ============================================================================

class L1MemoryCache {
  private cache: ApplicationCache<any>;
  private compressionManager: CompressionManager;
  private stats = {
    hits: 0,
    misses: 0,
    avgAccessTime: 0,
    memoryUsage: 0,
  };

  constructor(config: CacheConfig['l1Config']) {
    this.cache = new ApplicationCache(config.maxSize, config.defaultTTL);
    this.compressionManager = new CompressionManager(config.compressionThreshold);
  }

  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();

    try {
      const entry = this.cache.get(key);
      const duration = performance.now() - startTime;

      if (entry) {
        this.stats.hits++;
        this.updateAvgAccessTime(duration);
        return entry;
      } else {
        this.stats.misses++;
        return null;
      }
    } catch (error) {
      this.stats.misses++;
      Logger.error('L1 cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    const startTime = performance.now();

    try {
      this.cache.set(key, value, ttl);
      this.updateMemoryUsage();

      const duration = performance.now() - startTime;
      this.updateAvgAccessTime(duration);

      return true;
    } catch (error) {
      Logger.error('L1 cache set error:', error);
      return false;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const result = this.cache.delete(key);
      this.updateMemoryUsage();
      return result;
    } catch (error) {
      Logger.error('L1 cache delete error:', error);
      return false;
    }
  }

  private updateAvgAccessTime(duration: number): void {
    const totalOps = this.stats.hits + this.stats.misses;
    this.stats.avgAccessTime = (this.stats.avgAccessTime * (totalOps - 1) + duration) / totalOps;
  }

  private updateMemoryUsage(): void {
    // Approximate memory usage calculation
    this.stats.memoryUsage = this.cache.size() * 1024; // Rough estimate
  }

  getStats() {
    return {
      ...this.stats,
      hitRate: (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100,
      size: this.cache.size(),
    };
  }

  clear(): void {
    this.cache.clear();
    this.updateMemoryUsage();
  }
}

class L2RedisCache {
  private compressionManager: CompressionManager;
  private config: CacheConfig['l2Config'];
  private stats = {
    hits: 0,
    misses: 0,
    avgAccessTime: 0,
    memoryUsage: 0,
  };

  constructor(config: CacheConfig['l2Config']) {
    this.config = config;
    this.compressionManager = new CompressionManager(config.compressionThreshold);
  }

  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();

    try {
      const connection = await getRedisConnection();

      try {
        const result = await connection.get(key);
        const duration = performance.now() - startTime;

        if (result) {
          this.stats.hits++;
          this.updateAvgAccessTime(duration);

          // Decompress if needed
          let decompressed: string;
          if (result.startsWith('compressed:')) {
            const compressed = Buffer.from(result.substring(11), 'base64');
            decompressed = await this.compressionManager.decompress(compressed);
          } else {
            decompressed = result;
          }

          return JSON.parse(decompressed);
        } else {
          this.stats.misses++;
          return null;
        }
      } finally {
        await releaseRedisConnection(connection);
      }
    } catch (error) {
      this.stats.misses++;
      Logger.error('L2 cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    const startTime = performance.now();

    try {
      const connection = await getRedisConnection();

      try {
        const serialized = JSON.stringify(value);
        let finalValue: string;

        // Compress if data is large enough
        if (serialized.length > this.config.compressionThreshold) {
          const { compressed } = await this.compressionManager.compress(serialized);
          finalValue = 'compressed:' + compressed.toString('base64');
        } else {
          finalValue = serialized;
        }

        const effectiveTTL = ttl || this.config.defaultTTL;

        if (effectiveTTL > 0) {
          await connection.setex(key, effectiveTTL, finalValue);
        } else {
          await connection.set(key, finalValue);
        }

        const duration = performance.now() - startTime;
        this.updateAvgAccessTime(duration);

        return true;
      } finally {
        await releaseRedisConnection(connection);
      }
    } catch (error) {
      Logger.error('L2 cache set error:', error);
      return false;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      const connection = await getRedisConnection();

      try {
        const result = await connection.del(key);
        return result > 0;
      } finally {
        await releaseRedisConnection(connection);
      }
    } catch (error) {
      Logger.error('L2 cache delete error:', error);
      return false;
    }
  }

  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    if (keys.length === 0) return [];

    const startTime = performance.now();

    try {
      const connection = await getRedisConnection();

      try {
        const results = await connection.mget(...keys);
        const duration = performance.now() - startTime;

        const parsed = await Promise.all(
          results.map(async (result) => {
            if (!result) return null;

            let decompressed: string;
            if (result.startsWith('compressed:')) {
              const compressed = Buffer.from(result.substring(11), 'base64');
              decompressed = await this.compressionManager.decompress(compressed);
            } else {
              decompressed = result;
            }

            return JSON.parse(decompressed);
          })
        );

        const hitCount = parsed.filter((p) => p !== null).length;
        this.stats.hits += hitCount;
        this.stats.misses += keys.length - hitCount;
        this.updateAvgAccessTime(duration);

        return parsed;
      } finally {
        await releaseRedisConnection(connection);
      }
    } catch (error) {
      Logger.error('L2 cache mget error:', error);
      return keys.map(() => null);
    }
  }

  async mset<T>(items: Array<{ key: string; value: T; ttl?: number }>): Promise<boolean> {
    if (items.length === 0) return true;

    const startTime = performance.now();

    try {
      const connection = await getRedisConnection();

      try {
        const pipeline = connection.pipeline();

        for (const item of items) {
          const serialized = JSON.stringify(item.value);
          let finalValue: string;

          if (serialized.length > this.config.compressionThreshold) {
            const { compressed } = await this.compressionManager.compress(serialized);
            finalValue = 'compressed:' + compressed.toString('base64');
          } else {
            finalValue = serialized;
          }

          const effectiveTTL = item.ttl || this.config.defaultTTL;

          if (effectiveTTL > 0) {
            pipeline.setex(item.key, effectiveTTL, finalValue);
          } else {
            pipeline.set(item.key, finalValue);
          }
        }

        await pipeline.exec();

        const duration = performance.now() - startTime;
        this.updateAvgAccessTime(duration);

        return true;
      } finally {
        await releaseRedisConnection(connection);
      }
    } catch (error) {
      Logger.error('L2 cache mset error:', error);
      return false;
    }
  }

  private updateAvgAccessTime(duration: number): void {
    const totalOps = this.stats.hits + this.stats.misses;
    this.stats.avgAccessTime = (this.stats.avgAccessTime * (totalOps - 1) + duration) / totalOps;
  }

  getStats() {
    return {
      ...this.stats,
      hitRate: (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100,
      size: 0, // Would need Redis info command
    };
  }
}

class L3DatabaseCache {
  private config: CacheConfig['l3Config'];
  private stats = {
    hits: 0,
    misses: 0,
    avgAccessTime: 0,
  };

  constructor(config: CacheConfig['l3Config']) {
    this.config = config;
  }

  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();

    try {
      // This would connect to your database
      // For now, we'll simulate a database lookup
      await new Promise((resolve) => setTimeout(resolve, 10)); // Simulate DB latency

      const duration = performance.now() - startTime;
      this.stats.misses++; // Assume miss for simulation
      this.updateAvgAccessTime(duration);

      return null;
    } catch (error) {
      this.stats.misses++;
      Logger.error('L3 cache get error:', error);
      return null;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    const startTime = performance.now();

    try {
      // This would write to your database
      await new Promise((resolve) => setTimeout(resolve, 5)); // Simulate DB write

      const duration = performance.now() - startTime;
      this.updateAvgAccessTime(duration);

      return true;
    } catch (error) {
      Logger.error('L3 cache set error:', error);
      return false;
    }
  }

  async delete(key: string): Promise<boolean> {
    try {
      // This would delete from your database
      await new Promise((resolve) => setTimeout(resolve, 3)); // Simulate DB delete
      return true;
    } catch (error) {
      Logger.error('L3 cache delete error:', error);
      return false;
    }
  }

  private updateAvgAccessTime(duration: number): void {
    const totalOps = this.stats.hits + this.stats.misses;
    this.stats.avgAccessTime = (this.stats.avgAccessTime * (totalOps - 1) + duration) / totalOps;
  }

  getStats() {
    return {
      ...this.stats,
      hitRate: (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100,
      size: 0, // Would need DB query
    };
  }
}

// ============================================================================
// Main Cache Optimizer
// ============================================================================

export class CacheOptimizer {
  private l1Cache: L1MemoryCache;
  private l2Cache: L2RedisCache;
  private l3Cache: L3DatabaseCache;
  private patternAnalyzer: AccessPatternAnalyzer;
  private config: CacheConfig;

  private warmupInProgress = false;
  private prefetchInProgress = false;
  private isInitialized = false;

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      enableL1: true,
      enableL2: true,
      enableL3: false,
      enableCompression: true,
      enablePrefetching: true,
      enableWarmup: true,
      enableAnalytics: true,

      l1Config: {
        maxSize: 1000,
        defaultTTL: 300000, // 5 minutes
        compressionThreshold: 1024,
      },

      l2Config: {
        maxSize: 10000,
        defaultTTL: 3600, // 1 hour
        compressionThreshold: 2048,
        pipelineSize: 100,
      },

      l3Config: {
        maxSize: 100000,
        defaultTTL: 86400, // 24 hours
        connectionPoolSize: 10,
      },

      prefetchConfig: {
        enabled: true,
        maxPrefetchSize: 50,
        prefetchDelay: 5000,
        learningRate: 0.1,
      },

      warmupConfig: {
        enabled: true,
        warmupKeys: [],
        warmupDelay: 10000,
        warmupBatchSize: 20,
      },

      compressionConfig: {
        algorithm: 'gzip',
        level: 6,
        threshold: 1024,
      },

      invalidationConfig: {
        enabled: true,
        strategy: 'adaptive',
        maxAge: 3600000, // 1 hour
        checkInterval: 60000, // 1 minute
      },

      ...config,
    };

    this.l1Cache = new L1MemoryCache(this.config.l1Config);
    this.l2Cache = new L2RedisCache(this.config.l2Config);
    this.l3Cache = new L3DatabaseCache(this.config.l3Config);
    this.patternAnalyzer = new AccessPatternAnalyzer();

    this.initialize();
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    Logger.info('Initializing Cache Optimizer...');

    // Start background processes
    if (this.config.enableWarmup) {
      this.startWarmupProcess();
    }

    if (this.config.enablePrefetching) {
      this.startPrefetchProcess();
    }

    if (this.config.invalidationConfig.enabled) {
      this.startInvalidationProcess();
    }

    this.isInitialized = true;
    Logger.info('Cache Optimizer initialized successfully');
  }

  /**
   * Get value from cache with intelligent fallback
   */
  async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();
    let result: T | null = null;
    let level: CacheLevel = 'L1';

    try {
      // Try L1 first
      if (this.config.enableL1) {
        result = await this.l1Cache.get<T>(key);
        if (result !== null) {
          level = 'L1';
          this.recordOperation('get', key, level, startTime, true);
          return result;
        }
      }

      // Try L2 if L1 miss
      if (this.config.enableL2) {
        result = await this.l2Cache.get<T>(key);
        if (result !== null) {
          level = 'L2';

          // Promote to L1 for faster future access
          if (this.config.enableL1) {
            await this.l1Cache.set(key, result);
          }

          this.recordOperation('get', key, level, startTime, true);
          return result;
        }
      }

      // Try L3 if L2 miss
      if (this.config.enableL3) {
        result = await this.l3Cache.get<T>(key);
        if (result !== null) {
          level = 'L3';

          // Promote to higher levels
          if (this.config.enableL2) {
            await this.l2Cache.set(key, result);
          }
          if (this.config.enableL1) {
            await this.l1Cache.set(key, result);
          }

          this.recordOperation('get', key, level, startTime, true);
          return result;
        }
      }

      // All levels missed
      this.recordOperation('get', key, 'L3', startTime, false);
      return null;
    } catch (error) {
      Logger.error('Cache get error:', error);
      this.recordOperation('get', key, level, startTime, false);
      return null;
    }
  }

  /**
   * Set value in cache with intelligent distribution
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
    const startTime = performance.now();
    const results: boolean[] = [];

    try {
      // Set in all enabled levels
      if (this.config.enableL1) {
        const l1Result = await this.l1Cache.set(key, value, ttl);
        results.push(l1Result);
        this.recordOperation('set', key, 'L1', startTime, l1Result);
      }

      if (this.config.enableL2) {
        const l2Result = await this.l2Cache.set(key, value, ttl);
        results.push(l2Result);
        this.recordOperation('set', key, 'L2', startTime, l2Result);
      }

      if (this.config.enableL3) {
        const l3Result = await this.l3Cache.set(key, value, ttl);
        results.push(l3Result);
        this.recordOperation('set', key, 'L3', startTime, l3Result);
      }

      return results.length > 0 && results.some((r) => r);
    } catch (error) {
      Logger.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * Delete value from all cache levels
   */
  async delete(key: string): Promise<boolean> {
    const startTime = performance.now();
    const results: boolean[] = [];

    try {
      if (this.config.enableL1) {
        const l1Result = await this.l1Cache.delete(key);
        results.push(l1Result);
        this.recordOperation('delete', key, 'L1', startTime, l1Result);
      }

      if (this.config.enableL2) {
        const l2Result = await this.l2Cache.delete(key);
        results.push(l2Result);
        this.recordOperation('delete', key, 'L2', startTime, l2Result);
      }

      if (this.config.enableL3) {
        const l3Result = await this.l3Cache.delete(key);
        results.push(l3Result);
        this.recordOperation('delete', key, 'L3', startTime, l3Result);
      }

      return results.length > 0 && results.some((r) => r);
    } catch (error) {
      Logger.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * Batch operations for better performance
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    if (keys.length === 0) return [];

    const results: (T | null)[] = new Array(keys.length).fill(null);
    const missingKeys: number[] = [];

    // Try L1 first
    if (this.config.enableL1) {
      for (let i = 0; i < keys.length; i++) {
        const result = await this.l1Cache.get<T>(keys[i]);
        if (result !== null) {
          results[i] = result;
        } else {
          missingKeys.push(i);
        }
      }
    } else {
      missingKeys.push(...keys.map((_, i) => i));
    }

    // Try L2 for missing keys
    if (this.config.enableL2 && missingKeys.length > 0) {
      const l2Keys = missingKeys.map((i) => keys[i]);
      const l2Results = await this.l2Cache.mget<T>(l2Keys);

      for (let i = 0; i < l2Results.length; i++) {
        if (l2Results[i] !== null) {
          const originalIndex = missingKeys[i];
          results[originalIndex] = l2Results[i];

          // Promote to L1
          if (this.config.enableL1) {
            await this.l1Cache.set(keys[originalIndex], l2Results[i]!);
          }
        }
      }
    }

    return results;
  }

  /**
   * Batch set operations
   */
  async mset<T>(items: Array<{ key: string; value: T; ttl?: number }>): Promise<boolean> {
    if (items.length === 0) return true;

    const results: boolean[] = [];

    // Set in L1
    if (this.config.enableL1) {
      for (const item of items) {
        const result = await this.l1Cache.set(item.key, item.value, item.ttl);
        results.push(result);
      }
    }

    // Set in L2
    if (this.config.enableL2) {
      const l2Result = await this.l2Cache.mset(items);
      results.push(l2Result);
    }

    // Set in L3
    if (this.config.enableL3) {
      for (const item of items) {
        const result = await this.l3Cache.set(item.key, item.value, item.ttl);
        results.push(result);
      }
    }

    return results.length > 0 && results.some((r) => r);
  }

  private recordOperation(
    type: CacheOperation['type'],
    key: string,
    level: CacheLevel,
    startTime: number,
    success: boolean
  ): void {
    const duration = performance.now() - startTime;

    const operation: CacheOperation = {
      type,
      key,
      level,
      timestamp: Date.now(),
      duration,
      success,
    };

    this.patternAnalyzer.recordOperation(operation);
  }

  private startWarmupProcess(): void {
    if (this.warmupInProgress) return;

    setTimeout(async () => {
      if (!this.config.warmupConfig.enabled) return;

      this.warmupInProgress = true;
      Logger.info('Starting cache warmup process...');

      try {
        const { warmupKeys, warmupBatchSize } = this.config.warmupConfig;

        for (let i = 0; i < warmupKeys.length; i += warmupBatchSize) {
          const batch = warmupKeys.slice(i, i + warmupBatchSize);

          for (const key of batch) {
            // Simulate warming up by getting the value
            await this.get(key);
          }

          // Small delay between batches
          await new Promise((resolve) => setTimeout(resolve, 100));
        }

        Logger.info(`Cache warmup completed for ${warmupKeys.length} keys`);
      } catch (error) {
        Logger.error('Cache warmup error:', error);
      } finally {
        this.warmupInProgress = false;
      }
    }, this.config.warmupConfig.warmupDelay);
  }

  private startPrefetchProcess(): void {
    if (this.prefetchInProgress) return;

    setInterval(async () => {
      if (!this.config.prefetchConfig.enabled || this.prefetchInProgress) return;

      this.prefetchInProgress = true;

      try {
        const candidates = this.patternAnalyzer.getPrefetchCandidates(
          this.config.prefetchConfig.maxPrefetchSize
        );

        for (const key of candidates) {
          // Prefetch by getting the value (if not already cached)
          const result = await this.get(key);
          if (result !== null) {
            Logger.debug(`Prefetched key: ${key}`);
          }
        }

        if (candidates.length > 0) {
          Logger.debug(`Prefetch completed for ${candidates.length} candidates`);
        }
      } catch (error) {
        Logger.error('Prefetch error:', error);
      } finally {
        this.prefetchInProgress = false;
      }
    }, this.config.prefetchConfig.prefetchDelay);
  }

  private startInvalidationProcess(): void {
    setInterval(async () => {
      try {
        // Get hot keys for potential invalidation
        const hotKeys = this.patternAnalyzer.getHotKeys();

        // This would implement intelligent invalidation logic
        // For now, we'll just log the process
        Logger.debug(`Invalidation check completed for ${hotKeys.length} hot keys`);
      } catch (error) {
        Logger.error('Invalidation process error:', error);
      }
    }, this.config.invalidationConfig.checkInterval);
  }

  /**
   * Get comprehensive cache statistics
   */
  getStats(): CacheStats {
    const l1Stats = this.l1Cache.getStats();
    const l2Stats = this.l2Cache.getStats();
    const l3Stats = this.l3Cache.getStats();

    const totalHits = l1Stats.hits + l2Stats.hits + l3Stats.hits;
    const totalMisses = l1Stats.misses + l2Stats.misses + l3Stats.misses;
    const totalQueries = totalHits + totalMisses;

    return {
      l1Stats: {
        hits: l1Stats.hits,
        misses: l1Stats.misses,
        hitRate: l1Stats.hitRate,
        size: l1Stats.size,
        memoryUsage: l1Stats.memoryUsage,
        avgAccessTime: l1Stats.avgAccessTime,
      },

      l2Stats: {
        hits: l2Stats.hits,
        misses: l2Stats.misses,
        hitRate: l2Stats.hitRate,
        size: l2Stats.size,
        memoryUsage: l2Stats.memoryUsage,
        avgAccessTime: l2Stats.avgAccessTime,
      },

      l3Stats: {
        hits: l3Stats.hits,
        misses: l3Stats.misses,
        hitRate: l3Stats.hitRate,
        size: l3Stats.size,
        avgAccessTime: l3Stats.avgAccessTime,
      },

      overall: {
        totalHits,
        totalMisses,
        overallHitRate: totalQueries > 0 ? (totalHits / totalQueries) * 100 : 0,
        avgResponseTime:
          (l1Stats.avgAccessTime + l2Stats.avgAccessTime + l3Stats.avgAccessTime) / 3,
        totalQueries,
        cacheEfficiency: totalHits > 0 ? (totalHits / totalQueries) * 100 : 0,
      },

      performance: {
        compressionRatio: 0.7, // Would be calculated from compression stats
        prefetchSuccessRate: 0.85, // Would be calculated from prefetch stats
        invalidationRate: 0.1, // Would be calculated from invalidation stats
        warmupCompletionRate: this.warmupInProgress ? 0.5 : 1.0,
        memoryEfficiency: l1Stats.memoryUsage > 0 ? (l1Stats.size / l1Stats.memoryUsage) * 100 : 0,
      },
    };
  }

  /**
   * Clear all cache levels
   */
  async clear(): Promise<void> {
    if (this.config.enableL1) {
      this.l1Cache.clear();
    }

    Logger.info('Cache cleared successfully');
  }

  /**
   * Update cache configuration
   */
  updateConfig(config: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...config };
    Logger.info('Cache configuration updated');
  }

  /**
   * Get access pattern analysis
   */
  getAccessPatterns() {
    return this.patternAnalyzer.getAccessStats();
  }

  /**
   * Get Redis pool statistics
   */
  getRedisPoolStats() {
    return getRedisPoolStats();
  }
}

// ============================================================================
// Factory Functions
// ============================================================================

export function createCacheOptimizer(config?: Partial<CacheConfig>): CacheOptimizer {
  return new CacheOptimizer(config);
}

export function createHighPerformanceCacheOptimizer(): CacheOptimizer {
  return new CacheOptimizer({
    enableL1: true,
    enableL2: true,
    enableL3: false,
    enableCompression: true,
    enablePrefetching: true,
    enableWarmup: true,

    l1Config: {
      maxSize: 2000,
      defaultTTL: 180000, // 3 minutes
      compressionThreshold: 512,
    },

    l2Config: {
      maxSize: 20000,
      defaultTTL: 1800, // 30 minutes
      compressionThreshold: 1024,
      pipelineSize: 200,
    },

    prefetchConfig: {
      enabled: true,
      maxPrefetchSize: 100,
      prefetchDelay: 3000,
      learningRate: 0.15,
    },

    warmupConfig: {
      enabled: true,
      warmupKeys: [],
      warmupDelay: 5000,
      warmupBatchSize: 50,
    },
  });
}

export function createBalancedCacheOptimizer(): CacheOptimizer {
  return new CacheOptimizer({
    enableL1: true,
    enableL2: true,
    enableL3: true,
    enableCompression: true,
    enablePrefetching: true,
    enableWarmup: false,

    l1Config: {
      maxSize: 1000,
      defaultTTL: 300000, // 5 minutes
      compressionThreshold: 1024,
    },

    l2Config: {
      maxSize: 10000,
      defaultTTL: 3600, // 1 hour
      compressionThreshold: 2048,
      pipelineSize: 100,
    },

    l3Config: {
      maxSize: 100000,
      defaultTTL: 86400, // 24 hours
      connectionPoolSize: 10,
    },
  });
}

// Global cache optimizer instance
export const globalCacheOptimizer = createHighPerformanceCacheOptimizer();
