/**
 * API请求和响应类型定义
 * 为生词本管理API提供类型安全保障
 */

import { z } from 'zod';

// 认证相关类型
export interface AuthenticatedUser {
  id: string;
  name: string | null;
  email: string;
  role?: string;
}

export interface AuthContext {
  user: AuthenticatedUser;
  authMethod: 'session' | 'jwt';
  clientType: 'web' | 'mobile' | 'extension';
  permissions: string[];
  scopes: string[];
  ipAddress: string;
  userAgent: string;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  pagination?: PaginationInfo;
}

export interface PaginationInfo {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasMore: boolean;
}

// 生词本API相关类型
export interface WordWithProficiency {
  id: number;
  word: string;
  phonetics: string[];
  freqRank: number | null;
  proficiency: {
    practiceCount: number;
    errorCount: number;
    averageTime: number;
    proficiencyScore: number;
    isMarked: boolean;
    lastPracticed: Date | null;
  };
}

export interface UserWordList {
  words: WordWithProficiency[];
  pagination: PaginationInfo;
}

// 请求验证Schema
export const GetWordsQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sortBy: z.enum(['time', 'proficiency', 'alphabetical']).default('time'),
  order: z.enum(['asc', 'desc']).default('desc'),
});

export const AddWordsBodySchema = z.object({
  words: z.array(z.string().min(1).max(100)).min(1).max(50).optional(),
  wordIds: z.array(z.number().int().positive()).min(1).max(50).optional(),
}).refine(
  (data) => data.words || data.wordIds,
  {
    message: "Either 'words' or 'wordIds' must be provided",
  }
);

export const DeleteWordsBodySchema = z.object({
  wordIds: z.array(z.number().int().positive()).min(1).max(50),
});

// JWT相关类型
export interface JWTPayload {
  sub: string; // userId
  email: string;
  iat: number; // issued at
  exp: number; // expires at
  jti: string; // JWT ID for revocation
  clientType: 'mobile' | 'extension';
  scopes: string[];
}

export interface RefreshTokenPayload {
  sub: string; // userId
  tokenId: string; // refresh token ID
  iat: number;
  exp: number;
  jti: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: 'Bearer';
}

// 错误类型
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

export class ValidationError extends ApiError {
  constructor(message: string, public details?: any) {
    super(400, message, 'VALIDATION_ERROR');
  }
}

export class UnauthorizedError extends ApiError {
  constructor(message: string = 'Unauthorized') {
    super(401, message, 'UNAUTHORIZED');
  }
}

export class ForbiddenError extends ApiError {
  constructor(message: string = 'Forbidden') {
    super(403, message, 'FORBIDDEN');
  }
}

export class NotFoundError extends ApiError {
  constructor(message: string = 'Not found') {
    super(404, message, 'NOT_FOUND');
  }
}

export class ConflictError extends ApiError {
  constructor(message: string = 'Conflict') {
    super(409, message, 'CONFLICT');
  }
}

export class RateLimitError extends ApiError {
  constructor(message: string = 'Rate limit exceeded') {
    super(429, message, 'RATE_LIMIT_EXCEEDED');
  }
}

// 类型导出
export type GetWordsQuery = z.infer<typeof GetWordsQuerySchema>;
export type AddWordsBody = z.infer<typeof AddWordsBodySchema>;
export type DeleteWordsBody = z.infer<typeof DeleteWordsBodySchema>;