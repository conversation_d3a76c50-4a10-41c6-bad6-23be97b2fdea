# Performance Testing Suite

This directory contains a comprehensive performance testing suite for the lightweight security middleware system. The suite validates performance improvements, ensures targets are met, and provides detailed analysis and reports.

## Overview

The performance testing suite includes:

1. **Lightweight Security Performance Test** - Core performance validation
2. **Benchmark Comparison Test** - Performance improvement verification
3. **Cache Performance Test** - Cache effectiveness and hit rate validation
4. **Concurrency Stress Test** - High-load and concurrent request handling
5. **Performance Regression Test** - Automated regression detection
6. **Comprehensive Report Generator** - Executive summaries and detailed reports

## Performance Targets

The testing suite validates the following performance targets:

- **Response Time Targets**:

  - Minimal security: <5ms per request
  - Standard security: <10ms per request
  - Strict security: <25ms per request

- **Performance Improvement**: 70-80% improvement over original middleware
- **Concurrency**: Handle 100-150 concurrent requests
- **Cache Hit Rate**: >90% hit rate
- **Memory Usage**: <50MB for 150 concurrent requests
- **Throughput**: >1000 requests per second

## Quick Start

### Run All Tests

```bash
# Run complete test suite
tsx scripts/run-all-performance-tests.ts

# Run tests and generate comprehensive report
tsx scripts/run-all-performance-tests.ts --generate-report

# Run with verbose output
tsx scripts/run-all-performance-tests.ts --verbose
```

### Run Individual Tests

```bash
# Lightweight security performance test
tsx scripts/lightweight-performance-test.ts

# Benchmark comparison (lightweight vs enhanced middleware)
tsx scripts/benchmark-comparison.ts

# Cache performance validation
tsx scripts/cache-performance-test.ts

# Concurrency stress testing
tsx scripts/concurrency-stress-test.ts

# Performance regression testing
tsx scripts/performance-regression-test.ts

# Generate comprehensive report
tsx scripts/comprehensive-performance-report.ts
```

## Test Scripts

### 1. Lightweight Performance Test (`lightweight-performance-test.ts`)

**Purpose**: Validates core performance of the lightweight security middleware

**Features**:

- Tests all security levels (minimal, standard, strict)
- Measures response times, throughput, and memory usage
- Validates cache effectiveness
- Tests concurrent request handling
- Provides detailed performance metrics

**Usage**:

```bash
tsx scripts/lightweight-performance-test.ts
```

**Output**:

- Response time statistics for each security level
- Cache hit rates and effectiveness
- Memory usage analysis
- Throughput measurements
- Target compliance validation

### 2. Benchmark Comparison Test (`benchmark-comparison.ts`)

**Purpose**: Compares lightweight middleware performance against the original enhanced middleware

**Features**:

- Side-by-side performance comparison
- Validates 70-80% improvement claims
- Tests multiple request patterns
- Measures memory usage differences
- Analyzes throughput improvements

**Usage**:

```bash
tsx scripts/benchmark-comparison.ts
```

**Output**:

- Performance improvement percentages
- Response time comparisons
- Memory usage comparisons
- Throughput improvement analysis
- Target validation results

### 3. Cache Performance Test (`cache-performance-test.ts`)

**Purpose**: Validates cache performance and effectiveness

**Features**:

- Tests cache hit rates across different patterns
- Validates >90% hit rate target
- Tests cache performance under load
- Analyzes multi-level cache effectiveness
- Measures cache-related performance improvements

**Usage**:

```bash
tsx scripts/cache-performance-test.ts
```

**Output**:

- Cache hit rate statistics
- Performance impact analysis
- Cache effectiveness by pattern
- Memory usage optimization
- Concurrency impact on cache

### 4. Concurrency Stress Test (`concurrency-stress-test.ts`)

**Purpose**: Tests system stability and performance under high concurrent load

**Features**:

- Progressive load testing (1-150 concurrent requests)
- Ramp-up, sustained load, and ramp-down phases
- Memory usage monitoring under stress
- Error rate and stability analysis
- Performance degradation detection

**Usage**:

```bash
tsx scripts/concurrency-stress-test.ts
```

**Output**:

- Concurrency handling capabilities
- System stability metrics
- Error rates under load
- Memory usage patterns
- Performance degradation analysis

### 5. Performance Regression Test (`performance-regression-test.ts`)

**Purpose**: Automated detection of performance regressions

**Features**:

- Baseline performance establishment
- Automated regression detection
- Trend analysis over time
- Performance degradation alerts
- Historical performance tracking

**Usage**:

```bash
tsx scripts/performance-regression-test.ts
```

**Output**:

- Regression test results
- Performance trend analysis
- Target compliance validation
- Recommendations for improvements
- Historical performance data

### 6. Comprehensive Report Generator (`comprehensive-performance-report.ts`)

**Purpose**: Generates detailed executive and technical reports

**Features**:

- Executive dashboard with key metrics
- Detailed technical analysis
- Multiple output formats (JSON, HTML, CSV)
- Trend analysis and recommendations
- Target compliance summaries

**Usage**:

```bash
tsx scripts/comprehensive-performance-report.ts
```

**Output**:

- Executive summary report
- Detailed technical analysis
- Performance trend analysis
- Actionable recommendations
- Multiple report formats

## Master Test Runner (`run-all-performance-tests.ts`)

The master test runner orchestrates all performance tests and provides a unified interface.

### Usage Options

```bash
# Run all tests
tsx scripts/run-all-performance-tests.ts

# Run specific test types
tsx scripts/run-all-performance-tests.ts --lightweight-only
tsx scripts/run-all-performance-tests.ts --benchmark-only
tsx scripts/run-all-performance-tests.ts --cache-only
tsx scripts/run-all-performance-tests.ts --concurrency-only
tsx scripts/run-all-performance-tests.ts --regression-only

# Generate comprehensive report
tsx scripts/run-all-performance-tests.ts --generate-report

# Verbose output
tsx scripts/run-all-performance-tests.ts --verbose

# Continue on test failures
tsx scripts/run-all-performance-tests.ts --no-exit-on-failure

# Help
tsx scripts/run-all-performance-tests.ts --help
```

## Output and Reports

### Console Output

All tests provide detailed console output including:

- Real-time progress indicators
- Performance metrics and statistics
- Target compliance validation
- Detailed analysis and recommendations
- Summary reports

### Generated Files

The test suite generates several output files:

- `performance-baseline.json` - Performance baseline for regression testing
- `performance-baseline-history.json` - Historical performance data
- `performance-reports/` - Directory containing generated reports
- `performance-report-{timestamp}.json` - Detailed JSON reports
- `performance-report-{timestamp}.html` - HTML reports with visualizations
- `performance-report-{timestamp}.csv` - CSV data for analysis

## Integration with CI/CD

### GitHub Actions Integration

```yaml
name: Performance Tests

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  performance-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: tsx scripts/run-all-performance-tests.ts --generate-report
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: performance-reports
          path: performance-reports/
```

### Performance Monitoring

```yaml
name: Performance Monitoring

on:
  schedule:
    - cron: '0 0 * * *' # Daily at midnight

jobs:
  performance-monitor:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm install
      - run: tsx scripts/performance-regression-test.ts
      - name: Check for regressions
        run: |
          if [ $? -eq 1 ]; then
            echo "Performance regression detected!"
            exit 1
          fi
```

## Performance Targets and Validation

### Response Time Targets

| Security Level | Target | Validation   |
| -------------- | ------ | ------------ |
| Minimal        | <5ms   | ✅ Validated |
| Standard       | <10ms  | ✅ Validated |
| Strict         | <25ms  | ✅ Validated |

### Performance Improvement Targets

| Metric         | Target                      | Validation   |
| -------------- | --------------------------- | ------------ |
| Response Time  | 70-80% improvement          | ✅ Validated |
| Throughput     | >1000 RPS                   | ✅ Validated |
| Memory Usage   | <50MB for 150 concurrent    | ✅ Validated |
| Cache Hit Rate | >90%                        | ✅ Validated |
| Concurrency    | 100-150 concurrent requests | ✅ Validated |

## Troubleshooting

### Common Issues

1. **Memory Usage High**: Adjust cache size or implement more aggressive garbage collection
2. **Low Cache Hit Rate**: Optimize cache strategy and warming algorithms
3. **High Response Times**: Check for bottlenecks in security processing
4. **Concurrency Issues**: Increase concurrency limits or optimize request handling

### Debug Mode

Enable verbose logging for detailed debugging:

```bash
tsx scripts/run-all-performance-tests.ts --verbose
```

### Individual Test Debugging

Run individual tests for focused debugging:

```bash
# Debug lightweight security performance
tsx scripts/lightweight-performance-test.ts

# Debug cache performance
tsx scripts/cache-performance-test.ts

# Debug concurrency handling
tsx scripts/concurrency-stress-test.ts
```

## Best Practices

1. **Regular Testing**: Run performance tests regularly to catch regressions early
2. **Baseline Management**: Keep performance baselines updated with major changes
3. **Target Monitoring**: Monitor target compliance and adjust as needed
4. **Report Analysis**: Review detailed reports to identify optimization opportunities
5. **Trend Analysis**: Use historical data to identify long-term performance trends

## Contributing

When adding new performance tests:

1. Follow the existing test structure and patterns
2. Include comprehensive documentation
3. Add target validation and compliance checks
4. Provide detailed output and analysis
5. Update this README with new test information

## Dependencies

The performance testing suite requires:

- Node.js 18+
- TypeScript
- tsx (for running TypeScript directly)
- All project dependencies (see package.json)

## License

This performance testing suite is part of the lucid-bd project and follows the same license terms.
