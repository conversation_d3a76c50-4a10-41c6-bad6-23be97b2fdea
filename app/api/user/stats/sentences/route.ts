/**
 * 用户句子练习统计 API
 * GET /api/user/stats/sentences
 * 
 * 获取用户的句子练习统计信息和需要练习的句子推荐
 */

import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { z } from 'zod';

// 请求参数验证 schema
const getSentenceStatsQuerySchema = z.object({
  includeRecommendations: z.string().transform(val => val.toLowerCase() === 'true').default('true').optional(),
  recommendationLimit: z.coerce.number().min(1).max(20).default(10).optional()
});

export async function GET(request: NextRequest) {
  try {
    // 用户认证
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    
    // 验证查询参数
    const queryResult = getSentenceStatsQuerySchema.safeParse(
      Object.fromEntries(searchParams.entries())
    );

    if (!queryResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters',
          details: queryResult.error.flatten().fieldErrors
        },
        { status: 400 }
      );
    }

    const { includeRecommendations, recommendationLimit } = queryResult.data;

    const practiceService = createPracticeService();

    // 获取用户句子练习统计
    const practiceStats = await practiceService.getUserSentencePracticeStats(userId);
    
    let sentencesNeedingPractice = null;
    if (includeRecommendations) {
      sentencesNeedingPractice = await practiceService.getSentencesNeedingPractice(
        userId, 
        recommendationLimit || 10
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        practiceStats,
        ...(sentencesNeedingPractice && { 
          recommendations: {
            sentencesNeedingPractice,
            count: sentencesNeedingPractice.length
          }
        }),
        message: 'Sentence practice statistics retrieved successfully'
      }
    });

  } catch (error) {
    console.error('Error fetching sentence practice statistics:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to retrieve sentence practice statistics'
      },
      { status: 500 }
    );
  }
}