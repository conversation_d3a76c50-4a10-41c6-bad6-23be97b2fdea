/**
 * 并发请求监控器
 * 用于跟踪系统并发负载和性能指标
 */

import { Logger } from './logger';

interface ConcurrencyMetrics {
  activeRequests: number;
  totalRequests: number;
  peakConcurrency: number;
  averageResponseTime: number;
  requestsPerSecond: number;
  lastResetTime: number;
}

interface RequestTracker {
  id: string;
  startTime: number;
  operation: string;
  stage: string;
}

export class ConcurrencyMonitor {
  private static instance: ConcurrencyMonitor;
  private activeRequests = new Map<string, RequestTracker>();
  private metrics: ConcurrencyMetrics = {
    activeRequests: 0,
    totalRequests: 0,
    peakConcurrency: 0,
    averageResponseTime: 0,
    requestsPerSecond: 0,
    lastResetTime: Date.now(),
  };
  private responseTimes: number[] = [];
  private requestTimestamps: number[] = [];

  private constructor() {
    // 每30秒重置RPS计算
    setInterval(() => this.resetRpsMetrics(), 30000);
  }

  static getInstance(): ConcurrencyMonitor {
    if (!ConcurrencyMonitor.instance) {
      ConcurrencyMonitor.instance = new ConcurrencyMonitor();
    }
    return ConcurrencyMonitor.instance;
  }

  /**
   * 开始跟踪请求
   */
  startRequest(requestId: string, operation: string): void {
    const tracker: RequestTracker = {
      id: requestId,
      startTime: performance.now(),
      operation,
      stage: 'started',
    };

    this.activeRequests.set(requestId, tracker);
    this.metrics.activeRequests = this.activeRequests.size;
    this.metrics.totalRequests++;

    // 更新峰值并发数
    if (this.metrics.activeRequests > this.metrics.peakConcurrency) {
      this.metrics.peakConcurrency = this.metrics.activeRequests;
    }

    // 记录请求时间戳用于RPS计算
    this.requestTimestamps.push(Date.now());

    // 🔍 DEBUG: 请求开始
    Logger.debug(`[CONCUR-${requestId}] 📈 并发请求开始`, {
      requestId,
      operation,
      activeRequests: this.metrics.activeRequests,
      peakConcurrency: this.metrics.peakConcurrency,
      totalRequests: this.metrics.totalRequests,
    });
  }

  /**
   * 更新请求阶段
   */
  updateRequestStage(requestId: string, stage: string): void {
    const tracker = this.activeRequests.get(requestId);
    if (tracker) {
      tracker.stage = stage;

      // 🔍 DEBUG: 请求阶段更新
      Logger.debug(`[CONCUR-${requestId}] 🔄 请求阶段: ${stage}`, {
        requestId,
        stage,
        elapsedTime: Math.round(performance.now() - tracker.startTime),
      });
    }
  }

  /**
   * 结束跟踪请求
   */
  endRequest(requestId: string): void {
    const tracker = this.activeRequests.get(requestId);
    if (!tracker) return;

    const responseTime = performance.now() - tracker.startTime;
    this.activeRequests.delete(requestId);
    this.metrics.activeRequests = this.activeRequests.size;

    // 更新响应时间统计
    this.responseTimes.push(responseTime);
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000);
    }

    // 计算平均响应时间
    this.metrics.averageResponseTime =
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;

    // 🔍 DEBUG: 请求完成
    Logger.debug(`[CONCUR-${requestId}] ✅ 并发请求完成`, {
      requestId,
      operation: tracker.operation,
      responseTime: Math.round(responseTime),
      activeRequests: this.metrics.activeRequests,
      averageResponseTime: Math.round(this.metrics.averageResponseTime),
    });
  }

  /**
   * 获取当前并发指标
   */
  getMetrics(): ConcurrencyMetrics {
    this.calculateRps();
    return { ...this.metrics };
  }

  /**
   * 获取详细状态
   */
  getDetailedStatus(): {
    metrics: ConcurrencyMetrics;
    activeRequests: Array<{
      id: string;
      operation: string;
      stage: string;
      elapsedTime: number;
    }>;
  } {
    const now = performance.now();
    const activeRequestsDetails = Array.from(this.activeRequests.values()).map((tracker) => ({
      id: tracker.id,
      operation: tracker.operation,
      stage: tracker.stage,
      elapsedTime: Math.round(now - tracker.startTime),
    }));

    return {
      metrics: this.getMetrics(),
      activeRequests: activeRequestsDetails,
    };
  }

  /**
   * 计算每秒请求数
   */
  private calculateRps(): void {
    const now = Date.now();
    const oneSecondAgo = now - 1000;

    // 过滤最近1秒的请求
    const recentRequests = this.requestTimestamps.filter((timestamp) => timestamp > oneSecondAgo);
    this.metrics.requestsPerSecond = recentRequests.length;
  }

  /**
   * 重置RPS相关指标
   */
  private resetRpsMetrics(): void {
    const now = Date.now();
    const thirtySecondsAgo = now - 30000;

    // 清理30秒前的时间戳
    this.requestTimestamps = this.requestTimestamps.filter(
      (timestamp) => timestamp > thirtySecondsAgo
    );

    this.metrics.lastResetTime = now;
  }

  /**
   * 记录并发状态日志
   */
  logConcurrencyStatus(): void {
    const status = this.getDetailedStatus();

    Logger.debug('📊 并发状态报告', {
      activeRequests: status.metrics.activeRequests,
      peakConcurrency: status.metrics.peakConcurrency,
      averageResponseTime: Math.round(status.metrics.averageResponseTime),
      requestsPerSecond: status.metrics.requestsPerSecond,
      totalRequests: status.metrics.totalRequests,
      activeRequestsDetails: status.activeRequests.slice(0, 5), // 只显示前5个活跃请求
    });
  }

  /**
   * 检查是否处于高负载状态
   */
  isHighLoad(): boolean {
    return (
      this.metrics.activeRequests > 20 ||
      this.metrics.averageResponseTime > 1000 ||
      this.metrics.requestsPerSecond > 50
    );
  }

  /**
   * 获取负载等级
   */
  getLoadLevel(): 'low' | 'medium' | 'high' | 'critical' {
    const { activeRequests, averageResponseTime, requestsPerSecond } = this.metrics;

    if (activeRequests > 50 || averageResponseTime > 2000 || requestsPerSecond > 100) {
      return 'critical';
    } else if (activeRequests > 30 || averageResponseTime > 1500 || requestsPerSecond > 75) {
      return 'high';
    } else if (activeRequests > 15 || averageResponseTime > 800 || requestsPerSecond > 40) {
      return 'medium';
    } else {
      return 'low';
    }
  }
}

/**
 * 全局并发监控实例
 */
export const concurrencyMonitor = ConcurrencyMonitor.getInstance();

/**
 * 请求装饰器 - 自动跟踪请求生命周期
 */
export function withConcurrencyTracking<T extends any[], R>(
  operation: string,
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const requestId = Math.random().toString(36).substring(2, 10);

    concurrencyMonitor.startRequest(requestId, operation);

    try {
      const result = await fn(...args);
      concurrencyMonitor.endRequest(requestId);
      return result;
    } catch (error) {
      concurrencyMonitor.endRequest(requestId);
      throw error;
    }
  };
}
