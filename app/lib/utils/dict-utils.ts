import { isValidToken } from './wordValidation.js';

/**
 * 处理词条验证，返回有效和无效的词条列表
 * @param prompt 逗号分隔的词条字符串
 * @returns 处理结果，包含有效和无效的词条列表
 */
export function processTokens(prompt: string): { validTokens: string[]; invalidTokens: string[] } {
  const tokens = prompt
    .split(',')
    .map((t) => t.trim())
    .filter(Boolean);

  const invalidTokens = tokens.filter((t) => !isValidToken(t));
  const validTokens = tokens.filter(isValidToken);

  return { validTokens, invalidTokens };
}

/**
 * 记录API调用的性能数据
 * @param startTime 开始时间戳
 * @param operation 操作名称（可选）
 * @returns 结束记录函数，调用时会打印耗时
 */
export function logPerformance(startTime: number, operation: string = 'API调用'): () => void {
  return () => {
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    console.log(`${operation}耗时: ${Math.round(duration * 100) / 100} s`);
  };
}

/**
 * 格式化单词列表为请求友好的字符串
 * @param words 单词数组
 * @returns 格式化后的字符串
 */
export function formatWordList(words: string[]): string {
  return words.join(',');
}

/**
 * 默认示例词列表（逗号分隔）
 * 可以根据需要替换成任意词列表以获取相应释义
 */
export const DEFAULT_WORD_LIST =
  'world,apple,banana,cherry,date,elderberry,fig,grape,honeydew,wiki';
