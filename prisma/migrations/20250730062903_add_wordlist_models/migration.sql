-- CreateTable
CREATE TABLE "word_list" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "difficulty" INTEGER NOT NULL,
    "totalWords" INTEGER NOT NULL DEFAULT 0,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "word_list_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "word_list_entry" (
    "id" SERIAL NOT NULL,
    "word" TEXT NOT NULL,
    "wordListIds" INTEGER[],
    "wordListId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "word_list_entry_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "word_list_name_key" ON "word_list"("name");

-- CreateIndex
CREATE INDEX "word_list_isActive_idx" ON "word_list"("isActive");

-- CreateIndex
CREATE INDEX "word_list_difficulty_idx" ON "word_list"("difficulty");

-- CreateIndex
CREATE INDEX "word_list_entry_word_idx" ON "word_list_entry"("word");

-- CreateIndex
CREATE INDEX "word_list_entry_wordListId_idx" ON "word_list_entry"("wordListId");

-- CreateIndex
CREATE INDEX "word_list_entry_wordListIds_idx" ON "word_list_entry"("wordListIds");

-- AddForeignKey
ALTER TABLE "word_list_entry" ADD CONSTRAINT "word_list_entry_wordListId_fkey" FOREIGN KEY ("wordListId") REFERENCES "word_list"("id") ON DELETE CASCADE ON UPDATE CASCADE;
