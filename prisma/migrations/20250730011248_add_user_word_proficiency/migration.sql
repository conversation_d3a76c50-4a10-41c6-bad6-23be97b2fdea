-- CreateTable
CREATE TABLE "user_word_proficiency" (
    "userId" TEXT NOT NULL,
    "wordId" INTEGER NOT NULL,
    "practiceCount" INTEGER NOT NULL DEFAULT 0,
    "errorCount" INTEGER NOT NULL DEFAULT 0,
    "averageTime" INTEGER NOT NULL DEFAULT 0,
    "isMarked" BOOLEAN NOT NULL DEFAULT false,
    "proficiencyScore" DOUBLE PRECISION NOT NULL DEFAULT 0.0,
    "lastPracticed" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "user_word_proficiency_pkey" PRIMARY KEY ("userId","wordId")
);

-- CreateIndex
CREATE INDEX "user_word_proficiency_userId_idx" ON "user_word_proficiency"("userId");

-- CreateIndex
CREATE INDEX "user_word_proficiency_wordId_idx" ON "user_word_proficiency"("wordId");

-- CreateIndex
CREATE INDEX "user_word_proficiency_proficiencyScore_idx" ON "user_word_proficiency"("proficiencyScore");

-- CreateIndex
CREATE INDEX "user_word_proficiency_lastPracticed_idx" ON "user_word_proficiency"("lastPracticed");

-- CreateIndex
CREATE INDEX "user_word_proficiency_userId_proficiencyScore_idx" ON "user_word_proficiency"("userId", "proficiencyScore");

-- CreateIndex
CREATE INDEX "user_word_proficiency_userId_isMarked_idx" ON "user_word_proficiency"("userId", "isMarked");

-- AddForeignKey
ALTER TABLE "user_word_proficiency" ADD CONSTRAINT "user_word_proficiency_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_word_proficiency" ADD CONSTRAINT "user_word_proficiency_wordId_fkey" FOREIGN KEY ("wordId") REFERENCES "Vocabulary"("id") ON DELETE CASCADE ON UPDATE CASCADE;
