/**
 * 记录句子练习结果 API
 * POST /api/practice/sentence/[sentenceId]/practice
 * 
 * 记录用户对特定句子的练习情况
 */

import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { ValidationError } from '@/lib/types/api-types';

interface RouteContext {
  params: Promise<{
    sentenceId: string;
  }>;
}

export async function POST(request: NextRequest, context: RouteContext) {
  try {
    // 用户认证
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { sentenceId: sentenceIdParam } = await context.params;
    const sentenceId = parseInt(sentenceIdParam);

    // 验证句子 ID
    if (isNaN(sentenceId) || sentenceId <= 0) {
      return NextResponse.json(
        { error: 'Invalid sentence ID' },
        { status: 400 }
      );
    }

    const practiceService = createPracticeService();

    try {
      const updatedUserSavedSentence = await practiceService.recordSentencePractice(userId, sentenceId);

      if (!updatedUserSavedSentence) {
        return NextResponse.json(
          { error: 'Failed to record practice' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        success: true,
        data: {
          userSavedSentence: updatedUserSavedSentence,
          message: 'Sentence practice recorded successfully'
        }
      });

    } catch (error) {
      if (error instanceof ValidationError) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      
      throw error;
    }

  } catch (error) {
    console.error('Error recording sentence practice:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to record sentence practice'
      },
      { status: 500 }
    );
  }
}