/**
 * @fileoverview Storage适配器测试套件
 *
 * 测试平台特定的存储适配器，包括：
 * - WebStorageAdapter: Web localStorage适配器
 * - ExtensionStorageAdapter: 浏览器扩展存储适配器
 * - MobileStorageAdapter: 移动端存储适配器
 * - MemoryStorageAdapter: 内存存储适配器
 * - PlatformDetector: 平台检测工具
 * - StorageFactory: 存储工厂
 * - PlatformTokenStorage: 平台令牌存储
 * - 多平台兼容性和错误处理
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  StorageAdapter,
  WebStorageAdapter,
  ExtensionStorageAdapter,
  MobileStorageAdapter,
  MemoryStorageAdapter,
  PlatformDetector,
  StorageFactory,
  PlatformTokenStorage,
} from '../storage';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  key: vi.fn(),
  length: 0,
};

// Mock chrome.storage.local
const mockChromeStorage = {
  get: vi.fn(),
  set: vi.fn(),
  remove: vi.fn(),
  clear: vi.fn(),
};

// Mock navigator
const mockNavigator = {
  userAgent: '',
};

describe('Storage Adapters', () => {
  beforeEach(() => {
    // Mock global objects
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    Object.defineProperty(window, 'chrome', {
      value: {
        storage: {
          local: mockChromeStorage,
        },
        runtime: {
          id: 'test-extension-id',
        },
      },
      writable: true,
    });

    Object.defineProperty(window, 'navigator', {
      value: mockNavigator,
      writable: true,
    });

    // Mock global functions
    global.btoa = vi.fn((str) => Buffer.from(str).toString('base64'));
    global.atob = vi.fn((str) => Buffer.from(str, 'base64').toString());

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('WebStorageAdapter', () => {
    let adapter: WebStorageAdapter;

    beforeEach(() => {
      adapter = new WebStorageAdapter();
    });

    describe('基础操作', () => {
      it('应该正确存储和获取数据', async () => {
        const key = 'test-key';
        const value = 'test-value';

        mockLocalStorage.getItem.mockReturnValue(value);

        await adapter.set(key, value);
        const result = await adapter.get(key);

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(key, value);
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith(key);
        expect(result).toBe(value);
      });

      it('应该正确删除数据', async () => {
        const key = 'test-key';

        await adapter.remove(key);

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(key);
      });

      it('应该正确清空所有数据', async () => {
        await adapter.clear();

        expect(mockLocalStorage.clear).toHaveBeenCalled();
      });

      it('应该在键不存在时返回null', async () => {
        mockLocalStorage.getItem.mockReturnValue(null);

        const result = await adapter.get('nonexistent-key');

        expect(result).toBeNull();
      });
    });

    describe('服务器端渲染兼容性', () => {
      it('应该在非浏览器环境中返回null', async () => {
        // Mock server environment
        Object.defineProperty(window, 'localStorage', {
          value: undefined,
          writable: true,
        });

        const result = await adapter.get('test-key');

        expect(result).toBeNull();
      });

      it('应该在非浏览器环境中静默处理设置操作', async () => {
        Object.defineProperty(window, 'localStorage', {
          value: undefined,
          writable: true,
        });

        await expect(adapter.set('key', 'value')).resolves.toBeUndefined();
        await expect(adapter.remove('key')).resolves.toBeUndefined();
        await expect(adapter.clear()).resolves.toBeUndefined();
      });
    });

    describe('错误处理', () => {
      it('应该处理localStorage访问错误', async () => {
        mockLocalStorage.getItem.mockImplementation(() => {
          throw new Error('localStorage access denied');
        });

        await expect(adapter.get('test-key')).rejects.toThrow('localStorage access denied');
      });

      it('应该处理存储配额错误', async () => {
        mockLocalStorage.setItem.mockImplementation(() => {
          throw new DOMException('QuotaExceededError');
        });

        await expect(adapter.set('key', 'value')).rejects.toThrow('QuotaExceededError');
      });
    });
  });

  describe('ExtensionStorageAdapter', () => {
    let adapter: ExtensionStorageAdapter;

    beforeEach(() => {
      adapter = new ExtensionStorageAdapter();
    });

    describe('基础操作', () => {
      it('应该正确存储和获取数据', async () => {
        const key = 'test-key';
        const value = 'test-value';

        mockChromeStorage.get.mockImplementation((keys, callback) => {
          callback({ [key]: value });
        });

        mockChromeStorage.set.mockImplementation((data, callback) => {
          callback();
        });

        await adapter.set(key, value);
        const result = await adapter.get(key);

        expect(mockChromeStorage.set).toHaveBeenCalledWith({ [key]: value }, expect.any(Function));
        expect(mockChromeStorage.get).toHaveBeenCalledWith([key], expect.any(Function));
        expect(result).toBe(value);
      });

      it('应该正确删除数据', async () => {
        const key = 'test-key';

        mockChromeStorage.remove.mockImplementation((keys, callback) => {
          callback();
        });

        await adapter.remove(key);

        expect(mockChromeStorage.remove).toHaveBeenCalledWith([key], expect.any(Function));
      });

      it('应该正确清空所有数据', async () => {
        mockChromeStorage.clear.mockImplementation((callback) => {
          callback();
        });

        await adapter.clear();

        expect(mockChromeStorage.clear).toHaveBeenCalledWith(expect.any(Function));
      });

      it('应该在键不存在时返回null', async () => {
        mockChromeStorage.get.mockImplementation((keys, callback) => {
          callback({});
        });

        const result = await adapter.get('nonexistent-key');

        expect(result).toBeNull();
      });
    });

    describe('环境检测', () => {
      it('应该在非扩展环境中返回null', async () => {
        Object.defineProperty(window, 'chrome', {
          value: undefined,
          writable: true,
        });

        const result = await adapter.get('test-key');

        expect(result).toBeNull();
      });

      it('应该在扩展API不可用时静默处理操作', async () => {
        Object.defineProperty(window, 'chrome', {
          value: { storage: undefined },
          writable: true,
        });

        await expect(adapter.set('key', 'value')).resolves.toBeUndefined();
        await expect(adapter.remove('key')).resolves.toBeUndefined();
        await expect(adapter.clear()).resolves.toBeUndefined();
      });
    });

    describe('异步操作', () => {
      it('应该正确处理chrome.storage的异步回调', async () => {
        const key = 'async-key';
        const value = 'async-value';

        mockChromeStorage.set.mockImplementation((data, callback) => {
          setTimeout(() => callback(), 10);
        });

        mockChromeStorage.get.mockImplementation((keys, callback) => {
          setTimeout(() => callback({ [key]: value }), 10);
        });

        await adapter.set(key, value);
        const result = await adapter.get(key);

        expect(result).toBe(value);
      });

      it('应该处理并发操作', async () => {
        const operations = [];

        for (let i = 0; i < 10; i++) {
          mockChromeStorage.set.mockImplementation((data, callback) => {
            setTimeout(callback, Math.random() * 50);
          });

          operations.push(adapter.set(`key-${i}`, `value-${i}`));
        }

        await expect(Promise.all(operations)).resolves.toBeDefined();
      });
    });
  });

  describe('MobileStorageAdapter', () => {
    let adapter: MobileStorageAdapter;

    beforeEach(() => {
      adapter = new MobileStorageAdapter();
    });

    describe('基础操作', () => {
      it('应该使用前缀存储数据', async () => {
        const key = 'test-key';
        const value = 'test-value';
        const prefixedKey = 'lucid_mobile_' + key;

        mockLocalStorage.getItem.mockReturnValue(value);

        await adapter.set(key, value);
        const result = await adapter.get(key);

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(prefixedKey, value);
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith(prefixedKey);
        expect(result).toBe(value);
      });

      it('应该正确删除带前缀的数据', async () => {
        const key = 'test-key';
        const prefixedKey = 'lucid_mobile_' + key;

        await adapter.remove(key);

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith(prefixedKey);
      });

      it('应该只清除带前缀的数据', async () => {
        const keys = [
          'lucid_mobile_key1',
          'lucid_mobile_key2',
          'other_app_key',
          'lucid_mobile_key3',
        ];

        Object.defineProperty(mockLocalStorage, 'length', { value: keys.length });
        Object.keys = vi.fn().mockReturnValue(keys);

        await adapter.clear();

        expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(3);
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('lucid_mobile_key1');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('lucid_mobile_key2');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('lucid_mobile_key3');
        expect(mockLocalStorage.removeItem).not.toHaveBeenCalledWith('other_app_key');
      });
    });

    describe('前缀隔离', () => {
      it('应该与其他应用的数据隔离', async () => {
        const adapter1 = new MobileStorageAdapter();
        const key = 'shared-key';
        const value1 = 'app1-value';

        await adapter1.set(key, value1);

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('lucid_mobile_shared-key', value1);
      });

      it('应该处理特殊字符的键名', async () => {
        const specialKey = 'key@#$%^&*()';
        const value = 'special-value';
        const prefixedKey = 'lucid_mobile_' + specialKey;

        mockLocalStorage.getItem.mockReturnValue(value);

        await adapter.set(specialKey, value);
        const result = await adapter.get(specialKey);

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith(prefixedKey, value);
        expect(result).toBe(value);
      });
    });
  });

  describe('MemoryStorageAdapter', () => {
    let adapter: MemoryStorageAdapter;

    beforeEach(() => {
      adapter = new MemoryStorageAdapter();
    });

    describe('基础操作', () => {
      it('应该正确存储和获取数据', async () => {
        const key = 'test-key';
        const value = 'test-value';

        await adapter.set(key, value);
        const result = await adapter.get(key);

        expect(result).toBe(value);
      });

      it('应该正确删除数据', async () => {
        const key = 'test-key';
        const value = 'test-value';

        await adapter.set(key, value);
        await adapter.remove(key);
        const result = await adapter.get(key);

        expect(result).toBeNull();
      });

      it('应该正确清空所有数据', async () => {
        await adapter.set('key1', 'value1');
        await adapter.set('key2', 'value2');
        await adapter.clear();

        const result1 = await adapter.get('key1');
        const result2 = await adapter.get('key2');

        expect(result1).toBeNull();
        expect(result2).toBeNull();
      });

      it('应该在键不存在时返回null', async () => {
        const result = await adapter.get('nonexistent-key');

        expect(result).toBeNull();
      });
    });

    describe('内存隔离', () => {
      it('应该在不同实例间隔离数据', async () => {
        const adapter1 = new MemoryStorageAdapter();
        const adapter2 = new MemoryStorageAdapter();

        await adapter1.set('key', 'value1');
        await adapter2.set('key', 'value2');

        const result1 = await adapter1.get('key');
        const result2 = await adapter2.get('key');

        expect(result1).toBe('value1');
        expect(result2).toBe('value2');
      });

      it('应该处理大量数据', async () => {
        const count = 1000;

        for (let i = 0; i < count; i++) {
          await adapter.set(`key-${i}`, `value-${i}`);
        }

        for (let i = 0; i < count; i++) {
          const result = await adapter.get(`key-${i}`);
          expect(result).toBe(`value-${i}`);
        }
      });
    });

    describe('并发操作', () => {
      it('应该正确处理并发读写', async () => {
        const operations = [];

        for (let i = 0; i < 100; i++) {
          operations.push(adapter.set(`key-${i}`, `value-${i}`));
        }

        await Promise.all(operations);

        for (let i = 0; i < 100; i++) {
          const result = await adapter.get(`key-${i}`);
          expect(result).toBe(`value-${i}`);
        }
      });
    });
  });

  describe('PlatformDetector', () => {
    describe('平台检测', () => {
      it('应该检测Web环境', () => {
        Object.defineProperty(window, 'chrome', {
          value: undefined,
          writable: true,
        });
        mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

        expect(PlatformDetector.isWeb).toBe(true);
        expect(PlatformDetector.isExtension).toBe(false);
        expect(PlatformDetector.isMobile).toBe(false);
        expect(PlatformDetector.platform).toBe('web');
      });

      it('应该检测Chrome扩展环境', () => {
        Object.defineProperty(window, 'chrome', {
          value: {
            runtime: { id: 'extension-id' },
          },
          writable: true,
        });

        expect(PlatformDetector.isExtension).toBe(true);
        expect(PlatformDetector.isWeb).toBe(false);
        expect(PlatformDetector.platform).toBe('extension');
      });

      it('应该检测移动设备', () => {
        Object.defineProperty(window, 'chrome', {
          value: undefined,
          writable: true,
        });
        mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)';

        expect(PlatformDetector.isMobile).toBe(true);
        expect(PlatformDetector.isIOS).toBe(true);
        expect(PlatformDetector.isAndroid).toBe(false);
        expect(PlatformDetector.platform).toBe('mobile');
      });

      it('应该检测Android设备', () => {
        Object.defineProperty(window, 'chrome', {
          value: undefined,
          writable: true,
        });
        mockNavigator.userAgent = 'Mozilla/5.0 (Linux; Android 11; SM-G991B)';

        expect(PlatformDetector.isMobile).toBe(true);
        expect(PlatformDetector.isAndroid).toBe(true);
        expect(PlatformDetector.isIOS).toBe(false);
        expect(PlatformDetector.platform).toBe('mobile');
      });

      it('应该处理未知环境', () => {
        // Mock environment without window
        const originalWindow = global.window;
        delete (global as any).window;

        expect(PlatformDetector.platform).toBe('unknown');

        global.window = originalWindow;
      });
    });

    describe('用户代理字符串测试', () => {
      const testCases = [
        {
          ua: 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X)',
          expected: { isMobile: true, isIOS: true, isAndroid: false },
        },
        {
          ua: 'Mozilla/5.0 (Linux; Android 12; Pixel 6)',
          expected: { isMobile: true, isIOS: false, isAndroid: true },
        },
        {
          ua: 'Mozilla/5.0 (BlackBerry; U; BlackBerry 9900)',
          expected: { isMobile: true, isIOS: false, isAndroid: false },
        },
        {
          ua: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
          expected: { isMobile: false, isIOS: false, isAndroid: false },
        },
      ];

      testCases.forEach(({ ua, expected }) => {
        it(`应该正确识别用户代理: ${ua.substring(0, 50)}...`, () => {
          mockNavigator.userAgent = ua;

          expect(PlatformDetector.isMobile).toBe(expected.isMobile);
          expect(PlatformDetector.isIOS).toBe(expected.isIOS);
          expect(PlatformDetector.isAndroid).toBe(expected.isAndroid);
        });
      });
    });
  });

  describe('StorageFactory', () => {
    describe('适配器创建', () => {
      it('应该为Web环境创建WebStorageAdapter', () => {
        vi.spyOn(PlatformDetector, 'platform', 'get').mockReturnValue('web');

        const adapter = StorageFactory.createAdapter();

        expect(adapter).toBeInstanceOf(WebStorageAdapter);
      });

      it('应该为扩展环境创建ExtensionStorageAdapter', () => {
        vi.spyOn(PlatformDetector, 'platform', 'get').mockReturnValue('extension');

        const adapter = StorageFactory.createAdapter();

        expect(adapter).toBeInstanceOf(ExtensionStorageAdapter);
      });

      it('应该为移动环境创建MobileStorageAdapter', () => {
        vi.spyOn(PlatformDetector, 'platform', 'get').mockReturnValue('mobile');

        const adapter = StorageFactory.createAdapter();

        expect(adapter).toBeInstanceOf(MobileStorageAdapter);
      });

      it('应该为未知环境创建MemoryStorageAdapter', () => {
        vi.spyOn(PlatformDetector, 'platform', 'get').mockReturnValue('unknown');

        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
        const adapter = StorageFactory.createAdapter();

        expect(adapter).toBeInstanceOf(MemoryStorageAdapter);
        expect(consoleSpy).toHaveBeenCalledWith('Unknown platform, falling back to memory storage');

        consoleSpy.mockRestore();
      });
    });

    describe('安全适配器创建', () => {
      it('应该为不同平台创建安全适配器', () => {
        const platforms = ['web', 'extension', 'mobile'] as const;

        platforms.forEach((platform) => {
          vi.spyOn(PlatformDetector, 'platform', 'get').mockReturnValue(platform);
          const adapter = StorageFactory.createSecureAdapter();
          expect(adapter).toBeDefined();
        });
      });

      it('应该为未知平台创建内存适配器', () => {
        vi.spyOn(PlatformDetector, 'platform', 'get').mockReturnValue('unknown');

        const adapter = StorageFactory.createSecureAdapter();

        expect(adapter).toBeInstanceOf(MemoryStorageAdapter);
      });
    });
  });

  describe('PlatformTokenStorage', () => {
    beforeEach(() => {
      // Mock the adapter to use memory storage for testing
      vi.spyOn(StorageFactory, 'createSecureAdapter').mockReturnValue(new MemoryStorageAdapter());
    });

    describe('令牌管理', () => {
      it('应该正确存储和获取访问令牌', async () => {
        const token = 'test-access-token';

        await PlatformTokenStorage.setAccessToken(token);
        const result = await PlatformTokenStorage.getAccessToken();

        expect(result).toBe(token);
      });

      it('应该正确存储和获取刷新令牌', async () => {
        const token = 'test-refresh-token';

        await PlatformTokenStorage.setRefreshToken(token);
        const result = await PlatformTokenStorage.getRefreshToken();

        expect(result).toBe(token);
      });

      it('应该在令牌不存在时返回null', async () => {
        const accessToken = await PlatformTokenStorage.getAccessToken();
        const refreshToken = await PlatformTokenStorage.getRefreshToken();

        expect(accessToken).toBeNull();
        expect(refreshToken).toBeNull();
      });
    });

    describe('用户数据管理', () => {
      it('应该正确存储和获取用户数据', async () => {
        const user = { id: '123', email: '<EMAIL>', name: 'Test User' };

        await PlatformTokenStorage.setUserData(user);
        const result = await PlatformTokenStorage.getUserData();

        expect(result).toEqual(user);
      });

      it('应该在用户数据不存在时返回null', async () => {
        const result = await PlatformTokenStorage.getUserData();

        expect(result).toBeNull();
      });

      it('应该处理无效的JSON数据', async () => {
        // Mock adapter to return invalid JSON
        const mockAdapter = {
          get: vi.fn().mockResolvedValue('invalid-json'),
          set: vi.fn(),
          remove: vi.fn(),
          clear: vi.fn(),
        };
        vi.spyOn(StorageFactory, 'createSecureAdapter').mockReturnValue(mockAdapter);

        const result = await PlatformTokenStorage.getUserData();

        expect(result).toBeNull();
      });
    });

    describe('数据清理', () => {
      it('应该清除所有认证数据', async () => {
        await PlatformTokenStorage.setAccessToken('access-token');
        await PlatformTokenStorage.setRefreshToken('refresh-token');
        await PlatformTokenStorage.setUserData({ id: '123' });

        await PlatformTokenStorage.clearAll();

        const accessToken = await PlatformTokenStorage.getAccessToken();
        const refreshToken = await PlatformTokenStorage.getRefreshToken();
        const userData = await PlatformTokenStorage.getUserData();

        expect(accessToken).toBeNull();
        expect(refreshToken).toBeNull();
        expect(userData).toBeNull();
      });
    });

    describe('令牌验证', () => {
      it('应该验证有效的JWT令牌', async () => {
        const validPayload = {
          sub: 'user-123',
          exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour from now
        };
        const validToken = `header.${btoa(JSON.stringify(validPayload))}.signature`;

        await PlatformTokenStorage.setAccessToken(validToken);
        const isValid = await PlatformTokenStorage.isTokenValid();

        expect(isValid).toBe(true);
      });

      it('应该拒绝过期的JWT令牌', async () => {
        const expiredPayload = {
          sub: 'user-123',
          exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
        };
        const expiredToken = `header.${btoa(JSON.stringify(expiredPayload))}.signature`;

        await PlatformTokenStorage.setAccessToken(expiredToken);
        const isValid = await PlatformTokenStorage.isTokenValid();

        expect(isValid).toBe(false);
      });

      it('应该处理无效格式的令牌', async () => {
        await PlatformTokenStorage.setAccessToken('invalid-token');
        const isValid = await PlatformTokenStorage.isTokenValid();

        expect(isValid).toBe(false);
      });

      it('应该在没有令牌时返回false', async () => {
        const isValid = await PlatformTokenStorage.isTokenValid();

        expect(isValid).toBe(false);
      });
    });

    describe('localStorage迁移', () => {
      it('应该从localStorage迁移现有数据', async () => {
        const accessToken = 'old-access-token';
        const refreshToken = 'old-refresh-token';
        const userData = JSON.stringify({ id: '123', email: '<EMAIL>' });

        mockLocalStorage.getItem
          .mockReturnValueOnce(accessToken)
          .mockReturnValueOnce(refreshToken)
          .mockReturnValueOnce(userData);

        await PlatformTokenStorage.migrateFromLocalStorage();

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');

        const migratedAccessToken = await PlatformTokenStorage.getAccessToken();
        const migratedRefreshToken = await PlatformTokenStorage.getRefreshToken();
        const migratedUserData = await PlatformTokenStorage.getUserData();

        expect(migratedAccessToken).toBe(accessToken);
        expect(migratedRefreshToken).toBe(refreshToken);
        expect(migratedUserData).toEqual(JSON.parse(userData));
      });

      it('应该处理部分数据的迁移', async () => {
        const accessToken = 'old-access-token';

        mockLocalStorage.getItem
          .mockReturnValueOnce(accessToken)
          .mockReturnValueOnce(null) // no refresh token
          .mockReturnValueOnce(null); // no user data

        await PlatformTokenStorage.migrateFromLocalStorage();

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledTimes(1);

        const migratedAccessToken = await PlatformTokenStorage.getAccessToken();
        expect(migratedAccessToken).toBe(accessToken);
      });

      it('应该在非浏览器环境中静默处理迁移', async () => {
        Object.defineProperty(window, 'localStorage', {
          value: undefined,
          writable: true,
        });

        await expect(PlatformTokenStorage.migrateFromLocalStorage()).resolves.toBeUndefined();
      });
    });

    describe('错误处理', () => {
      it('应该处理存储操作错误', async () => {
        const errorAdapter = {
          get: vi.fn().mockRejectedValue(new Error('Storage error')),
          set: vi.fn().mockRejectedValue(new Error('Storage error')),
          remove: vi.fn().mockRejectedValue(new Error('Storage error')),
          clear: vi.fn().mockRejectedValue(new Error('Storage error')),
        };
        vi.spyOn(StorageFactory, 'createSecureAdapter').mockReturnValue(errorAdapter);

        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        const result = await PlatformTokenStorage.getAccessToken();
        await PlatformTokenStorage.setAccessToken('token');

        expect(result).toBeNull();
        expect(consoleSpy).toHaveBeenCalled();

        consoleSpy.mockRestore();
      });

      it('应该处理令牌验证错误', async () => {
        const errorAdapter = {
          get: vi.fn().mockRejectedValue(new Error('Storage error')),
          set: vi.fn(),
          remove: vi.fn(),
          clear: vi.fn(),
        };
        vi.spyOn(StorageFactory, 'createSecureAdapter').mockReturnValue(errorAdapter);

        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        const isValid = await PlatformTokenStorage.isTokenValid();

        expect(isValid).toBe(false);
        expect(consoleSpy).toHaveBeenCalledWith('Token validation error:', expect.any(Error));

        consoleSpy.mockRestore();
      });
    });
  });

  describe('适配器接口一致性', () => {
    const createAdapters = (): StorageAdapter[] => [
      new WebStorageAdapter(),
      new MobileStorageAdapter(),
      new MemoryStorageAdapter(),
    ];

    it('所有适配器都应该实现相同的接口', () => {
      const adapters = createAdapters();

      adapters.forEach((adapter) => {
        expect(typeof adapter.get).toBe('function');
        expect(typeof adapter.set).toBe('function');
        expect(typeof adapter.remove).toBe('function');
        expect(typeof adapter.clear).toBe('function');
      });
    });

    it('所有适配器都应该返回Promise', () => {
      const adapters = createAdapters();

      adapters.forEach((adapter) => {
        expect(adapter.get('test')).toBeInstanceOf(Promise);
        expect(adapter.set('test', 'value')).toBeInstanceOf(Promise);
        expect(adapter.remove('test')).toBeInstanceOf(Promise);
        expect(adapter.clear()).toBeInstanceOf(Promise);
      });
    });
  });

  describe('性能测试', () => {
    it('应该高效处理大量操作', async () => {
      const adapter = new MemoryStorageAdapter();
      const count = 1000;
      const startTime = Date.now();

      // 批量写入
      const writeOps = [];
      for (let i = 0; i < count; i++) {
        writeOps.push(adapter.set(`key-${i}`, `value-${i}`));
      }
      await Promise.all(writeOps);

      // 批量读取
      const readOps = [];
      for (let i = 0; i < count; i++) {
        readOps.push(adapter.get(`key-${i}`));
      }
      await Promise.all(readOps);

      const endTime = Date.now();
      const duration = endTime - startTime;

      // 性能要求：1000次操作应该在1秒内完成
      expect(duration).toBeLessThan(1000);
    });

    it('应该有效处理并发访问', async () => {
      const adapter = new MemoryStorageAdapter();
      const concurrency = 100;

      const operations = [];
      for (let i = 0; i < concurrency; i++) {
        operations.push(
          Promise.all([
            adapter.set(`key-${i}`, `value-${i}`),
            adapter.get(`key-${i}`),
            adapter.remove(`key-${i}`),
          ])
        );
      }

      await expect(Promise.all(operations)).resolves.toBeDefined();
    });
  });
});
