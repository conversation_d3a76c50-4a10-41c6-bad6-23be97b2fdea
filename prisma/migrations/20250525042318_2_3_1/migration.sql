/*
  Warnings:

  - You are about to drop the column `createdAt` on the `Vocabulary` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[vocabularyId]` on the table `WordFormat` will be added. If there are existing duplicate values, this will fail.

*/
-- DropForeignKey
ALTER TABLE "WordFormat" DROP CONSTRAINT "WordFormat_vocabularyId_fkey";

-- DropIndex
DROP INDEX "Vocabulary_word_key";

-- AlterTable
ALTER TABLE "Vocabulary" DROP COLUMN "createdAt";

-- AlterTable
ALTER TABLE "WordFormat" ADD COLUMN     "baseFormId" INTEGER,
ALTER COLUMN "vocabularyId" DROP NOT NULL;

-- CreateIndex
CREATE UNIQUE INDEX "WordFormat_vocabularyId_key" ON "WordFormat"("vocabularyId");

-- AddForeignKey
ALTER TABLE "WordFormat" ADD CONSTRAINT "WordFormat_vocabularyId_fkey" FOREIGN KEY ("vocabularyId") REFERENCES "Vocabulary"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WordFormat" ADD CONSTRAINT "WordFormat_baseFormId_fkey" FOREIGN KEY ("baseFormId") REFERENCES "WordFormat"("id") ON DELETE SET NULL ON UPDATE CASCADE;
