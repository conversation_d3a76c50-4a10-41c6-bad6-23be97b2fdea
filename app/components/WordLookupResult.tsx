'use client';

import React, { useState } from 'react';
import { EnhancedDictionaryResponseType, WordFormRelationshipType } from '@/lib/utils/dict-types';

interface WordLookupResultProps {
  result: EnhancedDictionaryResponseType;
  onWordClick?: (word: string) => void;
}

export default function WordLookupResult({ result, onWordClick }: WordLookupResultProps) {
  const [activeTab, setActiveTab] = useState<'definition' | 'forms' | 'related'>('definition');

  if (!result.words.length) {
    return <NotFoundResult result={result} onWordClick={onWordClick} />;
  }

  const mainWord = result.words[0];
  const relationship = (result as any).wordRelationships?.[0];

  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto">
      {/* 查询状态指示器 */}
      <QueryStatusIndicator metadata={result.queryMetadata} />

      {/* 主词汇信息 */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <h1 className="text-3xl font-bold text-gray-900">{mainWord.word}</h1>
          <div className="flex gap-2">
            {mainWord.phonetic?.us && (
              <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                US: {mainWord.phonetic.us}
              </span>
            )}
            {mainWord.phonetic?.uk && (
              <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
                UK: {mainWord.phonetic.uk}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* 标签页导航 */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <TabButton
            active={activeTab === 'definition'}
            onClick={() => setActiveTab('definition')}
            label="释义"
            count={mainWord.explain.length}
          />
          <TabButton
            active={activeTab === 'forms'}
            onClick={() => setActiveTab('forms')}
            label="词形变化"
            count={relationship?.totalFormsCount || 0}
          />
          <TabButton
            active={activeTab === 'related'}
            onClick={() => setActiveTab('related')}
            label="相关词汇"
            count={(result as any).relatedSuggestions?.length || 0}
          />
        </nav>
      </div>

      {/* 内容区域 */}
      <div className="min-h-[300px]">
        {activeTab === 'definition' && <DefinitionTab explain={mainWord.explain} />}
        {activeTab === 'forms' && relationship && (
          <WordFormsTab relationship={relationship} onWordClick={onWordClick} />
        )}
        {activeTab === 'related' && (
          <RelatedWordsTab
            suggestions={(result as any).relatedSuggestions || []}
            onWordClick={onWordClick}
          />
        )}
      </div>
    </div>
  );
}

// 查询状态指示器组件
function QueryStatusIndicator({ metadata }: { metadata: any }) {
  const getStatusInfo = () => {
    switch (metadata.searchStrategy) {
      case 'exact_vocabulary':
        return { color: 'green', text: '精确匹配', icon: '✓' };
      case 'main_wordformat':
        return { color: 'blue', text: '词形匹配', icon: '→' };
      case 'derived_wordformat':
        return { color: 'orange', text: '派生形式', icon: '↗' };
      default:
        return { color: 'gray', text: '未知', icon: '?' };
    }
  };

  const status = getStatusInfo();

  return (
    <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
      <div className="flex items-center gap-2">
        <span className={`w-2 h-2 rounded-full bg-${status.color}-500`}></span>
        <span className="text-sm text-gray-600">
          {status.icon} {status.text} • 查询词: "{metadata.searchTerm}"
        </span>
        {!metadata.isBaseFormQuery && (
          <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
            通过变形查找
          </span>
        )}
      </div>
      <span className="text-xs text-gray-500">{metadata.processingTimeMs}ms</span>
    </div>
  );
}

// 标签按钮组件
function TabButton({
  active,
  onClick,
  label,
  count,
}: {
  active: boolean;
  onClick: () => void;
  label: string;
  count: number;
}) {
  return (
    <button
      onClick={onClick}
      className={`py-2 px-1 border-b-2 font-medium text-sm ${
        active
          ? 'border-blue-500 text-blue-600'
          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
      }`}
    >
      {label}
      {count > 0 && (
        <span className="ml-2 bg-gray-100 text-gray-600 py-1 px-2 rounded-full text-xs">
          {count}
        </span>
      )}
    </button>
  );
}

// 释义标签页
function DefinitionTab({ explain }: { explain: any[] }) {
  return (
    <div className="space-y-6">
      {explain.map((item, index) => (
        <div key={index} className="border-l-4 border-blue-500 pl-4">
          <h3 className="font-semibold text-lg text-gray-900 mb-3">{item.pos}</h3>
          <div className="space-y-3">
            {item.definitions.map((def: any, defIndex: number) => (
              <div key={defIndex} className="bg-gray-50 p-4 rounded-lg">
                <p className="text-gray-800 mb-2">{def.definition}</p>
                <p className="text-gray-600 text-sm mb-1">{def.chinese}</p>
                <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                  {def.chinese_short}
                </span>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
}

// 词形变化标签页
function WordFormsTab({
  relationship,
  onWordClick,
}: {
  relationship: WordFormRelationshipType;
  onWordClick?: (word: string) => void;
}) {
  const mainForms = relationship.allForms.filter((f) => f.isMainForm);
  const derivedForms = relationship.allForms.filter((f) => !f.isMainForm);

  return (
    <div className="space-y-6">
      {/* 基础形式 */}
      <div>
        <h3 className="font-semibold text-lg text-gray-900 mb-3">基础形式 ({mainForms.length})</h3>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
          {mainForms.map((form) => (
            <WordFormCard
              key={form.id}
              form={form}
              isBase={form.name === '原型'}
              onClick={onWordClick}
            />
          ))}
        </div>
      </div>

      {/* 派生形式 */}
      {derivedForms.length > 0 && (
        <div>
          <h3 className="font-semibold text-lg text-gray-900 mb-3">
            派生形式 ({derivedForms.length})
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {derivedForms.map((form) => (
              <WordFormCard key={form.id} form={form} isBase={false} onClick={onWordClick} />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

// 词形卡片组件
function WordFormCard({
  form,
  isBase,
  onClick,
}: {
  form: any;
  isBase: boolean;
  onClick?: (word: string) => void;
}) {
  return (
    <div
      className={`p-3 rounded-lg border cursor-pointer transition-colors ${
        isBase
          ? 'bg-blue-50 border-blue-200 hover:bg-blue-100'
          : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
      }`}
      onClick={() => onClick?.(form.form)}
    >
      <div className="font-medium text-gray-900">{form.form}</div>
      <div className="text-sm text-gray-600">{form.name}</div>
      {isBase && (
        <span className="inline-block mt-1 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
          原型
        </span>
      )}
    </div>
  );
}

// 相关词汇标签页
function RelatedWordsTab({
  suggestions,
  onWordClick,
}: {
  suggestions: string[];
  onWordClick?: (word: string) => void;
}) {
  if (!suggestions.length) {
    return <div className="text-center text-gray-500 py-8">暂无相关词汇建议</div>;
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
      {suggestions.map((word, index) => (
        <button
          key={index}
          onClick={() => onWordClick?.(word)}
          className="p-3 text-left bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors"
        >
          <span className="font-medium text-gray-900">{word}</span>
        </button>
      ))}
    </div>
  );
}

// 未找到结果组件
function NotFoundResult({
  result,
  onWordClick,
}: {
  result: EnhancedDictionaryResponseType;
  onWordClick?: (word: string) => void;
}) {
  return (
    <div className="bg-white rounded-lg shadow-lg p-6 max-w-4xl mx-auto text-center">
      <div className="mb-4">
        <h2 className="text-xl font-semibold text-gray-900">
          未找到 "{result.queryMetadata.searchTerm}"
        </h2>
        <p className="text-gray-600 mt-2">处理时间: {result.queryMetadata.processingTimeMs}ms</p>
      </div>

      {(result as any).relatedSuggestions && (result as any).relatedSuggestions.length > 0 && (
        <div>
          <h3 className="font-medium text-gray-900 mb-3">您是否要查找：</h3>
          <div className="flex flex-wrap gap-2 justify-center">
            {(result as any).relatedSuggestions.map((word: any, index: number) => (
              <button
                key={index}
                onClick={() => onWordClick?.(word)}
                className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full hover:bg-blue-200 transition-colors"
              >
                {word}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
