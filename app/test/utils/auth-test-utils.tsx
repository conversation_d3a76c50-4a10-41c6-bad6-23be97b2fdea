/**
 * @fileoverview 认证系统测试工具集
 *
 * 提供认证相关测试的完整工具链，包括：
 * - Mock认证Provider
 * - 测试数据生成器
 * - API响应模拟
 * - 组件渲染辅助函数
 * - 断言工具
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import React, { ReactElement, ReactNode, createContext, useContext } from 'react';
import { render, RenderOptions, RenderResult } from '@testing-library/react';
import { vi, MockedFunction, expect } from 'vitest';

// 定义用户类型
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

// 定义认证上下文类型
export interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, name: string) => Promise<void>;
  loginWithOAuth: (provider: string) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<string>;
}

/**
 * 测试用户数据生成器
 */
export const createMockUser = (overrides: Partial<User> = {}): User => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  name: 'Test User',
  avatar: 'https://example.com/avatar.jpg',
  role: 'user',
  createdAt: '2024-01-01T00:00:00.000Z',
  updatedAt: '2024-01-01T00:00:00.000Z',
  ...overrides,
});

/**
 * Mock认证上下文类型
 */
export interface MockAuthContextType extends Partial<AuthContextType> {
  user?: User | null;
  loading?: boolean;
  error?: string | null;
  isAuthenticated?: boolean;
  isLoading?: boolean;
  login?: MockedFunction<AuthContextType['login']>;
  signup?: MockedFunction<AuthContextType['signup']>;
  loginWithOAuth?: MockedFunction<AuthContextType['loginWithOAuth']>;
  logout?: MockedFunction<AuthContextType['logout']>;
  refreshToken?: MockedFunction<AuthContextType['refreshToken']>;
}

/**
 * 创建Mock认证上下文的默认值
 */
export const createMockAuthContext = (overrides: MockAuthContextType = {}): AuthContextType => {
  const defaultUser = overrides.user !== undefined ? overrides.user : createMockUser();

  return {
    user: defaultUser,
    loading: false,
    error: null,
    isAuthenticated: !!defaultUser,
    isLoading: false,
    login: vi.fn().mockResolvedValue(undefined),
    signup: vi.fn().mockResolvedValue(undefined),
    loginWithOAuth: vi.fn().mockResolvedValue(undefined),
    logout: vi.fn().mockResolvedValue(undefined),
    refreshToken: vi.fn().mockResolvedValue('mocked-token'),
    ...overrides,
  };
};

// 创建测试用的认证上下文
const TestAuthContext = createContext<AuthContextType | undefined>(undefined);

/**
 * Mock认证Provider组件
 */
export const MockAuthProvider: React.FC<{
  children: ReactNode;
  value?: MockAuthContextType;
}> = ({ children, value = {} }) => {
  const mockContext = createMockAuthContext(value);

  return (
    <TestAuthContext.Provider value={mockContext}>
      <div data-testid="mock-auth-provider">{children}</div>
    </TestAuthContext.Provider>
  );
};

/**
 * 测试用的useAuth hook
 */
export const useTestAuth = () => {
  const context = useContext(TestAuthContext);
  if (!context) {
    throw new Error('useTestAuth must be used within a MockAuthProvider');
  }
  return context;
};

/**
 * 带认证上下文的组件渲染工具
 */
interface AuthRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  authValue?: MockAuthContextType;
  wrapper?: React.ComponentType<{ children: ReactNode }>;
}

export const renderWithAuth = (
  ui: ReactElement,
  options: AuthRenderOptions = {}
): RenderResult & { authContext: AuthContextType } => {
  const { authValue = {}, wrapper: Wrapper, ...renderOptions } = options;

  const mockAuthContext = createMockAuthContext(authValue);

  // Create wrapper component with auth context
  const AuthWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {
    return (
      <MockAuthProvider value={authValue}>
        {Wrapper ? <Wrapper>{children}</Wrapper> : children}
      </MockAuthProvider>
    );
  };

  const result = render(ui, {
    wrapper: AuthWrapper,
    ...renderOptions,
  });

  return {
    ...result,
    authContext: mockAuthContext,
  };
};

/**
 * API响应模拟工具
 */
export const mockApiResponse = {
  /**
   * 模拟登录成功响应
   */
  loginSuccess: (user: User = createMockUser()) => ({
    ok: true,
    status: 200,
    json: vi.fn().mockResolvedValue({
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      user,
      userId: user.id,
    }),
  }),

  /**
   * 模拟登录失败响应
   */
  loginError: (message: string = 'Invalid credentials') => ({
    ok: false,
    status: 401,
    json: vi.fn().mockResolvedValue({
      message,
      error: 'Unauthorized',
    }),
  }),

  /**
   * 模拟注册成功响应
   */
  signupSuccess: (user: User = createMockUser()) => ({
    ok: true,
    status: 201,
    json: vi.fn().mockResolvedValue({
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
      user,
      userId: user.id,
    }),
  }),

  /**
   * 模拟注册失败响应
   */
  signupError: (message: string = 'Email already exists') => ({
    ok: false,
    status: 409,
    json: vi.fn().mockResolvedValue({
      message,
      error: 'Conflict',
    }),
  }),

  /**
   * 模拟用户资料响应
   */
  userProfile: (user: User = createMockUser()) => ({
    ok: true,
    status: 200,
    json: vi.fn().mockResolvedValue(user),
  }),

  /**
   * 模拟token刷新成功响应
   */
  refreshSuccess: () => ({
    ok: true,
    status: 200,
    json: vi.fn().mockResolvedValue({
      accessToken: 'new-mock-access-token',
      refreshToken: 'new-mock-refresh-token',
    }),
  }),

  /**
   * 模拟网络错误
   */
  networkError: () => {
    throw new Error('Network error');
  },
};

/**
 * 安全存储模拟工具
 */
export const mockSecureTokenStorage = {
  /**
   * 模拟认证状态
   */
  mockAuthStatus: (isAuthenticated: boolean = true, user: User | null = createMockUser()) => {
    vi.mock('@/lib/utils/secure-auth', () => ({
      SecureTokenStorage: {
        getAuthStatus: vi.fn().mockResolvedValue({
          isAuthenticated,
          user,
          hasValidRefreshToken: isAuthenticated,
        }),
        setTokens: vi.fn().mockResolvedValue(true),
        clearTokens: vi.fn().mockResolvedValue(undefined),
        refreshToken: vi.fn().mockResolvedValue(true),
      },
      AutoTokenManager: {
        start: vi.fn(),
        stop: vi.fn(),
      },
    }));
  },

  /**
   * 模拟令牌存储失败
   */
  mockStorageFailure: () => {
    vi.mock('@/lib/utils/secure-auth', () => ({
      SecureTokenStorage: {
        getAuthStatus: vi.fn().mockResolvedValue({
          isAuthenticated: false,
          user: null,
          hasValidRefreshToken: false,
        }),
        setTokens: vi.fn().mockResolvedValue(false),
        clearTokens: vi.fn().mockResolvedValue(undefined),
        refreshToken: vi.fn().mockRejectedValue(new Error('Token refresh failed')),
      },
      AutoTokenManager: {
        start: vi.fn(),
        stop: vi.fn(),
      },
    }));
  },
};

/**
 * 测试断言工具
 */
export const authAssertions = {
  /**
   * 断言用户已登录状态
   */
  expectUserLoggedIn: (authContext: AuthContextType, user: User = createMockUser()) => {
    expect(authContext.isAuthenticated).toBe(true);
    expect(authContext.user).toEqual(user);
    expect(authContext.loading).toBe(false);
    expect(authContext.error).toBe(null);
  },

  /**
   * 断言用户未登录状态
   */
  expectUserLoggedOut: (authContext: AuthContextType) => {
    expect(authContext.isAuthenticated).toBe(false);
    expect(authContext.user).toBe(null);
    expect(authContext.loading).toBe(false);
  },

  /**
   * 断言加载状态
   */
  expectLoadingState: (authContext: AuthContextType) => {
    expect(authContext.loading).toBe(true);
    expect(authContext.isLoading).toBe(true);
  },

  /**
   * 断言错误状态
   */
  expectErrorState: (authContext: AuthContextType, errorMessage: string) => {
    expect(authContext.error).toBe(errorMessage);
    expect(authContext.loading).toBe(false);
  },

  /**
   * 断言函数被正确调用
   */
  expectFunctionCalled: (mockFn: MockedFunction<any>, ...args: any[]) => {
    if (args.length > 0) {
      expect(mockFn).toHaveBeenCalledWith(...args);
    } else {
      expect(mockFn).toHaveBeenCalled();
    }
  },
};

/**
 * 测试场景生成器
 */
export const testScenarios = {
  /**
   * 已认证用户场景
   */
  authenticatedUser: (user: User = createMockUser()): MockAuthContextType => ({
    user,
    loading: false,
    error: null,
    isAuthenticated: true,
    isLoading: false,
  }),

  /**
   * 未认证用户场景
   */
  unauthenticatedUser: (): MockAuthContextType => ({
    user: null,
    loading: false,
    error: null,
    isAuthenticated: false,
    isLoading: false,
  }),

  /**
   * 加载状态场景
   */
  loadingState: (): MockAuthContextType => ({
    user: null,
    loading: true,
    error: null,
    isAuthenticated: false,
    isLoading: true,
  }),

  /**
   * 错误状态场景
   */
  errorState: (errorMessage: string = 'Authentication error'): MockAuthContextType => ({
    user: null,
    loading: false,
    error: errorMessage,
    isAuthenticated: false,
    isLoading: false,
  }),

  /**
   * 管理员用户场景
   */
  adminUser: (overrides: Partial<User> = {}): MockAuthContextType => ({
    user: createMockUser({ role: 'admin', ...overrides }),
    loading: false,
    error: null,
    isAuthenticated: true,
    isLoading: false,
  }),
};

/**
 * 快速测试工具
 */
export const quickTests = {
  /**
   * 测试组件在不同认证状态下的渲染
   */
  testComponentWithAuthStates: async (
    component: ReactElement,
    scenarios: Array<{
      name: string;
      authValue: MockAuthContextType;
      test: (result: RenderResult) => void | Promise<void>;
    }>
  ) => {
    for (const scenario of scenarios) {
      const result = renderWithAuth(component, { authValue: scenario.authValue });
      await scenario.test(result);
      result.unmount();
    }
  },
};
