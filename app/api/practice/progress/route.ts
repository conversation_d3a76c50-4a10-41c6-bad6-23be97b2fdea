import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { requireAuth, AuthContext } from '@/lib/auth/route-protection';

/**
 * 记录词库练习进度API
 * POST /api/practice/progress
 */

// 请求体验证schema
const RequestBodySchema = z.object({
  wordListId: z.number().int().positive(),
  word: z.string().min(1).max(50),
  practiceResult: z.object({
    isCorrect: z.boolean(),
    timeTaken: z.number().int().min(0), // 毫秒
    errorCount: z.number().int().min(0).optional(),
    sessionType: z.enum(['typing', 'quiz', 'review']).default('typing')
  }),
  addToWordBook: z.boolean().optional() // 是否加入生词本
});

const protectedHandler = async (request: NextRequest, context: AuthContext) => {
  try {
    // 解析请求体
    const body = await request.json();
    const validatedData = RequestBodySchema.parse(body);

    // 使用认证的用户ID
    const userId = context.user.id;

    const { wordListId, word, practiceResult, addToWordBook } = validatedData;

    // 查找或创建Vocabulary记录
    let vocabulary = await prisma.vocabulary.findUnique({
      where: { word: word.toLowerCase() }
    });

    if (!vocabulary) {
      // 如果词汇不存在，创建基础记录
      vocabulary = await prisma.vocabulary.create({
        data: {
          word: word.toLowerCase(),
          phonetics: [],
          freqRank: null
        }
      });
    }

    // 查找或创建UserWordProficiency记录
    let userProficiency = await prisma.userWordProficiency.findUnique({
      where: {
        userId_wordId: {
          userId,
          wordId: vocabulary.id
        }
      }
    });

    if (!userProficiency) {
      // 创建新的熟练度记录
      userProficiency = await prisma.userWordProficiency.create({
        data: {
          userId,
          wordId: vocabulary.id,
          practiceCount: 1,
          errorCount: practiceResult.isCorrect ? 0 : (practiceResult.errorCount || 1),
          averageTime: practiceResult.timeTaken,
          proficiencyScore: practiceResult.isCorrect ? 0.2 : 0.1,
          lastPracticed: new Date(),
          isMarked: false
        }
      });
    } else {
      // 更新现有熟练度记录
      const newPracticeCount = userProficiency.practiceCount + 1;
      const newErrorCount = userProficiency.errorCount + (practiceResult.isCorrect ? 0 : (practiceResult.errorCount || 1));
      
      // 计算新的平均时间
      const newAverageTime = Math.round(
        (userProficiency.averageTime * userProficiency.practiceCount + practiceResult.timeTaken) / newPracticeCount
      );

      // 计算新的熟练度分数 (0.0 - 1.0)
      const accuracy = 1 - (newErrorCount / newPracticeCount);
      const speedFactor = Math.max(0.1, Math.min(1.0, 3000 / newAverageTime)); // 基于平均3秒的目标时间
      const practiceFactor = Math.min(1.0, newPracticeCount / 10); // 练习次数因子，10次为满分
      
      const newProficiencyScore = Math.min(1.0, accuracy * 0.5 + speedFactor * 0.3 + practiceFactor * 0.2);

      userProficiency = await prisma.userWordProficiency.update({
        where: {
          userId_wordId: {
            userId,
            wordId: vocabulary.id
          }
        },
        data: {
          practiceCount: newPracticeCount,
          errorCount: newErrorCount,
          averageTime: newAverageTime,
          proficiencyScore: newProficiencyScore,
          lastPracticed: new Date()
        }
      });
    }

    // 如果需要加入生词本，标记为困难单词
    if (addToWordBook) {
      await prisma.userWordProficiency.update({
        where: {
          userId_wordId: {
            userId,
            wordId: vocabulary.id
          }
        },
        data: {
          isMarked: true
        }
      });
    }

    // 记录练习活动到统计表（如果存在）
    try {
      await prisma.userWordStat.upsert({
        where: {
          userId_vocabularyId: {
            userId,
            vocabularyId: vocabulary.id
          }
        },
        update: {
          lookupCount: { increment: 1 },
          lastQueried: new Date()
        },
        create: {
          userId,
          vocabularyId: vocabulary.id,
          lookupCount: 1,
          lastQueried: new Date(),
          level: 0,
          starred: addToWordBook || false
        }
      });
    } catch (error) {
      // 如果统计记录失败，不影响主要功能
      console.warn('Failed to update word statistics:', error);
    }

    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: {
        word: vocabulary.word,
        proficiency: {
          practiceCount: userProficiency.practiceCount,
          errorCount: userProficiency.errorCount,
          averageTime: userProficiency.averageTime,
          proficiencyScore: userProficiency.proficiencyScore,
          isMarked: userProficiency.isMarked,
          lastPracticed: userProficiency.lastPracticed
        },
        practiceResult: {
          improvement: userProficiency.practiceCount > 1 ? 
            userProficiency.proficiencyScore - (userProficiency.practiceCount === 2 ? 0.1 : 0) : 0,
          accuracy: 1 - (userProficiency.errorCount / userProficiency.practiceCount),
          suggestion: userProficiency.proficiencyScore < 0.3 ? 'needs_more_practice' :
                     userProficiency.proficiencyScore < 0.7 ? 'good_progress' : 'mastered'
        }
      },
      message: 'Practice progress recorded successfully'
    });

  } catch (error) {
    console.error('记录练习进度失败:', error);

    // Zod验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors
      }, { status: 400 });
    }

    // 数据库错误
    if (error instanceof Error && error.message.includes('Record to update not found')) {
      return NextResponse.json({
        success: false,
        error: 'Word or user not found',
        message: 'Unable to find the specified word or user'
      }, { status: 404 });
    }

    // 通用错误处理
    return NextResponse.json({
      success: false,
      error: 'Failed to record practice progress',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

export const POST = requireAuth()(protectedHandler);