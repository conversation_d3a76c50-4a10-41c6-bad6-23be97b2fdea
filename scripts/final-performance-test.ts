/**
 * 最终性能验证测试
 * 验证并发控制优化后的性能表现
 */

async function testConcurrency(
  concurrency: number,
  iterations: number = 3
): Promise<{
  concurrency: number;
  avgResponseTime: number;
  successRate: number;
  rps: number;
  p95ResponseTime: number;
}> {
  console.log(`🚀 测试 ${concurrency} 个并发请求...`);

  const testWords = ['test', 'apple', 'book', 'car', 'dog', 'cat', 'run', 'walk', 'eat', 'drink'];
  const allResults: number[] = [];

  for (let iteration = 1; iteration <= iterations; iteration++) {
    const promises = Array.from({ length: concurrency }, (_, i) => {
      const word = testWords[i % testWords.length];
      const startTime = performance.now();

      return fetch(`http://localhost:3000/api/dictionary/en/${word}?fresh=true`)
        .then(async (response) => {
          const responseTime = performance.now() - startTime;
          const success = response.ok;
          return { responseTime, success };
        })
        .catch(() => ({ responseTime: performance.now() - startTime, success: false }));
    });

    const iterationStart = performance.now();
    const results = await Promise.all(promises);
    const iterationTime = performance.now() - iterationStart;

    const successful = results.filter((r) => r.success);
    const responseTimes = successful.map((r) => r.responseTime);
    allResults.push(...responseTimes);

    const avgResponseTime =
      responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;

    console.log(`   迭代 ${iteration}/${iterations}:`);
    console.log(`     - 成功: ${successful.length}/${concurrency}`);
    console.log(`     - 平均响应时间: ${Math.round(avgResponseTime)}ms`);
    console.log(`     - 总耗时: ${Math.round(iterationTime)}ms`);
  }

  // 计算总体统计
  const avgResponseTime = allResults.reduce((sum, time) => sum + time, 0) / allResults.length;
  const successRate = (allResults.length / (concurrency * iterations)) * 100;

  // 计算P95响应时间
  const sortedTimes = allResults.sort((a, b) => a - b);
  const p95Index = Math.floor(sortedTimes.length * 0.95);
  const p95ResponseTime = sortedTimes[p95Index] || 0;

  // 计算RPS（基于最后一次迭代的总耗时）
  const promises = Array.from({ length: concurrency }, (_, i) => {
    const word = testWords[i % testWords.length];
    return fetch(`http://localhost:3000/api/dictionary/en/${word}?fresh=true`);
  });

  const rpsStart = performance.now();
  await Promise.all(promises);
  const rpsTime = performance.now() - rpsStart;
  const rps = Math.round((concurrency / rpsTime) * 1000);

  return {
    concurrency,
    avgResponseTime: Math.round(avgResponseTime),
    successRate: Math.round(successRate * 100) / 100,
    rps,
    p95ResponseTime: Math.round(p95ResponseTime),
  };
}

async function runFinalPerformanceTest() {
  console.log('🎯 最终性能验证测试');
  console.log('📊 测试并发控制优化效果\n');

  // 检查并发控制器状态
  try {
    const statusResponse = await fetch('http://localhost:3000/api/debug/concurrency-status');
    const status = await statusResponse.json();
    console.log('🔧 并发控制器状态:');
    console.log(`   - 最大并发数: ${status.concurrencyLimiter.maxConcurrency}`);
    console.log(`   - 当前活跃请求: ${status.concurrencyLimiter.activeRequests}`);
    console.log(`   - 队列长度: ${status.concurrencyLimiter.queueLength}`);
    console.log(`   - 系统状态: ${status.concurrencyLimiter.status}\n`);
  } catch (error) {
    console.log('⚠️ 无法获取并发控制器状态\n');
  }

  const testCases = [1, 5, 10, 20, 30, 50, 75, 100];
  const results: any[] = [];

  for (const concurrency of testCases) {
    try {
      const result = await testConcurrency(concurrency);
      results.push(result);

      console.log(`✅ ${concurrency} 并发完成:`);
      console.log(`   - 成功率: ${result.successRate}%`);
      console.log(`   - 平均响应时间: ${result.avgResponseTime}ms`);
      console.log(`   - P95响应时间: ${result.p95ResponseTime}ms`);
      console.log(`   - RPS: ${result.rps}\n`);

      // 检查是否达到性能目标
      if (concurrency === 50) {
        const targetMet = result.avgResponseTime <= 500;
        console.log(`🎯 50并发性能目标检查:`);
        console.log(`   - 目标: ≤500ms`);
        console.log(`   - 实际: ${result.avgResponseTime}ms`);
        console.log(`   - 结果: ${targetMet ? '✅ 达标' : '❌ 未达标'}\n`);
      }
    } catch (error) {
      console.error(`❌ ${concurrency} 并发测试失败:`, error);
    }
  }

  // 输出汇总表格
  console.log('📊 性能测试汇总:');
  console.log('并发数 | 成功率 | 平均响应时间 | P95响应时间 | RPS');
  console.log('-------|--------|-------------|------------|----');

  results.forEach((result) => {
    console.log(
      `${result.concurrency.toString().padStart(6)} | ${result.successRate.toString().padStart(6)}% | ${result.avgResponseTime.toString().padStart(11)}ms | ${result.p95ResponseTime.toString().padStart(10)}ms | ${result.rps.toString().padStart(3)}`
    );
  });

  // 性能分析
  console.log('\n🔍 性能分析:');
  const optimalConcurrency = results.reduce((best, current) =>
    current.rps > best.rps ? current : best
  );
  console.log(`🎯 最佳性能点: ${optimalConcurrency.concurrency} 并发`);
  console.log(`   - RPS: ${optimalConcurrency.rps}`);
  console.log(`   - 平均响应时间: ${optimalConcurrency.avgResponseTime}ms`);

  const target50 = results.find((r) => r.concurrency === 50);
  if (target50) {
    const improvement = target50.avgResponseTime <= 500 ? '✅ 已达标' : '⚠️ 需继续优化';
    console.log(`\n🎯 50并发目标评估: ${improvement}`);
    console.log(`   - 当前性能: ${target50.avgResponseTime}ms`);
    console.log(`   - 目标性能: 500ms`);
    if (target50.avgResponseTime <= 500) {
      const margin = 500 - target50.avgResponseTime;
      console.log(`   - 性能余量: ${margin}ms`);
    }
  }
}

// 运行测试
runFinalPerformanceTest().catch(console.error);
