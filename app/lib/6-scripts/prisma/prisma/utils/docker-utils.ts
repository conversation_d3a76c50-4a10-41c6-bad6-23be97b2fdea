/**
 * Docker工具类 - 管理PostgreSQL容器操作
 */

import { exec } from 'child_process';
import { promisify } from 'util';
import { DockerContainerInfo, DatabaseConnection } from './backup-types.js';

const execAsync = promisify(exec);

export class DockerUtils {
  constructor(private containerName: string) {}

  /**
   * 检查Docker是否可用
   */
  async isDockerAvailable(): Promise<boolean> {
    try {
      await execAsync('docker --version');
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 检查容器是否存在
   */
  async containerExists(): Promise<boolean> {
    try {
      const { stdout } = await execAsync(
        `docker ps -a --filter "name=${this.containerName}" --format "{{.Names}}"`
      );
      return stdout.trim().includes(this.containerName);
    } catch {
      return false;
    }
  }

  /**
   * 检查容器是否正在运行
   */
  async isContainerRunning(): Promise<boolean> {
    try {
      const { stdout } = await execAsync(
        `docker ps --filter "name=${this.containerName}" --format "{{.Names}}"`
      );
      return stdout.trim().includes(this.containerName);
    } catch {
      return false;
    }
  }

  /**
   * 获取容器信息
   */
  async getContainerInfo(): Promise<DockerContainerInfo | null> {
    try {
      const { stdout } = await execAsync(
        `docker inspect ${this.containerName} --format '{{.Id}}|{{.Name}}|{{.Config.Image}}|{{.State.Status}}|{{.State.Health.Status}}' 2>/dev/null`
      );

      if (!stdout.trim()) return null;

      const [id, name, image, state, health] = stdout.trim().split('|');

      // 获取端口映射
      const { stdout: portsOutput } = await execAsync(
        `docker port ${this.containerName} 2>/dev/null || echo ""`
      );

      const ports = portsOutput
        .trim()
        .split('\n')
        .filter((line) => line.trim());

      return {
        id: id.slice(0, 12), // 短ID
        name: name.replace('/', ''), // 移除前导斜杠
        image,
        state,
        status: state,
        ports,
        isHealthy: health === 'healthy' || (health === '' && state === 'running'),
      };
    } catch {
      return null;
    }
  }

  /**
   * 启动容器
   */
  async startContainer(): Promise<boolean> {
    try {
      await execAsync(`docker start ${this.containerName}`);

      // 等待容器启动
      for (let i = 0; i < 30; i++) {
        if (await this.isContainerRunning()) {
          return true;
        }
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      return false;
    } catch {
      return false;
    }
  }

  /**
   * 执行容器内命令
   */
  async execInContainer(command: string): Promise<{ stdout: string; stderr: string }> {
    try {
      const { stdout, stderr } = await execAsync(
        `docker exec ${this.containerName} bash -c "${command}"`
      );
      return { stdout, stderr };
    } catch (error: any) {
      throw new Error(`容器命令执行失败: ${error.message}`);
    }
  }

  /**
   * 测试数据库连接
   */
  async testDatabaseConnection(
    database: string,
    username: string,
    password?: string
  ): Promise<DatabaseConnection> {
    try {
      // 简化连接测试，只检查版本，注意引号转义
      const pgCommand = password
        ? `PGPASSWORD='${password}' psql -U ${username} -d ${database} -c 'SELECT version();'`
        : `psql -U ${username} -d ${database} -c 'SELECT version();'`;

      const { stdout } = await this.execInContainer(pgCommand);

      const lines = stdout.trim().split('\n');
      const versionLine = lines.find((line) => line.includes('PostgreSQL'));

      if (versionLine) {
        // 解析版本信息
        const versionMatch = versionLine.match(/PostgreSQL ([\d.]+)/);
        const version = versionMatch ? versionMatch[1] : 'Unknown';

        return {
          isConnected: true,
          version,
        };
      }

      return {
        isConnected: true,
        version: 'Unknown',
      };
    } catch (error: any) {
      return {
        isConnected: false,
        error: error.message,
      };
    }
  }

  /**
   * 执行pg_dump备份命令
   */
  async executePgDump(
    database: string,
    username: string,
    password: string,
    outputFile: string,
    options: {
      format?: string;
      compress?: boolean;
      schemaOnly?: boolean;
      dataOnly?: boolean;
      excludeTables?: string[];
      includeTables?: string[];
    } = {}
  ): Promise<{ success: boolean; output: string; error?: string }> {
    try {
      let command = `PGPASSWORD='${password}' pg_dump -U ${username} -d ${database}`;

      // 格式选项
      if (options.format) {
        command += ` --format=${options.format}`;
      }

      // 压缩选项
      if (options.compress) {
        command += ` --compress=9`;
      }

      // 仅模式
      if (options.schemaOnly) {
        command += ` --schema-only`;
      }

      // 仅数据
      if (options.dataOnly) {
        command += ` --data-only`;
      }

      // 排除表
      if (options.excludeTables?.length) {
        options.excludeTables.forEach((table) => {
          command += ` --exclude-table=${table}`;
        });
      }

      // 包含表
      if (options.includeTables?.length) {
        options.includeTables.forEach((table) => {
          command += ` --table=${table}`;
        });
      }

      command += ` --file=/tmp/${outputFile}`;

      const { stdout, stderr } = await this.execInContainer(command);

      return {
        success: !stderr.includes('error') && !stderr.includes('ERROR'),
        output: stdout,
        error: stderr.includes('error') || stderr.includes('ERROR') ? stderr : undefined,
      };
    } catch (error: any) {
      return {
        success: false,
        output: '',
        error: error.message,
      };
    }
  }

  /**
   * 从容器复制文件到宿主机
   */
  async copyFromContainer(containerPath: string, hostPath: string): Promise<boolean> {
    try {
      await execAsync(`docker cp ${this.containerName}:${containerPath} ${hostPath}`);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 复制文件到容器
   */
  async copyToContainer(hostPath: string, containerPath: string): Promise<boolean> {
    try {
      await execAsync(`docker cp ${hostPath} ${this.containerName}:${containerPath}`);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 清理容器内临时文件
   */
  async cleanupTempFiles(pattern: string = '/tmp/lucid-bd-*'): Promise<boolean> {
    try {
      await this.execInContainer(`rm -f ${pattern}`);
      return true;
    } catch {
      return false;
    }
  }
}
