import { UserWordProficiencyRepository } from '@/lib/2-repositories/UserWordProficiencyRepository';

export interface PracticeSession {
  userId: string;
  wordId: number;
  timeTaken: number; // milliseconds
  hasError: boolean;
}

export interface ProficiencyCalculationInput {
  practiceCount: number;
  errorCount: number;
  averageTime: number;
  isMarked: boolean;
}

/**
 * Service for calculating and managing word proficiency scores
 * Implements business logic for proficiency calculation and word marking
 */
export class ProficiencyCalculationService {
  constructor(private repository: UserWordProficiencyRepository) {}

  /**
   * Calculate proficiency score based on practice metrics
   * Score ranges from 0.0 (lowest proficiency) to 1.0 (highest proficiency)
   * 
   * Algorithm considers:
   * - Error rate (lower is better)
   * - Average typing speed (lower time is better)
   * - Practice frequency (more practice improves score)
   * - Manual marking (reduces score for difficult words)
   */
  calculateProficiencyScore(input: ProficiencyCalculationInput): number {
    const { practiceCount, errorCount, averageTime, isMarked } = input;

    // If no practice yet, return 0
    if (practiceCount === 0) {
      return 0.0;
    }

    // 计算各项分数组件
    const accuracyScore = this.calculateAccuracyScore(errorCount, practiceCount);
    const speedScore = this.calculateSpeedScore(averageTime);
    const practiceScore = this.calculatePracticeScore(practiceCount);

    // 加权计算基础分数
    const baseScore = this.calculateWeightedScore(accuracyScore, speedScore, practiceScore);

    // 应用标记惩罚
    return this.applyMarkingPenalty(baseScore, isMarked);
  }

  /**
   * 计算准确率分数
   */
  private calculateAccuracyScore(errorCount: number, practiceCount: number): number {
    const errorRate = errorCount / practiceCount;
    return Math.max(0, 1.0 - errorRate);
  }

  /**
   * 计算速度分数
   */
  private calculateSpeedScore(averageTime: number): number {
    const IDEAL_TIME_MS = 1000; // 理想打字时间
    const MIN_TIME_THRESHOLD = IDEAL_TIME_MS * 0.1; // 防止除零
    return Math.max(0, Math.min(1.0, IDEAL_TIME_MS / Math.max(averageTime, MIN_TIME_THRESHOLD)));
  }

  /**
   * 计算练习频率分数
   */
  private calculatePracticeScore(practiceCount: number): number {
    const MAX_PRACTICE_FOR_FULL_SCORE = 20;
    return Math.min(1.0, practiceCount / MAX_PRACTICE_FOR_FULL_SCORE);
  }

  /**
   * 计算加权分数
   */
  private calculateWeightedScore(accuracyScore: number, speedScore: number, practiceScore: number): number {
    const ACCURACY_WEIGHT = 0.4;   // 40% - 最重要
    const SPEED_WEIGHT = 0.3;      // 30% - 打字重要指标
    const PRACTICE_WEIGHT = 0.3;   // 30% - 练习一致性

    return accuracyScore * ACCURACY_WEIGHT +
           speedScore * SPEED_WEIGHT +
           practiceScore * PRACTICE_WEIGHT;
  }

  /**
   * 应用标记惩罚
   */
  private applyMarkingPenalty(baseScore: number, isMarked: boolean): number {
    const MARKING_PENALTY_FACTOR = 0.7; // 标记困难单词减少30%分数
    const finalScore = isMarked ? baseScore * MARKING_PENALTY_FACTOR : baseScore;
    return Math.max(0.0, Math.min(1.0, finalScore));
  }

  /**
   * Update proficiency based on a practice session
   */
  async updateProficiencyFromSession(session: PracticeSession): Promise<void> {
    const { userId, wordId, timeTaken, hasError } = session;

    // Get existing proficiency record or create new one
    let proficiency = await this.repository.findByUserAndWord(userId, wordId);

    if (!proficiency) {
      // Create new proficiency record
      proficiency = await this.repository.create({
        userId,
        wordId,
        practiceCount: 1,
        errorCount: hasError ? 1 : 0,
        averageTime: timeTaken,
        lastPracticed: new Date(),
      });
    } else {
      // Update existing record
      const newPracticeCount = proficiency.practiceCount + 1;
      const newErrorCount = proficiency.errorCount + (hasError ? 1 : 0);
      
      // Calculate new average time using weighted average
      const newAverageTime = Math.round(
        (proficiency.averageTime * proficiency.practiceCount + timeTaken) / newPracticeCount
      );

      proficiency = await this.repository.update(userId, wordId, {
        practiceCount: newPracticeCount,
        errorCount: newErrorCount,
        averageTime: newAverageTime,
        lastPracticed: new Date(),
      });
    }

    // Recalculate proficiency score
    const newScore = this.calculateProficiencyScore({
      practiceCount: proficiency.practiceCount,
      errorCount: proficiency.errorCount,
      averageTime: proficiency.averageTime,
      isMarked: proficiency.isMarked,
    });

    // Update the score in database
    await this.repository.update(userId, wordId, {
      proficiencyScore: newScore,
    });
  }

  /**
   * Process multiple practice sessions in batch
   */
  async updateProficienciesFromSessions(sessions: PracticeSession[]): Promise<void> {
    // Process each session sequentially to maintain data consistency
    for (const session of sessions) {
      await this.updateProficiencyFromSession(session);
    }
  }

  /**
   * Mark a word as difficult (or unmark it)
   */
  async toggleWordMarking(userId: string, wordId: number, isMarked: boolean): Promise<void> {
    // Update the marking status
    const proficiency = await this.repository.toggleMarked(userId, wordId, isMarked);

    // Recalculate proficiency score with new marking status
    const newScore = this.calculateProficiencyScore({
      practiceCount: proficiency.practiceCount,
      errorCount: proficiency.errorCount,
      averageTime: proficiency.averageTime,
      isMarked,
    });

    // Update the score
    await this.repository.update(userId, wordId, {
      proficiencyScore: newScore,
    });
  }

  /**
   * Get recommended words for practice based on proficiency scores
   * Returns words with lowest proficiency scores (most need practice)
   */
  async getRecommendedWordsForPractice(
    userId: string,
    limit: number = 20,
    includeMarkedOnly: boolean = false
  ) {
    if (includeMarkedOnly) {
      return this.repository.findMarkedWords(userId);
    }
    
    return this.repository.findLowestProficiencyWords(userId, limit);
  }

  /**
   * Get words that need review (haven't been practiced recently)
   */
  async getWordsForReview(userId: string, daysSinceLastPractice: number = 7) {
    return this.repository.findWordsForReview(userId, daysSinceLastPractice);
  }

  /**
   * Get user's overall proficiency statistics
   */
  async getUserProficiencyStats(userId: string) {
    const stats = await this.repository.getUserStats(userId);
    
    // Calculate proficiency level based on average score
    let proficiencyLevel = 'Beginner';
    if (stats.averageProficiency >= 0.8) proficiencyLevel = 'Expert';
    else if (stats.averageProficiency >= 0.6) proficiencyLevel = 'Advanced';
    else if (stats.averageProficiency >= 0.4) proficiencyLevel = 'Intermediate';

    return {
      ...stats,
      proficiencyLevel,
    };
  }

  /**
   * Recalculate all proficiency scores for a user
   * Useful for algorithm updates or data migrations
   */
  async recalculateAllProficiencyScores(userId: string): Promise<void> {
    const proficiencies = await this.repository.findManyByUser(userId);

    for (const proficiency of proficiencies) {
      const newScore = this.calculateProficiencyScore({
        practiceCount: proficiency.practiceCount,
        errorCount: proficiency.errorCount,
        averageTime: proficiency.averageTime,
        isMarked: proficiency.isMarked,
      });

      await this.repository.update(userId, proficiency.wordId, {
        proficiencyScore: newScore,
      });
    }
  }
}