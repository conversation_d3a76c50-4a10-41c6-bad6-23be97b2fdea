/**
 * Authentication Types Index
 * 认证类型导出文件
 */

export * from './jwt';
export * from './errors';
export * from './next-auth';

// 常用类型重新导出
export type {
  JWTPayload,
  AccessTokenPayload,
  RefreshTokenPayload,
  TokenResponse,
  UserInfo,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  LogoutRequest,
  LogoutResponse,
} from './jwt';

export type {
  AuthError,
  ValidationError,
  AuthApiError,
  RateLimitError,
  TokenError,
  ValidationErrorResponse,
} from './errors';

export { AuthErrorCode, AUTH_ERROR_MESSAGES, AuthenticationError } from './errors';

export type { NextAuthConfig, AuthProvider, CredentialsConfig } from './next-auth';

// 常用常量
export const TOKEN_CONSTANTS = {
  /** 访问令牌过期时间 (15分钟) */
  ACCESS_TOKEN_EXPIRES_IN: 15 * 60,
  /** 刷新令牌过期时间 (30天) */
  REFRESH_TOKEN_EXPIRES_IN: 30 * 24 * 60 * 60,
  /** 令牌类型 */
  TOKEN_TYPE: 'Bearer' as const,
  /** JWT签名算法 */
  JWT_ALGORITHM: 'RS256' as const,
  /** 令牌发行者 */
  JWT_ISSUER: 'lucid-bd' as const,
  /** 令牌受众 */
  JWT_AUDIENCE: 'lucid-bd-users' as const,
};

export const AUTH_ROUTES = {
  /** 登录页面 */
  SIGN_IN: '/auth/signin',
  /** 注册页面 */
  SIGN_UP: '/auth/signup',
  /** 忘记密码页面 */
  FORGOT_PASSWORD: '/auth/forgot-password',
  /** 重置密码页面 */
  RESET_PASSWORD: '/auth/reset-password',
  /** 邮箱验证页面 */
  VERIFY_EMAIL: '/auth/verify-email',
  /** 错误页面 */
  ERROR: '/auth/error',
  /** 默认重定向页面 */
  DEFAULT_REDIRECT: '/dashboard',
  /** 登录重定向页面 */
  LOGIN_REDIRECT: '/auth/signin',
};

export const API_ROUTES = {
  /** NextAuth API */
  NEXTAUTH: '/api/auth',
  /** 用户注册 */
  REGISTER: '/api/auth/register',
  /** 令牌登录 */
  TOKEN_LOGIN: '/api/auth/token/login',
  /** 令牌刷新 */
  TOKEN_REFRESH: '/api/auth/token/refresh',
  /** 用户登出 */
  TOKEN_LOGOUT: '/api/auth/token/logout',
  /** Google登录 */
  GOOGLE_LOGIN: '/api/auth/token/google',
  /** GitHub登录 */
  GITHUB_LOGIN: '/api/auth/token/github',
  /** 用户资料 */
  USER_PROFILE: '/api/user/profile',
  /** 用户数据导出 */
  USER_EXPORT: '/api/user/export',
  /** 删除用户 */
  USER_DELETE: '/api/user/account',
  /** 审计日志 */
  AUDIT_LOGS: '/api/user/audit-logs',
  /** 管理员审计日志 */
  ADMIN_AUDIT_LOGS: '/api/admin/audit-logs',
};

export const VALIDATION_RULES = {
  /** 邮箱正则 */
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  /** 密码最小长度 */
  PASSWORD_MIN_LENGTH: 8,
  /** 密码最大长度 */
  PASSWORD_MAX_LENGTH: 128,
  /** 密码必须包含数字 */
  PASSWORD_REQUIRE_NUMBERS: true,
  /** 密码必须包含字母 */
  PASSWORD_REQUIRE_LETTERS: true,
  /** 密码必须包含特殊字符 */
  PASSWORD_REQUIRE_SPECIAL: true,
  /** 用户名最小长度 */
  NAME_MIN_LENGTH: 2,
  /** 用户名最大长度 */
  NAME_MAX_LENGTH: 50,
};

export const RATE_LIMITS = {
  /** 登录限制 - 每IP每分钟 */
  LOGIN_PER_IP_PER_MINUTE: 5,
  /** 登录限制 - 每用户每分钟 */
  LOGIN_PER_USER_PER_MINUTE: 3,
  /** 令牌刷新限制 - 每令牌每分钟 */
  REFRESH_PER_TOKEN_PER_MINUTE: 10,
  /** 注册限制 - 每IP每小时 */
  REGISTER_PER_IP_PER_HOUR: 3,
  /** 密码重置限制 - 每邮箱每小时 */
  PASSWORD_RESET_PER_EMAIL_PER_HOUR: 2,
};

export const SECURITY_CONFIG = {
  /** bcrypt 加密轮数 */
  BCRYPT_ROUNDS: 12,
  /** 最大登录失败次数 */
  MAX_LOGIN_ATTEMPTS: 5,
  /** 账户锁定时间 (小时) */
  ACCOUNT_LOCK_DURATION: 24,
  /** 会话过期时间 (小时) */
  SESSION_TIMEOUT: 24,
  /** 记住我功能过期时间 (天) */
  REMEMBER_ME_DURATION: 30,
  /** CSRF 保护 */
  CSRF_PROTECTION: true,
  /** 安全头配置 */
  SECURITY_HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
  },
};
