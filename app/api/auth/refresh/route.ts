/**
 * JWT刷新令牌轮换端点
 * 实现OAuth2.1最佳实践：每次刷新都生成新的refresh token并使旧token失效
 */

import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { tokenBlacklistService } from '@/lib/1-services/auth/TokenBlacklistService';
import { getJWTSecret, getJWTRefreshSecret } from '@/lib/utils/env';
import { generateJTI, hashString } from '@/lib/utils/crypto';
import { redisRateLimiter } from '@/lib/utils/redis';
import {
  JWTPayload,
  RefreshTokenPayload,
  TokenPair,
  ValidationError,
  UnauthorizedError,
  RateLimitError,
  ApiResponse,
} from '@/lib/types/api-types';

// 请求体验证Schema
const RefreshTokenRequestSchema = z.object({
  refreshToken: z.string().min(1, 'Refresh token is required'),
});

/**
 * POST /api/auth/refresh - 刷新JWT令牌
 * 实现令牌轮换机制，提高安全性
 */
export async function POST(request: NextRequest) {
  try {
    // 1. 检查速率限制 (每分钟最多10次刷新)
    await checkRefreshRateLimit(request);

    const body = await request.json();

    // 验证请求体
    const bodyResult = RefreshTokenRequestSchema.safeParse(body);
    if (!bodyResult.success) {
      throw new ValidationError('Invalid request body', bodyResult.error.issues);
    }

    const { refreshToken } = bodyResult.data;

    // 刷新令牌并实现轮换
    const tokenPair = await refreshTokenWithRotation(refreshToken);

    const response: ApiResponse<TokenPair> = {
      success: true,
      data: tokenPair,
      message: 'Token refreshed successfully',
    };

    return NextResponse.json(response);
  } catch (error) {
    return handleRefreshError(error);
  }
}

/**
 * 实现刷新令牌轮换机制
 */
async function refreshTokenWithRotation(oldRefreshToken: string): Promise<TokenPair> {
  try {
    // 1. 验证旧的refresh token
    const decoded = jwt.verify(
      oldRefreshToken,
      getJWTRefreshSecret()
    ) as RefreshTokenPayload;

    if (!decoded || !decoded.sub || !decoded.jti || !decoded.tokenId) {
      throw new UnauthorizedError('Invalid refresh token format');
    }

    // 2. 检查refresh token是否在黑名单中
    const isBlacklisted = await tokenBlacklistService.isBlacklisted(decoded.jti);
    if (isBlacklisted) {
      throw new UnauthorizedError('Refresh token has been revoked');
    }

    // 3. 验证数据库中的refresh token记录
    const refreshTokenRecord = await prisma.refreshToken.findUnique({
      where: {
        id: decoded.tokenId,
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            isActive: true,
          },
        },
      },
    });

    if (!refreshTokenRecord) {
      throw new UnauthorizedError('Refresh token not found');
    }

    if (refreshTokenRecord.isRevoked) {
      throw new UnauthorizedError('Refresh token has been revoked');
    }

    if (refreshTokenRecord.expiresAt <= new Date()) {
      throw new UnauthorizedError('Refresh token has expired');
    }

    if (refreshTokenRecord.userId !== decoded.sub) {
      throw new UnauthorizedError('Token user mismatch');
    }

    if (!refreshTokenRecord.user.isActive) {
      throw new UnauthorizedError('User account is inactive');
    }

    // 4. 立即将旧的refresh token加入黑名单
    await tokenBlacklistService.blacklistToken({
      jti: decoded.jti,
      userId: decoded.sub,
      tokenHash: hashString(oldRefreshToken),
      reason: 'refresh_rotation',
      expiresAt: new Date(decoded.exp * 1000),
    });

    // 5. 将数据库中的旧refresh token标记为已吊销
    await prisma.refreshToken.update({
      where: { id: decoded.tokenId },
      data: {
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy: 'refresh_rotation',
      },
    });

    // 6. 生成新的access token
    const newAccessToken = generateAccessToken({
      userId: refreshTokenRecord.userId,
      email: refreshTokenRecord.user.email,
      clientType: 'mobile', // Default to mobile for refresh tokens
      scopes: ['word:read', 'word:write'], // Default scopes
    });

    // 7. 生成新的refresh token
    const newRefreshTokenData = await generateRefreshToken({
      userId: refreshTokenRecord.userId,
      clientType: 'mobile', // Default to mobile for refresh tokens
      ipAddress: 'unknown', // Default IP address
      userAgent: 'unknown', // Default user agent
    });

    // 8. 返回新的token pair
    return {
      accessToken: newAccessToken,
      refreshToken: newRefreshTokenData.token,
      expiresIn: 3600, // 1小时
      tokenType: 'Bearer' as const,
    };
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      throw new UnauthorizedError('Invalid refresh token');
    }
    throw error;
  }
}

/**
 * 生成新的Access Token
 */
function generateAccessToken(params: {
  userId: string;
  email: string;
  clientType: 'mobile' | 'extension';
  scopes: string[];
}): string {
  const { userId, email, clientType, scopes } = params;
  const now = Math.floor(Date.now() / 1000);
  const expiresIn = 3600; // 1小时

  const payload: JWTPayload = {
    sub: userId,
    email,
    iat: now,
    exp: now + expiresIn,
    jti: generateJTI(),
    clientType,
    scopes,
  };

  return jwt.sign(payload, getJWTSecret(), {
    algorithm: 'HS256',
  });
}

/**
 * 生成新的Refresh Token并保存到数据库
 */
async function generateRefreshToken(params: {
  userId: string;
  clientType: 'mobile' | 'extension';
  ipAddress?: string;
  userAgent?: string;
}): Promise<{ token: string; id: string }> {
  const { userId, clientType, ipAddress = 'unknown', userAgent = 'unknown' } = params;
  const now = Math.floor(Date.now() / 1000);
  const expiresIn = 30 * 24 * 3600; // 30天
  const expiresAt = new Date((now + expiresIn) * 1000);

  // 生成临时token用于JWT payload
  const tempToken = jwt.sign(
    {
      sub: userId,
      iat: now,
      exp: now + expiresIn,
      jti: generateJTI(),
    },
    getJWTRefreshSecret(),
    { algorithm: 'HS256' }
  );

  // 创建refresh token记录
  const refreshTokenRecord = await prisma.refreshToken.create({
    data: {
      token: tempToken,
      userId,
      expiresAt,
      isRevoked: false,
      ipAddress,
      userAgent,
    },
  });

  const payload: RefreshTokenPayload = {
    sub: userId,
    tokenId: refreshTokenRecord.id,
    iat: now,
    exp: now + expiresIn,
    jti: generateJTI(),
  };

  const token = jwt.sign(payload, getJWTRefreshSecret(), {
    algorithm: 'HS256',
  });

  // 更新数据库中的token
  await prisma.refreshToken.update({
    where: { id: refreshTokenRecord.id },
    data: { token },
  });

  return {
    token,
    id: refreshTokenRecord.id,
  };
}

/**
 * 检查refresh端点的速率限制
 */
async function checkRefreshRateLimit(request: NextRequest): Promise<void> {
  const clientIP = getClientIP(request);
  const key = `refresh_rate_limit:${clientIP}`;
  
  try {
    const result = await redisRateLimiter.checkSlidingWindowRateLimit(
      key,
      10, // 每分钟最多10次
      60  // 60秒窗口
    );
    
    if (!result.allowed) {
      const waitMinutes = Math.ceil((result.resetTime - Date.now()) / 60000);
      throw new RateLimitError(`Refresh rate limit exceeded. Please wait ${waitMinutes} minute(s) before trying again.`);
    }
  } catch (error) {
    if (error instanceof RateLimitError) {
      throw error;
    }
    // Redis不可用时记录日志但不阻止请求
    console.warn('Refresh rate limit check failed:', error);
  }
}

/**
 * 获取客户端IP地址
 */
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  const clientIP = request.headers.get('x-client-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  return realIP || clientIP || 'unknown';
}

/**
 * 处理刷新令牌错误
 */
function handleRefreshError(error: unknown): NextResponse {
  console.error('Token refresh error:', error);

  if (error instanceof ValidationError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
        details: error.details,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof UnauthorizedError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
      },
      { status: error.statusCode }
    );
  }

  // 未知错误
  return NextResponse.json(
    {
      success: false,
      error: 'Token refresh failed',
      code: 'REFRESH_ERROR',
    },
    { status: 500 }
  );
}