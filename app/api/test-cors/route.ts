import { NextRequest, NextResponse } from 'next/server';
import { SecurityHeadersManager } from '@/lib/utils/cookies';

/**
 * Test endpoint for CORS configuration
 * GET /api/test-cors
 */
export async function GET(request: NextRequest) {
  const response = NextResponse.json({
    message: 'CORS test successful',
    origin: request.headers.get('origin'),
    method: request.method,
    timestamp: new Date().toISOString(),
  });

  // Add security headers including CORS
  SecurityHeadersManager.addSecurityHeaders(response, request);

  return response;
}

/**
 * Handle OPTIONS preflight requests
 * OPTIONS /api/test-cors
 */
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 });
  
  // Add security headers including CORS
  SecurityHeadersManager.addSecurityHeaders(response, request);

  return response;
}

/**
 * Test POST endpoint for CORS
 * POST /api/test-cors
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const response = NextResponse.json({
      message: 'CORS POST test successful',
      origin: request.headers.get('origin'),
      method: request.method,
      receivedData: body,
      timestamp: new Date().toISOString(),
    });

    // Add security headers including CORS
    SecurityHeadersManager.addSecurityHeaders(response, request);

    return response;
  } catch (error) {
    const response = NextResponse.json(
      { error: 'Invalid JSON body' },
      { status: 400 }
    );
    
    SecurityHeadersManager.addSecurityHeaders(response, request);
    return response;
  }
}
