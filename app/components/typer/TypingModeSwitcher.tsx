import { TypingTest } from './TypingTest';
import { WordPractice } from './WordPractice';
import { SentencePractice } from './SentencePractice';

interface TypingModeSwitcherProps {
  mode: string;
}

/**
 * 打字练习模式调度组件
 * 根据传入的模式参数动态渲染对应的练习组件
 * 替代了原有大型 TypingPractice 组件的功能
 */
export function TypingModeSwitcher({ mode }: TypingModeSwitcherProps) {
  switch (mode) {
    case 'typing-test':
      return <TypingTest />;
    case 'word-practice':
      return <WordPractice />;
    case 'sentence-practice':
      return <SentencePractice />;
    default:
      return (
        <div className="flex items-center justify-center h-64">
          <div className="text-center space-y-4">
            <h2 className="text-2xl font-bold text-muted-foreground">Invalid Practice Mode</h2>
            <p className="text-muted-foreground">
              The requested practice mode "{mode}" is not available.
            </p>
            <p className="text-sm text-muted-foreground">
              Available modes: typing-test, word-practice, sentence-practice
            </p>
          </div>
        </div>
      );
  }
}
