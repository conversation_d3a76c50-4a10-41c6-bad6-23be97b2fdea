/**
 * 词汇查询门面 (Facade)
 * 提供简化的接口，内部使用分层架构
 */

import { DictionaryService, LookupOptions } from './1-services/dictionary/DictionaryService';
import { OptimizedVocabularyRepository } from './2-repositories/OptimizedVocabularyRepository';
import { WordFormatRepository } from './2-repositories/WordFormatRepository';
import { CacheRepository } from './2-repositories/CacheRepository';
import { SearchResult } from './3-domain/value-objects/SearchResult';
import {
  EnhancedDictionaryResponseType,
  WordFormRelationshipType,
  QueryMetadataType,
} from './utils/dict-types';
// Logger 在函数内部动态导入

// 兼容性接口（保持向后兼容）
interface LookupResult {
  vocabulary: any;
  wordRelationships: WordFormRelationshipType[];
  queryMetadata: QueryMetadataType;
  relatedSuggestions: string[];
}

/**
 * 服务实例（单例模式）
 */
let dictionaryService: DictionaryService | null = null;

function getDictionaryService(): DictionaryService {
  if (!dictionaryService) {
    // 依赖注入 - 使用优化版本的Repository
    const vocabularyRepository = new OptimizedVocabularyRepository();
    const wordFormatRepository = new WordFormatRepository();
    const cacheRepository = new CacheRepository();

    dictionaryService = new DictionaryService({
      vocabularyRepository,
      wordFormatRepository,
      cacheRepository,
    });
  }
  return dictionaryService;
}

/**
 * [用于测试] 重置单例服务实例，确保测试隔离性
 */
export function resetDictionaryServiceForTesting() {
  dictionaryService = null;
  console.log('🧪 DictionaryService单例实例已重置，用于测试隔离。');
}

/**
 * 优化的词汇查询函数（缓存优先版本）
 * 优先返回缓存的API响应，避免重复转换
 */
export async function lookupWordWithRelationshipsOptimized(
  searchTerm: string,
  lang: string = 'en',
  options: LookupOptions = {}
): Promise<any | null> {
  const startTime = performance.now();
  const lookupId = Math.random().toString(36).substring(2, 10);

  try {
    const service = getDictionaryService();
    const normalizedTerm = searchTerm.trim().toLowerCase();

    // 🔍 DEBUG: 查询函数开始
    const { Logger } = await import('./utils/logger');
    Logger.debug(`[LOOKUP-${lookupId}] 🚀 查询函数开始`, {
      lookupId,
      searchTerm,
      normalizedTerm,
      lang,
      useCache: options.useCache !== false,
      cacheTTL: options.cacheTTL,
    });

    // 1. 优先检查缓存的API响应（带缓存来源跟踪）
    if (options.useCache !== false) {
      const cacheStart = performance.now();
      Logger.debug(`[LOOKUP-${lookupId}] 🎯 检查API缓存`, { lookupId });

      const cachedResult = await service.getCachedApiResponse(normalizedTerm);
      const cacheTime = performance.now() - cacheStart;

      if (cachedResult) {
        const totalTime = performance.now() - startTime;
        const { data: cachedApiResponse, cacheMetadata } = cachedResult;

        // 🔍 DEBUG: API缓存命中
        Logger.debug(`[LOOKUP-${lookupId}] ✅ API缓存命中`, {
          lookupId,
          cacheTime: Math.round(cacheTime),
          totalTime: Math.round(totalTime),
          cacheSource: cacheMetadata.source,
          cacheLayer: cacheMetadata.layer,
        });

        // 优化：直接修改对象，避免对象展开操作
        cachedApiResponse.queryMetadata.processingTimeMs = Math.round(totalTime);
        cachedApiResponse.queryMetadata.cacheHit = true;
        cachedApiResponse.queryMetadata.cacheSource = cacheMetadata.source;
        cachedApiResponse.queryMetadata.cacheHitTime = cacheMetadata.hitTime;
        cachedApiResponse.queryMetadata.cacheLayer = cacheMetadata.layer;

        return {
          ...cachedApiResponse,
          cacheMetadata, // 添加缓存元数据到响应中
        };
      } else {
        // 🔍 DEBUG: API缓存未命中
        Logger.debug(`[LOOKUP-${lookupId}] ❌ API缓存未命中`, {
          lookupId,
          cacheTime: Math.round(cacheTime),
        });
      }
    }

    // 2. 缓存未命中，执行正常查询
    const serviceStart = performance.now();
    Logger.debug(`[LOOKUP-${lookupId}] 🔍 执行服务查询`, { lookupId });

    const result = await service.lookupWord(searchTerm, options);
    const serviceTime = performance.now() - serviceStart;

    if (!result) {
      Logger.debug(`[LOOKUP-${lookupId}] ❌ 服务查询无结果`, {
        lookupId,
        serviceTime: Math.round(serviceTime),
      });
      return null;
    }

    // 3. 返回API响应格式
    const responseStart = performance.now();
    const apiResponse = result.toApiResponse();
    const responseTime = performance.now() - responseStart;
    const totalTime = performance.now() - startTime;

    // 🔍 DEBUG: 查询完成
    Logger.debug(`[LOOKUP-${lookupId}] 🎉 查询完成`, {
      lookupId,
      serviceTime: Math.round(serviceTime),
      responseTime: Math.round(responseTime),
      totalTime: Math.round(totalTime),
      found: !!result,
      strategy: result.queryMetadata?.searchStrategy,
    });

    return apiResponse;
  } catch (error) {
    const errorTime = performance.now() - startTime;

    // 🔍 DEBUG: 查询失败
    const { Logger } = await import('./utils/logger');
    Logger.debug(`[LOOKUP-${lookupId}] ❌ 查询失败`, {
      lookupId,
      errorTime: Math.round(errorTime),
      error: error instanceof Error ? error.message : String(error),
    });

    console.error('Lookup error:', error);
    return null;
  }
}

/**
 * 优化的词汇查询函数（新版本）
 * 使用分层架构，支持通过原型和派生形式查找
 */
export async function lookupWordWithRelationships(
  searchTerm: string,
  _lang: string = 'en',
  options: LookupOptions = {}
): Promise<LookupResult | null> {
  try {
    const service = getDictionaryService();
    const result = await service.lookupWord(searchTerm, options);

    if (!result) return null;

    // 转换为兼容格式
    return {
      vocabulary: result.vocabulary?.toApiResponse() || null,
      wordRelationships: result.wordRelationships,
      queryMetadata: result.queryMetadata,
      relatedSuggestions: result.relatedSuggestions,
    };
  } catch (error) {
    console.error('Lookup error:', error);
    return null;
  }
}

/**
 * 批量查询词汇
 */
export async function lookupMultipleWords(
  searchTerms: string[],
  options: LookupOptions = {}
): Promise<SearchResult[]> {
  const service = getDictionaryService();
  return service.lookupWords(searchTerms, options);
}

/**
 * 获取搜索建议
 */
export async function getSearchSuggestions(query: string, limit: number = 5): Promise<string[]> {
  const service = getDictionaryService();
  return service.getRelatedSuggestions(query, limit);
}

/**
 * 兼容性函数：保持原有接口
 */
export async function lookupWord(word: string, lang: string = 'en') {
  const result = await lookupWordWithRelationships(word, lang);
  return result?.vocabulary || null;
}
