/**
 * NextAuth.js Type Extensions
 * NextAuth.js 类型扩展
 */

import NextAuth from 'next-auth';
import { JWT } from 'next-auth/jwt';

declare module 'next-auth' {
  interface Session {
    /** 用户信息 */
    user: {
      /** 用户ID */
      id: string;
      /** 用户邮箱 */
      email: string;
      /** 用户名 */
      name?: string;
      /** 用户头像 */
      avatar?: string;
      /** 认证提供商 */
      provider?: string;
      /** 账户激活状态 */
      isActive: boolean;
      /** 最后登录时间 */
      lastLoginAt?: Date;
      /** 用户头像 */
      image?: string;
    };
    /** 访问令牌 */
    accessToken?: string;
    /** 刷新令牌 */
    refreshToken?: string;
    /** 令牌过期时间 */
    expires: string;
    /** 错误信息 */
    error?: string;
  }

  interface User {
    /** 用户ID */
    id: string;
    /** 用户邮箱 */
    email: string;
    /** 用户名 */
    name?: string;
    /** 用户头像 */
    avatar?: string;
    /** 用户头像 (NextAuth 标准) */
    image?: string;
    /** 认证提供商 */
    provider?: string;
    /** 提供商ID */
    providerId?: string;
    /** 密码哈希 */
    passwordHash?: string;
    /** 账户激活状态 */
    isActive?: boolean;
    /** 最后登录时间 */
    lastLoginAt?: Date;
    /** 登录IP */
    loginIP?: string;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
  }

  interface Account {
    /** 账户ID */
    id: string;
    /** 用户ID */
    userId?: string;
    /** 账户类型 */
    // type: string;
    /** 认证提供商 */
    provider: string;
    /** 提供商账户ID */
    providerAccountId: string;
    /** 刷新令牌 */
    refresh_token?: string;
    /** 访问令牌 */
    access_token?: string;
    /** 过期时间 */
    expires_at?: number;
    /** 令牌类型 */
    token_type?: string;
    /** 令牌范围 */
    scope?: string;
    /** ID令牌 */
    id_token?: string;
    /** 会话状态 */
    session_state?: string;
    /** 创建时间 */
    createdAt: Date;
    /** 更新时间 */
    updatedAt: Date;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    /** 用户ID */
    sub: string;
    /** 用户邮箱 */
    email: string;
    /** 用户名 */
    name?: string;
    /** 用户头像 */
    avatar?: string;
    /** 认证提供商 */
    provider?: string;
    /** 账户激活状态 */
    isActive?: boolean;
    /** 访问令牌 */
    accessToken?: string;
    /** 刷新令牌 */
    refreshToken?: string;
    /** 访问令牌过期时间 */
    accessTokenExpires?: number;
    /** 错误信息 */
    error?: string;
  }
}

export interface NextAuthConfig {
  /** 会话策略 */
  session: {
    strategy: 'jwt';
    maxAge: number;
    updateAge: number;
  };
  /** JWT配置 */
  jwt: {
    secret: string;
    maxAge: number;
  };
  /** 页面配置 */
  pages: {
    signIn: string;
    signUp: string;
    error: string;
    verifyRequest: string;
    newUser: string;
  };
  /** 回调函数 */
  callbacks: {
    jwt: (params: any) => Promise<JWT>;
    session: (params: any) => Promise<any>;
    signIn: (params: any) => Promise<boolean>;
    redirect: (params: any) => Promise<string>;
  };
  /** 事件处理 */
  events: {
    signIn: (message: any) => Promise<void>;
    signOut: (message: any) => Promise<void>;
    createUser: (message: any) => Promise<void>;
    updateUser: (message: any) => Promise<void>;
    linkAccount: (message: any) => Promise<void>;
    session: (message: any) => Promise<void>;
  };
}

export interface AuthProvider {
  /** 提供商ID */
  id: string;
  /** 提供商名称 */
  name: string;
  /** 提供商类型 */
  type: 'oauth' | 'email' | 'credentials';
  /** 客户端ID */
  clientId?: string;
  /** 客户端密钥 */
  clientSecret?: string;
  /** 认证URL */
  authorizationUrl?: string;
  /** 令牌URL */
  tokenUrl?: string;
  /** 用户信息URL */
  userInfoUrl?: string;
  /** 范围 */
  scope?: string;
  /** 配置参数 */
  options?: Record<string, any>;
}

export interface CredentialsConfig {
  /** 凭证配置 */
  credentials: {
    email: {
      label: string;
      type: string;
      placeholder?: string;
    };
    password: {
      label: string;
      type: string;
      placeholder?: string;
    };
  };
  /** 授权函数 */
  authorize: (credentials: any) => Promise<any>;
}
