# Story 2.1: 前端后端API集成重构

## Status

📝 **Draft** - 2025-07-30

## Story

**As a** 打字练习系统用户,
**I want** 前端应用能够与后端API服务无缝集成，获取动态数据和保存练习进度,
**so that** 我的练习数据能够持久化保存，获得个性化的练习体验，并享受完整的功能特性。

## Acceptance Criteria

1. 替换现有静态数据源，集成已实现的后端API服务（Story 1.1-1.4）
2. 实现API客户端层，支持用户认证和请求管理
3. 单词练习模块应集成生词本管理API（GET/POST/DELETE /api/word）
4. 单词练习模块应集成词库练习API（GET /api/practice/wordlists, /api/practice/wordlist/:listId）
5. 句子练习模块应集成句子收藏API（GET/POST/DELETE /api/practice/sentences/favorites）
6. 所有练习模式应支持练习结果提交（POST /api/practice/progress, /api/practice/session-result）
7. 实现用户认证状态管理，支持Session和JWT双认证策略
8. 添加loading状态、错误处理和网络异常处理
9. 保持现有UI/UX的同时，增强功能完整性
10. 所有API集成应包含适当的类型定义和错误边界处理

## Tasks / Subtasks

- [ ] API客户端基础设施建设 (AC: 1, 2, 7, 8)

  - [ ] 创建API客户端基础类，支持请求拦截和错误处理
  - [ ] 实现认证管理Hook（useAuth），支持Session+JWT双策略
  - [ ] 创建通用的API Hook模式（useApi），处理loading、error、data状态
  - [ ] 添加请求缓存策略和重试机制
  - [ ] 实现网络状态检测和离线提示功能

- [ ] 生词本功能API集成 (AC: 3, 6, 8, 9)

  - [ ] 重构WordPractice组件，集成生词本管理API
  - [ ] 实现useUserWords Hook，管理用户生词列表状态
  - [ ] 集成生词标记功能与POST /api/word API
  - [ ] 集成练习结果提交与POST /api/practice/session-result
  - [ ] 添加生词列表加载和错误处理UI状态

- [ ] 词库练习功能API集成 (AC: 4, 6, 8, 9)

  - [ ] 创建useWordLists Hook，获取可用词库列表
  - [ ] 创建useWordListWords Hook，获取特定词库单词
  - [ ] 重构TypingTest组件，支持词库选择功能
  - [ ] 集成词库练习进度记录API
  - [ ] 添加词库切换UI和加载状态处理

- [ ] 句子练习功能API集成 (AC: 5, 6, 8, 9)

  - [ ] 重构SentencePractice组件，集成动态句子获取
  - [ ] 实现useSentenceFavorites Hook，管理收藏句子
  - [ ] 集成句子收藏和取消收藏功能
  - [ ] 实现收藏句子专项练习模式
  - [ ] 添加句子加载和收藏状态的UI反馈

- [ ] 全局状态管理和用户体验优化 (AC: 7, 8, 9, 10)

  - [ ] 实现全局错误边界组件，处理API异常
  - [ ] 创建统一的loading组件和错误提示组件
  - [ ] 添加离线模式检测和提示
  - [ ] 实现练习数据本地缓存，支持离线恢复
  - [ ] 优化现有UI组件，添加数据加载状态

- [ ] 类型定义和测试 (AC: 1-10)
  - [ ] 创建完整的API响应类型定义
  - [ ] 为所有新的Hook和组件编写单元测试
  - [ ] 编写API集成测试，模拟各种网络状况
  - [ ] 测试认证流程和权限控制
  - [ ] 测试离线/在线状态切换

## Dev Notes

### Previous Story Insights

从Story 1.1-1.4和现有前端代码分析得到的关键信息：

**后端API已完成实现**：

- Story 1.1: UserWordProficiency数据模型和相关API
- Story 1.2: 生词本管理完整API（GET/POST/DELETE /api/word）
- Story 1.3: 词库练习API（wordlists, wordlist/:listId, progress）
- Story 1.4: 句子收藏API（sentences/favorites相关接口）

**现有前端架构优势**：

- 清晰的组件分层结构（TypingTest, WordPractice, SentencePractice）
- 优秀的useTypingEngine Hook设计，易于扩展
- 完整的字符级UI反馈系统
- 良好的TypeScript类型安全实践

**技术集成点**：

- 双认证策略：Session Cookie（Web）+ JWT Token（移动端）
- API前缀：`/api/practice/`, `/api/word`
- 现有静态数据需要替换为动态API调用

### API Specifications

需要集成的后端API端点：

**生词本管理API**：

- `GET /api/word` - 获取用户生词列表，支持分页和过滤
- `POST /api/word` - 批量添加单词到生词本
- `DELETE /api/word` - 批量从生词本移除单词
- Response格式：`{ words: Word[], totalCount: number, hasMore: boolean }`

**词库练习API**：

- `GET /api/practice/wordlists` - 获取所有可用词库列表
- `GET /api/practice/wordlist/:listId` - 获取特定词库单词，支持分页和随机抽取
- `POST /api/practice/progress` - 记录用户词库练习进度
- Response格式：包含词库信息、单词数据、用户熟练度状态

**句子练习API**：

- `GET /api/practice/sentences` - 获取练习句子（支持随机或收藏模式）
- `POST /api/practice/sentences/favorites` - 收藏句子
- `DELETE /api/practice/sentences/favorites/:sentenceId` - 取消收藏
- `GET /api/practice/sentences/favorites` - 获取用户收藏句子列表

**练习结果API**：

- `POST /api/practice/session-result` - 提交练习会话结果，更新用户熟练度
- `POST /api/practice/progress` - 更新特定练习的进度记录

[Source: docs/打字机功能/architecture/5-核心api接口设计-core-api-interface-design.md]

### Component Integration Strategy

**现有组件需要的API集成修改**：

**TypingTest组件**：

- 替换`generateRandomWords()`静态函数为API调用
- 集成词库选择功能（从wordlists API获取）
- 添加练习结果提交到progress API
- 保持现有UI和打字引擎逻辑不变

**WordPractice组件**：

- 替换硬编码的`practiceWords`数组为生词本API数据
- 集成生词标记功能与POST /api/word API
- 添加生词列表动态加载和刷新
- 保持现有的翻译卡片和标记UI

**SentencePractice组件**：

- 替换静态`sentences`数组为动态API获取
- 集成句子收藏功能按钮和状态
- 添加收藏句子专项练习模式切换
- 保持现有的自动切换和翻译显示

[Source: 现有前端代码分析结果]

### Data Models and Types

需要创建的TypeScript类型定义：

```typescript
// API响应基础类型
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// 生词本相关类型
interface UserWord {
  id: string;
  word: string;
  addedAt: string;
  practiceCount: number;
  proficiencyScore: number;
}

// 词库相关类型
interface WordList {
  id: string;
  name: string;
  description: string;
  difficulty: number;
  totalWords: number;
  userProgress?: {
    completedWords: number;
    averageAccuracy: number;
    lastPracticed: string;
  };
}

interface WordListEntry {
  word: string;
  wordListIds: string[];
  userProficiency?: {
    practiceCount: number;
    averageTime: number;
    errorCount: number;
    proficiencyScore: number;
  };
}

// 句子相关类型
interface Sentence {
  id: string;
  content: string;
  translation: string;
  difficulty: number;
  source?: string;
  isFavorited?: boolean;
}

// 练习结果类型
interface PracticeSession {
  mode: 'typing-test' | 'word-practice' | 'sentence-practice';
  duration: number;
  wpm: number;
  accuracy: number;
  errors: number;
  completedContent: string[];
  timestamp: string;
}
```

### File Locations

基于现有DDD架构和前端结构，新增文件位置：

**API客户端层**：

- `app/lib/api/client.ts` - API客户端基础类
- `app/lib/api/auth.ts` - 认证管理
- `app/lib/api/endpoints/` - API端点定义
  - `words.ts` - 生词本API
  - `wordlists.ts` - 词库API
  - `sentences.ts` - 句子API
  - `practice.ts` - 练习结果API

**React Hooks**：

- `app/hooks/api/` - API相关Hook
  - `useAuth.ts` - 认证状态管理
  - `useApi.ts` - 通用API Hook
  - `useUserWords.ts` - 生词本管理
  - `useWordLists.ts` - 词库管理
  - `useSentenceFavorites.ts` - 句子收藏管理

**组件修改**：

- `app/components/typer/TypingTest.tsx` - 集成词库选择
- `app/components/typer/WordPractice.tsx` - 集成生词本API
- `app/components/typer/SentencePractice.tsx` - 集成句子收藏API

**新增UI组件**：

- `app/components/ui/LoadingSpinner.tsx` - 加载状态
- `app/components/ui/ErrorBoundary.tsx` - 错误边界
- `app/components/ui/NetworkStatus.tsx` - 网络状态提示

**类型定义**：

- `app/types/api.ts` - API相关类型
- `app/types/practice.ts` - 练习相关类型

### User Authentication Implementation

基于Story 1.2和1.3的实现经验，前端需要支持双认证策略：

**认证策略**：

- **Web应用**: 自动使用Session Cookie认证
- **API请求**: 如果需要，支持JWT Bearer Token
- **状态管理**: React Context + LocalStorage持久化

**实现要点**：

```typescript
// useAuth Hook设计
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

// API请求自动认证
const apiClient = axios.create({
  baseURL: '/api',
  withCredentials: true, // 支持Session Cookie
});

// JWT Token支持（如果需要）
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});
```

[Source: Story 1.2和1.3的认证实现模式]

### Performance Requirements

**API响应时间目标**：

- 生词本列表查询: < 300ms
- 词库数据获取: < 500ms
- 句子获取: < 200ms
- 练习结果提交: < 400ms

**用户体验优化**：

- 实现乐观更新（生词标记、句子收藏）
- 添加请求缓存和预加载策略
- 离线模式下的基础功能支持
- 错误重试和网络恢复机制

**数据缓存策略**：

- 词库列表：长期缓存（1小时）
- 用户生词：短期缓存（5分钟）
- 句子数据：会话缓存
- 练习结果：立即提交，本地备份

### Technical Constraints

- 保持现有的Next.js 15 App Router架构
- 继续使用Tailwind CSS进行样式设计
- 维护现有的TypeScript严格类型检查
- 复用现有的useTypingEngine核心逻辑
- 兼容现有的组件设计和用户体验
- 支持双认证策略（Session + JWT）
- 遵循现有的错误处理和状态管理模式

[Source: CLAUDE.md#Technology Stack 和现有前端代码架构]

### Testing Requirements

**单元测试**：

- 为所有新的API Hook编写单元测试
- Mock API响应，测试各种网络状况
- 测试认证状态管理和权限控制
- 测试组件的API集成逻辑

**集成测试**：

- 测试完整的用户工作流（登录→练习→数据保存）
- 测试离线/在线状态切换
- 测试API错误处理和用户反馈
- 验证与后端API的完整集成

**测试文件位置**：

- `app/hooks/api/__tests__/` - API Hook测试
- `app/lib/api/__tests__/` - API客户端测试
- `app/components/typer/__tests__/` - 组件集成测试

**测试工具**：

- 使用Vitest框架进行单元测试
- 使用MSW (Mock Service Worker)模拟API
- React Testing Library测试组件交互
- 测试超时设置: 180000秒（5分钟）

[Source: CLAUDE.md#Testing Framework]

## Change Log

| Date       | Version | Description                                                 | Author             |
| ---------- | ------- | ----------------------------------------------------------- | ------------------ |
| 2025-07-30 | 1.0     | Initial story creation for frontend-backend API integration | Scrum Master (Bob) |

## Dev Agent Record

### Agent Model Used

[To be filled by Dev Agent]

### Debug Log References

[To be filled by Dev Agent]

### Completion Notes List

[To be filled by Dev Agent]

### File List

[To be filled by Dev Agent]

## QA Results

[To be filled by QA Agent]
