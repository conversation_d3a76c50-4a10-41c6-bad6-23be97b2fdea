name: E2E Authentication Tests

on:
  push:
    branches: [ main, develop, feature/auth-system-* ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每日午夜运行完整的E2E测试套件
    - cron: '0 0 * * *'

jobs:
  # 快速烟雾测试 - 在每次推送时运行
  smoke-tests:
    name: 烟雾测试 (Chrome)
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install chromium

    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}" >> .env
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
        echo "GOOGLE_CLIENT_ID=test" >> .env
        echo "GOOGLE_CLIENT_SECRET=test" >> .env
        echo "GITHUB_CLIENT_ID=test" >> .env
        echo "GITHUB_CLIENT_SECRET=test" >> .env

    - name: Build application
      run: npm run build

    - name: Run smoke tests
      run: npx playwright test e2e/auth/auth-setup.setup.ts e2e/auth/flows/basic-auth.spec.ts --project=chromium-desktop --reporter=github
      env:
        CI: true

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: smoke-test-results
        path: |
          e2e-results/
          test-results/
        retention-days: 7

  # 全面的认证测试
  auth-tests:
    name: 认证测试套件
    runs-on: ubuntu-latest
    timeout-minutes: 45
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        shard: [1/3, 2/3, 3/3]
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install

    - name: Setup test database
      run: |
        # 为每个分片创建独立的测试数据库
        echo "TEST_DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}_${{ matrix.browser }}_${{ strategy.job-index }}" >> .env

    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=${{ env.TEST_DATABASE_URL }}" >> .env
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
        echo "GOOGLE_CLIENT_ID=test" >> .env
        echo "GOOGLE_CLIENT_SECRET=test" >> .env
        echo "GITHUB_CLIENT_ID=test" >> .env
        echo "GITHUB_CLIENT_SECRET=test" >> .env

    - name: Run database migrations
      run: npx prisma migrate deploy

    - name: Build application
      run: npm run build

    - name: Run authentication tests
      run: npx playwright test e2e/auth/ --project=${{ matrix.browser }}-desktop --shard=${{ matrix.shard }} --reporter=github
      env:
        CI: true

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: auth-test-results-${{ matrix.browser }}-${{ strategy.job-index }}
        path: |
          e2e-results/
          test-results/
        retention-days: 7

  # 安全测试
  security-tests:
    name: 安全测试套件
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install chromium

    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}" >> .env
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env

    - name: Build application
      run: npm run build

    - name: Run security tests
      run: npx playwright test e2e/security/ --project=chromium-desktop --reporter=github
      env:
        CI: true

    - name: Upload security test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-test-results
        path: |
          e2e-results/
          test-results/
        retention-days: 30

  # 性能测试
  performance-tests:
    name: 性能基准测试
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install chromium

    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}" >> .env
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env

    - name: Build application
      run: npm run build

    - name: Run performance tests
      run: npx playwright test e2e/performance/ --project=chromium-desktop --reporter=github
      env:
        CI: true

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: performance-test-results
        path: |
          e2e-results/
          test-results/
        retention-days: 30

  # 视觉回归测试
  visual-tests:
    name: 视觉回归测试
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install

    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}" >> .env
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env

    - name: Build application
      run: npm run build

    - name: Run visual regression tests
      run: npx playwright test e2e/visual/ --reporter=github
      env:
        CI: true

    - name: Upload visual test results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: visual-test-results
        path: |
          e2e-results/
          test-results/
        retention-days: 7

  # 完整用户旅程测试 (每日运行)
  user-journey-tests:
    name: 用户旅程测试
    runs-on: ubuntu-latest
    timeout-minutes: 60
    if: github.event_name == 'schedule' || contains(github.event.head_commit.message, '[full-test]')
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install

    - name: Setup test environment
      run: |
        cp .env.example .env
        echo "DATABASE_URL=${{ secrets.TEST_DATABASE_URL }}" >> .env
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env
        echo "GOOGLE_CLIENT_ID=test" >> .env
        echo "GOOGLE_CLIENT_SECRET=test" >> .env
        echo "GITHUB_CLIENT_ID=test" >> .env
        echo "GITHUB_CLIENT_SECRET=test" >> .env

    - name: Build application
      run: npm run build

    - name: Run user journey tests
      run: npx playwright test e2e/user-journey/ --reporter=github
      env:
        CI: true

    - name: Upload journey test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: user-journey-test-results
        path: |
          e2e-results/
          test-results/
        retention-days: 30

  # 测试报告汇总
  test-report:
    name: 生成测试报告
    runs-on: ubuntu-latest
    needs: [smoke-tests, auth-tests, security-tests, performance-tests, visual-tests]
    if: always()
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Download all test results
      uses: actions/download-artifact@v4
      with:
        pattern: '*test-results*'
        path: test-results/

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install Playwright
      run: npm install @playwright/test

    - name: Generate consolidated report
      run: |
        npx playwright merge-reports --reporter=html test-results/*/e2e-results/
        
    - name: Upload consolidated report
      uses: actions/upload-artifact@v4
      with:
        name: consolidated-test-report
        path: playwright-report/
        retention-days: 30

    - name: Deploy test report to GitHub Pages
      if: github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./playwright-report
        destination_dir: e2e-reports/${{ github.run_number }}

  # 通知测试结果
  notify:
    name: 通知测试结果
    runs-on: ubuntu-latest
    needs: [smoke-tests, auth-tests, security-tests, performance-tests, visual-tests]
    if: failure() && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    steps:
    - name: Send notification
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: '🚨 E2E认证测试失败'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}