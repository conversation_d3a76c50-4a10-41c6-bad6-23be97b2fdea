/**
 * SentenceRepository
 * 
 * Data access layer for sentence operations.
 * Handles CRUD operations and complex queries for sentences.
 */

import { prisma } from '@/lib/4-infrastructure/database/prisma';
import type {
  Sentence,
  CreateSentenceInput,
  UpdateSentenceInput,
  SentenceWithUserContext
} from '@/lib/3-domain/entities/Sentence';

export interface SentenceQueryOptions {
  difficulty?: number | number[];
  category?: string | string[];
  isActive?: boolean;
  minLength?: number;
  maxLength?: number;
  search?: string;
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'difficulty' | 'favoriteCount' | 'length' | 'random';
  sortOrder?: 'asc' | 'desc';
  excludeIds?: number[];
}

export interface SentenceWithContextOptions extends SentenceQueryOptions {
  userId?: string; // To include user context (saved status, practice stats)
}

export class SentenceRepository {
  /**
   * Calculate difficulty based on sentence content
   */
  private calculateDifficulty(content: string): number {
    const words = content.split(/\s+/).filter(word => word.length > 0);
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    const sentenceLength = content.length;

    // Base difficulty on word length and sentence length
    let difficulty = 1;

    // Word complexity
    if (avgWordLength > 6) difficulty += 2;
    else if (avgWordLength > 4) difficulty += 1;

    // Sentence length
    if (sentenceLength > 80) difficulty += 2;
    else if (sentenceLength > 40) difficulty += 1;

    // Complex punctuation
    const complexPunctuation = /[;:,\(\)\[\]{}]/g;
    const punctuationCount = (content.match(complexPunctuation) || []).length;
    if (punctuationCount > 3) difficulty += 1;

    return Math.min(Math.max(difficulty, 1), 10); // Clamp between 1-10
  }

  /**
   * Create a new sentence
   */
  async create(input: CreateSentenceInput): Promise<Sentence> {
    const data = {
      content: input.content,
      source: input.source,
      difficulty: input.difficulty ?? this.calculateDifficulty(input.content),
      category: input.category,
      length: input.length ?? input.content.length,
      isActive: input.isActive ?? true,
    };

    const sentence = await prisma.sentence.create({
      data
    });

    return this.mapPrismaToSentence(sentence);
  }

  /**
   * Find sentence by ID
   */
  async findById(id: number): Promise<Sentence | null> {
    const sentence = await prisma.sentence.findUnique({
      where: { id }
    });

    return sentence ? this.mapPrismaToSentence(sentence) : null;
  }

  /**
   * Find sentence by ID with user context
   */
  async findByIdWithUserContext(id: number, userId: string): Promise<SentenceWithUserContext | null> {
    const sentence = await prisma.sentence.findUnique({
      where: { id },
      include: {
        savedByUsers: {
          where: { userId },
          select: {
            practiceCount: true,
            lastPracticedAt: true
          }
        }
      }
    });

    if (!sentence) return null;

    const userSavedData = sentence.savedByUsers[0];

    return {
      ...this.mapPrismaToSentence(sentence),
      isSavedByUser: !!userSavedData,
      userPracticeCount: userSavedData?.practiceCount,
      userLastPracticedAt: userSavedData?.lastPracticedAt || undefined
    };
  }

  /**
   * Find multiple sentences with query options
   */
  async findMany(options: SentenceQueryOptions = {}): Promise<Sentence[]> {
    const {
      difficulty,
      category,
      isActive = true,
      minLength,
      maxLength,
      search,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      excludeIds = []
    } = options;

    const where: any = {
      isActive,
      ...(excludeIds.length > 0 && { id: { notIn: excludeIds } }),
      ...(difficulty && {
        difficulty: Array.isArray(difficulty)
          ? { in: difficulty }
          : difficulty
      }),
      ...(category && {
        category: Array.isArray(category)
          ? { in: category }
          : category
      }),
      ...(minLength && { length: { gte: minLength } }),
      ...(maxLength && { length: { lte: maxLength } }),
      ...(search && {
        content: {
          contains: search,
          mode: 'insensitive'
        }
      })
    };

    let orderBy: any = {};
    if (sortBy === 'random') {
      // For random sorting, we'll use a different approach
      const totalCount = await prisma.sentence.count({ where });
      const skip = Math.floor(Math.random() * Math.max(0, totalCount - limit));

      const sentences = await prisma.sentence.findMany({
        where,
        skip,
        take: limit
      });

      // Shuffle the results
      return sentences
        .sort(() => Math.random() - 0.5)
        .map(this.mapPrismaToSentence);
    } else {
      orderBy[sortBy] = sortOrder;
    }

    const sentences = await prisma.sentence.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit
    });

    return sentences.map(this.mapPrismaToSentence);
  }

  /**
   * Find sentences with user context
   */
  async findManyWithUserContext(
    options: SentenceWithContextOptions = {}
  ): Promise<SentenceWithUserContext[]> {
    const { userId, ...queryOptions } = options;

    if (!userId) {
      const sentences = await this.findMany(queryOptions);
      return sentences.map(sentence => ({
        ...sentence,
        isSavedByUser: false
      }));
    }

    const {
      difficulty,
      category,
      isActive = true,
      minLength,
      maxLength,
      search,
      page = 1,
      limit = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc',
      excludeIds = []
    } = queryOptions;

    const where: any = {
      isActive,
      ...(excludeIds.length > 0 && { id: { notIn: excludeIds } }),
      ...(difficulty && {
        difficulty: Array.isArray(difficulty)
          ? { in: difficulty }
          : difficulty
      }),
      ...(category && {
        category: Array.isArray(category)
          ? { in: category }
          : category
      }),
      ...(minLength && { length: { gte: minLength } }),
      ...(maxLength && { length: { lte: maxLength } }),
      ...(search && {
        content: {
          contains: search,
          mode: 'insensitive'
        }
      })
    };

    let orderBy: any = {};
    if (sortBy !== 'random') {
      orderBy[sortBy] = sortOrder;
    }

    const sentences = await prisma.sentence.findMany({
      where,
      ...(sortBy !== 'random' && { orderBy }),
      skip: sortBy === 'random' ? 0 : (page - 1) * limit,
      take: sortBy === 'random' ? limit * 3 : limit, // Get more for random sampling
      include: {
        savedByUsers: {
          where: { userId },
          select: {
            practiceCount: true,
            lastPracticedAt: true
          }
        }
      }
    });

    let results = sentences.map(sentence => {
      const userSavedData = sentence.savedByUsers[0];
      return {
        ...this.mapPrismaToSentence(sentence),
        isSavedByUser: !!userSavedData,
        userPracticeCount: userSavedData?.practiceCount,
        userLastPracticedAt: userSavedData?.lastPracticedAt || undefined
      };
    });

    // Handle random sorting
    if (sortBy === 'random') {
      results = results
        .sort(() => Math.random() - 0.5)
        .slice(0, limit);
    }

    return results;
  }

  /**
   * Get a random sentence for practice
   */
  async getRandomSentence(options: SentenceWithContextOptions = {}): Promise<SentenceWithUserContext | null> {
    const sentences = await this.findManyWithUserContext({
      ...options,
      sortBy: 'random',
      limit: 1
    });

    return sentences[0] || null;
  }

  /**
   * Update sentence
   */
  async update(id: number, input: UpdateSentenceInput): Promise<Sentence | null> {
    try {
      const sentence = await prisma.sentence.update({
        where: { id },
        data: input
      });

      return this.mapPrismaToSentence(sentence);
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Delete sentence
   */
  async delete(id: number): Promise<boolean> {
    try {
      await prisma.sentence.delete({
        where: { id }
      });
      return true;
    } catch (error: any) {
      if (error.code === 'P2025') {
        return false; // Record not found
      }
      throw error;
    }
  }

  /**
   * Increment favorite count
   */
  async incrementFavoriteCount(id: number): Promise<void> {
    await prisma.sentence.update({
      where: { id },
      data: {
        favoriteCount: {
          increment: 1
        }
      }
    });
  }

  /**
   * Decrement favorite count
   */
  async decrementFavoriteCount(id: number): Promise<void> {
    await prisma.sentence.update({
      where: { id },
      data: {
        favoriteCount: {
          decrement: 1
        }
      }
    });
  }

  /**
   * Get sentences count
   */
  async count(options: Omit<SentenceQueryOptions, 'page' | 'limit' | 'sortBy' | 'sortOrder'> = {}): Promise<number> {
    const {
      difficulty,
      category,
      isActive = true,
      minLength,
      maxLength,
      search,
      excludeIds = []
    } = options;

    const where: any = {
      isActive,
      ...(excludeIds.length > 0 && { id: { notIn: excludeIds } }),
      ...(difficulty && {
        difficulty: Array.isArray(difficulty)
          ? { in: difficulty }
          : difficulty
      }),
      ...(category && {
        category: Array.isArray(category)
          ? { in: category }
          : category
      }),
      ...(minLength && { length: { gte: minLength } }),
      ...(maxLength && { length: { lte: maxLength } }),
      ...(search && {
        content: {
          contains: search,
          mode: 'insensitive'
        }
      })
    };

    return prisma.sentence.count({ where });
  }

  /**
   * Get sentence statistics
   */
  async getStatistics(): Promise<{
    totalSentences: number;
    activeSentences: number;
    averageDifficulty: number;
    averageLength: number;
    categoryDistribution: { [category: string]: number };
    difficultyDistribution: { [difficulty: number]: number };
  }> {
    const [
      totalSentences,
      activeSentences,
      averageStats,
      categoryStats,
      difficultyStats
    ] = await Promise.all([
      prisma.sentence.count(),
      prisma.sentence.count({ where: { isActive: true } }),
      prisma.sentence.aggregate({
        where: { isActive: true },
        _avg: {
          difficulty: true,
          length: true
        }
      }),
      prisma.sentence.groupBy({
        by: ['category'],
        where: { isActive: true },
        _count: true
      }),
      prisma.sentence.groupBy({
        by: ['difficulty'],
        where: { isActive: true },
        _count: true
      })
    ]);

    const categoryDistribution: { [category: string]: number } = {};
    categoryStats.forEach(stat => {
      if (stat.category) {
        categoryDistribution[stat.category] = stat._count;
      }
    });

    const difficultyDistribution: { [difficulty: number]: number } = {};
    difficultyStats.forEach(stat => {
      difficultyDistribution[stat.difficulty] = stat._count;
    });

    return {
      totalSentences,
      activeSentences,
      averageDifficulty: averageStats._avg.difficulty || 0,
      averageLength: averageStats._avg.length || 0,
      categoryDistribution,
      difficultyDistribution
    };
  }

  /**
   * Map Prisma result to domain entity
   */
  private mapPrismaToSentence(sentence: any): Sentence {
    return {
      id: sentence.id,
      content: sentence.content,
      source: sentence.source,
      difficulty: sentence.difficulty,
      category: sentence.category,
      favoriteCount: sentence.favoriteCount,
      length: sentence.length,
      isActive: sentence.isActive,
      createdAt: sentence.createdAt,
      updatedAt: sentence.updatedAt
    };
  }
}