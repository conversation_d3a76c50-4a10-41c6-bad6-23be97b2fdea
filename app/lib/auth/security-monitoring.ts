import { EventEmitter } from 'events';
import { createHash } from 'crypto';

// 安全事件类型
export enum SecurityEventType {
  AUTHENTICATION_FAILURE = 'auth_failure',
  AUTHORIZATION_FAILURE = 'authz_failure',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  MALICIOUS_REQUEST = 'malicious_request',
  TOKEN_VALIDATION_FAILURE = 'token_validation_failure',
  BRUTE_FORCE_ATTACK = 'brute_force_attack',
  ACCOUNT_LOCKOUT = 'account_lockout',
  PRIVILEGE_ESCALATION = 'privilege_escalation',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  SYSTEM_COMPROMISE = 'system_compromise',
  INSIDER_THREAT = 'insider_threat',
}

// 安全事件严重程度
export enum SecuritySeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// 安全事件状态
export enum SecurityEventStatus {
  OPEN = 'open',
  INVESTIGATING = 'investigating',
  RESOLVED = 'resolved',
  FALSE_POSITIVE = 'false_positive',
}

// 安全事件接口
export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecuritySeverity;
  status: SecurityEventStatus;
  timestamp: number;
  title: string;
  description: string;
  source: {
    ip: string;
    userAgent?: string;
    userId?: string;
    sessionId?: string;
    endpoint?: string;
    method?: string;
  };
  evidence: {
    requestData?: any;
    responseData?: any;
    headers?: Record<string, string>;
    stackTrace?: string;
    additionalInfo?: any;
  };
  metadata: {
    attackVector?: string;
    riskScore: number;
    confidence: number;
    tags: string[];
    correlationId?: string;
  };
  mitigation: {
    automaticActions: string[];
    recommendations: string[];
    blocked: boolean;
  };
}

// 告警规则
export interface AlertRule {
  id: string;
  name: string;
  description: string;
  eventTypes: SecurityEventType[];
  severity: SecuritySeverity[];
  conditions: {
    threshold: number;
    timeWindow: number; // 分钟
    aggregation: 'count' | 'rate' | 'unique';
    groupBy?: string[];
  };
  enabled: boolean;
  actions: AlertAction[];
}

// 告警动作
export interface AlertAction {
  type: 'email' | 'webhook' | 'slack' | 'sms' | 'block_ip' | 'disable_user';
  config: {
    recipients?: string[];
    url?: string;
    message?: string;
    template?: string;
  };
  enabled: boolean;
}

// 告警
export interface Alert {
  id: string;
  ruleId: string;
  ruleName: string;
  severity: SecuritySeverity;
  timestamp: number;
  title: string;
  description: string;
  events: SecurityEvent[];
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: number;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: number;
  metadata: {
    eventCount: number;
    timeWindow: number;
    riskScore: number;
    affectedUsers: string[];
    affectedIPs: string[];
  };
}

// 监控配置
export interface MonitoringConfig {
  enabled: boolean;
  realTimeAlerts: boolean;
  batchProcessing: boolean;
  batchInterval: number; // 分钟
  retention: {
    events: number; // 天
    alerts: number; // 天
  };
  storage: {
    type: 'memory' | 'file' | 'database';
    config: any;
  };
  thresholds: {
    maxEventsPerMinute: number;
    maxAlertsPerHour: number;
    riskScoreThreshold: number;
  };
}

// 安全监控管理器
export class SecurityMonitoringManager extends EventEmitter {
  private config: MonitoringConfig;
  private events: Map<string, SecurityEvent> = new Map();
  private alerts: Map<string, Alert> = new Map();
  private alertRules: Map<string, AlertRule> = new Map();
  private eventBuffer: SecurityEvent[] = [];
  private metrics = {
    totalEvents: 0,
    totalAlerts: 0,
    eventsPerType: new Map<SecurityEventType, number>(),
    alertsPerSeverity: new Map<SecuritySeverity, number>(),
    falsePositives: 0,
    responseTime: 0,
  };
  private correlationWindow = new Map<string, SecurityEvent[]>();
  private ipReputation = new Map<
    string,
    {
      riskScore: number;
      lastSeen: number;
      eventCount: number;
    }
  >();

  constructor(config: MonitoringConfig) {
    super();
    this.config = config;
    this.initializeDefaultRules();
    this.startBatchProcessor();
    this.startCleanupTimer();
  }

  // 记录安全事件
  async recordSecurityEvent(
    event: Omit<SecurityEvent, 'id' | 'timestamp'>
  ): Promise<SecurityEvent> {
    const fullEvent: SecurityEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: Date.now(),
    };

    // 更新指标
    this.metrics.totalEvents++;
    this.metrics.eventsPerType.set(
      event.type,
      (this.metrics.eventsPerType.get(event.type) || 0) + 1
    );

    // 更新IP信誉
    this.updateIPReputation(fullEvent.source.ip, fullEvent.metadata.riskScore);

    // 事件关联
    await this.correlateEvent(fullEvent);

    // 存储事件
    this.events.set(fullEvent.id, fullEvent);

    // 实时告警检查
    if (this.config.realTimeAlerts) {
      await this.checkAlertRules(fullEvent);
    } else {
      this.eventBuffer.push(fullEvent);
    }

    // 发出事件
    this.emit('securityEvent', fullEvent);

    return fullEvent;
  }

  // 创建告警规则
  createAlertRule(rule: Omit<AlertRule, 'id'>): AlertRule {
    const fullRule: AlertRule = {
      ...rule,
      id: this.generateRuleId(),
    };

    this.alertRules.set(fullRule.id, fullRule);
    return fullRule;
  }

  // 更新告警规则
  updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
    const rule = this.alertRules.get(ruleId);
    if (!rule) return false;

    const updatedRule = { ...rule, ...updates };
    this.alertRules.set(ruleId, updatedRule);
    return true;
  }

  // 删除告警规则
  deleteAlertRule(ruleId: string): boolean {
    return this.alertRules.delete(ruleId);
  }

  // 获取告警规则
  getAlertRule(ruleId: string): AlertRule | undefined {
    return this.alertRules.get(ruleId);
  }

  // 获取所有告警规则
  getAllAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values());
  }

  // 检查告警规则
  private async checkAlertRules(event: SecurityEvent): Promise<void> {
    const applicableRules = Array.from(this.alertRules.values()).filter(
      (rule) => rule.enabled && rule.eventTypes.includes(event.type)
    );

    for (const rule of applicableRules) {
      await this.evaluateRule(rule, event);
    }
  }

  // 评估告警规则
  private async evaluateRule(rule: AlertRule, triggerEvent: SecurityEvent): Promise<void> {
    const timeWindow = rule.conditions.timeWindow * 60 * 1000; // 转换为毫秒
    const windowStart = Date.now() - timeWindow;

    // 获取时间窗口内的相关事件
    const relevantEvents = Array.from(this.events.values()).filter(
      (event) =>
        event.timestamp >= windowStart &&
        rule.eventTypes.includes(event.type) &&
        rule.severity.includes(event.severity)
    );

    let shouldAlert = false;
    let aggregatedValue = 0;

    switch (rule.conditions.aggregation) {
      case 'count':
        aggregatedValue = relevantEvents.length;
        break;
      case 'rate':
        aggregatedValue = relevantEvents.length / (timeWindow / 60000); // 每分钟事件数
        break;
      case 'unique':
        const uniqueIPs = new Set(relevantEvents.map((e) => e.source.ip));
        aggregatedValue = uniqueIPs.size;
        break;
    }

    shouldAlert = aggregatedValue >= rule.conditions.threshold;

    if (shouldAlert) {
      await this.createAlert(rule, relevantEvents);
    }
  }

  // 创建告警
  private async createAlert(rule: AlertRule, events: SecurityEvent[]): Promise<void> {
    const alert: Alert = {
      id: this.generateAlertId(),
      ruleId: rule.id,
      ruleName: rule.name,
      severity: events.reduce(
        (max, event) =>
          this.getSeverityLevel(event.severity) > this.getSeverityLevel(max) ? event.severity : max,
        SecuritySeverity.LOW as any
      ),
      timestamp: Date.now(),
      title: `告警: ${rule.name}`,
      description: `检测到 ${events.length} 个相关安全事件`,
      events,
      acknowledged: false,
      resolved: false,
      metadata: {
        eventCount: events.length,
        timeWindow: rule.conditions.timeWindow,
        riskScore: Math.max(...events.map((e) => e.metadata.riskScore)),
        affectedUsers: [...new Set(events.map((e) => e.source.userId).filter(Boolean))] as string[],
        affectedIPs: [...new Set(events.map((e) => e.source.ip))],
      },
    };

    // 存储告警
    this.alerts.set(alert.id, alert);

    // 更新指标
    this.metrics.totalAlerts++;
    this.metrics.alertsPerSeverity.set(
      alert.severity,
      (this.metrics.alertsPerSeverity.get(alert.severity) || 0) + 1
    );

    // 执行告警动作
    await this.executeAlertActions(rule, alert);

    // 发出告警事件
    this.emit('securityAlert', alert);
  }

  // 执行告警动作
  private async executeAlertActions(rule: AlertRule, alert: Alert): Promise<void> {
    for (const action of rule.actions.filter((a) => a.enabled)) {
      try {
        await this.executeAction(action, alert);
      } catch (error) {
        console.error(`执行告警动作失败: ${action.type}`, error);
      }
    }
  }

  // 执行具体动作
  private async executeAction(action: AlertAction, alert: Alert): Promise<void> {
    switch (action.type) {
      case 'email':
        await this.sendEmailAlert(action, alert);
        break;
      case 'webhook':
        await this.sendWebhookAlert(action, alert);
        break;
      case 'slack':
        await this.sendSlackAlert(action, alert);
        break;
      case 'sms':
        await this.sendSMSAlert(action, alert);
        break;
      case 'block_ip':
        await this.blockIPs(alert.metadata.affectedIPs);
        break;
      case 'disable_user':
        await this.disableUsers(alert.metadata.affectedUsers);
        break;
    }
  }

  // 发送邮件告警
  private async sendEmailAlert(action: AlertAction, alert: Alert): Promise<void> {
    const emailContent = this.formatAlertMessage(alert, action.config.template);
    console.log(`发送邮件告警到: ${action.config.recipients?.join(', ')}`);
    console.log(`内容: ${emailContent}`);
    // 这里实现实际的邮件发送逻辑
  }

  // 发送Webhook告警
  private async sendWebhookAlert(action: AlertAction, alert: Alert): Promise<void> {
    const payload = {
      alert,
      timestamp: Date.now(),
      source: 'security-monitoring',
    };

    if (action.config.url) {
      try {
        await fetch(action.config.url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(payload),
        });
      } catch (error) {
        console.error('Webhook发送失败:', error);
      }
    }
  }

  // 发送Slack告警
  private async sendSlackAlert(action: AlertAction, alert: Alert): Promise<void> {
    const message = this.formatAlertMessage(alert, action.config.template);
    console.log(`发送Slack告警: ${message}`);
    // 这里实现实际的Slack发送逻辑
  }

  // 发送SMS告警
  private async sendSMSAlert(action: AlertAction, alert: Alert): Promise<void> {
    const message = this.formatAlertMessage(alert, action.config.template);
    console.log(`发送SMS告警到: ${action.config.recipients?.join(', ')}`);
    console.log(`内容: ${message}`);
    // 这里实现实际的SMS发送逻辑
  }

  // 封锁IP
  private async blockIPs(ips: string[]): Promise<void> {
    console.log(`封锁IP: ${ips.join(', ')}`);
    // 这里实现实际的IP封锁逻辑
  }

  // 禁用用户
  private async disableUsers(userIds: string[]): Promise<void> {
    console.log(`禁用用户: ${userIds.join(', ')}`);
    // 这里实现实际的用户禁用逻辑
  }

  // 格式化告警消息
  private formatAlertMessage(alert: Alert, template?: string): string {
    const defaultTemplate = `
🚨 安全告警 - ${alert.severity.toUpperCase()}

规则: ${alert.ruleName}
时间: ${new Date(alert.timestamp).toLocaleString()}
事件数量: ${alert.metadata.eventCount}
风险评分: ${alert.metadata.riskScore}

描述: ${alert.description}

受影响的IP: ${alert.metadata.affectedIPs.join(', ')}
受影响的用户: ${alert.metadata.affectedUsers.join(', ')}

告警ID: ${alert.id}
    `.trim();

    return template || defaultTemplate;
  }

  // 事件关联
  private async correlateEvent(event: SecurityEvent): Promise<void> {
    const correlationKey = `${event.source.ip}:${event.type}`;
    const windowMs = 5 * 60 * 1000; // 5分钟窗口
    const now = Date.now();

    // 清理过期的关联窗口
    const expiredKeys = Array.from(this.correlationWindow.keys()).filter((key) => {
      const events = this.correlationWindow.get(key) || [];
      return events.length === 0 || now - events[0].timestamp > windowMs;
    });
    expiredKeys.forEach((key) => this.correlationWindow.delete(key));

    // 添加事件到关联窗口
    if (!this.correlationWindow.has(correlationKey)) {
      this.correlationWindow.set(correlationKey, []);
    }
    this.correlationWindow.get(correlationKey)!.push(event);

    // 生成关联ID
    const correlatedEvents = this.correlationWindow.get(correlationKey)!;
    if (correlatedEvents.length > 1) {
      const correlationId = this.generateCorrelationId(correlationKey);
      correlatedEvents.forEach((e) => {
        e.metadata.correlationId = correlationId;
      });
    }
  }

  // 更新IP信誉
  private updateIPReputation(ip: string, riskScore: number): void {
    const reputation = this.ipReputation.get(ip) || {
      riskScore: 0,
      lastSeen: Date.now(),
      eventCount: 0,
    };

    reputation.riskScore = Math.max(reputation.riskScore, riskScore);
    reputation.lastSeen = Date.now();
    reputation.eventCount++;

    this.ipReputation.set(ip, reputation);
  }

  // 确认告警
  acknowledgeAlert(alertId: string, acknowledgedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert) return false;

    alert.acknowledged = true;
    alert.acknowledgedBy = acknowledgedBy;
    alert.acknowledgedAt = Date.now();

    this.emit('alertAcknowledged', alert);
    return true;
  }

  // 解决告警
  resolveAlert(alertId: string, resolvedBy: string): boolean {
    const alert = this.alerts.get(alertId);
    if (!alert) return false;

    alert.resolved = true;
    alert.resolvedBy = resolvedBy;
    alert.resolvedAt = Date.now();

    this.emit('alertResolved', alert);
    return true;
  }

  // 获取告警
  getAlert(alertId: string): Alert | undefined {
    return this.alerts.get(alertId);
  }

  // 获取所有告警
  getAllAlerts(): Alert[] {
    return Array.from(this.alerts.values());
  }

  // 获取未解决的告警
  getUnresolvedAlerts(): Alert[] {
    return Array.from(this.alerts.values()).filter((alert) => !alert.resolved);
  }

  // 获取安全事件
  getSecurityEvent(eventId: string): SecurityEvent | undefined {
    return this.events.get(eventId);
  }

  // 获取所有安全事件
  getAllSecurityEvents(): SecurityEvent[] {
    return Array.from(this.events.values());
  }

  // 获取监控指标
  getMetrics() {
    return {
      ...this.metrics,
      eventsPerType: Object.fromEntries(this.metrics.eventsPerType),
      alertsPerSeverity: Object.fromEntries(this.metrics.alertsPerSeverity),
      activeAlerts: this.getUnresolvedAlerts().length,
      ipReputationEntries: this.ipReputation.size,
      correlationWindowSize: this.correlationWindow.size,
    };
  }

  // 获取IP信誉
  getIPReputation(ip: string) {
    return this.ipReputation.get(ip) || null;
  }

  // 批量处理器
  private startBatchProcessor(): void {
    if (!this.config.batchProcessing) return;

    setInterval(
      async () => {
        if (this.eventBuffer.length === 0) return;

        const eventsToProcess = this.eventBuffer.splice(0);

        for (const event of eventsToProcess) {
          await this.checkAlertRules(event);
        }
      },
      this.config.batchInterval * 60 * 1000
    );
  }

  // 清理定时器
  private startCleanupTimer(): void {
    setInterval(
      () => {
        const now = Date.now();

        // 清理过期事件
        const eventRetentionMs = this.config.retention.events * 24 * 60 * 60 * 1000;
        const expiredEventIds = Array.from(this.events.keys()).filter((id) => {
          const event = this.events.get(id)!;
          return now - event.timestamp > eventRetentionMs;
        });
        expiredEventIds.forEach((id) => this.events.delete(id));

        // 清理过期告警
        const alertRetentionMs = this.config.retention.alerts * 24 * 60 * 60 * 1000;
        const expiredAlertIds = Array.from(this.alerts.keys()).filter((id) => {
          const alert = this.alerts.get(id)!;
          return now - alert.timestamp > alertRetentionMs;
        });
        expiredAlertIds.forEach((id) => this.alerts.delete(id));

        // 清理过期IP信誉
        const ipRetentionMs = 30 * 24 * 60 * 60 * 1000; // 30天
        const expiredIPs = Array.from(this.ipReputation.keys()).filter((ip) => {
          const reputation = this.ipReputation.get(ip)!;
          return now - reputation.lastSeen > ipRetentionMs;
        });
        expiredIPs.forEach((ip) => this.ipReputation.delete(ip));
      },
      60 * 60 * 1000
    ); // 每小时清理一次
  }

  // 初始化默认规则
  private initializeDefaultRules(): void {
    const defaultRules: Omit<AlertRule, 'id'>[] = [
      {
        name: '暴力破解攻击检测',
        description: '检测短时间内多次认证失败',
        eventTypes: [SecurityEventType.AUTHENTICATION_FAILURE],
        severity: [SecuritySeverity.HIGH],
        conditions: {
          threshold: 5,
          timeWindow: 5,
          aggregation: 'count',
        },
        enabled: true,
        actions: [
          {
            type: 'block_ip',
            config: {},
            enabled: true,
          },
          {
            type: 'email',
            config: {
              recipients: ['<EMAIL>'],
              template: '检测到暴力破解攻击尝试',
            },
            enabled: true,
          },
        ],
      },
      {
        name: '恶意请求检测',
        description: '检测高风险的恶意请求',
        eventTypes: [SecurityEventType.MALICIOUS_REQUEST],
        severity: [SecuritySeverity.HIGH, SecuritySeverity.CRITICAL],
        conditions: {
          threshold: 3,
          timeWindow: 1,
          aggregation: 'count',
        },
        enabled: true,
        actions: [
          {
            type: 'webhook',
            config: {
              url: 'https://api.example.com/security/alert',
            },
            enabled: true,
          },
        ],
      },
      {
        name: '速率限制超出',
        description: '检测频繁的速率限制违规',
        eventTypes: [SecurityEventType.RATE_LIMIT_EXCEEDED],
        severity: [SecuritySeverity.MEDIUM, SecuritySeverity.HIGH],
        conditions: {
          threshold: 10,
          timeWindow: 10,
          aggregation: 'count',
        },
        enabled: true,
        actions: [
          {
            type: 'email',
            config: {
              recipients: ['<EMAIL>'],
            },
            enabled: true,
          },
        ],
      },
    ];

    defaultRules.forEach((rule) => this.createAlertRule(rule));
  }

  // 生成ID的辅助方法
  private generateEventId(): string {
    return `evt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertId(): string {
    return `alt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateCorrelationId(key: string): string {
    return createHash('md5')
      .update(key + Date.now())
      .digest('hex')
      .substring(0, 16);
  }

  private getSeverityLevel(severity: SecuritySeverity): number {
    switch (severity) {
      case SecuritySeverity.LOW:
        return 1;
      case SecuritySeverity.MEDIUM:
        return 2;
      case SecuritySeverity.HIGH:
        return 3;
      case SecuritySeverity.CRITICAL:
        return 4;
      default:
        return 0;
    }
  }
}

// 预设配置
export const monitoringConfigs = {
  production: {
    enabled: true,
    realTimeAlerts: true,
    batchProcessing: false,
    batchInterval: 1,
    retention: {
      events: 90,
      alerts: 365,
    },
    storage: {
      type: 'database',
      config: {},
    },
    thresholds: {
      maxEventsPerMinute: 1000,
      maxAlertsPerHour: 50,
      riskScoreThreshold: 70,
    },
  } as MonitoringConfig,

  development: {
    enabled: true,
    realTimeAlerts: true,
    batchProcessing: false,
    batchInterval: 5,
    retention: {
      events: 7,
      alerts: 30,
    },
    storage: {
      type: 'memory',
      config: {},
    },
    thresholds: {
      maxEventsPerMinute: 100,
      maxAlertsPerHour: 10,
      riskScoreThreshold: 50,
    },
  } as MonitoringConfig,
};

// 工厂函数
export function createSecurityMonitoringManager(
  config: MonitoringConfig
): SecurityMonitoringManager {
  return new SecurityMonitoringManager(config);
}

export function createProductionMonitoringManager(): SecurityMonitoringManager {
  return new SecurityMonitoringManager(monitoringConfigs.production);
}

export function createDevelopmentMonitoringManager(): SecurityMonitoringManager {
  return new SecurityMonitoringManager(monitoringConfigs.development);
}
