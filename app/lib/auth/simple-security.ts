/**
 * 简化版基础安全功能
 * 包含基础输入验证、简单的安全检查
 */

import { NextRequest } from 'next/server';

// 基础输入验证
export function validateInput(input: string): { isValid: boolean; error?: string } {
  if (!input || typeof input !== 'string') {
    return { isValid: false, error: 'Invalid input' };
  }

  // 检查长度
  if (input.length > 1000) {
    return { isValid: false, error: 'Input too long' };
  }

  // 检查基础SQL注入模式
  const sqlPatterns = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
    /(['"](\s|%20)*(OR|AND))/i,
  ];

  for (const pattern of sqlPatterns) {
    if (pattern.test(input)) {
      return { isValid: false, error: 'Potentially malicious input detected' };
    }
  }

  // 检查基础XSS模式
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe[^>]*>/i,
  ];

  for (const pattern of xssPatterns) {
    if (pattern.test(input)) {
      return { isValid: false, error: 'Potentially malicious input detected' };
    }
  }

  return { isValid: true };
}

// 邮箱验证
export function validateEmail(email: string): boolean {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
}

// 密码强度检查
export function validatePassword(password: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// 获取客户端IP
export function getClientIP(request: NextRequest): string {
  return (
    request.headers.get('x-forwarded-for') ||
    request.headers.get('x-real-ip') ||
    (request as any).ip ||
    '127.0.0.1'
  );
}

// 基础安全头设置
export const securityHeaders = {
  'X-Content-Type-Options': 'nosniff',
  'X-Frame-Options': 'DENY',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Content-Security-Policy':
    "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';",
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
};

// 检查是否为生产环境
export function isProductionEnvironment(): boolean {
  return process.env.NODE_ENV === 'production';
}

// 强制HTTPS检查
export function requireHTTPS(request: NextRequest): boolean {
  if (!isProductionEnvironment()) {
    return true; // 开发环境跳过HTTPS检查
  }

  const protocol = request.headers.get('x-forwarded-proto') || request.nextUrl.protocol;
  return protocol === 'https:';
}

// 基础日志记录
export function logSecurityEvent(event: string, details: any) {
  console.log(`[SECURITY] ${new Date().toISOString()} - ${event}`, details);
}
