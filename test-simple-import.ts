/**
 * @fileoverview 词典数据导入端到端测试脚本
 *
 * 本文件提供完整的词典数据导入流程测试，包括：
 * - API调用测试（Gemini词典服务）
 * - 数据库操作测试（Prisma ORM）
 * - 数据导入逻辑验证
 * - 结果统计和错误处理
 *
 * 主要用于验证词典系统的核心数据流：
 * LLM API → 数据处理 → 数据库存储 → 数据验证
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

// 简单的词典导入测试
import { PrismaClient } from '@prisma/client';
import { fetchDictionary } from './app/lib/5-providers/gemini.js';
import { importFromDictionaryData } from './app/lib/6-scripts/llm-response-to-db/importDictionaryData.js';

/**
 * 执行完整的词典数据导入测试流程
 *
 * 该函数测试词典系统的核心功能，包括：
 *
 * **测试流程**：
 * 1. 数据库清理：清空所有词典相关表
 * 2. API调用：通过Gemini获取单词"hello"的定义
 * 3. 数据导入：将API结果导入到数据库
 * 4. 结果验证：统计导入结果和数据完整性
 *
 * **错误处理**：
 * - API调用失败时提前返回
 * - 捕获所有异常并输出详细错误信息
 * - 确保数据库连接正确关闭
 *
 * **性能特征**：
 * - 网络依赖：需要访问Gemini API
 * - 数据库操作：多表级联删除和插入
 * - 时间复杂度：O(n) 其中n为API返回的词汇数量
 *
 * **依赖服务**：
 * - Gemini API服务（词典数据提供）
 * - PostgreSQL数据库（数据存储）
 * - Prisma ORM（数据访问层）
 *
 * **输出格式**：
 * - 使用表情符号和结构化日志
 * - 详细的统计信息和错误报告
 * - 数据验证结果展示
 *
 * @async
 * @function testSimpleImport
 * @returns {Promise<void>} 无返回值，通过控制台输出测试结果
 * @throws {Error} 数据库连接错误、API调用错误、数据导入错误
 * @complexity O(n) - n为API返回的词汇数量
 *
 * @example
 * ```typescript
 * // 直接运行测试
 * await testSimpleImport();
 *
 * // 预期输出：
 * // 🧪 开始简单导入测试...
 * // 🧹 清理测试数据...
 * // 📝 测试单个单词: "hello"
 * // ✅ API 调用成功, 返回数据:
 * // ...
 * ```
 */
async function testSimpleImport() {
  console.log('🧪 开始简单导入测试...');

  const prisma = new PrismaClient();

  try {
    // 1. 清空测试数据
    console.log('🧹 清理测试数据...');
    await prisma.definition.deleteMany();
    await prisma.explain.deleteMany();
    await prisma.wordFormat.deleteMany();
    await prisma.vocabulary.deleteMany();

    // 2. 测试单个单词
    console.log('📝 测试单个单词: "hello"');
    const result = await fetchDictionary('hello');

    if (result.statusCode !== 200 || !result.data) {
      console.error('❌ API 调用失败:', result);
      return;
    }

    console.log('✅ API 调用成功, 返回数据:');
    console.log('  单词数量:', result.data.wordCount);
    console.log('  单词列表:', result.data.wordList);

    // 3. 导入到数据库
    console.log('💾 导入数据到数据库...');
    const importResult = await importFromDictionaryData(result.data, prisma, {
      forceOverwrite: true,
      verbose: true,
    });

    console.log('📊 导入结果:');
    console.log('  成功:', importResult.successCount);
    console.log('  失败:', importResult.failureCount);
    console.log('  建议词成功:', importResult.suggestsSuccessCount);
    console.log('  错误数量:', importResult.errors.length);

    if (importResult.errors.length > 0) {
      console.log('❌ 错误详情:');
      importResult.errors.forEach((err) => {
        console.log(`  ${err.word}: ${err.error}`);
      });
    }

    // 4. 验证数据库中的数据
    console.log('🔍 验证数据库中的数据...');
    const vocabCount = await prisma.vocabulary.count();
    const explainCount = await prisma.explain.count();
    const definitionCount = await prisma.definition.count();

    console.log('📊 数据库统计:');
    console.log('  词汇记录:', vocabCount);
    console.log('  解释记录:', explainCount);
    console.log('  定义记录:', definitionCount);

    if (vocabCount > 0) {
      console.log('✅ 数据成功写入数据库！');

      // 显示第一个词汇的详细信息
      const firstVocab = await prisma.vocabulary.findFirst({
        include: {
          explains: {
            include: {
              definitions: true,
            },
          },
        },
      });

      if (firstVocab) {
        console.log('📝 第一个词汇详情:');
        console.log('  单词:', firstVocab.word);
        console.log('  音标:', firstVocab.phonetics);
        console.log('  解释数量:', firstVocab.explains.length);

        firstVocab.explains.forEach((explain, i) => {
          console.log(`  解释 ${i + 1}: ${explain.pos} - ${explain.definitions.length} 个定义`);
        });
      }
    } else {
      console.log('❌ 数据库中没有找到数据，可能导入失败');
    }
  } catch (error) {
    console.error('💥 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 执行测试脚本
testSimpleImport();
