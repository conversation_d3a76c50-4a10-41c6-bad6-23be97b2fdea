#!/usr/bin/env tsx
/**
 * 数据库恢复脚本
 * 支持从各种格式的备份文件恢复PostgreSQL数据库
 */

import { promises as fs } from 'fs';
import { join, basename, extname } from 'path';
import { stat } from 'fs/promises';
import {
  BackupConfig,
  RestoreOptions,
  RestoreResult,
  BackupFormat,
  BackupInfo,
} from './utils/backup-types.js';
import { getBackupConfig, validateBackupConfig } from './utils/backup-config.js';
import { DockerUtils } from './utils/docker-utils.js';

export class DatabaseRestore {
  private config: BackupConfig;
  private dockerUtils: DockerUtils;

  constructor(config?: Partial<BackupConfig>) {
    this.config = { ...getBackupConfig(), ...config };
    this.dockerUtils = new DockerUtils(this.config.docker.containerName);

    // 验证配置
    const validation = validateBackupConfig(this.config);
    if (!validation.valid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * 执行恢复
   */
  async restore(options: RestoreOptions): Promise<RestoreResult> {
    const startTime = Date.now();
    const warnings: string[] = [];

    console.log(`🔄 开始恢复数据库: ${this.config.database.database}`);
    console.log(`📁 备份文件: ${options.backupFile}`);

    try {
      // 1. 检查环境
      await this.checkEnvironment();

      // 2. 验证备份文件
      const backupInfo = await this.validateBackupFile(options.backupFile);
      console.log(`📋 备份信息: ${backupInfo.type} 格式, ${backupInfo.sizeFormatted}`);

      // 3. 准备目标数据库
      if (options.dropDatabase || options.createDatabase) {
        await this.prepareDatabaseEnvironment(options, warnings);
      }

      // 4. 执行恢复
      const restoreSuccess = await this.performRestore(options, backupInfo);

      if (!restoreSuccess) {
        throw new Error('数据库恢复失败');
      }

      // 5. 验证恢复结果
      const tableCount = await this.getRestoredTableCount();

      const duration = Date.now() - startTime;

      const result: RestoreResult = {
        success: true,
        duration,
        restoredTables: tableCount,
        warnings,
      };

      console.log(`✅ 恢复完成`);
      console.log(`📊 恢复表数量: ${tableCount}`);
      console.log(`⏱️ 耗时: ${duration}ms`);

      if (warnings.length > 0) {
        console.log(`⚠️ 警告信息:`);
        warnings.forEach((warning) => console.log(`  - ${warning}`));
      }

      return result;
    } catch (error: any) {
      const duration = Date.now() - startTime;
      console.error(`❌ 恢复失败: ${error.message}`);

      return {
        success: false,
        duration,
        restoredTables: 0,
        warnings,
        error: error.message,
      };
    }
  }

  /**
   * 检查环境
   */
  private async checkEnvironment(): Promise<void> {
    // 检查Docker是否可用
    if (this.config.docker.useDocker) {
      if (!(await this.dockerUtils.isDockerAvailable())) {
        throw new Error('Docker不可用，请确保Docker已安装并运行');
      }

      // 检查容器是否存在且运行
      if (!(await this.dockerUtils.containerExists())) {
        throw new Error(`容器 ${this.config.docker.containerName} 不存在`);
      }

      if (!(await this.dockerUtils.isContainerRunning())) {
        console.log(`🔄 启动容器: ${this.config.docker.containerName}`);
        const started = await this.dockerUtils.startContainer();
        if (!started) {
          throw new Error(`无法启动容器: ${this.config.docker.containerName}`);
        }
      }

      // 测试数据库连接
      const connection = await this.dockerUtils.testDatabaseConnection(
        this.config.database.database,
        this.config.database.username,
        this.config.database.password
      );

      if (!connection.isConnected) {
        throw new Error(`数据库连接失败: ${connection.error}`);
      }

      console.log(`✅ 数据库连接正常 (PostgreSQL ${connection.version})`);
    }
  }

  /**
   * 验证备份文件
   */
  private async validateBackupFile(backupFile: string): Promise<BackupInfo> {
    try {
      const fileStats = await stat(backupFile);
      const filename = basename(backupFile);

      // 检测备份格式
      const format = this.detectBackupFormat(filename);
      const compressed = filename.includes('.gz');

      return {
        filename,
        filePath: backupFile,
        size: fileStats.size,
        sizeFormatted: this.formatFileSize(fileStats.size),
        createdAt: fileStats.mtime,
        type: 'full', // 从文件名无法准确判断，默认为full
        format,
        compressed,
        isValid: true,
      };
    } catch (error: any) {
      throw new Error(`备份文件验证失败: ${error.message}`);
    }
  }

  /**
   * 检测备份格式
   */
  private detectBackupFormat(filename: string): BackupFormat {
    const ext = extname(filename.replace('.gz', '')).toLowerCase();

    switch (ext) {
      case '.sql':
        return 'sql';
      case '.tar':
        return 'tar';
      case '.backup':
        return 'custom';
      default:
        return 'custom'; // 默认格式
    }
  }

  /**
   * 准备数据库环境
   */
  private async prepareDatabaseEnvironment(
    options: RestoreOptions,
    warnings: string[]
  ): Promise<void> {
    const targetDb = options.targetDatabase || this.config.database.database;

    try {
      if (options.dropDatabase) {
        console.log(`🗑️ 删除数据库: ${targetDb}`);
        await this.dropDatabase(targetDb);
      }

      if (options.createDatabase) {
        console.log(`🆕 创建数据库: ${targetDb}`);
        await this.createDatabase(targetDb);
      }
    } catch (error: any) {
      warnings.push(`数据库环境准备警告: ${error.message}`);
    }
  }

  /**
   * 删除数据库
   */
  private async dropDatabase(database: string): Promise<void> {
    try {
      const command = `PGPASSWORD="${this.config.database.password}" psql -U ${this.config.database.username} -d postgres -c "DROP DATABASE IF EXISTS ${database};"`;
      const result = await this.dockerUtils.execInContainer(command);

      if (result.stderr && result.stderr.includes('ERROR')) {
        throw new Error(result.stderr);
      }
    } catch (error: any) {
      throw new Error(`删除数据库失败: ${error.message}`);
    }
  }

  /**
   * 创建数据库
   */
  private async createDatabase(database: string): Promise<void> {
    try {
      const command = `PGPASSWORD="${this.config.database.password}" psql -U ${this.config.database.username} -d postgres -c "CREATE DATABASE ${database};"`;
      const result = await this.dockerUtils.execInContainer(command);

      if (result.stderr && result.stderr.includes('ERROR')) {
        throw new Error(result.stderr);
      }
    } catch (error: any) {
      throw new Error(`创建数据库失败: ${error.message}`);
    }
  }

  /**
   * 执行恢复操作
   */
  private async performRestore(options: RestoreOptions, backupInfo: BackupInfo): Promise<boolean> {
    const tempFilename = `restore-${Date.now()}-${backupInfo.filename}`;
    const targetDb = options.targetDatabase || this.config.database.database;

    try {
      // 将备份文件复制到容器内
      const copySuccess = await this.dockerUtils.copyToContainer(
        backupInfo.filePath,
        `/tmp/${tempFilename}`
      );

      if (!copySuccess) {
        throw new Error('复制备份文件到容器失败');
      }

      // 根据备份格式选择恢复方法
      let restoreCommand: string;

      if (backupInfo.format === 'sql') {
        // SQL格式使用psql恢复
        restoreCommand = `PGPASSWORD="${this.config.database.password}" psql -U ${this.config.database.username} -d ${targetDb}`;

        if (options.verbose) {
          restoreCommand += ' -v ON_ERROR_STOP=1';
        }

        restoreCommand += ` -f /tmp/${tempFilename}`;
      } else {
        // 其他格式使用pg_restore恢复
        restoreCommand = `PGPASSWORD="${this.config.database.password}" pg_restore -U ${this.config.database.username} -d ${targetDb}`;

        if (options.verbose) {
          restoreCommand += ' --verbose';
        }

        if (options.skipData) {
          restoreCommand += ' --schema-only';
        } else if (options.skipSchema) {
          restoreCommand += ' --data-only';
        }

        restoreCommand += ` /tmp/${tempFilename}`;
      }

      console.log(`🔄 执行恢复命令...`);
      const result = await this.dockerUtils.execInContainer(restoreCommand);

      // 清理临时文件
      await this.dockerUtils.cleanupTempFiles(`/tmp/${tempFilename}`);

      // 检查是否有错误
      if (result.stderr && result.stderr.includes('ERROR')) {
        throw new Error(`恢复过程中出现错误: ${result.stderr}`);
      }

      return true;
    } catch (error) {
      // 清理失败时也要尝试删除临时文件
      await this.dockerUtils.cleanupTempFiles(`/tmp/${tempFilename}`);
      throw error;
    }
  }

  /**
   * 获取恢复后的表数量
   */
  private async getRestoredTableCount(): Promise<number> {
    try {
      const command = `PGPASSWORD="${this.config.database.password}" psql -U ${this.config.database.username} -d ${this.config.database.database} -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';"`;
      const result = await this.dockerUtils.execInContainer(command);

      const count = parseInt(result.stdout.trim());
      return isNaN(count) ? 0 : count;
    } catch {
      return 0;
    }
  }

  /**
   * 列出可用的备份文件
   */
  async listBackupFiles(): Promise<BackupInfo[]> {
    try {
      const files = await fs.readdir(this.config.backup.outputDir);
      const backupFiles = files
        .filter((file) => file.startsWith(this.config.backup.filenamePrefix))
        .map((file) => join(this.config.backup.outputDir, file));

      const backupInfos: BackupInfo[] = [];

      for (const file of backupFiles) {
        try {
          const info = await this.validateBackupFile(file);
          backupInfos.push(info);
        } catch {
          // 跳过无效文件
        }
      }

      // 按创建时间倒序排列
      backupInfos.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

      return backupInfos;
    } catch (error: any) {
      throw new Error(`列出备份文件失败: ${error.message}`);
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}

/**
 * 命令行入口
 */
async function main() {
  const args = process.argv.slice(2);

  if (args.includes('--help') || args.length === 0) {
    console.log(`
数据库恢复工具

用法:
  tsx restore-database.ts <备份文件> [选项]
  tsx restore-database.ts --list              # 列出可用备份

选项:
  --drop-database      恢复前删除目标数据库
  --create-database    恢复前创建目标数据库
  --target=<database>  指定目标数据库名称
  --skip-data          只恢复结构，跳过数据
  --skip-schema        只恢复数据，跳过结构
  --verbose            详细输出
  --list               列出可用的备份文件
  --help               显示帮助信息

示例:
  tsx restore-database.ts backups/lucid-bd-full-2024-01-01.backup
  tsx restore-database.ts backup.sql --drop-database --create-database
  tsx restore-database.ts --list
    `);
    return;
  }

  const restore = new DatabaseRestore();

  // 列出备份文件
  if (args.includes('--list')) {
    try {
      const backups = await restore.listBackupFiles();

      if (backups.length === 0) {
        console.log('📭 没有找到备份文件');
        return;
      }

      console.log(`📋 可用备份文件 (${backups.length}个):\n`);

      backups.forEach((backup, index) => {
        console.log(`${index + 1}. ${backup.filename}`);
        console.log(`   📁 路径: ${backup.filePath}`);
        console.log(`   📊 大小: ${backup.sizeFormatted}`);
        console.log(`   📅 时间: ${backup.createdAt.toLocaleString()}`);
        console.log(`   🗜️ 格式: ${backup.format}${backup.compressed ? ' (压缩)' : ''}`);
        console.log('');
      });
    } catch (error: any) {
      console.error(`❌ 列出备份文件失败: ${error.message}`);
      process.exit(1);
    }
    return;
  }

  // 恢复数据库
  const backupFile = args[0];
  if (!backupFile) {
    console.error('❌ 请指定备份文件路径');
    process.exit(1);
  }

  const options: RestoreOptions = {
    backupFile,
    dropDatabase: args.includes('--drop-database'),
    createDatabase: args.includes('--create-database'),
    verbose: args.includes('--verbose'),
    skipData: args.includes('--skip-data'),
    skipSchema: args.includes('--skip-schema'),
    targetDatabase: args.find((arg) => arg.startsWith('--target='))?.split('=')[1],
  };

  try {
    const result = await restore.restore(options);

    if (result.success) {
      console.log(`\n✅ 恢复成功完成!`);
      console.log(`📊 恢复表数量: ${result.restoredTables}`);
      process.exit(0);
    } else {
      console.error(`\n❌ 恢复失败: ${result.error}`);
      process.exit(1);
    }
  } catch (error: any) {
    console.error(`\n💥 程序错误: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
