import React from 'react';

interface CharacterProps {
  expected: string;
  actual: string | undefined;
  isTyped: boolean;
  isMarked?: boolean;
}

/**
 * 字符显示组件
 * 根据字符状态显示不同颜色的字符（正确、错误、未输入、标记）
 */
export const Character: React.FC<CharacterProps> = React.memo(
  ({ expected, actual, isTyped, isMarked = false }) => {
    if (!isTyped) {
      return <span className="text-muted-foreground">{expected}</span>;
    }

    const isCorrect = actual === expected;

    if (isCorrect) {
      return <span className={isMarked ? 'text-orange-500' : 'text-primary'}>{expected}</span>;
    }

    if (expected === ' ') {
      return <span className="bg-destructive/50">&nbsp;</span>;
    }

    return <span className="text-destructive">{expected}</span>;
  }
);

Character.displayName = 'Character';
