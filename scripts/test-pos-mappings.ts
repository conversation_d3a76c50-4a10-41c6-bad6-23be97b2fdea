/**
 * 测试词性映射功能的示例脚本
 */

import {
  getPosAbbreviation,
  getPosChinese,
  getAllSupportedPos,
  isSupportedPos,
  POS_TO_ABBREVIATION,
  POS_TO_CHINESE,
} from './pos-mappings.js';

function testPosMappings() {
  console.log('🧪 测试词性映射功能\n');

  // 测试一些常见的词性映射
  const testCases = [
    'noun',
    'verb',
    'adjective',
    'adverb',
    'verb (present participle)',
    'noun (informal)',
    'proper noun',
    'offensive slang',
    'golf term',
    'Latin Prefix',
  ];

  console.log('📝 基础映射测试:');
  console.log('词性'.padEnd(35) + '缩写'.padEnd(15) + '中文');
  console.log('-'.repeat(65));

  testCases.forEach((pos) => {
    const abbr = getPosAbbreviation(pos);
    const chinese = getPosChinese(pos);
    console.log(`${pos.padEnd(35)}${abbr.padEnd(15)}${chinese}`);
  });

  console.log('\n🔍 功能测试:');

  // 测试支持的词性数量
  const allPos = getAllSupportedPos();
  console.log(`✅ 支持的词性总数: ${allPos.length}`);

  // 测试词性检查功能
  console.log(`✅ 'noun' 是否支持: ${isSupportedPos('noun')}`);
  console.log(`❌ 'unknown-pos' 是否支持: ${isSupportedPos('unknown-pos')}`);

  // 测试特殊情况
  console.log('\n🎯 特殊情况测试:');

  // 测试带括号的词性（忽略特殊描述）
  const specialCases = [
    'verb (present participle)',
    'noun (derogatory, offensive)',
    'adjective (comparative)',
  ];

  specialCases.forEach((pos) => {
    const abbr = getPosAbbreviation(pos);
    const chinese = getPosChinese(pos);
    console.log(`"${pos}" -> "${abbr}" -> "${chinese}"`);
  });

  // 测试复合词性
  console.log('\n🔗 复合词性测试:');
  const compoundCases = ['noun/verb', 'adverb/adjective', 'proper noun/slang'];

  compoundCases.forEach((pos) => {
    const abbr = getPosAbbreviation(pos);
    const chinese = getPosChinese(pos);
    console.log(`"${pos}" -> "${abbr}" -> "${chinese}"`);
  });

  // 统计信息
  console.log('\n📊 统计信息:');
  console.log(`缩写映射数量: ${Object.keys(POS_TO_ABBREVIATION).length}`);
  console.log(`中文映射数量: ${Object.keys(POS_TO_CHINESE).length}`);

  // 检查映射一致性
  const abbrKeys = Object.keys(POS_TO_ABBREVIATION);
  const chineseKeys = Object.keys(POS_TO_CHINESE);
  const missingInChinese = abbrKeys.filter((key) => !chineseKeys.includes(key));
  const missingInAbbr = chineseKeys.filter((key) => !abbrKeys.includes(key));

  if (missingInChinese.length === 0 && missingInAbbr.length === 0) {
    console.log('✅ 映射一致性检查通过');
  } else {
    console.log('❌ 映射一致性检查失败');
    if (missingInChinese.length > 0) {
      console.log(`缺少中文映射: ${missingInChinese.join(', ')}`);
    }
    if (missingInAbbr.length > 0) {
      console.log(`缺少缩写映射: ${missingInAbbr.join(', ')}`);
    }
  }

  console.log('\n🎉 测试完成！');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  testPosMappings();
}

export { testPosMappings };
