/**
 * Redis缓存工具类
 * 提供高性能的缓存和速率限制功能
 */

import Redis from 'ioredis';
import { getOptionalEnv } from './env';

// Redis客户端单例
let redisClient: Redis | null = null;

/**
 * 获取Redis客户端实例
 */
export function getRedisClient(): Redis {
  if (!redisClient) {
    const redisUrl = getOptionalEnv('REDIS_URL', 'redis://localhost:6379');
    
    redisClient = new Redis(redisUrl, {
      enableReadyCheck: false,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      enableOfflineQueue: false,
    });

    redisClient.on('error', (error) => {
      console.error('Redis连接错误:', error);
    });

    redisClient.on('connect', () => {
      console.log('✅ Redis连接成功');
    });
  }

  return redisClient;
}

/**
 * 关闭Redis连接
 */
export async function closeRedisConnection(): Promise<void> {
  if (redisClient) {
    await redisClient.quit();
    redisClient = null;
  }
}

/**
 * Redis速率限制器
 */
export class RedisRateLimiter {
  private redis: Redis;

  constructor(redis?: Redis) {
    this.redis = redis || getRedisClient();
  }

  /**
   * 检查速率限制
   * @param key 限制键名
   * @param limit 限制次数
   * @param window 时间窗口（秒）
   * @returns { allowed: boolean, remaining: number, resetTime: number }
   */
  async checkRateLimit(
    key: string,
    limit: number,
    window: number
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
    total: number;
  }> {
    const now = Math.floor(Date.now() / 1000);
    const windowStart = now - window;

    // 使用Lua脚本确保原子性操作
    const script = `
      local key = KEYS[1]
      local window_start = tonumber(ARGV[1])
      local current_time = tonumber(ARGV[2])
      local limit = tonumber(ARGV[3])
      local window_size = tonumber(ARGV[4])
      
      -- 清理过期的记录
      redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
      
      -- 获取当前窗口内的请求数
      local current_requests = redis.call('ZCARD', key)
      
      if current_requests < limit then
        -- 添加当前请求
        redis.call('ZADD', key, current_time, current_time)
        redis.call('EXPIRE', key, window_size)
        return {1, limit - current_requests - 1, current_time + window_size, current_requests + 1}
      else
        return {0, 0, current_time + window_size, current_requests}
      end
    `;

    try {
      const result = await this.redis.eval(
        script,
        1,
        key,
        windowStart.toString(),
        now.toString(),
        limit.toString(),
        window.toString()
      ) as number[];

      return {
        allowed: result[0] === 1,
        remaining: result[1],
        resetTime: result[2],
        total: result[3],
      };
    } catch (error) {
      console.error('Redis速率限制检查失败:', error);
      // 降级处理：当Redis不可用时，允许请求通过
      return {
        allowed: true,
        remaining: limit - 1,
        resetTime: now + window,
        total: 1,
      };
    }
  }

  /**
   * 滑动窗口速率限制（更精确）
   */
  async checkSlidingWindowRateLimit(
    key: string,
    limit: number,
    window: number
  ): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const now = Date.now();
    const windowStart = now - (window * 1000);

    const script = `
      local key = KEYS[1]
      local window_start = tonumber(ARGV[1])
      local current_time = tonumber(ARGV[2])
      local limit = tonumber(ARGV[3])
      local window_ms = tonumber(ARGV[4])
      
      -- 清理过期的记录
      redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
      
      -- 获取当前窗口内的请求数
      local current_requests = redis.call('ZCARD', key)
      
      if current_requests < limit then
        -- 添加当前请求（使用毫秒精度）
        redis.call('ZADD', key, current_time, current_time .. ':' .. math.random())
        redis.call('EXPIRE', key, math.ceil(window_ms / 1000))
        return {1, limit - current_requests - 1, current_time + window_ms}
      else
        -- 获取最早的请求时间，计算重置时间
        local oldest_request = redis.call('ZRANGE', key, 0, 0, 'WITHSCORES')
        local reset_time = current_time + window_ms
        if #oldest_request > 0 then
          reset_time = tonumber(oldest_request[2]) + window_ms
        end
        return {0, 0, reset_time}
      end
    `;

    try {
      const result = await this.redis.eval(
        script,
        1,
        key,
        windowStart.toString(),
        now.toString(),
        limit.toString(),
        (window * 1000).toString()
      ) as number[];

      return {
        allowed: result[0] === 1,
        remaining: result[1],
        resetTime: result[2],
      };
    } catch (error) {
      console.error('Redis滑动窗口速率限制检查失败:', error);
      return {
        allowed: true,
        remaining: limit - 1,
        resetTime: now + (window * 1000),
      };
    }
  }
}

/**
 * Redis缓存管理器
 */
export class RedisCacheManager {
  private redis: Redis;

  constructor(redis?: Redis) {
    this.redis = redis || getRedisClient();
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttlSeconds?: number): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      if (ttlSeconds) {
        await this.redis.setex(key, ttlSeconds, serialized);
      } else {
        await this.redis.set(key, serialized);
      }
    } catch (error) {
      console.error('Redis设置缓存失败:', error);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const value = await this.redis.get(key);
      if (value === null) return null;
      return JSON.parse(value) as T;
    } catch (error) {
      console.error('Redis获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error('Redis删除缓存失败:', error);
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redis.exists(key);
      return result === 1;
    } catch (error) {
      console.error('Redis检查键存在失败:', error);
      return false;
    }
  }

  /**
   * 设置键的过期时间
   */
  async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.redis.expire(key, seconds);
    } catch (error) {
      console.error('Redis设置过期时间失败:', error);
    }
  }

  /**
   * 获取键的TTL
   */
  async ttl(key: string): Promise<number> {
    try {
      return await this.redis.ttl(key);
    } catch (error) {
      console.error('Redis获取TTL失败:', error);
      return -1;
    }
  }
}

// 导出实例
export const redisRateLimiter = new RedisRateLimiter();
export const redisCacheManager = new RedisCacheManager();