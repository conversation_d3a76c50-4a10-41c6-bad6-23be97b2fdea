'use client';

import { useState, useCallback } from 'react';
import { EnhancedDictionaryResponseType } from '@/lib/utils/dict-types';

interface LookupState {
  data: EnhancedDictionaryResponseType | null;
  loading: boolean;
  error: string | null;
  history: string[];
}

interface UseWordLookupReturn {
  state: LookupState;
  lookupWord: (word: string, options?: LookupOptions) => Promise<void>;
  clearResults: () => void;
  goBack: () => void;
  canGoBack: boolean;
}

interface LookupOptions {
  fresh?: boolean;
  includeRelationships?: boolean;
}

export function useWordLookup(): UseWordLookupReturn {
  const [state, setState] = useState<LookupState>({
    data: null,
    loading: false,
    error: null,
    history: [],
  });

  const lookupWord = useCallback(async (word: string, options: LookupOptions = {}) => {
    if (!word.trim()) return;

    setState((prev) => ({
      ...prev,
      loading: true,
      error: null,
    }));

    try {
      const params = new URLSearchParams({
        ...(options.fresh && { fresh: 'true' }),
        ...(options.includeRelationships && { relationships: 'true' }),
      });

      const response = await fetch(`/api/dictionary/en/${encodeURIComponent(word)}?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result: EnhancedDictionaryResponseType = await response.json();

      setState((prev) => ({
        data: result,
        loading: false,
        error: null,
        history: prev.history.includes(word) ? prev.history : [...prev.history, word],
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '查询失败',
      }));
    }
  }, []);

  const clearResults = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      history: [],
    });
  }, []);

  const goBack = useCallback(() => {
    setState((prev) => {
      if (prev.history.length <= 1) return prev;

      const newHistory = prev.history.slice(0, -1);
      const previousWord = newHistory[newHistory.length - 1];

      // 重新查询上一个词
      if (previousWord) {
        lookupWord(previousWord);
      }

      return {
        ...prev,
        history: newHistory,
      };
    });
  }, [lookupWord]);

  const canGoBack = state.history.length > 1;

  return {
    state,
    lookupWord,
    clearResults,
    goBack,
    canGoBack,
  };
}

// 词汇缓存Hook - 用于优化性能
export function useWordCache() {
  const [cache, setCache] = useState<Map<string, EnhancedDictionaryResponseType>>(new Map());

  const getCachedWord = useCallback(
    (word: string): EnhancedDictionaryResponseType | null => {
      return cache.get(word.toLowerCase()) || null;
    },
    [cache]
  );

  const setCachedWord = useCallback((word: string, data: EnhancedDictionaryResponseType) => {
    setCache((prev) => {
      const newCache = new Map(prev);
      newCache.set(word.toLowerCase(), data);

      // 限制缓存大小
      if (newCache.size > 100) {
        const firstKey = newCache.keys().next().value;
        if (firstKey) {
          newCache.delete(firstKey);
        }
      }

      return newCache;
    });
  }, []);

  const clearCache = useCallback(() => {
    setCache(new Map());
  }, []);

  return {
    getCachedWord,
    setCachedWord,
    clearCache,
    cacheSize: cache.size,
  };
}

// 词形关系处理Hook
export function useWordFormRelationships() {
  const getBaseForm = useCallback((relationships: any[]) => {
    if (!relationships.length) return null;
    return relationships[0].baseForm;
  }, []);

  const getAllForms = useCallback((relationships: any[]) => {
    if (!relationships.length) return [];
    return relationships[0].allForms;
  }, []);

  const getFormsByType = useCallback(
    (relationships: any[], formType: string) => {
      const allForms = getAllForms(relationships);
      return allForms.filter((form: any) => form.name === formType);
    },
    [getAllForms]
  );

  const getMainForms = useCallback(
    (relationships: any[]) => {
      const allForms = getAllForms(relationships);
      return allForms.filter((form: any) => form.isMainForm);
    },
    [getAllForms]
  );

  const getDerivedForms = useCallback(
    (relationships: any[]) => {
      const allForms = getAllForms(relationships);
      return allForms.filter((form: any) => !form.isMainForm);
    },
    [getAllForms]
  );

  return {
    getBaseForm,
    getAllForms,
    getFormsByType,
    getMainForms,
    getDerivedForms,
  };
}

// 搜索建议Hook
export function useSearchSuggestions() {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const getSuggestions = useCallback(async (query: string) => {
    if (!query.trim() || query.length < 2) {
      setSuggestions([]);
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/suggestions?q=${encodeURIComponent(query)}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.suggestions || []);
      }
    } catch (error) {
      console.error('Failed to get suggestions:', error);
      setSuggestions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearSuggestions = useCallback(() => {
    setSuggestions([]);
  }, []);

  return {
    suggestions,
    loading,
    getSuggestions,
    clearSuggestions,
  };
}

// 查询统计Hook
export function useQueryStats() {
  const [stats, setStats] = useState({
    totalQueries: 0,
    successfulQueries: 0,
    averageResponseTime: 0,
    mostQueriedWords: [] as string[],
  });

  const updateStats = useCallback((result: EnhancedDictionaryResponseType) => {
    setStats((prev) => ({
      totalQueries: prev.totalQueries + 1,
      successfulQueries:
        result.words.length > 0 ? prev.successfulQueries + 1 : prev.successfulQueries,
      averageResponseTime:
        (prev.averageResponseTime * prev.totalQueries + result.queryMetadata.processingTimeMs) /
        (prev.totalQueries + 1),
      mostQueriedWords: prev.mostQueriedWords, // 这里可以实现更复杂的统计逻辑
    }));
  }, []);

  const resetStats = useCallback(() => {
    setStats({
      totalQueries: 0,
      successfulQueries: 0,
      averageResponseTime: 0,
      mostQueriedWords: [],
    });
  }, []);

  return {
    stats,
    updateStats,
    resetStats,
  };
}
