/**
 * Cache Performance Validation Test
 *
 * Comprehensive testing of cache performance in the lightweight security middleware
 *
 * Test Goals:
 * - Validate >90% cache hit rate target
 * - Test multi-level cache (L1 in-memory, L2 Redis) effectiveness
 * - Measure cache warming performance
 * - Test cache eviction intelligence
 * - Validate cache performance under load
 */

import { NextRequest } from 'next/server';
import {
  LightweightSecurityMiddleware,
  SecurityLevel,
} from '../app/lib/auth/lightweight-security-middleware';
import { createPerformanceCache } from '../app/lib/auth/cache-optimization';

// ============================================================================
// Test Configuration
// ============================================================================

interface CacheTestConfig {
  testDuration: number;
  warmupDuration: number;
  concurrencyLevels: number[];
  requestsPerPattern: number;
  cacheHitRateTarget: number;
  patterns: {
    cacheable: string[];
    nonCacheable: string[];
    mixed: string[];
  };
}

const DEFAULT_CACHE_TEST_CONFIG: CacheTestConfig = {
  testDuration: 60000, // 60 seconds
  warmupDuration: 10000, // 10 seconds
  concurrencyLevels: [1, 10, 25, 50, 100, 150],
  requestsPerPattern: 100,
  cacheHitRateTarget: 0.9, // 90%
  patterns: {
    cacheable: [
      'hello',
      'world',
      'test',
      'cache',
      'performance',
      'security',
      'middleware',
      'validation',
      'request',
      'response',
    ],
    nonCacheable: [
      'random1',
      'random2',
      'random3',
      'random4',
      'random5',
      'unique1',
      'unique2',
      'unique3',
      'unique4',
      'unique5',
    ],
    mixed: ['mixed1', 'mixed2', 'mixed3', 'mixed4', 'mixed5'],
  },
};

// ============================================================================
// Cache Test Request Generator
// ============================================================================

class CacheTestRequestGenerator {
  private static requestCounter = 0;
  private static ipPool = Array.from({ length: 10 }, (_, i) => `192.168.1.${i + 100}`);

  static generateCacheableRequest(pattern: string, useRandomIP: boolean = false): NextRequest {
    const ip = useRandomIP
      ? this.ipPool[Math.floor(Math.random() * this.ipPool.length)]
      : '*************'; // Fixed IP for better caching

    const url = new URL(`/api/dictionary/en/${pattern}`, 'http://localhost:3000');
    const request = new NextRequest(url, {
      method: 'GET',
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-forwarded-for': ip,
        accept: 'application/json',
        'content-type': 'application/json',
      },
    });

    (request as any).ip = ip;
    return request;
  }

  static generateNonCacheableRequest(pattern: string): NextRequest {
    this.requestCounter++;
    const uniqueParam = `${pattern}_${this.requestCounter}_${Date.now()}`;
    const url = new URL(`/api/dictionary/en/${uniqueParam}`, 'http://localhost:3000');

    const request = new NextRequest(url, {
      method: 'GET',
      headers: {
        'user-agent': `TestAgent/${this.requestCounter}`,
        'x-forwarded-for': this.generateUniqueIP(),
        accept: 'application/json',
        'content-type': 'application/json',
      },
    });

    (request as any).ip = this.generateUniqueIP();
    return request;
  }

  static generateMixedRequest(pattern: string, cacheableRatio: number = 0.7): NextRequest {
    const shouldBeCacheable = Math.random() < cacheableRatio;

    if (shouldBeCacheable) {
      return this.generateCacheableRequest(pattern, false);
    } else {
      return this.generateNonCacheableRequest(pattern);
    }
  }

  private static generateUniqueIP(): string {
    return `10.0.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  }
}

// ============================================================================
// Cache Performance Metrics
// ============================================================================

interface CacheMetrics {
  totalRequests: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  l1HitRate: number;
  l2HitRate: number;
  averageResponseTime: number;
  cacheResponseTime: number;
  nonCacheResponseTime: number;
  cacheSize: number;
  evictions: number;
  memoryUsage: number;
}

interface CacheTestResults {
  overall: CacheMetrics;
  byPattern: Map<string, CacheMetrics>;
  byConcurrency: Map<number, CacheMetrics>;
  bySecurityLevel: Map<SecurityLevel, CacheMetrics>;
  performanceImpact: {
    withCache: number;
    withoutCache: number;
    improvement: number;
  };
  targetsMet: {
    hitRate: boolean;
    responseTime: boolean;
    memoryUsage: boolean;
    concurrencyHandling: boolean;
  };
}

// ============================================================================
// Cache Performance Test Runner
// ============================================================================

class CachePerformanceTest {
  private config: CacheTestConfig;
  private middleware: LightweightSecurityMiddleware;
  private performanceCache: any;
  private metrics: Map<string, number[]> = new Map();
  private cacheStats: Map<string, any> = new Map();

  constructor(config: Partial<CacheTestConfig> = {}) {
    this.config = { ...DEFAULT_CACHE_TEST_CONFIG, ...config };
    this.initializeMiddleware();
  }

  private initializeMiddleware(): void {
    this.middleware = new LightweightSecurityMiddleware({
      enableCaching: true,
      enableThreatDetection: true,
      enableRateLimiting: true,
      enableFingerprinting: true,
      performanceMode: true,
      maxConcurrency: 150,
      cacheSize: 2000,
    });

    this.performanceCache = createPerformanceCache({
      l1MaxSize: 1000,
      l1TTL: 300,
      enableIntelligentEviction: true,
    });
  }

  async runCachePerformanceTest(): Promise<CacheTestResults> {
    console.log('🚀 Starting Cache Performance Test');
    console.log('='.repeat(60));
    console.log('');

    // Clear caches before testing
    this.middleware.clearCaches();

    const results: CacheTestResults = {
      overall: await this.runOverallCacheTest(),
      byPattern: await this.runPatternBasedTests(),
      byConcurrency: await this.runConcurrencyTests(),
      bySecurityLevel: await this.runSecurityLevelTests(),
      performanceImpact: await this.measurePerformanceImpact(),
      targetsMet: {
        hitRate: false,
        responseTime: false,
        memoryUsage: false,
        concurrencyHandling: false,
      },
    };

    // Evaluate targets
    results.targetsMet = this.evaluateTargets(results);

    this.printCacheResults(results);
    return results;
  }

  private async runOverallCacheTest(): Promise<CacheMetrics> {
    console.log('📊 Running overall cache performance test...');

    const metrics = this.createEmptyMetrics();
    const responseTimes: number[] = [];
    const cacheHits: number[] = [];

    // Generate mixed workload
    const totalRequests = this.config.requestsPerPattern * 10;

    for (let i = 0; i < totalRequests; i++) {
      const patternIndex = i % this.config.patterns.cacheable.length;
      const pattern = this.config.patterns.cacheable[patternIndex];

      // Create request (70% cacheable, 30% unique)
      const request = CacheTestRequestGenerator.generateMixedRequest(pattern, 0.7);
      const result = await this.processRequestWithMetrics(request);

      metrics.totalRequests++;
      responseTimes.push(result.responseTime);

      if (result.cached) {
        metrics.cacheHits++;
        cacheHits.push(result.responseTime);
      } else {
        metrics.cacheMisses++;
      }
    }

    // Calculate final metrics
    metrics.hitRate = metrics.cacheHits / metrics.totalRequests;
    metrics.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    metrics.cacheResponseTime =
      cacheHits.length > 0 ? cacheHits.reduce((a, b) => a + b, 0) / cacheHits.length : 0;

    const middlewareMetrics = this.middleware.getMetrics();
    metrics.cacheSize = middlewareMetrics.cacheSize;
    metrics.memoryUsage = process.memoryUsage().heapUsed;

    console.log(`  ✅ Overall test completed: ${metrics.hitRate.toFixed(3)} hit rate`);
    return metrics;
  }

  private async runPatternBasedTests(): Promise<Map<string, CacheMetrics>> {
    console.log('🔍 Running pattern-based cache tests...');

    const results = new Map<string, CacheMetrics>();

    // Test cacheable patterns
    for (const pattern of this.config.patterns.cacheable) {
      const metrics = await this.testPattern(pattern, 'cacheable');
      results.set(`cacheable_${pattern}`, metrics);
    }

    // Test non-cacheable patterns
    for (const pattern of this.config.patterns.nonCacheable) {
      const metrics = await this.testPattern(pattern, 'noncacheable');
      results.set(`noncacheable_${pattern}`, metrics);
    }

    // Test mixed patterns
    for (const pattern of this.config.patterns.mixed) {
      const metrics = await this.testPattern(pattern, 'mixed');
      results.set(`mixed_${pattern}`, metrics);
    }

    console.log(`  ✅ Pattern-based tests completed: ${results.size} patterns tested`);
    return results;
  }

  private async testPattern(
    pattern: string,
    type: 'cacheable' | 'noncacheable' | 'mixed'
  ): Promise<CacheMetrics> {
    const metrics = this.createEmptyMetrics();
    const responseTimes: number[] = [];
    const cacheHits: number[] = [];

    for (let i = 0; i < this.config.requestsPerPattern; i++) {
      let request: NextRequest;

      switch (type) {
        case 'cacheable':
          request = CacheTestRequestGenerator.generateCacheableRequest(pattern);
          break;
        case 'noncacheable':
          request = CacheTestRequestGenerator.generateNonCacheableRequest(pattern);
          break;
        case 'mixed':
          request = CacheTestRequestGenerator.generateMixedRequest(pattern, 0.6);
          break;
      }

      const result = await this.processRequestWithMetrics(request);

      metrics.totalRequests++;
      responseTimes.push(result.responseTime);

      if (result.cached) {
        metrics.cacheHits++;
        cacheHits.push(result.responseTime);
      } else {
        metrics.cacheMisses++;
      }
    }

    // Calculate metrics
    metrics.hitRate = metrics.cacheHits / metrics.totalRequests;
    metrics.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    metrics.cacheResponseTime =
      cacheHits.length > 0 ? cacheHits.reduce((a, b) => a + b, 0) / cacheHits.length : 0;

    return metrics;
  }

  private async runConcurrencyTests(): Promise<Map<number, CacheMetrics>> {
    console.log('🚀 Running concurrency-based cache tests...');

    const results = new Map<number, CacheMetrics>();

    for (const concurrency of this.config.concurrencyLevels) {
      console.log(`  📊 Testing ${concurrency} concurrent requests...`);

      const metrics = this.createEmptyMetrics();
      const promises: Promise<void>[] = [];

      for (let i = 0; i < concurrency; i++) {
        promises.push(this.runConcurrentBatch(metrics, 50));
      }

      await Promise.all(promises);

      metrics.hitRate = metrics.cacheHits / metrics.totalRequests;
      results.set(concurrency, metrics);

      console.log(`    ✅ ${concurrency} concurrency: ${metrics.hitRate.toFixed(3)} hit rate`);
    }

    console.log(`  ✅ Concurrency tests completed: ${results.size} levels tested`);
    return results;
  }

  private async runConcurrentBatch(metrics: CacheMetrics, batchSize: number): Promise<void> {
    const pattern =
      this.config.patterns.cacheable[
        Math.floor(Math.random() * this.config.patterns.cacheable.length)
      ];

    for (let i = 0; i < batchSize; i++) {
      const request = CacheTestRequestGenerator.generateMixedRequest(pattern, 0.8);
      const result = await this.processRequestWithMetrics(request);

      metrics.totalRequests++;

      if (result.cached) {
        metrics.cacheHits++;
      } else {
        metrics.cacheMisses++;
      }
    }
  }

  private async runSecurityLevelTests(): Promise<Map<SecurityLevel, CacheMetrics>> {
    console.log('🛡️  Running security level cache tests...');

    const results = new Map<SecurityLevel, CacheMetrics>();
    const levels: SecurityLevel[] = ['minimal', 'standard', 'strict'];

    for (const level of levels) {
      console.log(`  🔒 Testing ${level} security level...`);

      this.middleware.updateSecurityLevel(level);
      this.middleware.clearCaches(); // Clear cache between levels

      const metrics = this.createEmptyMetrics();
      const responseTimes: number[] = [];
      const cacheHits: number[] = [];

      // Test with mixed workload
      for (let i = 0; i < this.config.requestsPerPattern * 2; i++) {
        const pattern = this.config.patterns.cacheable[i % this.config.patterns.cacheable.length];
        const request = CacheTestRequestGenerator.generateMixedRequest(pattern, 0.75);
        const result = await this.processRequestWithMetrics(request);

        metrics.totalRequests++;
        responseTimes.push(result.responseTime);

        if (result.cached) {
          metrics.cacheHits++;
          cacheHits.push(result.responseTime);
        } else {
          metrics.cacheMisses++;
        }
      }

      metrics.hitRate = metrics.cacheHits / metrics.totalRequests;
      metrics.averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      metrics.cacheResponseTime =
        cacheHits.length > 0 ? cacheHits.reduce((a, b) => a + b, 0) / cacheHits.length : 0;

      results.set(level, metrics);

      console.log(
        `    ✅ ${level}: ${metrics.hitRate.toFixed(3)} hit rate, ${metrics.averageResponseTime.toFixed(2)}ms avg`
      );
    }

    console.log(`  ✅ Security level tests completed: ${results.size} levels tested`);
    return results;
  }

  private async measurePerformanceImpact(): Promise<CacheTestResults['performanceImpact']> {
    console.log('⚡ Measuring cache performance impact...');

    const testRequests = 200;
    const pattern = 'performance_test';

    // Test with cache enabled
    this.middleware.clearCaches();
    const withCacheStart = performance.now();

    for (let i = 0; i < testRequests; i++) {
      const request = CacheTestRequestGenerator.generateCacheableRequest(pattern);
      await this.middleware.checkSecurity(request);
    }

    const withCacheTime = performance.now() - withCacheStart;

    // Test without cache (create new middleware instance)
    const noCacheMiddleware = new LightweightSecurityMiddleware({
      enableCaching: false,
      enableThreatDetection: true,
      enableRateLimiting: true,
      enableFingerprinting: true,
      performanceMode: true,
      maxConcurrency: 150,
    });

    const withoutCacheStart = performance.now();

    for (let i = 0; i < testRequests; i++) {
      const request = CacheTestRequestGenerator.generateCacheableRequest(pattern);
      await noCacheMiddleware.checkSecurity(request);
    }

    const withoutCacheTime = performance.now() - withoutCacheStart;

    const improvement = ((withoutCacheTime - withCacheTime) / withoutCacheTime) * 100;

    console.log(`  ✅ Performance impact measured: ${improvement.toFixed(2)}% improvement`);

    return {
      withCache: withCacheTime,
      withoutCache: withoutCacheTime,
      improvement,
    };
  }

  private async processRequestWithMetrics(
    request: NextRequest
  ): Promise<{ responseTime: number; cached: boolean }> {
    const startTime = performance.now();

    try {
      const result = await this.middleware.checkSecurity(request);
      const responseTime = performance.now() - startTime;

      return {
        responseTime,
        cached: result.cached,
      };
    } catch (error) {
      const responseTime = performance.now() - startTime;
      return {
        responseTime,
        cached: false,
      };
    }
  }

  private createEmptyMetrics(): CacheMetrics {
    return {
      totalRequests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      hitRate: 0,
      l1HitRate: 0,
      l2HitRate: 0,
      averageResponseTime: 0,
      cacheResponseTime: 0,
      nonCacheResponseTime: 0,
      cacheSize: 0,
      evictions: 0,
      memoryUsage: 0,
    };
  }

  private evaluateTargets(results: CacheTestResults): CacheTestResults['targetsMet'] {
    const targetHitRate = results.overall.hitRate >= this.config.cacheHitRateTarget;
    const targetResponseTime =
      results.overall.cacheResponseTime < results.overall.nonCacheResponseTime;
    const targetMemoryUsage = results.overall.memoryUsage < 100 * 1024 * 1024; // 100MB
    const targetConcurrency = Array.from(results.byConcurrency.values()).every(
      (m) => m.hitRate >= 0.8
    );

    return {
      hitRate: targetHitRate,
      responseTime: targetResponseTime,
      memoryUsage: targetMemoryUsage,
      concurrencyHandling: targetConcurrency,
    };
  }

  private printCacheResults(results: CacheTestResults): void {
    console.log('\n📊 CACHE PERFORMANCE TEST RESULTS');
    console.log('='.repeat(60));
    console.log('');

    // Overall metrics
    console.log('📈 OVERALL CACHE METRICS');
    console.log('-'.repeat(40));
    console.log(`Total Requests: ${results.overall.totalRequests}`);
    console.log(`Cache Hits: ${results.overall.cacheHits}`);
    console.log(`Cache Misses: ${results.overall.cacheMisses}`);
    console.log(
      `Hit Rate: ${(results.overall.hitRate * 100).toFixed(2)}% (Target: ${(this.config.cacheHitRateTarget * 100).toFixed(0)}%) ${results.targetsMet.hitRate ? '✅' : '❌'}`
    );
    console.log(`Average Response Time: ${results.overall.averageResponseTime.toFixed(2)}ms`);
    console.log(`Cache Response Time: ${results.overall.cacheResponseTime.toFixed(2)}ms`);
    console.log(`Cache Size: ${results.overall.cacheSize}`);
    console.log(`Memory Usage: ${(results.overall.memoryUsage / 1024 / 1024).toFixed(2)}MB`);
    console.log('');

    // Performance impact
    console.log('⚡ CACHE PERFORMANCE IMPACT');
    console.log('-'.repeat(40));
    console.log(`With Cache: ${results.performanceImpact.withCache.toFixed(2)}ms`);
    console.log(`Without Cache: ${results.performanceImpact.withoutCache.toFixed(2)}ms`);
    console.log(`Performance Improvement: ${results.performanceImpact.improvement.toFixed(2)}%`);
    console.log('');

    // Concurrency analysis
    console.log('🚀 CONCURRENCY ANALYSIS');
    console.log('-'.repeat(40));
    console.log('Concurrency | Hit Rate | Avg Response Time');
    console.log('-'.repeat(40));

    for (const [concurrency, metrics] of results.byConcurrency.entries()) {
      console.log(
        `${concurrency.toString().padStart(11)} | ${(metrics.hitRate * 100).toFixed(2).padStart(8)}% | ${metrics.averageResponseTime.toFixed(2).padStart(17)}ms`
      );
    }
    console.log('');

    // Security level analysis
    console.log('🛡️  SECURITY LEVEL ANALYSIS');
    console.log('-'.repeat(40));
    console.log('Level    | Hit Rate | Avg Response Time | Cache Response Time');
    console.log('-'.repeat(40));

    for (const [level, metrics] of results.bySecurityLevel.entries()) {
      console.log(
        `${level.padStart(8)} | ${(metrics.hitRate * 100).toFixed(2).padStart(8)}% | ${metrics.averageResponseTime.toFixed(2).padStart(17)}ms | ${metrics.cacheResponseTime.toFixed(2).padStart(19)}ms`
      );
    }
    console.log('');

    // Pattern analysis (top 5 patterns)
    console.log('🔍 PATTERN ANALYSIS (Top 5)');
    console.log('-'.repeat(40));
    const topPatterns = Array.from(results.byPattern.entries())
      .sort(([, a], [, b]) => b.hitRate - a.hitRate)
      .slice(0, 5);

    console.log('Pattern                    | Hit Rate | Requests');
    console.log('-'.repeat(40));
    for (const [pattern, metrics] of topPatterns) {
      console.log(
        `${pattern.padEnd(26)} | ${(metrics.hitRate * 100).toFixed(2).padStart(8)}% | ${metrics.totalRequests.toString().padStart(8)}`
      );
    }
    console.log('');

    // Target evaluation
    console.log('🎯 TARGET EVALUATION');
    console.log('-'.repeat(40));
    console.log(
      `Hit Rate Target (${(this.config.cacheHitRateTarget * 100).toFixed(0)}%): ${results.targetsMet.hitRate ? '✅ MET' : '❌ MISSED'} (${(results.overall.hitRate * 100).toFixed(2)}%)`
    );
    console.log(
      `Response Time Improvement: ${results.targetsMet.responseTime ? '✅ MET' : '❌ MISSED'}`
    );
    console.log(`Memory Usage Target: ${results.targetsMet.memoryUsage ? '✅ MET' : '❌ MISSED'}`);
    console.log(
      `Concurrency Handling: ${results.targetsMet.concurrencyHandling ? '✅ MET' : '❌ MISSED'}`
    );
    console.log('');

    // Overall assessment
    const allTargetsMet = Object.values(results.targetsMet).every((met) => met);
    console.log('🏆 OVERALL ASSESSMENT');
    console.log('-'.repeat(40));
    console.log(
      `Cache Performance: ${allTargetsMet ? '✅ ALL TARGETS MET' : '❌ SOME TARGETS MISSED'}`
    );

    if (results.overall.hitRate >= this.config.cacheHitRateTarget) {
      console.log(
        `✅ Cache hit rate target achieved (${(results.overall.hitRate * 100).toFixed(2)}%)`
      );
    } else {
      console.log(
        `⚠️  Cache hit rate below target (${(results.overall.hitRate * 100).toFixed(2)}%)`
      );
    }

    if (results.performanceImpact.improvement > 50) {
      console.log(
        `✅ Significant performance improvement from caching (${results.performanceImpact.improvement.toFixed(2)}%)`
      );
    } else {
      console.log(
        `⚠️  Lower than expected performance improvement (${results.performanceImpact.improvement.toFixed(2)}%)`
      );
    }
  }
}

// ============================================================================
// Main Execution
// ============================================================================

export async function runCachePerformanceTest(): Promise<CacheTestResults> {
  const test = new CachePerformanceTest();
  return await test.runCachePerformanceTest();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runCachePerformanceTest()
    .then((results) => {
      console.log('\n🎉 Cache performance test completed successfully!');

      // Output summary for automation
      console.log('\n📊 SUMMARY FOR AUTOMATION:');
      console.log(`CACHE_HIT_RATE: ${(results.overall.hitRate * 100).toFixed(2)}%`);
      console.log(`PERFORMANCE_IMPROVEMENT: ${results.performanceImpact.improvement.toFixed(2)}%`);
      console.log(`TARGETS_MET: ${Object.values(results.targetsMet).every((met) => met)}`);

      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Cache performance test failed:', error);
      process.exit(1);
    });
}
