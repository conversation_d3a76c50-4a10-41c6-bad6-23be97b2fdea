import { describe, it, expect, beforeAll } from 'vitest';
import { fetchDictionary } from '../../gemini';
import { HttpStatusCode } from '../../../utils/dict-types';
import fs from 'fs';
import path from 'path';

const TEST_RESULTS_DIR = path.join(__dirname, 'test_results');

beforeAll(() => {
  // Ensure the test_results directory exists
  if (!fs.existsSync(TEST_RESULTS_DIR)) {
    fs.mkdirSync(TEST_RESULTS_DIR, { recursive: true });
    console.log(`Created directory: ${TEST_RESULTS_DIR}`);
  }
});

describe('Gemini Provider 连通性测试 基础结构测试', () => {
  it('should return a valid response with correct structure', async () => {
    // 验证正确的结构
    const result = await fetchDictionary('hello,world');
    // console.log(JSON.stringify(result, null, 4));

    // 基本结构验证
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    expect(result).toHaveProperty('statusCode');

    // 验证状态码为成功状态
    expect(result.statusCode).toBe(HttpStatusCode.OK);

    // 验证数据结构存在
    expect(result).toHaveProperty('data');
    expect(result.data).toBeDefined();
    expect(typeof result.data).toBe('object');

    // 类型守卫：确保 data 存在后再进行后续验证
    if (!result.data) {
      throw new Error('result.data is undefined');
    }

    // 验证必需的顶级字段
    expect(result.data).toHaveProperty('words');
    expect(result.data).toHaveProperty('wordCount');
    expect(result.data).toHaveProperty('wordList');

    // 验证数据类型
    expect(Array.isArray(result.data.words)).toBe(true);
    expect(typeof result.data.wordCount).toBe('number');
    expect(Array.isArray(result.data.wordList)).toBe(true);

    // 验证数据逻辑一致性
    expect(result.data.wordCount).toBe(result.data.wordList.length);
    expect(result.data.words.length).toBe(result.data.wordCount);

    // 验证至少有一个单词
    expect(result.data.words.length).toBeGreaterThan(0);
    expect(result.data.wordCount).toBeGreaterThan(0);

    // 验证每个单词的结构
    result.data.words.forEach((word) => {
      // 基本字段验证
      expect(word).toHaveProperty('word');
      expect(word).toHaveProperty('explain');
      expect(word).toHaveProperty('phonetic');
      expect(word).toHaveProperty('wordFormats');

      // 数据类型验证
      expect(typeof word.word).toBe('string');
      expect(Array.isArray(word.explain)).toBe(true);
      expect(typeof word.phonetic).toBe('object');
      expect(Array.isArray(word.wordFormats)).toBe(true);

      // 单词名称非空验证
      expect(word.word.trim()).not.toBe('');

      // 音标结构验证
      expect(word.phonetic).toHaveProperty('uk');
      expect(word.phonetic).toHaveProperty('us');
      expect(typeof word.phonetic.uk).toBe('string');
      expect(typeof word.phonetic.us).toBe('string');

      // 词性解释验证
      expect(word.explain.length).toBeGreaterThan(0);
      word.explain.forEach((partOfSpeech) => {
        expect(partOfSpeech).toHaveProperty('pos');
        expect(partOfSpeech).toHaveProperty('definitions');
        expect(typeof partOfSpeech.pos).toBe('string');
        expect(Array.isArray(partOfSpeech.definitions)).toBe(true);
        expect(partOfSpeech.pos.trim()).not.toBe('');
        expect(partOfSpeech.definitions.length).toBeGreaterThan(0);

        // 验证每个定义的结构
        partOfSpeech.definitions.forEach((definition) => {
          expect(definition).toHaveProperty('definition');
          expect(definition).toHaveProperty('chinese');
          expect(definition).toHaveProperty('chinese_short');
          expect(typeof definition.definition).toBe('string');
          expect(typeof definition.chinese).toBe('string');
          expect(typeof definition.chinese_short).toBe('string');
          expect(definition.definition.trim()).not.toBe('');
          expect(definition.chinese.trim()).not.toBe('');
          expect(definition.chinese_short.trim()).not.toBe('');
        });
      });

      // 单词形式验证（可能为空数组）
      word.wordFormats.forEach((format) => {
        expect(format).toHaveProperty('name');
        expect(format).toHaveProperty('form');
        expect(typeof format.name).toBe('string');
        expect(typeof format.form).toBe('string');
        expect(format.name.trim()).not.toBe('');
        expect(format.form.trim()).not.toBe('');
      });
    });

    // 验证单词列表内容与实际单词匹配
    const actualWords = result.data.words.map((w) => w.word);
    expect(result.data.wordList).toEqual(expect.arrayContaining(actualWords));
    expect(actualWords).toEqual(expect.arrayContaining(result.data.wordList));
  });

  it('should handle invalid input gracefully', async () => {
    const result = await fetchDictionary('   ,,,   ');
    // console.log('Invalid input result:', JSON.stringify(result, null, 2));

    // 验证基本结构
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    expect(result).toHaveProperty('statusCode');

    // 验证错误状态码
    expect(result.statusCode).toBe(HttpStatusCode.BAD_REQUEST);

    // 验证错误信息存在
    expect(result).toHaveProperty('error');
    expect(result.error).toBeDefined();

    // 验证数据不存在（因为是错误情况）
    expect(result.data).toBeUndefined();
  });

  //   stdout | app / lib / 5 - providers / test / gemini / gemini.test.ts > Gemini Provider 连通性测试 基础结构测试 > should handle invalid input gracefully
  // Invalid input result: {
  //     "error": {
  //       "message": "全部词条非法，已取消查询"
  //     },
  //     "info": {
  //       "invalidTokens": []
  //     },
  //     "statusCode": 400
  //   }

  it('should handle mixed valid and invalid tokens', async () => {
    const result = await fetchDictionary('hello,   , world,123invalid');
    // console.log('Mixed tokens result:', JSON.stringify(result, null, 2));

    // 验证基本结构
    expect(result).toBeDefined();
    expect(typeof result).toBe('object');
    expect(result).toHaveProperty('statusCode');

    // 应该成功处理有效词条
    expect(result.statusCode).toBe(HttpStatusCode.OK);

    // 验证数据存在
    expect(result).toHaveProperty('data');
    expect(result.data).toBeDefined();

    // 验证无效词条信息
    expect(result).toHaveProperty('info');
    expect(result.info).toBeDefined();

    // 类型守卫：确保 info 存在后再进行后续验证
    if (!result.info) {
      throw new Error('result.info is undefined');
    }

    expect(result.info).toHaveProperty('invalidTokens');
    expect(Array.isArray(result.info.invalidTokens)).toBe(true);

    if (result.info.invalidTokens) {
      expect(result.info.invalidTokens.length).toBeGreaterThan(0);
    }
  });

  it('should handle illegal words', async () => {
    const result = await fetchDictionary('hello,world,123invalid,aapple,1password,k3s,4ever'); //123invalid 是非法词条 会被调入 LLM 之前拦截
    // console.log('Illegal words result:', JSON.stringify(result, null, 2));
  });
});

const words10 = `bananas,dated,dating,cherries,banana,cherry,date,elderberry,fig,grape,dates,d, didn't, i'm, didn`;
const words50 =
  'apple,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla,watermelon,xigua,yam,zucchini,apricot,blackberry,cantaloupe,dragonfruit,eggplant,feijoa,guava,hackberry,imbe,jackfruit,kumquat,lime,mulberry,nutmeg,olive,peach,plum,quararibea,rambutan,soursop,tomato,ugni,voavanga,wolfberry,yumberry,ziziphus';
const words100 =
  'apple,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla,watermelon,xigua,yam,zucchini,apricot,blackberry,cantaloupe,dragonfruit,eggplant,feijoa,guava,hackberry,imbe,jackfruit,kumquat,lime,mulberry,nutmeg,olive,peach,plum,quararibea,rambutan,soursop,tomato,ugni,voavanga,wolfberry,yumberry,ziziphus,almond,brazilnut,cashew,durian,endive,fennel,grapefruit,hazelnut,iceberg,jujube,kale,lettuce,mandarin,navel,okra,persimmon,quandong,radish,spinach,turnip,upland,valencia,wasabi,ximenia,yamamomo,zostera,avocado,bilberry,coconut,damson,elder,fingerlime,gooseberry,huckleberry,ilama,jaboticaba,kiwano,longan,miracle,nashi,pineapple,quenepa,roseapple,sapote,tamarind,watercress,yacon';
const words500 =
  'apple,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla,watermelon,xigua,yam,zucchini,apricot,blackberry,cantaloupe,dragonfruit,eggplant,feijoa,guava,hackberry,imbe,jackfruit,kumquat,lime,mulberry,nutmeg,olive,peach,plum,quararibea,rambutan,soursop,tomato,ugni,voavanga,wolfberry,yumberry,ziziphus,almond,brazilnut,cashew,durian,endive,fennel,grapefruit,hazelnut,iceberg,jujube,kale,lettuce,mandarin,navel,okra,persimmon,quandong,radish,spinach,turnip,upland,valencia,wasabi,ximenia,yamamomo,zostera,avocado,bilberry,coconut,damson,elder,fingerlime,gooseberry,huckleberry,ilama,jaboticaba,kiwano,longan,miracle,nashi,pineapple,quenepa,roseapple,sapote,tamarind,watercress,yacon,acai,boysenberry,currant,dewberry,eucalyptus,foxnut,ginger,hibiscus,inknut,juniper,kohlrabi,leek,melon,nut,onion,pear,quinoa,rutabaga,squash,truffle,ucuhuba,viburnum,walnut,xoconostle,yuzu,zedoary,artichoke,bergamot,chicory,daikon,elderflower,fennel,galangal,horseradish,indianfig,jicama,kangkong,lovage,marjoram,nutmeg,oregano,parsley,quinoa,rosemary,saffron,thyme,ulluco,verbena,wintermelon,xanthium,yarrow,zeodary,arugula,basil,chive,dill,epazote,fenugreek,garlic,houttuynia,inula,jasmine,kaffir,lemongrass,mint,nasturtium,opal,parsnip,rhubarb,sage,tarragon,uziza,vanilla,watermint,xylitol,ylangylang,zest,acerola,bearberry,chokeberry,dogwood,emblic,farkleberry,goumi,honeyberry,indianjujube,juneberry,kelp,lingonberry,mayapple,nannyberry,ogeechee,pawpaw,quince,redcurrant,serviceberry,teaberry,uva,viburnum,whitebeam,xylocarpus,yangmei,zante,abiu,babaco,calamondin,duku,etrog,fackelberry,guanabana,hondapara,indianalmond,jabuticaba,kaffir,langsat,mamoncillo,nance,oroblanco,poha,quince,rangpur,salak,tamarillo,umbra,voacanga,whiteapple,xylosma,yellowpassion,zapote,anise,barley,corn,dallisgrass,emmer,farro,grain,hops,indianrice,jowar,kamut,lupine,millet,niacin,oats,pearl,quinoa,rice,sorghum,teff,urad,velvetbean,wheat,ximenia,yardlong,zweiback,adzuki,blackeye,chickpea,drumstick,edamame,fava,garbanzo,hyacinth,indigowild,jackbean,kidney,lentil,mung,navy,oval,peanut,quail,red,snow,tepary,uguisu,vetch,winged,xanthocercis,yardlong,zolfino,amaranth,buckwheat,chia,dropseed,einhorn,fonio,groats,hemp,indianwheat,jobs,kaniwa,lima,maize,nopal,orzo,popcorn,quinoa,rye,spelt,triticale,udon,vermicelli,wheatberry,xylomelum,yardflong,zea,aubergine,beetroot,carrot,dolichos,eelgrass,flax,gherkin,hydrilla,ipomoea,jerusalem,kochia,laver,marrow,najas,orache,pokeweed,quinoa,romaine,swiss,turmeric,urtica,valerian,watsonia,xylocarpus,yautia,zinnia,ash,beech,cedar,dove,elm,fir,gum,hemlock,ironwood,juniper,kapok,larch,maple,neem,oak,palm,quandong,redwood,sequoia,teak,ulmus,valonia,willow,xylia,yellowwood,zelkova,acorn,beechnut,chestnut,drupelet,eucalyptusnut,filbert,groundnut,hickory,illipe,jobo,karité,lotus,macadamia,nangai,objectnut,pecan,queensland,ramón,saba,torreya,ucuúba,vitellaria,wingnut,xanthoceras,yokan,zamia,atemoya,breadfruit,cherimoya,duguetia,elephant,falsa,granadilla,hog,ilama,jagua,keppel,lucuma,mammee,naranjilla,obliqua,poshte,quararibea,rollinia,soncoya,theobroma,uvilla,velvet,wampee,ximenia,yellow,zwetschge,anchovy,barracuda,cod,dolphinfish,eel,flounder,grouper,halibut,ilish,jack,kingfish,ling,mackerel,nori,octopus,perch,queenfish,ray,salmon,tuna,unicornfish,wahoo,yellowtail,zander';
// End of Selection

describe('Gemini Provider 压力测试', () => {
  it('should handle 1 words', async () => {
    const startTime = performance.now();
    // const result = await fetchDictionary('t');
    const result = await fetchDictionary(`d, didn't, i'm, didn`);
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    console.log(`Execution time: ${executionTime.toFixed(2)}ms`);
    console.log('1 words result:', JSON.stringify(result, null, 2));
    //清空文件
    fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words1.json'), '');
    fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words1.json'), JSON.stringify(result, null, 2));
  });

  it('should handle 10 words', async () => {
    // 15394ms
    const startTime = performance.now();
    const result = await fetchDictionary(words10);
    const endTime = performance.now();
    const executionTime = endTime - startTime;
    console.log(`Execution time: ${executionTime.toFixed(2)}ms`);
    console.log('10 words result:', JSON.stringify(result, null, 2));
    //清空文件
    fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words1.json'), '');
    fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words10.json'), JSON.stringify(result, null, 2));
  });

  // it('should handle 50 words', async () => {
  //   // 47097.87ms
  //   const startTime = performance.now();
  //   const result = await fetchDictionary(words50);
  //   const endTime = performance.now();
  //   const executionTime = endTime - startTime;
  //   console.log(`Execution time: ${executionTime.toFixed(2)}ms`);
  //   //清空文件
  //   fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words1.json'), '');
  //   fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words50.json'), JSON.stringify(result, null, 2));
  //   // expect(executionTime).toBeLessThan(10000);
  // });

  // it('should handle 100 words', async () => {
  //   // 100 words 需要 90s 左右
  //   const startTime = performance.now();
  //   const result = await fetchDictionary(words100);
  //   const endTime = performance.now();
  //   const executionTime = endTime - startTime;
  //   console.log(`Execution time: ${executionTime.toFixed(2)}ms`);
  //   //清空文件
  //   fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words1.json'), '');
  //   fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words100.json'), JSON.stringify(result, null, 2));
  //   // expect(executionTime).toBeLessThan(20000);
  // });

  // it('should handle 500 words', async () => {
  //   const startTime = performance.now();
  //   const result = await fetchDictionary(words500);
  //   const endTime = performance.now();
  //   const executionTime = endTime - startTime;
  //   console.log(`Execution time: ${executionTime.toFixed(2)}ms`);
  //   fs.writeFileSync(path.join(TEST_RESULTS_DIR, 'words500.json'), JSON.stringify(result, null, 2));
  //   // expect(executionTime).toBeLessThan(60000);
  // });

  //     stdout | app / lib / providers / test / gemini / gemini.test.ts > Gemini Provider 压力测试 > should handle 200 words parallel
  // API call completed in 70.60 seconds
  // Gemini API调用耗时: 70.61 s

  //     stdout | app / lib / providers / test / gemini / gemini.test.ts > Gemini Provider 压力测试 > should handle 200 words parallel
  // API call completed in 86.04 seconds
  // Gemini API调用耗时: 86.04 s

  // 并行调用 200 words 需要 70s 左右
  // it('should handle 200 words parallel', async () => {
  //   const startTime = performance.now();
  //   // 正确的并行调用方式
  //   const [result1, result2] = await Promise.all([
  //     fetchDictionary(words100),
  //     fetchDictionary(words100),
  //   ]);
  //   const endTime = performance.now();
  //   const executionTime = endTime - startTime;
  //   console.log(`Execution time: ${executionTime.toFixed(2)}ms`);
  //   fs.writeFileSync(
  //     path.join(TEST_RESULTS_DIR, 'words200.json'),
  //     JSON.stringify([result1, result2], null, 2)
  //   );
  // });
});
