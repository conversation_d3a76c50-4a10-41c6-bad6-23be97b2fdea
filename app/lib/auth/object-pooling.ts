// Object pooling for memory optimization
export interface PooledObject {
  reset(): void;
  isInUse(): boolean;
  setInUse(inUse: boolean): void;
}

export class ObjectPool<T extends PooledObject> {
  private pool: T[] = [];
  private factory: () => T;
  private maxSize: number;
  private inUseCount = 0;
  private totalCreated = 0;

  constructor(factory: () => T, maxSize: number = 100) {
    this.factory = factory;
    this.maxSize = maxSize;
  }

  acquire(): T {
    let obj = this.pool.pop();

    if (!obj) {
      obj = this.factory();
      this.totalCreated++;
    }

    obj.setInUse(true);
    this.inUseCount++;
    return obj;
  }

  release(obj: T): void {
    if (!obj.isInUse()) return;

    obj.reset();
    obj.setInUse(false);
    this.inUseCount--;

    if (this.pool.length < this.maxSize) {
      this.pool.push(obj);
    }
  }

  getStats() {
    return {
      poolSize: this.pool.length,
      inUse: this.inUseCount,
      totalCreated: this.totalCreated,
      efficiency: (this.totalCreated - this.inUseCount) / this.totalCreated || 0,
    };
  }
}

// Request context object for pooling
export class PooledRequestContext implements PooledObject {
  private _inUse = false;
  public requestId: string = '';
  public timestamp: number = 0;
  public clientIP: string = '';
  public userAgent: string = '';
  public fingerprint: string = '';
  public threatLevel: number = 0;
  public securityFlags: Set<string> = new Set();

  reset(): void {
    this.requestId = '';
    this.timestamp = 0;
    this.clientIP = '';
    this.userAgent = '';
    this.fingerprint = '';
    this.threatLevel = 0;
    this.securityFlags.clear();
  }

  isInUse(): boolean {
    return this._inUse;
  }

  setInUse(inUse: boolean): void {
    this._inUse = inUse;
  }
}

// Security result object for pooling
export class PooledSecurityResult implements PooledObject {
  private _inUse = false;
  public isAllowed: boolean = true;
  public riskScore: number = 0;
  public threats: string[] = [];
  public processingTime: number = 0;
  public cacheHit: boolean = false;
  public metadata: Record<string, any> = {};

  reset(): void {
    this.isAllowed = true;
    this.riskScore = 0;
    this.threats.length = 0;
    this.processingTime = 0;
    this.cacheHit = false;
    this.metadata = {};
  }

  isInUse(): boolean {
    return this._inUse;
  }

  setInUse(inUse: boolean): void {
    this._inUse = inUse;
  }
}

// Threat analysis object for pooling
export class PooledThreatAnalysis implements PooledObject {
  private _inUse = false;
  public patterns: RegExp[] = [];
  public matches: Array<{ pattern: string; confidence: number }> = [];
  public severity: 'low' | 'medium' | 'high' = 'low';
  public recommendation: string = '';
  public timestamp: number = 0;

  reset(): void {
    this.patterns.length = 0;
    this.matches.length = 0;
    this.severity = 'low';
    this.recommendation = '';
    this.timestamp = 0;
  }

  isInUse(): boolean {
    return this._inUse;
  }

  setInUse(inUse: boolean): void {
    this._inUse = inUse;
  }
}

// Pool manager for all security objects
export class SecurityObjectPoolManager {
  private contextPool: ObjectPool<PooledRequestContext>;
  private resultPool: ObjectPool<PooledSecurityResult>;
  private threatPool: ObjectPool<PooledThreatAnalysis>;

  constructor(poolSize: number = 150) {
    this.contextPool = new ObjectPool(() => new PooledRequestContext(), poolSize);
    this.resultPool = new ObjectPool(() => new PooledSecurityResult(), poolSize);
    this.threatPool = new ObjectPool(() => new PooledThreatAnalysis(), poolSize);
  }

  acquireContext(): PooledRequestContext {
    return this.contextPool.acquire();
  }

  releaseContext(context: PooledRequestContext): void {
    this.contextPool.release(context);
  }

  acquireResult(): PooledSecurityResult {
    return this.resultPool.acquire();
  }

  releaseResult(result: PooledSecurityResult): void {
    this.resultPool.release(result);
  }

  acquireThreatAnalysis(): PooledThreatAnalysis {
    return this.threatPool.acquire();
  }

  releaseThreatAnalysis(analysis: PooledThreatAnalysis): void {
    this.threatPool.release(analysis);
  }

  getStats() {
    return {
      context: this.contextPool.getStats(),
      result: this.resultPool.getStats(),
      threat: this.threatPool.getStats(),
    };
  }
}

// Memory-efficient string pool for frequently used strings
export class StringPool {
  private pool = new Map<string, string>();
  private usage = new Map<string, number>();
  private maxSize: number;

  constructor(maxSize: number = 1000) {
    this.maxSize = maxSize;
  }

  intern(str: string): string {
    const existing = this.pool.get(str);
    if (existing) {
      this.usage.set(str, (this.usage.get(str) || 0) + 1);
      return existing;
    }

    if (this.pool.size >= this.maxSize) {
      // Evict least used string
      const leastUsed = Array.from(this.usage.entries()).sort((a, b) => a[1] - b[1])[0];

      if (leastUsed) {
        this.pool.delete(leastUsed[0]);
        this.usage.delete(leastUsed[0]);
      }
    }

    this.pool.set(str, str);
    this.usage.set(str, 1);
    return str;
  }

  getStats() {
    return {
      poolSize: this.pool.size,
      totalUsage: Array.from(this.usage.values()).reduce((a, b) => a + b, 0),
      averageUsage:
        Array.from(this.usage.values()).reduce((a, b) => a + b, 0) / this.usage.size || 0,
    };
  }
}

// Buffer pool for efficient memory management
export class BufferPool {
  private pools = new Map<number, Buffer[]>();
  private maxPoolSize: number;

  constructor(maxPoolSize: number = 50) {
    this.maxPoolSize = maxPoolSize;
  }

  acquire(size: number): Buffer {
    const pool = this.pools.get(size) || [];
    const buffer = pool.pop();

    if (buffer) {
      buffer.fill(0); // Clear buffer
      return buffer;
    }

    return Buffer.allocUnsafe(size);
  }

  release(buffer: Buffer): void {
    const size = buffer.length;
    const pool = this.pools.get(size) || [];

    if (pool.length < this.maxPoolSize) {
      pool.push(buffer);
      this.pools.set(size, pool);
    }
  }

  getStats() {
    const stats: Record<number, number> = {};
    for (const [size, pool] of this.pools) {
      stats[size] = pool.length;
    }
    return stats;
  }
}

// Factory function for creating optimized pool manager
export function createOptimizedPoolManager(
  options: {
    poolSize?: number;
    stringPoolSize?: number;
    bufferPoolSize?: number;
  } = {}
): {
  objectPool: SecurityObjectPoolManager;
  stringPool: StringPool;
  bufferPool: BufferPool;
} {
  return {
    objectPool: new SecurityObjectPoolManager(options.poolSize || 150),
    stringPool: new StringPool(options.stringPoolSize || 1000),
    bufferPool: new BufferPool(options.bufferPoolSize || 50),
  };
}

// Memory usage monitor
export class MemoryMonitor {
  private samples: number[] = [];
  private maxSamples: number;

  constructor(maxSamples: number = 100) {
    this.maxSamples = maxSamples;
  }

  recordUsage(): void {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      const usage = process.memoryUsage();
      this.samples.push(usage.heapUsed);

      if (this.samples.length > this.maxSamples) {
        this.samples.shift();
      }
    }
  }

  getStats() {
    if (this.samples.length === 0) return null;

    const sum = this.samples.reduce((a, b) => a + b, 0);
    const avg = sum / this.samples.length;
    const sorted = [...this.samples].sort((a, b) => a - b);

    return {
      average: avg,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      samples: this.samples.length,
    };
  }
}
