{"name": "lucid-bd", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "node scripts/start-dev.js", "build": "next build", "start": "next start", "lint": "eslint --config .config/eslint.config.js .", "prepare": "husky", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "lint-staged-eslint": "eslint --config .config/eslint.config.js --fix --ignore-path .eslintignore", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:auth": "tsx scripts/test-auth-clean.ts", "test:auth:vitest": "vitest run app/test/auth-system-clean.test.ts", "test:password-reset": "tsx scripts/test-password-reset.ts", "test:import": "node --loader ts-node/esm app/test/importDictionaryData.test.ts", "test:import:force": "node --loader ts-node/esm app/test/importDictionaryData.test.ts --force", "test:import:clear": "node --loader ts-node/esm app/lib/6-scripts/llm-response-to-db/importDictionaryData.test.ts --clear", "sync:freq-words": "tsx app/lib/6-scripts/sync6w/run-sync.ts", "sync:freq-words:test": "tsx app/lib/6-scripts/sync6w/test-small-sync.ts", "sync:freq-words:safe": "tsx app/lib/6-scripts/sync6w/conservative-sync.ts", "sync:retry-missing": "tsx app/lib/6-scripts/sync6w/retry-missing-words.ts", "sync:retry-failed": "tsx app/lib/6-scripts/sync6w/retry-failed-requests.ts", "sync:analyze-failures": "tsx app/lib/6-scripts/sync6w/analyze-failures.ts", "sync:demo": "tsx app/lib/6-scripts/sync6w/demo-retry-missing.ts", "test:redis": "tsx app/lib/6-scripts/test-redis-integration.ts", "cache:clear": "curl -X POST http://localhost:3000/api/cache/clear -H 'Content-Type: application/json' -d '{\"action\":\"all\"}'", "cache:stats": "curl -X GET http://localhost:3000/api/cache/stats", "redis:start": "docker compose up -d redis redisinsight", "redis:stop": "docker compose stop redis redisinsight", "redis:logs": "docker compose logs -f redis redisinsight", "redisinsight:open": "open http://localhost:5540", "redisinsight:fix": "tsx app/lib/6-scripts/fix-redisinsight-connection.ts", "redis:demo": "tsx app/lib/6-scripts/populate-redis-demo-data.ts", "env:show": "tsx scripts/configure-env.ts show", "env:list": "tsx scripts/configure-env.ts list", "env:dev": "tsx scripts/configure-env.ts apply development", "env:debug": "tsx scripts/configure-env.ts apply debug", "env:perf": "tsx scripts/configure-env.ts apply high-concurrency", "env:prod": "tsx scripts/configure-env.ts apply production", "env:test": "tsx scripts/configure-env.ts apply performance-test", "env:help": "tsx scripts/configure-env.ts help", "db:backup": "tsx app/lib/6-scripts/database/prisma/backup-database.ts", "db:backup:schema": "tsx app/lib/6-scripts/database/prisma/backup-database.ts --type=schema", "db:backup:data": "tsx app/lib/6-scripts/database/prisma/backup-database.ts --type=data", "db:restore": "tsx app/lib/6-scripts/database/prisma/restore-database.ts", "db:restore:list": "tsx app/lib/6-scripts/database/prisma/restore-database.ts --list", "db:backup:list": "tsx app/lib/6-scripts/database/prisma/backup-manager.ts list", "db:backup:stats": "tsx app/lib/6-scripts/database/prisma/backup-manager.ts stats", "db:backup:cleanup": "tsx app/lib/6-scripts/database/prisma/backup-manager.ts cleanup", "db:backup:verify": "tsx app/lib/6-scripts/database/prisma/backup-manager.ts verify", "auth:setup": "tsx app/lib/6-scripts/auth/setup-auth.ts", "auth:test": "tsx app/lib/6-scripts/auth/test-auth.ts", "auth:tokens:cleanup": "tsx app/lib/6-scripts/auth/cleanup-tokens.ts", "auth:users:list": "tsx app/lib/6-scripts/auth/list-users.ts", "auth:reset": "tsx app/lib/6-scripts/auth/reset-auth.ts", "e2e": "playwright test", "e2e:ui": "playwright test --ui", "e2e:debug": "playwright test --debug", "e2e:headed": "playwright test --headed", "e2e:chrome": "playwright test --project=chromium-desktop", "e2e:firefox": "playwright test --project=firefox-desktop", "e2e:safari": "playwright test --project=webkit-desktop", "e2e:mobile": "playwright test --project=mobile-chrome --project=mobile-safari", "e2e:auth": "playwright test e2e/auth/", "e2e:security": "playwright test e2e/security/", "e2e:performance": "playwright test e2e/performance/", "e2e:visual": "playwright test e2e/visual/", "e2e:journey": "playwright test e2e/user-journey/", "e2e:report": "playwright show-report e2e-results/html-report", "e2e:install": "playwright install", "e2e:update-snapshots": "playwright test --update-snapshots", "e2e:ci": "playwright test --reporter=github", "test:all": "npm run test && npm run e2e", "storybook": "storybook dev -p 6006", "storybook:build": "storybook build", "storybook:test": "test-storybook", "storybook:serve": "serve storybook-static"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@google/genai": "^1.0.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.8.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tabler/icons-react": "^3.34.0", "@tanstack/react-table": "^8.21.3", "@types/generic-pool": "^3.8.3", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto": "^1.0.1", "express-rate-limit": "^8.0.0", "generic-pool": "^3.9.0", "helmet": "^8.1.0", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.26.0", "json-schema": "^0.4.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "next": "15.3.2", "next-auth": "^4.24.0", "next-themes": "^0.4.6", "openai": "^4.100.0", "react": "^19.1.0", "react-dom": "^19.1.0", "recharts": "2.15.4", "resend": "^4.7.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "validator": "^13.15.15", "vaul": "^1.1.2", "xlsx": "^0.18.5", "zod": "^3.25.16", "zod-to-json-schema": "^3.24.5"}, "devDependencies": {"@playwright/test": "^1.54.1", "@storybook/addon-a11y": "^8.6.14", "@storybook/addon-actions": "^8.6.14", "@storybook/addon-controls": "^8.6.14", "@storybook/addon-essentials": "^8.6.14", "@storybook/addon-interactions": "^8.6.14", "@storybook/addon-links": "^8.6.14", "@storybook/addon-onboarding": "^8.6.14", "@storybook/addon-viewport": "^8.6.14", "@storybook/blocks": "^8.6.14", "@storybook/react": "^8.6.14", "@storybook/react-vite": "^8.6.14", "@storybook/test": "^8.6.14", "@storybook/theming": "^8.6.14", "@tailwindcss/postcss": "^4.1.7", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^3.0.0", "@types/express-rate-limit": "^6.0.2", "@types/json-schema": "^7.0.15", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22.15.21", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/xlsx": "^0.0.36", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.6.0", "@vitest/coverage-v8": "^3.1.4", "@vitest/ui": "^3.1.4", "buffer": "^6.0.3", "critters": "^0.0.25", "crypto-browserify": "^3.12.1", "dotenv": "^16.5.0", "eslint": "9.27.0", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "msw": "^2.8.4", "prettier": "^3.5.3", "prisma": "^6.8.2", "process": "^0.11.10", "storybook": "^8.6.14", "tailwindcss": "^4.1.7", "ts-node": "^10.9.2", "tsx": "^4.19.4", "tw-animate-css": "^1.3.5", "typescript": "^5.8.3", "vite": "^5.4.11", "vitest": "^3.1.4"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"], "*.{json,md}": ["prettier --write"]}}