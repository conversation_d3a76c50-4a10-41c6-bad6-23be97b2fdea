import {
  WordListEntry,
  CreateWordListEntryData,
  UpdateWordListEntryData,
  WordListEntryQueryOptions,
  WordListPracticeWord,
  WordListPracticeSession
} from '../3-domain/entities/WordListEntry';

/**
 * WordListEntry Repository Interface
 * 定义词库条目数据访问层的接口
 */
export interface IWordListEntryRepository {
  /**
   * 查找词库条目
   */
  findMany(options: WordListEntryQueryOptions): Promise<WordListEntry[]>;

  /**
   * 根据ID获取词库条目
   */
  findById(id: number): Promise<WordListEntry | null>;

  /**
   * 根据单词获取条目
   */
  findByWord(word: string): Promise<WordListEntry | null>;

  /**
   * 创建词库条目
   */
  create(data: CreateWordListEntryData): Promise<WordListEntry>;

  /**
   * 更新词库条目
   */
  update(id: number, data: UpdateWordListEntryData): Promise<WordListEntry>;

  /**
   * 删除词库条目
   */
  delete(id: number): Promise<void>;

  /**
   * 批量创建词库条目
   */
  createMany(data: CreateWordListEntryData[]): Promise<WordListEntry[]>;

  /**
   * 获取词库的练习单词（包含用户数据）
   */
  getPracticeWords(wordListId: number, userId: string, options: WordListEntryQueryOptions): Promise<WordListPracticeWord[]>;

  /**
   * 获取练习会话数据
   */
  getPracticeSession(wordListId: number, userId: string, options: WordListEntryQueryOptions): Promise<WordListPracticeSession>;

  /**
   * 更新单词在词库中的关联关系
   */
  updateWordListAssociation(word: string, wordListIds: number[]): Promise<WordListEntry>;
}

/**
 * WordListEntry Repository Implementation
 * 使用Prisma实现词库条目数据访问
 */
import { prisma } from '../4-infrastructure/database/prisma';

export class WordListEntryRepository implements IWordListEntryRepository {

  async findMany(options: WordListEntryQueryOptions): Promise<WordListEntry[]> {
    const where: any = {};

    if (options.wordListId) {
      where.wordListId = options.wordListId;
    }

    if (options.wordListIds && options.wordListIds.length > 0) {
      where.wordListIds = {
        hasSome: options.wordListIds
      };
    }

    if (options.word) {
      where.word = {
        contains: options.word,
        mode: 'insensitive'
      };
    }

    if (options.wordPattern) {
      where.word = {
        contains: options.wordPattern,
        mode: 'insensitive'
      };
    }

    const query: any = {
      where,
      orderBy: options.random ? undefined : { word: 'asc' }
    };

    if (options.limit) {
      query.take = options.limit;
    }

    if (options.offset !== undefined) {
      query.skip = options.offset;
    }

    let entries = await prisma.wordListEntry.findMany(query);

    // 如果需要随机排序
    if (options.random && entries.length > 0) {
      entries = this.shuffleArray(entries);
      if (options.limit) {
        entries = entries.slice(0, options.limit);
      }
    }

    return entries.map(this.mapToEntity);
  }

  async findById(id: number): Promise<WordListEntry | null> {
    const entry = await prisma.wordListEntry.findUnique({
      where: { id }
    });

    return entry ? this.mapToEntity(entry) : null;
  }

  async findByWord(word: string): Promise<WordListEntry | null> {
    const entry = await prisma.wordListEntry.findFirst({
      where: { word: word }
    });

    return entry ? this.mapToEntity(entry) : null;
  }

  async create(data: CreateWordListEntryData): Promise<WordListEntry> {
    const word = typeof data.word === 'string' ? data.word : String(data.word);
    const entry = await prisma.wordListEntry.create({
      data: {
        word: word.toLowerCase().trim(),
        wordListIds: data.wordListIds,
        wordListId: data.wordListId
      }
    });

    return this.mapToEntity(entry);
  }

  async update(id: number, data: UpdateWordListEntryData): Promise<WordListEntry> {
    const entry = await prisma.wordListEntry.update({
      where: { id },
      data: {
        ...(data.word && { word: (typeof data.word === 'string' ? data.word : String(data.word)).toLowerCase().trim() }),
        ...(data.wordListIds && { wordListIds: data.wordListIds }),
        ...(data.wordListId && { wordListId: data.wordListId })
      }
    });

    return this.mapToEntity(entry);
  }

  async delete(id: number): Promise<WordListEntry> {
    const entry = await prisma.wordListEntry.delete({
      where: { id }
    });
    return this.mapToEntity(entry);
  }

  async createMany(data: CreateWordListEntryData[]): Promise<WordListEntry[]> {
    const entries = await prisma.wordListEntry.createMany({
      data: data.map(item => ({
        word: item.word.toLowerCase().trim(),
        wordListIds: item.wordListIds,
        wordListId: item.wordListId
      })),
      skipDuplicates: true
    });

    // 由于createMany不返回创建的记录，我们需要重新查询
    const createdWords = data.map(item => item.word.toLowerCase().trim());
    const createdEntries = await prisma.wordListEntry.findMany({
      where: {
        word: { in: createdWords }
      }
    });

    return createdEntries.map(this.mapToEntity);
  }

  async getPracticeWords(
    wordListId: number,
    userId: string,
    options: WordListEntryQueryOptions
  ): Promise<WordListPracticeWord[]> {
    const entries = await this.findMany({
      ...options,
      wordListIds: [wordListId]
    });

    const practiceWords: WordListPracticeWord[] = [];

    for (const entry of entries) {
      // 获取用户对此单词的熟练度数据
      const userProficiency = await prisma.userWordProficiency.findFirst({
        where: {
          userId,
          vocabulary: {
            word: entry.word
          }
        },
        include: {
          vocabulary: {
            include: {
              explains: {
                include: {
                  definitions: true
                }
              }
            }
          }
        }
      });

      const practiceWord: WordListPracticeWord = {
        id: entry.id,
        word: entry.word,
        wordListIds: entry.wordListIds,
        practiceCount: userProficiency?.practiceCount ?? 0,
        errorCount: userProficiency?.errorCount ?? 0,
        averageTime: userProficiency?.averageTime ?? 0,
        proficiencyScore: userProficiency?.proficiencyScore ?? 0,
        lastPracticed: userProficiency?.lastPracticed ?? undefined,
        isMarked: userProficiency?.isMarked ?? false
      };

      // 添加词汇信息
      if (userProficiency?.vocabulary) {
        practiceWord.phonetics = userProficiency.vocabulary.phonetics;
        practiceWord.definitions = userProficiency.vocabulary.explains.map(explain => ({
          pos: explain.pos,
          definition: explain.definitions[0]?.definition ?? '',
          chinese: explain.definitions[0]?.chinese ?? ''
        }));
      }

      practiceWords.push(practiceWord);
    }

    return practiceWords;
  }

  async getPracticeSession(
    wordListId: number,
    userId: string,
    options: WordListEntryQueryOptions
  ): Promise<WordListPracticeSession> {
    const pageSize = options.limit ?? 20;
    const currentPage = Math.floor((options.offset ?? 0) / pageSize) + 1;

    // 获取总单词数
    const totalWords = await prisma.wordListEntry.count({
      where: {
        wordListIds: {
          has: wordListId
        }
      }
    });

    const words = await this.getPracticeWords(wordListId, userId, options);

    return {
      wordListId,
      words,
      sessionConfig: {
        pageSize,
        currentPage,
        totalWords,
        isRandomized: options.random ?? false,
        filterDifficult: false, // 可以根据需要实现
        skipMastered: false // 可以根据需要实现
      }
    };
  }

  async updateWordListAssociation(word: string, wordListIds: number[]): Promise<WordListEntry> {
    // 查找现有条目
    const existingEntry = await this.findByWord(word);

    if (existingEntry) {
      // 合并词库ID数组
      const mergedWordListIds = Array.from(new Set([...existingEntry.wordListIds, ...wordListIds]));

      return this.update(existingEntry.id, {
        wordListIds: mergedWordListIds
      });
    } else {
      // 创建新条目
      return this.create({
        word,
        wordListIds,
        wordListId: wordListIds[0] // 使用第一个词库ID作为主要关联
      });
    }
  }

  /**
   * 随机打乱数组
   */
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  /**
   * 将Prisma模型映射为领域实体
   */
  private mapToEntity(model: any): WordListEntry {
    return {
      id: model.id,
      word: model.word,
      wordListIds: model.wordListIds,
      wordListId: model.wordListId,
      createdAt: model.createdAt,
      updatedAt: model.updatedAt
    };
  }
}