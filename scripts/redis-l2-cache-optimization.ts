#!/usr/bin/env tsx

/**
 * Redis L2缓存优化测试
 * 专门测试L2缓存层的高并发性能
 *
 * 测试策略：
 * 1. 预热阶段：建立完整缓存
 * 2. 清理API缓存：只保留L2缓存
 * 3. 高并发测试：验证L2缓存在高并发下的表现
 * 4. 性能分析：对比不同缓存层的性能
 */

const baseUrl = 'http://localhost:3000';

interface TestMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  avgResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  cacheHitRate: number;
  databaseHitRate: number;
  redisHitRate: number;
  memoryHitRate: number;
}

interface ConcurrencyTestResult {
  concurrency: number;
  metrics: TestMetrics;
  responseTimes: number[];
  cacheSourceStats: Record<string, number>;
}

/**
 * 执行单个API请求
 */
async function makeRequest(
  word: string,
  fresh: boolean = false
): Promise<{
  success: boolean;
  responseTime: number;
  cacheSource: string;
  status: number;
}> {
  const startTime = performance.now();
  const url = `${baseUrl}/api/dictionary/en/${word}${fresh ? '?fresh=true' : ''}`;

  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'User-Agent': 'Redis-L2-Cache-Test/1.0',
      },
      signal: AbortSignal.timeout(10000),
    });

    const responseTime = performance.now() - startTime;
    const cacheSource = response.headers.get('x-cache-source') || 'unknown';

    return {
      success: response.ok,
      responseTime,
      cacheSource,
      status: response.status,
    };
  } catch (error) {
    return {
      success: false,
      responseTime: performance.now() - startTime,
      cacheSource: 'error',
      status: 0,
    };
  }
}

/**
 * 预热缓存
 */
async function warmupCache(testWords: string[]): Promise<void> {
  console.log('🔥 预热缓存阶段...');
  console.log(`   预热 ${testWords.length} 个单词的缓存`);

  // 串行预热，确保每个单词都被缓存
  for (let i = 0; i < testWords.length; i++) {
    const word = testWords[i];
    const result = await makeRequest(word, true); // fresh=true 确保从数据库获取并缓存
    console.log(
      `   ✅ 预热 ${i + 1}/${testWords.length}: ${word} (${Math.round(result.responseTime)}ms)`
    );
  }

  console.log('✅ 缓存预热完成');

  // 等待缓存写入完成
  await new Promise((resolve) => setTimeout(resolve, 2000));
}

/**
 * 清理API缓存（保留L2缓存）
 */
async function clearApiCache(): Promise<number> {
  console.log('\n🧹 清理API缓存（L1层）...');

  try {
    const response = await fetch(`${baseUrl}/api/cache/clear`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'pattern',
        pattern: 'search:*',
      }),
    });

    if (response.ok) {
      const result = await response.json();
      const deletedCount = result.result?.keysDeleted || 0;
      console.log(`✅ 清理了 ${deletedCount} 个API缓存键`);
      return deletedCount;
    } else {
      console.log('⚠️ API缓存清理失败');
      return 0;
    }
  } catch (error) {
    console.log('⚠️ 无法清理API缓存');
    return 0;
  }
}

/**
 * 执行并发测试
 */
async function runConcurrencyTest(
  testWords: string[],
  concurrency: number
): Promise<ConcurrencyTestResult> {
  console.log(`\n🚀 执行 ${concurrency} 并发测试...`);

  const startTime = performance.now();

  // 创建并发请求
  const promises = Array.from({ length: concurrency }, (_, i) => {
    const word = testWords[i % testWords.length];
    return makeRequest(word, false); // 不使用fresh，测试缓存效果
  });

  const results = await Promise.all(promises);
  const totalTime = performance.now() - startTime;

  // 分析结果
  const successfulResults = results.filter((r) => r.success);
  const responseTimes = successfulResults.map((r) => r.responseTime);
  responseTimes.sort((a, b) => a - b);

  // 计算统计指标
  const p95Index = Math.floor(responseTimes.length * 0.95);
  const p99Index = Math.floor(responseTimes.length * 0.99);

  // 统计缓存来源
  const cacheSourceStats = results.reduce(
    (acc, result) => {
      acc[result.cacheSource] = (acc[result.cacheSource] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  const metrics: TestMetrics = {
    totalRequests: results.length,
    successfulRequests: successfulResults.length,
    failedRequests: results.length - successfulResults.length,
    avgResponseTime:
      responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0,
    p95ResponseTime: responseTimes[p95Index] || 0,
    p99ResponseTime: responseTimes[p99Index] || 0,
    minResponseTime: responseTimes[0] || 0,
    maxResponseTime: responseTimes[responseTimes.length - 1] || 0,
    cacheHitRate: (cacheSourceStats['redis_cache'] || 0) / results.length,
    databaseHitRate: (cacheSourceStats['database'] || 0) / results.length,
    redisHitRate: (cacheSourceStats['redis_cache'] || 0) / results.length,
    memoryHitRate: (cacheSourceStats['memory_cache'] || 0) / results.length,
  };

  console.log(`   完成时间: ${Math.round(totalTime)}ms`);
  console.log(
    `   成功率: ${Math.round((metrics.successfulRequests / metrics.totalRequests) * 100)}%`
  );
  console.log(`   平均响应时间: ${Math.round(metrics.avgResponseTime)}ms`);
  console.log(`   Redis缓存命中率: ${Math.round(metrics.redisHitRate * 100)}%`);

  return {
    concurrency,
    metrics,
    responseTimes,
    cacheSourceStats,
  };
}

/**
 * 打印详细分析报告
 */
function printDetailedAnalysis(results: ConcurrencyTestResult[]): void {
  console.log('\n📊 详细性能分析报告');
  console.log('='.repeat(80));

  // 性能指标表格
  console.log('\n📈 性能指标对比:');
  console.log('并发数 | 平均响应时间 | P95响应时间 | Redis命中率 | 数据库访问率');
  console.log('-'.repeat(70));

  results.forEach((result) => {
    const { concurrency, metrics } = result;
    console.log(
      `${concurrency.toString().padStart(6)} | ` +
        `${Math.round(metrics.avgResponseTime).toString().padStart(10)}ms | ` +
        `${Math.round(metrics.p95ResponseTime).toString().padStart(10)}ms | ` +
        `${Math.round(metrics.redisHitRate * 100)
          .toString()
          .padStart(9)}% | ` +
        `${Math.round(metrics.databaseHitRate * 100)
          .toString()
          .padStart(11)}%`
    );
  });

  // L2缓存效果分析
  console.log('\n🎯 L2缓存效果分析:');
  const bestResult = results.reduce((best, current) =>
    current.metrics.redisHitRate > best.metrics.redisHitRate ? current : best
  );

  console.log(
    `   最佳Redis命中率: ${Math.round(bestResult.metrics.redisHitRate * 100)}% (并发数: ${bestResult.concurrency})`
  );
  console.log(`   最佳平均响应时间: ${Math.round(bestResult.metrics.avgResponseTime)}ms`);

  // 性能建议
  console.log('\n💡 性能优化建议:');
  if (bestResult.metrics.redisHitRate > 0.8) {
    console.log('   ✅ L2缓存效果优秀，Redis命中率超过80%');
  } else if (bestResult.metrics.redisHitRate > 0.5) {
    console.log('   🟡 L2缓存效果一般，建议优化缓存策略');
  } else {
    console.log('   ❌ L2缓存效果不佳，需要检查缓存配置');
  }

  if (bestResult.metrics.avgResponseTime < 100) {
    console.log('   ✅ 响应时间优秀，平均响应时间 < 100ms');
  } else if (bestResult.metrics.avgResponseTime < 300) {
    console.log('   🟡 响应时间良好，平均响应时间 < 300ms');
  } else {
    console.log('   ❌ 响应时间需要优化，建议检查数据库和缓存配置');
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🔧 Redis L2缓存优化测试');
  console.log('='.repeat(80));

  // 测试单词（使用更多单词以更好地测试缓存）
  const testWords = [
    'computer',
    'science',
    'technology',
    'database',
    'network',
    'algorithm',
    'programming',
    'software',
    'hardware',
    'internet',
    'application',
    'development',
    'framework',
    'library',
    'system',
    'security',
    'encryption',
    'authentication',
    'authorization',
    'protocol',
  ];

  try {
    // 1. 预热缓存
    await warmupCache(testWords);

    // 2. 清理API缓存
    await clearApiCache();

    // 等待清理完成
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 3. 执行不同并发级别的测试
    const concurrencyLevels = [5, 10, 20, 50];
    const results: ConcurrencyTestResult[] = [];

    for (const concurrency of concurrencyLevels) {
      const result = await runConcurrencyTest(testWords, concurrency);
      results.push(result);

      // 测试间隔
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }

    // 4. 打印详细分析
    printDetailedAnalysis(results);

    console.log('\n✅ Redis L2缓存优化测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
