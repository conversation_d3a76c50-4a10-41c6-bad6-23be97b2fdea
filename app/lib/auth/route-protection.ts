/**
 * API Route Protection Utilities
 * Provides HOC and decorators for protecting API routes
 *
 * Security Features:
 * - JWT and session authentication
 * - Role-based access control
 * - Rate limiting per route
 * - Request validation
 * - Audit logging
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { AUTH_ERROR_MESSAGES, AuthErrorCode } from '@/lib/types/auth';

export interface AuthenticatedUser {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  provider?: string;
  role?: string;
  permissions?: string[];
}

export interface AuthContext {
  user: AuthenticatedUser;
  authMethod: 'session';
  ipAddress: string;
  userAgent: string;
}

export interface RouteProtectionOptions {
  requireAuth?: boolean;
  roles?: string[];
  permissions?: string[];
  rateLimit?: {
    limit: number;
    window: number; // in milliseconds
  };
  validateUser?: boolean;
}

export type ProtectedRouteHandler = (
  request: NextRequest,
  context: AuthContext,
  params?: any
) => Promise<NextResponse> | NextResponse;

export type RouteHandler = (
  request: NextRequest,
  params?: any
) => Promise<NextResponse> | NextResponse;

/**
 * Higher-order function to protect API routes
 */
export function withAuth(
  handler: ProtectedRouteHandler,
  options: RouteProtectionOptions = {}
): RouteHandler {
  return async (request: NextRequest, params?: any) => {
    const {
      requireAuth = true,
      roles = [],
      permissions = [],
      rateLimit,
      validateUser = true,
    } = options;

    const clientIP = (request as any).ip || request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';

    try {
      // Apply rate limiting if configured
      if (rateLimit) {
        // Rate limiting implementation - simplified for now
        // TODO: Implement proper rate limiting
      }

      // Skip auth check if not required
      if (!requireAuth) {
        const dummyContext: AuthContext = {
          user: { id: '', email: '', name: 'Anonymous' },
          authMethod: 'session',
          ipAddress: clientIP,
          userAgent,
        };
        return handler(request, dummyContext, params);
      }

      // Get NextAuth session
      const session = await getServerSession();
      let user: AuthenticatedUser | null = null;
      const authMethod: 'session' = 'session';

      if (session?.user) {
        if (validateUser) {
          const dbUser = await prisma.user.findUnique({
            where: { id: session.user.id },
            select: {
              id: true,
              email: true,
              name: true,
              avatar: true,
              provider: true,
              isActive: true,
            },
          });

          if (!dbUser || !dbUser.isActive) {
            throw new Error('User not found or inactive');
          }

          user = {
            id: dbUser.id,
            email: dbUser.email,
            name: dbUser.name || undefined,
            avatar: dbUser.avatar || undefined,
            provider: dbUser.provider || undefined,
          };
        } else {
          user = {
            id: session.user.id || '',
            email: session.user.email || '',
            name: session.user.name,
            avatar: session.user.image,
          };
        }
      }

      // Check authentication requirement
      if (requireAuth && !user) {
        // TODO: Implement logSecurityEvent
        console.log('Security event:', {
          type: 'ACCESS_DENIED',
          userId: null,
          ipAddress: clientIP,
          userAgent,
          details: {
            path: request.nextUrl.pathname,
            method: request.method,
            reason: 'Authentication required',
          },
        });

        return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
      }

      // Role-based access control
      if (roles.length > 0 && user?.role && !roles.includes(user.role)) {
        // TODO: Implement logSecurityEvent
        console.log('Security event:', {
          type: 'ACCESS_DENIED',
          userId: user.id,
          ipAddress: clientIP,
          userAgent,
          details: {
            path: request.nextUrl.pathname,
            method: request.method,
            reason: 'Insufficient role',
            requiredRoles: roles,
            userRole: user.role,
          },
        });

        return NextResponse.json(
          { error: AUTH_ERROR_MESSAGES[AuthErrorCode.INSUFFICIENT_PERMISSIONS] },
          { status: 403 }
        );
      }

      // Permission-based access control
      if (permissions.length > 0 && user?.permissions) {
        const hasPermission = permissions.some((permission) =>
          user.permissions?.includes(permission)
        );

        if (!hasPermission) {
          // TODO: Implement logSecurityEvent
          console.log('Security event:', {
            type: 'ACCESS_DENIED',
            userId: user.id,
            ipAddress: clientIP,
            userAgent,
            details: {
              path: request.nextUrl.pathname,
              method: request.method,
              reason: 'Insufficient permissions',
              requiredPermissions: permissions,
              userPermissions: user.permissions,
            },
          });

          return NextResponse.json(
            { error: AUTH_ERROR_MESSAGES[AuthErrorCode.INSUFFICIENT_PERMISSIONS] },
            { status: 403 }
          );
        }
      }

      // Create auth context
      const context: AuthContext = {
        user: user!,
        authMethod,
        ipAddress: clientIP,
        userAgent,
      };

      // Log successful access
      // TODO: Implement logSecurityEvent
      console.log('Security event:', {
        type: 'ACCESS_GRANTED',
        userId: user?.id || null,
        ipAddress: clientIP,
        userAgent,
        details: {
          path: request.nextUrl.pathname,
          method: request.method,
          authMethod,
          roles: user?.role ? [user.role] : undefined,
          permissions: user?.permissions,
        },
      });

      // Call the protected handler
      return handler(request, context, params);
    } catch (error) {
      // Handle rate limiting errors
      if (error instanceof Error && error.message.includes('Rate limit')) {
        // TODO: Implement logSecurityEvent
        console.log('Security event:', {
          type: 'RATE_LIMIT_EXCEEDED',
          userId: null,
          ipAddress: clientIP,
          userAgent,
          details: {
            path: request.nextUrl.pathname,
            method: request.method,
            error: error.message,
          },
        });

        return NextResponse.json(
          { error: AUTH_ERROR_MESSAGES[AuthErrorCode.RATE_LIMIT_EXCEEDED] },
          { status: 429 }
        );
      }

      // Log unexpected errors
      console.error('Route protection error:', error);
      // TODO: Implement logSecurityEvent
      console.log('Security event:', {
        type: 'ROUTE_PROTECTION_ERROR',
        userId: null,
        ipAddress: clientIP,
        userAgent,
        details: {
          path: request.nextUrl.pathname,
          method: request.method,
          error: error instanceof Error ? error.message : 'Unknown error',
        },
      });

      return NextResponse.json({ error: 'Internal error' }, { status: 500 });
    }
  };
}

/**
 * Decorator for requiring authentication
 */
export const requireAuth =
  (options: Omit<RouteProtectionOptions, 'requireAuth'> = {}) =>
  (handler: ProtectedRouteHandler) =>
    withAuth(handler, { ...options, requireAuth: true });

/**
 * Decorator for requiring specific roles
 */
export const requireRoles =
  (roles: string[], options: Omit<RouteProtectionOptions, 'roles'> = {}) =>
  (handler: ProtectedRouteHandler) =>
    withAuth(handler, { ...options, roles });

/**
 * Decorator for requiring specific permissions
 */
export const requirePermissions =
  (permissions: string[], options: Omit<RouteProtectionOptions, 'permissions'> = {}) =>
  (handler: ProtectedRouteHandler) =>
    withAuth(handler, { ...options, permissions });

/**
 * Decorator for adding rate limiting
 */
export const withRateLimit =
  (limit: number, window: number, options: Omit<RouteProtectionOptions, 'rateLimit'> = {}) =>
  (handler: ProtectedRouteHandler) =>
    withAuth(handler, { ...options, rateLimit: { limit, window } });

/**
 * Utility function to get user from request headers (set by middleware)
 */
export function getUserFromHeaders(request: NextRequest): AuthenticatedUser | null {
  const userId = request.headers.get('x-user-id');
  const userEmail = request.headers.get('x-user-email');
  const userName = request.headers.get('x-user-name');

  if (!userId || !userEmail) {
    return null;
  }

  return {
    id: userId,
    email: userEmail,
    name: userName || undefined,
  };
}

/**
 * Utility function to check if user has specific permission
 */
export function hasPermission(user: AuthenticatedUser, permission: string): boolean {
  return user.permissions?.includes(permission) || false;
}

/**
 * Utility function to check if user has specific role
 */
export function hasRole(user: AuthenticatedUser, role: string): boolean {
  return user.role === role;
}

/**
 * Utility function to check if user has any of the specified roles
 */
export function hasAnyRole(user: AuthenticatedUser, roles: string[]): boolean {
  return user.role ? roles.includes(user.role) : false;
}
