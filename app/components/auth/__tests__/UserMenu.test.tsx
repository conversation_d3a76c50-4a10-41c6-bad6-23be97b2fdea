/**
 * @fileoverview UserMenu组件测试套件
 *
 * 测试用户菜单组件的功能，包括：
 * - 基础渲染和用户信息显示
 * - 下拉菜单交互
 * - 管理员权限显示
 * - 登出功能
 * - 不同变体组件
 * - 无障碍性测试
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserMenu, UserMenuCompact, UserDisplay } from '../UserMenu';
import {
  renderWithAuth,
  createMockUser,
  testScenarios,
  MockAuthContextType,
} from '@/test/utils/auth-test-utils';

// Mock toast
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
};

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

describe('UserMenu', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.mock('sonner', () => ({
      toast: mockToast,
    }));

    vi.mock('next/navigation', () => ({
      useRouter: () => mockRouter,
    }));

    vi.clearAllMocks();
  });

  describe('基础渲染和用户信息显示', () => {
    it('应该为已认证用户渲染菜单触发器', () => {
      const testUser = createMockUser({
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'user',
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('user')).toBeInTheDocument();
    });

    it('应该正确显示用户头像和初始字母', () => {
      const testUser = createMockUser({
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'https://example.com/avatar.jpg',
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const avatar = screen.getByRole('img', { name: 'John Doe' });
      expect(avatar).toBeInTheDocument();
      expect(avatar).toHaveAttribute('src', 'https://example.com/avatar.jpg');

      // Fallback initials should also be present
      expect(screen.getByText('JD')).toBeInTheDocument();
    });

    it('应该在没有头像时显示初始字母', () => {
      const testUser = createMockUser({
        name: 'Jane Smith',
        email: '<EMAIL>',
        avatar: undefined,
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      expect(screen.getByText('JS')).toBeInTheDocument();
    });

    it('应该在没有姓名时使用邮箱首字母', () => {
      const testUser = createMockUser({
        name: undefined,
        email: '<EMAIL>',
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      expect(screen.getByText('J')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('应该在未认证时不渲染菜单', () => {
      renderWithAuth(<UserMenu />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });
  });

  describe('下拉菜单交互', () => {
    it('应该在点击时打开下拉菜单', async () => {
      const testUser = createMockUser();

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const trigger = screen.getByRole('button');
      await user.click(trigger);

      // 等待菜单内容出现
      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
      });

      expect(screen.getByText('Settings')).toBeInTheDocument();
      expect(screen.getByText('Billing')).toBeInTheDocument();
      expect(screen.getByText('Help & Support')).toBeInTheDocument();
      expect(screen.getByText('Log out')).toBeInTheDocument();
    });

    it('应该在菜单中显示完整的用户信息', async () => {
      const testUser = createMockUser({
        name: 'John Doe',
        email: '<EMAIL>',
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('应该为菜单项提供正确的链接', async () => {
      const testUser = createMockUser();

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        const profileLink = screen.getByRole('link', { name: /profile/i });
        expect(profileLink).toHaveAttribute('href', '/profile');

        const settingsLink = screen.getByRole('link', { name: /settings/i });
        expect(settingsLink).toHaveAttribute('href', '/settings');

        const billingLink = screen.getByRole('link', { name: /billing/i });
        expect(billingLink).toHaveAttribute('href', '/billing');

        const helpLink = screen.getByRole('link', { name: /help & support/i });
        expect(helpLink).toHaveAttribute('href', '/help');
      });
    });
  });

  describe('管理员权限显示', () => {
    it('应该为管理员用户显示管理面板链接', async () => {
      const adminUser = createMockUser({ role: 'admin' });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.adminUser() });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        const adminLink = screen.getByRole('link', { name: /admin panel/i });
        expect(adminLink).toBeInTheDocument();
        expect(adminLink).toHaveAttribute('href', '/admin');
      });
    });

    it('应该为普通用户隐藏管理面板链接', async () => {
      const regularUser = createMockUser({ role: 'user' });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(regularUser) });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
      });

      expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument();
    });

    it('应该为没有角色的用户隐藏管理面板链接', async () => {
      const userWithoutRole = createMockUser({ role: undefined });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(userWithoutRole) });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
      });

      expect(screen.queryByText('Admin Panel')).not.toBeInTheDocument();
    });
  });

  describe('登出功能', () => {
    it('应该在点击登出时调用logout函数', async () => {
      const mockLogout = vi.fn().mockResolvedValue(undefined);
      const testUser = createMockUser();

      const authValue: MockAuthContextType = {
        ...testScenarios.authenticatedUser(testUser),
        logout: mockLogout,
      };

      renderWithAuth(<UserMenu />, { authValue });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Log out')).toBeInTheDocument();
      });

      await user.click(screen.getByText('Log out'));

      expect(mockLogout).toHaveBeenCalledOnce();
    });

    it('应该在logout失败时显示错误消息', async () => {
      const mockLogout = vi.fn().mockRejectedValue(new Error('Logout failed'));
      const testUser = createMockUser();

      const authValue: MockAuthContextType = {
        ...testScenarios.authenticatedUser(testUser),
        logout: mockLogout,
      };

      renderWithAuth(<UserMenu />, { authValue });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Log out')).toBeInTheDocument();
      });

      await user.click(screen.getByText('Log out'));

      await waitFor(() => {
        expect(mockToast.error).toHaveBeenCalledWith('Failed to logout');
      });
    });

    it('应该在加载时禁用登出按钮', async () => {
      const testUser = createMockUser();

      const authValue: MockAuthContextType = {
        ...testScenarios.authenticatedUser(testUser),
        isLoading: true,
        loading: true,
      };

      renderWithAuth(<UserMenu />, { authValue });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        const logoutButton = screen.getByText('Log out');
        expect(logoutButton).toBeInTheDocument();
        expect(logoutButton.closest('button')).toBeDisabled();
      });
    });
  });

  describe('UserMenuCompact 变体', () => {
    it('应该渲染紧凑版本的用户菜单', () => {
      const testUser = createMockUser({
        name: 'John Doe',
        email: '<EMAIL>',
      });

      renderWithAuth(<UserMenuCompact />, { authValue: testScenarios.authenticatedUser(testUser) });

      const trigger = screen.getByRole('button');
      expect(trigger).toBeInTheDocument();
      expect(trigger).toHaveClass('h-8', 'w-8', 'rounded-full');
    });

    it('应该在紧凑版本中显示简化的菜单选项', async () => {
      const testUser = createMockUser();

      renderWithAuth(<UserMenuCompact />, { authValue: testScenarios.authenticatedUser(testUser) });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
        expect(screen.getByText('Settings')).toBeInTheDocument();
        expect(screen.getByText('Log out')).toBeInTheDocument();
      });

      // 紧凑版本不应该显示 Billing 和 Help & Support
      expect(screen.queryByText('Billing')).not.toBeInTheDocument();
      expect(screen.queryByText('Help & Support')).not.toBeInTheDocument();
    });

    it('应该在未认证时不渲染紧凑菜单', () => {
      renderWithAuth(<UserMenuCompact />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });
  });

  describe('UserDisplay 变体', () => {
    it('应该渲染简单的用户显示组件', () => {
      const testUser = createMockUser({
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
      });

      renderWithAuth(<UserDisplay />, { authValue: testScenarios.authenticatedUser(testUser) });

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('admin')).toBeInTheDocument();

      // 应该没有按钮或交互元素
      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('应该在没有姓名时显示邮箱', () => {
      const testUser = createMockUser({
        name: undefined,
        email: '<EMAIL>',
        role: 'user',
      });

      renderWithAuth(<UserDisplay />, { authValue: testScenarios.authenticatedUser(testUser) });

      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('user')).toBeInTheDocument();
    });

    it('应该在没有角色时隐藏角色显示', () => {
      const testUser = createMockUser({
        name: 'John Doe',
        role: undefined,
      });

      renderWithAuth(<UserDisplay />, { authValue: testScenarios.authenticatedUser(testUser) });

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.queryByText('user')).not.toBeInTheDocument();
      expect(screen.queryByText('admin')).not.toBeInTheDocument();
    });

    it('应该在未认证时不渲染', () => {
      renderWithAuth(<UserDisplay />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.queryByText('User')).not.toBeInTheDocument();
    });
  });

  describe('无障碍性测试', () => {
    it('应该提供正确的ARIA标签', async () => {
      const testUser = createMockUser({ name: 'John Doe' });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const trigger = screen.getByRole('button');
      expect(trigger).toBeInTheDocument();

      await user.click(trigger);

      await waitFor(() => {
        const menu = screen.getByRole('menu');
        expect(menu).toBeInTheDocument();
      });
    });

    it('应该为头像提供正确的alt文本', () => {
      const testUser = createMockUser({
        name: 'John Doe',
        avatar: 'https://example.com/avatar.jpg',
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const avatar = screen.getByRole('img', { name: 'John Doe' });
      expect(avatar).toBeInTheDocument();
    });

    it('应该支持键盘导航', async () => {
      const testUser = createMockUser();

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const trigger = screen.getByRole('button');

      // 使用键盘打开菜单
      trigger.focus();
      await user.keyboard('{Enter}');

      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
      });

      // 测试Tab键导航
      await user.keyboard('{Tab}');
      expect(document.activeElement).toBeTruthy();
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理空用户对象', () => {
      const authValue: MockAuthContextType = {
        user: null,
        isAuthenticated: false,
        loading: false,
        isLoading: false,
        error: null,
      };

      renderWithAuth(<UserMenu />, { authValue });

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('应该处理缺失的用户属性', () => {
      const incompleteUser = {
        id: 'user-1',
        email: '<EMAIL>',
        // 缺失 name, avatar, role 等
      } as any;

      const authValue: MockAuthContextType = {
        user: incompleteUser,
        isAuthenticated: true,
        loading: false,
        isLoading: false,
        error: null,
      };

      renderWithAuth(<UserMenu />, { authValue });

      expect(screen.getByRole('button')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('T')).toBeInTheDocument(); // 邮箱首字母
    });

    it('应该处理极长的用户名', () => {
      const testUser = createMockUser({
        name: 'This is a very very very long user name that should be truncated',
        email: '<EMAIL>',
      });

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const nameElement = screen.getByText(
        'This is a very very very long user name that should be truncated'
      );
      expect(nameElement).toBeInTheDocument();
      expect(nameElement).toHaveClass('truncate', 'max-w-[120px]');
    });

    it('应该在网络错误时仍能显示菜单', async () => {
      const mockLogout = vi.fn().mockRejectedValue(new Error('Network error'));
      const testUser = createMockUser();

      const authValue: MockAuthContextType = {
        ...testScenarios.authenticatedUser(testUser),
        logout: mockLogout,
      };

      renderWithAuth(<UserMenu />, { authValue });

      await user.click(screen.getByRole('button'));

      await waitFor(() => {
        expect(screen.getByText('Log out')).toBeInTheDocument();
      });

      // 菜单应该仍然可用，即使logout会失败
      expect(screen.getByText('Profile')).toBeInTheDocument();
      expect(screen.getByText('Settings')).toBeInTheDocument();
    });
  });

  describe('性能和渲染优化', () => {
    it('应该在菜单关闭时不重新渲染用户信息', () => {
      const testUser = createMockUser();

      const { rerender } = renderWithAuth(<UserMenu />, {
        authValue: testScenarios.authenticatedUser(testUser),
      });

      const initialButton = screen.getByRole('button');

      // 重新渲染相同的props
      rerender(
        renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) })
          .container.firstChild as React.ReactElement
      );

      const buttonAfterRerender = screen.getByRole('button');
      expect(buttonAfterRerender).toBeInTheDocument();
    });

    it('应该正确处理快速的用户交互', async () => {
      const testUser = createMockUser();

      renderWithAuth(<UserMenu />, { authValue: testScenarios.authenticatedUser(testUser) });

      const trigger = screen.getByRole('button');

      // 快速点击多次
      await user.click(trigger);
      await user.click(trigger);
      await user.click(trigger);

      // 菜单应该仍然可以正常工作
      await waitFor(() => {
        expect(screen.getByText('Profile')).toBeInTheDocument();
      });
    });
  });
});
