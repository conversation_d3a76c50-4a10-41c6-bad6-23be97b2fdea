/**
 * @fileoverview LoginForm组件测试套件
 *
 * 测试登录表单组件的功能，包括：
 * - 表单渲染和基础交互
 * - 表单验证和错误处理
 * - OAuth登录流程
 * - 加载状态处理
 * - 表单提交和数据处理
 * - 无障碍性测试
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LoginForm } from '../login-form';
import {
  renderWithAuth,
  createMockUser,
  testScenarios,
  MockAuthContextType,
} from '@/test/utils/auth-test-utils';

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

describe('LoginForm', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.mock('next/navigation', () => ({
      useRouter: () => mockRouter,
    }));

    vi.clearAllMocks();
  });

  describe('表单渲染和基础交互', () => {
    it('应该渲染登录表单的所有元素', () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      // 标题和描述
      expect(screen.getByText('Welcome back')).toBeInTheDocument();
      expect(screen.getByText('Login with your Google or GitHub account')).toBeInTheDocument();

      // OAuth 按钮
      expect(screen.getByRole('button', { name: /login with google/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login with github/i })).toBeInTheDocument();

      // 表单字段
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /^login$/i })).toBeInTheDocument();

      // 链接
      expect(screen.getByRole('link', { name: /forgot your password/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /sign up/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /terms of service/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /privacy policy/i })).toBeInTheDocument();
    });

    it('应该正确设置表单字段的属性', () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);

      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('name', 'email');
      expect(emailInput).toHaveAttribute('placeholder', '<EMAIL>');
      expect(emailInput).toHaveAttribute('required');

      expect(passwordInput).toHaveAttribute('type', 'password');
      expect(passwordInput).toHaveAttribute('name', 'password');
      expect(passwordInput).toHaveAttribute('required');
    });

    it('应该正确设置链接的href属性', () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.getByRole('link', { name: /forgot your password/i })).toHaveAttribute(
        'href',
        '/forgot-password'
      );
      expect(screen.getByRole('link', { name: /sign up/i })).toHaveAttribute('href', '/signup');
    });
  });

  describe('表单输入和验证', () => {
    it('应该允许用户输入邮箱和密码', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      expect(emailInput).toHaveValue('<EMAIL>');
      expect(passwordInput).toHaveValue('password123');
    });

    it('应该在字段为空时显示验证错误', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const submitButton = screen.getByRole('button', { name: /^login$/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
        expect(screen.getByText('Password is required')).toBeInTheDocument();
      });
    });

    it('应该验证邮箱格式', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.type(emailInput, 'invalid-email');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
      });
    });

    it('应该验证密码长度', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, '12345'); // 少于6个字符
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument();
      });
    });

    it('应该在用户输入时清除验证错误', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      // 触发验证错误
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
      });

      // 开始输入应该清除错误
      await user.type(emailInput, '<EMAIL>');

      await waitFor(() => {
        expect(screen.queryByText('Email is required')).not.toBeInTheDocument();
      });
    });

    it('应该为有错误的字段添加错误样式', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.click(submitButton);

      await waitFor(() => {
        expect(emailInput).toHaveClass('border-red-500');
        expect(passwordInput).toHaveClass('border-red-500');
      });
    });
  });

  describe('表单提交和登录流程', () => {
    it('应该在表单有效时调用login函数', async () => {
      const mockLogin = vi.fn().mockResolvedValue(undefined);
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        login: mockLogin,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith({
          email: '<EMAIL>',
          password: 'password123',
        });
      });
    });

    it('应该在提交时防止默认表单行为', async () => {
      const mockLogin = vi.fn().mockResolvedValue(undefined);
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        login: mockLogin,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const form = screen.getByRole('form') || screen.getByText('Welcome back').closest('form');
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      const submitEvent = new Event('submit', { bubbles: true, cancelable: true });
      const preventDefaultSpy = vi.spyOn(submitEvent, 'preventDefault');

      form?.dispatchEvent(submitEvent);

      expect(preventDefaultSpy).toHaveBeenCalled();
    });

    it('应该处理登录错误', async () => {
      const mockLogin = vi.fn().mockRejectedValue(new Error('Invalid credentials'));
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        login: mockLogin,
      };

      // Mock console.error to avoid test output noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      renderWithAuth(<LoginForm />, { authValue });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalled();
        expect(consoleSpy).toHaveBeenCalledWith('Login error:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('应该在表单无效时不调用login', async () => {
      const mockLogin = vi.fn();
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        login: mockLogin,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const submitButton = screen.getByRole('button', { name: /^login$/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
      });

      expect(mockLogin).not.toHaveBeenCalled();
    });
  });

  describe('OAuth登录流程', () => {
    it('应该调用Google OAuth登录', async () => {
      const mockLoginWithOAuth = vi.fn().mockResolvedValue(undefined);
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loginWithOAuth: mockLoginWithOAuth,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const googleButton = screen.getByRole('button', { name: /login with google/i });
      await user.click(googleButton);

      expect(mockLoginWithOAuth).toHaveBeenCalledWith('google');
    });

    it('应该调用GitHub OAuth登录', async () => {
      const mockLoginWithOAuth = vi.fn().mockResolvedValue(undefined);
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loginWithOAuth: mockLoginWithOAuth,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const githubButton = screen.getByRole('button', { name: /login with github/i });
      await user.click(githubButton);

      expect(mockLoginWithOAuth).toHaveBeenCalledWith('github');
    });

    it('应该处理OAuth登录错误', async () => {
      const mockLoginWithOAuth = vi.fn().mockRejectedValue(new Error('OAuth failed'));
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loginWithOAuth: mockLoginWithOAuth,
      };

      // Mock console.error to avoid test output noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      renderWithAuth(<LoginForm />, { authValue });

      const googleButton = screen.getByRole('button', { name: /login with google/i });
      await user.click(googleButton);

      await waitFor(() => {
        expect(mockLoginWithOAuth).toHaveBeenCalled();
        expect(consoleSpy).toHaveBeenCalledWith('google login error:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('应该显示正确的OAuth提供商图标', () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const googleButton = screen.getByRole('button', { name: /login with google/i });
      const githubButton = screen.getByRole('button', { name: /login with github/i });

      // 检查按钮内容包含正确的SVG图标
      expect(googleButton.querySelector('svg')).toBeInTheDocument();
      expect(githubButton.querySelector('svg')).toBeInTheDocument();
    });
  });

  describe('加载状态处理', () => {
    it('应该在加载时显示加载状态', () => {
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loading: true,
        isLoading: true,
      };

      renderWithAuth(<LoginForm />, { authValue });

      expect(screen.getByText('Logging in...')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /logging in/i })).toBeDisabled();
    });

    it('应该在加载时禁用所有按钮', () => {
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loading: true,
        isLoading: true,
      };

      renderWithAuth(<LoginForm />, { authValue });

      expect(screen.getByRole('button', { name: /login with google/i })).toBeDisabled();
      expect(screen.getByRole('button', { name: /login with github/i })).toBeDisabled();
      expect(screen.getByRole('button', { name: /logging in/i })).toBeDisabled();
    });

    it('应该在加载时显示加载图标', () => {
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loading: true,
        isLoading: true,
      };

      renderWithAuth(<LoginForm />, { authValue });

      // 检查加载图标是否存在
      const loadingIcons =
        screen.getAllByTestId('loader-icon') || document.querySelectorAll('.animate-spin');
      expect(loadingIcons.length).toBeGreaterThan(0);
    });

    it('应该在非加载状态时显示正常按钮文本', () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.getByRole('button', { name: /^login$/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login with google/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /login with github/i })).toBeInTheDocument();

      expect(screen.queryByText('Logging in...')).not.toBeInTheDocument();
    });
  });

  describe('无障碍性测试', () => {
    it('应该为表单字段提供正确的标签', () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    });

    it('应该为错误消息提供正确的关联', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const submitButton = screen.getByRole('button', { name: /^login$/i });
      await user.click(submitButton);

      await waitFor(() => {
        const emailError = screen.getByText('Email is required');
        const passwordError = screen.getByText('Password is required');

        expect(emailError).toBeInTheDocument();
        expect(passwordError).toBeInTheDocument();
      });
    });

    it('应该支持键盘导航', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /^login$/i });

      // 测试Tab键导航
      emailInput.focus();
      expect(document.activeElement).toBe(emailInput);

      await user.keyboard('{Tab}');
      expect(document.activeElement).toBe(passwordInput);

      // 跳过忘记密码链接
      await user.keyboard('{Tab}');
      await user.keyboard('{Tab}');
      expect(document.activeElement).toBe(loginButton);
    });

    it('应该为按钮提供适当的角色和状态', () => {
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        loading: true,
        isLoading: true,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const loginButton = screen.getByRole('button', { name: /logging in/i });
      expect(loginButton).toHaveAttribute('disabled');
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该处理极长的邮箱地址', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const longEmail = 'a'.repeat(100) + '@example.com';

      await user.type(emailInput, longEmail);
      expect(emailInput).toHaveValue(longEmail);
    });

    it('应该处理特殊字符的邮箱', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const specialEmail = '<EMAIL>';

      await user.type(emailInput, specialEmail);
      expect(emailInput).toHaveValue(specialEmail);
    });

    it('应该处理空白字符的输入', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.type(emailInput, '   ');
      await user.type(passwordInput, '   ');
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Email is required')).toBeInTheDocument();
        expect(screen.getByText('Password is required')).toBeInTheDocument();
      });
    });

    it('应该处理快速连续的表单提交', async () => {
      const mockLogin = vi.fn().mockResolvedValue(undefined);
      const authValue: MockAuthContextType = {
        ...testScenarios.unauthenticatedUser(),
        login: mockLogin,
      };

      renderWithAuth(<LoginForm />, { authValue });

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');

      // 快速点击多次
      await user.click(submitButton);
      await user.click(submitButton);
      await user.click(submitButton);

      // login应该只被调用一次（防止重复提交）
      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('自定义props和样式', () => {
    it('应该接受并应用自定义className', () => {
      const { container } = renderWithAuth(<LoginForm className="custom-form-class" />, {
        authValue: testScenarios.unauthenticatedUser(),
      });

      expect(container.firstChild).toHaveClass('custom-form-class');
    });

    it('应该传递其他props到根元素', () => {
      const { container } = renderWithAuth(<LoginForm data-testid="login-form" />, {
        authValue: testScenarios.unauthenticatedUser(),
      });

      expect(container.firstChild).toHaveAttribute('data-testid', 'login-form');
    });

    it('应该应用条件样式类', async () => {
      renderWithAuth(<LoginForm />, { authValue: testScenarios.unauthenticatedUser() });

      const emailInput = screen.getByLabelText(/email/i);
      const submitButton = screen.getByRole('button', { name: /^login$/i });

      // 触发验证错误
      await user.click(submitButton);

      await waitFor(() => {
        expect(emailInput).toHaveClass('border-red-500');
      });

      // 修复错误
      await user.type(emailInput, '<EMAIL>');

      await waitFor(() => {
        expect(emailInput).not.toHaveClass('border-red-500');
      });
    });
  });
});
