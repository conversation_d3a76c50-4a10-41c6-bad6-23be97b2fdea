#!/usr/bin/env tsx

/**
 * 失败分析脚本
 *
 * 功能：
 * 1. 分析为什么某些单词无法成功导入或设置排名
 * 2. 提供详细的失败原因和解决建议
 * 3. 生成可操作的分析报告
 */

import { PrismaClient } from '@prisma/client';
import { findMissingWords } from './utils/missing-words-finder.js';
import { analyzeWordFailure, generateFailureReport } from './utils/failure-analyzer.js';
import { fetchDictionary } from '../../5-providers/gemini.js';

// 配置常量
const TOTAL_WORDS = 500;

// 初始化Prisma客户端
const prismaClient = new PrismaClient();

/**
 * 深度分析单个单词
 */
async function deepAnalyzeWord(word: string): Promise<any> {
  console.log(`🔍 深度分析单词: "${word}"`);

  const analysis: any = {
    word,
    databaseStatus: null,
    apiTest: null,
    recommendations: [],
  };

  try {
    // 1. 检查数据库状态
    console.log('  📊 检查数据库状态...');
    const vocabulary = await prismaClient.vocabulary.findFirst({
      where: { word: word },
      include: {
        explains: {
          include: {
            definitions: true,
          },
        },
        formats: true,
      },
    });

    if (vocabulary) {
      analysis.databaseStatus = {
        exists: true,
        hasFreqRank: vocabulary.freqRank !== null,
        freqRank: vocabulary.freqRank,
        explainCount: vocabulary.explains.length,
        formatCount: vocabulary.formats.length,
        phonetics: vocabulary.phonetics,
      };
      console.log(`    ✅ 数据库中存在，排名: ${vocabulary.freqRank || '无'}`);
    } else {
      analysis.databaseStatus = {
        exists: false,
      };
      console.log(`    ❌ 数据库中不存在`);
    }

    // 2. 测试API查询
    console.log('  🌐 测试API查询...');
    try {
      const apiResult = await fetchDictionary(word);
      analysis.apiTest = {
        statusCode: apiResult.statusCode,
        success: apiResult.statusCode === 200,
        wordCount: apiResult.data?.wordCount || 0,
        hasWord: apiResult.data?.words?.some((w) => w.word === word) || false,
        isIllegal: apiResult.data?.illegalWords?.includes(word) || false,
        error: apiResult.error?.message,
      };

      if (apiResult.statusCode === 200) {
        if (apiResult.data?.words?.some((w) => w.word === word)) {
          console.log(`    ✅ API查询成功，找到单词`);
        } else if (apiResult.data?.illegalWords?.includes(word)) {
          console.log(`    ⚠️  API认为这是无效单词`);
        } else {
          console.log(`    ❓ API查询成功但未返回该单词`);
        }
      } else {
        console.log(`    ❌ API查询失败: ${apiResult.error?.message}`);
      }
    } catch (error) {
      analysis.apiTest = {
        success: false,
        error: (error as Error).message,
      };
      console.log(`    💥 API查询异常: ${(error as Error).message}`);
    }

    // 3. 生成建议
    if (!analysis.databaseStatus.exists && analysis.apiTest.success && analysis.apiTest.hasWord) {
      analysis.recommendations.push('可以重新导入：API能找到该单词但数据库中不存在');
    } else if (analysis.databaseStatus.exists && !analysis.databaseStatus.hasFreqRank) {
      analysis.recommendations.push('只需更新排名：单词已存在但缺少频率排名');
    } else if (analysis.apiTest.isIllegal) {
      analysis.recommendations.push('无法处理：API认为这是无效单词，可能是专有名词或非标准词汇');
    } else if (!analysis.apiTest.success) {
      analysis.recommendations.push('API问题：需要检查网络连接或API配置');
    } else if (analysis.apiTest.success && !analysis.apiTest.hasWord) {
      analysis.recommendations.push('API解析问题：API返回成功但未包含该单词，可能需要调整查询方式');
    } else {
      analysis.recommendations.push('需要进一步调查：情况复杂，建议手动检查');
    }
  } catch (error) {
    console.error(`  💥 分析过程出错: ${(error as Error).message}`);
    analysis.error = (error as Error).message;
  }

  return analysis;
}

/**
 * 批量深度分析
 */
async function batchDeepAnalyze(words: string[]): Promise<void> {
  console.log(`🔍 开始批量深度分析 ${words.length} 个单词...\n`);

  const analyses: any[] = [];

  for (let i = 0; i < words.length; i++) {
    const word = words[i];
    console.log(`[${i + 1}/${words.length}] 分析: "${word}"`);

    const analysis = await deepAnalyzeWord(word);
    analyses.push(analysis);

    // 添加延迟避免API限制
    if (i < words.length - 1) {
      await new Promise((resolve) => setTimeout(resolve, 1000));
    }

    console.log(''); // 空行分隔
  }

  // 生成汇总报告
  console.log('📋 深度分析汇总报告');
  console.log('='.repeat(50));

  // 统计分类
  const categories = {
    canRetry: 0,
    needRankUpdate: 0,
    illegalWords: 0,
    apiIssues: 0,
    unknown: 0,
  };

  analyses.forEach((analysis) => {
    if (analysis.recommendations.some((r: string) => r.includes('重新导入'))) {
      categories.canRetry++;
    } else if (analysis.recommendations.some((r: string) => r.includes('更新排名'))) {
      categories.needRankUpdate++;
    } else if (analysis.recommendations.some((r: string) => r.includes('无效单词'))) {
      categories.illegalWords++;
    } else if (analysis.recommendations.some((r: string) => r.includes('API问题'))) {
      categories.apiIssues++;
    } else {
      categories.unknown++;
    }
  });

  console.log('\n📊 分类统计:');
  console.log(`  🔄 可重新导入: ${categories.canRetry} 个`);
  console.log(`  📊 需更新排名: ${categories.needRankUpdate} 个`);
  console.log(`  ❌ 无效单词: ${categories.illegalWords} 个`);
  console.log(`  🌐 API问题: ${categories.apiIssues} 个`);
  console.log(`  ❓ 需进一步调查: ${categories.unknown} 个`);

  // 详细报告
  console.log('\n📝 详细分析:');
  analyses.forEach((analysis, index) => {
    console.log(`\n${index + 1}. "${analysis.word}"`);
    console.log(`   数据库状态: ${analysis.databaseStatus?.exists ? '存在' : '不存在'}`);
    if (analysis.databaseStatus?.exists) {
      console.log(`   频率排名: ${analysis.databaseStatus.freqRank || '无'}`);
    }
    console.log(`   API测试: ${analysis.apiTest?.success ? '成功' : '失败'}`);
    if (analysis.apiTest?.isIllegal) {
      console.log(`   ⚠️  被标记为无效单词`);
    }
    console.log(`   建议: ${analysis.recommendations.join('; ')}`);
  });
}

/**
 * 主函数
 */
export async function main(): Promise<void> {
  console.log('🔍 开始失败分析...\n');

  try {
    // 1. 查找未匹配的单词
    console.log('🔍 步骤1: 查找未匹配的单词');
    const missingWords = await findMissingWords(TOTAL_WORDS, prismaClient);

    if (missingWords.length === 0) {
      console.log('🎉 所有单词都已正确匹配，无需分析！');
      return;
    }

    console.log(`📋 找到 ${missingWords.length} 个未匹配的单词\n`);

    // 2. 快速分析
    console.log('📊 步骤2: 快速分析');
    const quickAnalyses = missingWords.map((word) => analyzeWordFailure(word.word, {}));

    const quickReport = generateFailureReport(quickAnalyses);
    console.log(quickReport);

    // 3. 询问是否进行深度分析
    const wordsToAnalyze = missingWords.slice(0, 10).map((w) => w.word); // 限制前10个

    console.log(`\n🔍 步骤3: 深度分析（前${wordsToAnalyze.length}个单词）`);
    await batchDeepAnalyze(wordsToAnalyze);

    console.log('\n🎉 失败分析完成！');
  } catch (error) {
    console.error('\n💥 失败分析出错:', error);
    throw error;
  } finally {
    await prismaClient.$disconnect();
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .then(() => {
      console.log('\n✅ 分析完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 分析失败:', error);
      process.exit(1);
    });
}
