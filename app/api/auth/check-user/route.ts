/**
 * Check User Existence API
 * 检查用户邮箱是否已注册
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { validateInput, validateEmail } from '@/lib/auth/simple-security';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email } = body;

    // 验证输入
    if (!email || typeof email !== 'string') {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // 验证邮箱格式
    if (!validateEmail(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    // 基础安全验证
    const validation = validateInput(email);
    if (!validation.isValid) {
      return NextResponse.json({ error: validation.error }, { status: 400 });
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
      select: { id: true }, // 只需要知道是否存在
    });

    return NextResponse.json({
      exists: !!user,
      email: email.toLowerCase(),
    });
  } catch (error) {
    console.error('Check user error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
