import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
// import crypto from 'crypto';

/**
 * 安全Cookie配置
 */
export interface SecureCookieOptions {
  httpOnly?: boolean;
  secure?: boolean;
  sameSite?: 'strict' | 'lax' | 'none';
  maxAge?: number;
  path?: string;
  domain?: string;
}

/**
 * CSRF保护配置
 */
export interface CSRFConfig {
  enabled: boolean;
  tokenLength: number;
  headerName: string;
  cookieName: string;
}

/**
 * 默认安全Cookie配置
 */
export const DEFAULT_SECURE_COOKIE_OPTIONS: SecureCookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  path: '/',
  maxAge: 15 * 60, // 15分钟
};

/**
 * 刷新Token的Cookie配置（更长过期时间）
 */
export const REFRESH_TOKEN_COOKIE_OPTIONS: SecureCookieOptions = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'strict',
  path: '/',
  maxAge: 7 * 24 * 60 * 60, // 7天
};

/**
 * CSRF配置
 */
export const CSRF_CONFIG: CSRFConfig = {
  enabled: process.env.NODE_ENV === 'production',
  tokenLength: 32,
  headerName: 'x-csrf-token',
  cookieName: 'csrf-token',
};

/**
 * Cookie名称常量
 */
export const COOKIE_NAMES = {
  ACCESS_TOKEN: 'auth-token',
  REFRESH_TOKEN: 'refresh-token',
  USER_SESSION: 'user-session',
  CSRF_TOKEN: 'csrf-token',
} as const;

/**
 * 安全Cookie管理器
 */
export class SecureCookieManager {
  /**
   * 设置安全Cookie
   */
  static setCookie(
    name: string,
    value: string,
    options: SecureCookieOptions = DEFAULT_SECURE_COOKIE_OPTIONS
  ): void {
    const cookieStore = cookies();

    (cookieStore as any).set(name, value, {
      httpOnly: options.httpOnly ?? true,
      secure: options.secure ?? process.env.NODE_ENV === 'production',
      sameSite: options.sameSite ?? 'strict',
      maxAge: options.maxAge ?? 15 * 60,
      path: options.path ?? '/',
      ...(options.domain && { domain: options.domain }),
    });
  }

  /**
   * 获取Cookie值
   */
  static getCookie(name: string): string | null {
    const cookieStore = cookies();
    const cookie = (cookieStore as any).get(name);
    return cookie?.value ?? null;
  }

  /**
   * 删除Cookie
   */
  static deleteCookie(name: string): void {
    const cookieStore = cookies();
    (cookieStore as any).delete(name);
  }

  /**
   * 设置访问Token
   */
  static setAccessToken(token: string): void {
    this.setCookie(COOKIE_NAMES.ACCESS_TOKEN, token, DEFAULT_SECURE_COOKIE_OPTIONS);
  }

  /**
   * 获取访问Token
   */
  static getAccessToken(): string | null {
    return this.getCookie(COOKIE_NAMES.ACCESS_TOKEN);
  }

  /**
   * 设置刷新Token
   */
  static setRefreshToken(token: string): void {
    this.setCookie(COOKIE_NAMES.REFRESH_TOKEN, token, REFRESH_TOKEN_COOKIE_OPTIONS);
  }

  /**
   * 获取刷新Token
   */
  static getRefreshToken(): string | null {
    return this.getCookie(COOKIE_NAMES.REFRESH_TOKEN);
  }

  /**
   * 设置用户会话数据
   */
  static setUserSession(userData: any): void {
    const sessionData = JSON.stringify(userData);
    this.setCookie(COOKIE_NAMES.USER_SESSION, sessionData, DEFAULT_SECURE_COOKIE_OPTIONS);
  }

  /**
   * 获取用户会话数据
   */
  static getUserSession(): any | null {
    const sessionData = this.getCookie(COOKIE_NAMES.USER_SESSION);
    if (!sessionData) return null;

    try {
      return JSON.parse(sessionData);
    } catch (error) {
      console.error('Failed to parse user session data:', error);
      return null;
    }
  }

  /**
   * 清除所有认证相关的Cookie
   */
  static clearAuthCookies(): void {
    this.deleteCookie(COOKIE_NAMES.ACCESS_TOKEN);
    this.deleteCookie(COOKIE_NAMES.REFRESH_TOKEN);
    this.deleteCookie(COOKIE_NAMES.USER_SESSION);
    this.deleteCookie(COOKIE_NAMES.CSRF_TOKEN);
  }
}

/**
 * CSRF保护管理器
 */
export class CSRFProtection {
  /**
   * 生成CSRF Token
   */
  static generateToken(): string {
    const buffer = new Uint8Array(CSRF_CONFIG.tokenLength);
    crypto.getRandomValues(buffer);
    return Array.from(buffer, (byte) => byte.toString(16).padStart(2, '0')).join('');
  }

  /**
   * 设置CSRF Token到Cookie
   */
  static setCSRFToken(): string {
    const token = this.generateToken();

    const cookieStore = cookies();
    (cookieStore as any).set(CSRF_CONFIG.cookieName, token, {
      httpOnly: false, // 需要让前端访问
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60, // 1小时
      path: '/',
    });

    return token;
  }

  /**
   * 获取CSRF Token
   */
  static getCSRFToken(): string | null {
    const cookieStore = cookies();
    const cookie = (cookieStore as any).get(CSRF_CONFIG.cookieName);
    return cookie?.value ?? null;
  }

  /**
   * 验证CSRF Token
   */
  static async validateCSRFToken(request: NextRequest): Promise<boolean> {
    if (!CSRF_CONFIG.enabled) return true;

    const headerToken = request.headers.get(CSRF_CONFIG.headerName);
    const cookieToken = request.cookies.get(CSRF_CONFIG.cookieName)?.value;

    if (!headerToken || !cookieToken) return false;

    const encoder = new TextEncoder();
    const data = encoder.encode(headerToken);
    const expected = encoder.encode(cookieToken);

    if (data.byteLength !== expected.byteLength) {
      return false;
    }

    let result = 0;
    for (let i = 0; i < data.byteLength; i++) {
      result |= data[i] ^ expected[i];
    }
    return result === 0;
  }

  /**
   * 创建CSRF保护中间件
   */
  static middleware() {
    return (request: NextRequest): NextResponse | null => {
      // 对于写操作进行CSRF检查
      const isWriteOperation = ['POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method);

      if (isWriteOperation && !this.validateCSRFToken(request)) {
        return NextResponse.json({ error: 'CSRF token validation failed' }, { status: 403 });
      }

      return null; // 继续处理请求
    };
  }
}

/**
 * Response Cookie管理器（用于API路由）
 */
export class ResponseCookieManager {
  /**
   * 在响应中设置Cookie
   */
  static setCookie(
    response: NextResponse,
    name: string,
    value: string,
    options: SecureCookieOptions = DEFAULT_SECURE_COOKIE_OPTIONS
  ): void {
    const cookieString = this.buildCookieString(name, value, options);
    response.headers.append('Set-Cookie', cookieString);
  }

  /**
   * 在响应中删除Cookie
   */
  static deleteCookie(response: NextResponse, name: string): void {
    const cookieString = `${name}=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT; HttpOnly; Secure; SameSite=Strict`;
    response.headers.append('Set-Cookie', cookieString);
  }

  /**
   * 设置访问Token到响应
   */
  static setAccessToken(response: NextResponse, token: string): void {
    this.setCookie(response, COOKIE_NAMES.ACCESS_TOKEN, token, DEFAULT_SECURE_COOKIE_OPTIONS);
  }

  /**
   * 设置刷新Token到响应
   */
  static setRefreshToken(response: NextResponse, token: string): void {
    this.setCookie(response, COOKIE_NAMES.REFRESH_TOKEN, token, REFRESH_TOKEN_COOKIE_OPTIONS);
  }

  /**
   * 设置用户会话到响应
   */
  static setUserSession(response: NextResponse, userData: any): void {
    const sessionData = JSON.stringify(userData);
    this.setCookie(response, COOKIE_NAMES.USER_SESSION, sessionData, DEFAULT_SECURE_COOKIE_OPTIONS);
  }

  /**
   * 清除所有认证Cookie
   */
  static clearAuthCookies(response: NextResponse): void {
    this.deleteCookie(response, COOKIE_NAMES.ACCESS_TOKEN);
    this.deleteCookie(response, COOKIE_NAMES.REFRESH_TOKEN);
    this.deleteCookie(response, COOKIE_NAMES.USER_SESSION);
    this.deleteCookie(response, COOKIE_NAMES.CSRF_TOKEN);
  }

  /**
   * 构建Cookie字符串
   */
  private static buildCookieString(
    name: string,
    value: string,
    options: SecureCookieOptions
  ): string {
    let cookieString = `${name}=${value}`;

    if (options.path) {
      cookieString += `; Path=${options.path}`;
    }

    if (options.maxAge) {
      cookieString += `; Max-Age=${options.maxAge}`;
    }

    if (options.domain) {
      cookieString += `; Domain=${options.domain}`;
    }

    if (options.httpOnly) {
      cookieString += '; HttpOnly';
    }

    if (options.secure) {
      cookieString += '; Secure';
    }

    if (options.sameSite) {
      cookieString += `; SameSite=${options.sameSite}`;
    }

    return cookieString;
  }
}

/**
 * CORS配置接口
 */
export interface CORSConfig {
  origins: string[];
  credentials: boolean;
  methods: string[];
  allowedHeaders: string[];
  maxAge?: number;
}

/**
 * 默认CORS配置 - 等效于Express.js cors配置
 */
export const DEFAULT_CORS_CONFIG: CORSConfig = {
  origins: [
    'chrome-extension://*',  // 允许所有Chrome扩展
    'https://*',             // 允许HTTPS网站
    'http://localhost:*'     // 允许本地开发
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'],
  maxAge: 86400 // 24小时
};

/**
 * 安全头部管理器
 */
export class SecurityHeadersManager {
  /**
   * 检查origin是否被允许
   */
  private static isOriginAllowed(origin: string | null, allowedOrigins: string[]): boolean {
    if (!origin) return false;

    return allowedOrigins.some(allowed => {
      if (allowed === '*') return true;
      if (allowed.endsWith('*')) {
        const prefix = allowed.slice(0, -1);
        return origin.startsWith(prefix);
      }
      return origin === allowed;
    });
  }

  /**
   * 添加CORS头部到响应
   */
  static addCORSHeaders(
    response: NextResponse,
    request: NextRequest,
    config: CORSConfig = DEFAULT_CORS_CONFIG
  ): void {
    const origin = request.headers.get('origin');

    // 检查origin是否被允许
    if (this.isOriginAllowed(origin, config.origins)) {
      response.headers.set('Access-Control-Allow-Origin', origin || '*');
    }

    // 设置其他CORS头部
    response.headers.set('Access-Control-Allow-Credentials', config.credentials.toString());
    response.headers.set('Access-Control-Allow-Methods', config.methods.join(', '));
    response.headers.set('Access-Control-Allow-Headers', config.allowedHeaders.join(', '));

    if (config.maxAge) {
      response.headers.set('Access-Control-Max-Age', config.maxAge.toString());
    }

    // 暴露一些有用的头部给前端
    response.headers.set('Access-Control-Expose-Headers', 'X-Total-Count, X-Request-ID');
  }

  /**
   * 添加安全头部到响应
   */
  static addSecurityHeaders(response: NextResponse, request?: NextRequest): void {
    // 添加CORS头部（如果提供了request）
    if (request) {
      this.addCORSHeaders(response, request);
    }

    // CSRF保护
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');

    // 严格传输安全（HSTS）
    if (process.env.NODE_ENV === 'production') {
      response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    }

    // 内容安全策略（根据环境调整）
    const isDev = process.env.NODE_ENV === 'development';
    // 在所有环境中都添加 'unsafe-eval' 以支持 webpack
    const scriptSrc = `'self' 'unsafe-inline' 'unsafe-eval'`;
    const csp = `default-src 'self'; script-src ${scriptSrc}; style-src 'self' 'unsafe-inline';`;
    response.headers.set('Content-Security-Policy', csp);

    // 推荐人策略
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

    // 添加请求ID用于调试
    if (!response.headers.get('X-Request-ID')) {
      response.headers.set('X-Request-ID', crypto.randomUUID());
    }
  }
}
