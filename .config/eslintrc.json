{"extends": ["next/core-web-vitals", "plugin:@typescript-eslint/recommended", "prettier", "plugin:prettier/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "parserOptions": {"project": "./tsconfig.json"}, "rules": {"prettier/prettier": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn"}}