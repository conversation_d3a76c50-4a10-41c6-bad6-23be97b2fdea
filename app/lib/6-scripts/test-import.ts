import fs from 'fs';
import path from 'path';
import { prisma } from '../4-infrastructure/database/prisma';

async function testImport() {
  console.log('🚀 测试词库数据导入...');
  
  try {
    // 1. 创建一个测试词库
    const wordList = await prisma.wordList.create({
      data: {
        name: '初中词汇',
        description: '初中英语词汇，适合英语基础学习',
        difficulty: 2,
        totalWords: 0,
        isActive: true
      }
    });
    
    console.log('✅ 创建词库:', wordList);
    
    // 2. 读取并解析一个小文件
    const filePath = path.join(process.cwd(), 'docs', 'res', 'english-vocabulary', '1 初中-乱序.txt');
    console.log('📖 读取文件:', filePath);
    
    if (!fs.existsSync(filePath)) {
      console.error('❌ 文件不存在:', filePath);
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim()).slice(0, 10); // 只取前10行测试
    
    console.log('📝 文件内容前10行:');
    lines.forEach((line, index) => {
      console.log(`  ${index + 1}: ${line}`);
    });
    
    // 3. 解析单词
    const words: string[] = [];
    for (const line of lines) {
      const parts = line.split('\t');
      if (parts.length >= 2) {
        const word = parts[0].trim().toLowerCase();
        if (word && /^[a-zA-Z][a-zA-Z'-]*[a-zA-Z]$|^[a-zA-Z]$/.test(word)) {
          words.push(word);
        }
      }
    }
    
    console.log('✅ 解析到的单词:', words);
    
    // 4. 创建词库条目
    for (const word of words) {
      await prisma.wordListEntry.create({
        data: {
          word,
          wordListIds: [wordList.id],
          wordListId: wordList.id
        }
      });
    }
    
    // 5. 更新词库统计
    await prisma.wordList.update({
      where: { id: wordList.id },
      data: { totalWords: words.length }
    });
    
    console.log(`🎉 成功导入 ${words.length} 个单词到词库 "${wordList.name}"`);
    
  } catch (error) {
    console.error('❌ 测试导入失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testImport();