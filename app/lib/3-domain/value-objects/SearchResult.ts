/**
 * 搜索结果值对象
 * 封装搜索操作的结果和元数据
 */

import { Vocabulary } from '../entities/Vocabulary';
import { CacheSource } from '../../utils/cache-types';
import { convertWordRelationshipsToWordForms } from '../../utils/dict-types';

export type SearchStrategy =
  | 'exact_vocabulary'
  | 'main_wordformat'
  | 'derived_wordformat'
  | 'not_found';

export interface QueryMetadata {
  searchTerm: string;
  matchedForm: string;
  searchStrategy: SearchStrategy;
  isBaseFormQuery: boolean;
  processingTimeMs: number;
  cacheHit?: boolean;
  databaseQueries?: number;
  cacheSource?: CacheSource; // 缓存来源
  cacheHitTime?: number; // 缓存命中耗时（毫秒）
  cacheLayer?: string; // 缓存层级描述
}

export interface WordFormRelationship {
  baseForm: string;
  baseFormId: number;
  allForms: Array<{
    id: number;
    name: string;
    form: string;
    isMainForm: boolean;
  }>;
  totalFormsCount: number;
}

/**
 * 搜索结果值对象
 * 不可变对象，包含搜索的完整结果
 */
export class SearchResult {
  private constructor(
    private readonly _vocabulary: Vocabulary | null,
    private readonly _wordRelationships: WordFormRelationship[],
    private readonly _queryMetadata: QueryMetadata,
    private readonly _relatedSuggestions: string[]
  ) {}

  static create(
    vocabulary: Vocabulary | null,
    wordRelationships: WordFormRelationship[],
    queryMetadata: QueryMetadata,
    relatedSuggestions: string[] = []
  ): SearchResult {
    return new SearchResult(vocabulary, wordRelationships, queryMetadata, relatedSuggestions);
  }

  static notFound(
    searchTerm: string,
    processingTimeMs: number,
    suggestions: string[] = []
  ): SearchResult {
    const metadata: QueryMetadata = {
      searchTerm,
      matchedForm: '',
      searchStrategy: 'not_found',
      isBaseFormQuery: false,
      processingTimeMs,
    };

    return new SearchResult(null, [], metadata, suggestions);
  }

  // Getters
  get vocabulary(): Vocabulary | null {
    return this._vocabulary;
  }

  get wordRelationships(): WordFormRelationship[] {
    return [...this._wordRelationships];
  }

  get queryMetadata(): QueryMetadata {
    return { ...this._queryMetadata };
  }

  get relatedSuggestions(): string[] {
    return [...this._relatedSuggestions];
  }

  // 业务方法

  /**
   * 检查是否找到了词汇
   */
  isFound(): boolean {
    return this._vocabulary !== null;
  }

  /**
   * 检查是否为缓存命中
   */
  isCacheHit(): boolean {
    return this._queryMetadata.cacheHit === true;
  }

  /**
   * 检查是否为精确匹配
   */
  isExactMatch(): boolean {
    return this._queryMetadata.searchStrategy === 'exact_vocabulary';
  }

  /**
   * 检查是否通过词形变化找到
   */
  isFoundByWordForm(): boolean {
    return (
      this._queryMetadata.searchStrategy === 'main_wordformat' ||
      this._queryMetadata.searchStrategy === 'derived_wordformat'
    );
  }

  /**
   * 获取处理时间等级
   */
  getPerformanceLevel(): 'excellent' | 'good' | 'acceptable' | 'slow' {
    const time = this._queryMetadata.processingTimeMs;
    if (time < 10) return 'excellent';
    if (time < 50) return 'good';
    if (time < 200) return 'acceptable';
    return 'slow';
  }

  /**
   * 获取所有相关的词形
   */
  getAllWordForms(): string[] {
    if (!this._wordRelationships.length) return [];

    return this._wordRelationships[0].allForms.map((form) => form.form);
  }

  /**
   * 获取基础形式
   */
  getBaseForm(): string | null {
    if (!this._wordRelationships.length) return null;
    return this._wordRelationships[0].baseForm;
  }

  /**
   * 获取主要形式（直接关联vocabulary的形式）
   */
  getMainForms(): Array<{ id: number; name: string; form: string }> {
    if (!this._wordRelationships.length) return [];

    return this._wordRelationships[0].allForms.filter((form) => form.isMainForm);
  }

  /**
   * 获取派生形式
   */
  getDerivedForms(): Array<{ id: number; name: string; form: string }> {
    if (!this._wordRelationships.length) return [];

    return this._wordRelationships[0].allForms.filter((form) => !form.isMainForm);
  }

  /**
   * 检查是否有相关建议
   */
  hasSuggestions(): boolean {
    return this._relatedSuggestions.length > 0;
  }

  /**
   * 获取搜索质量评分
   * 基于匹配策略、处理时间、是否有建议等因素
   */
  getQualityScore(): number {
    let score = 0;

    // 基础分数（是否找到）
    if (this.isFound()) {
      score += 50;
    }

    // 匹配策略分数
    switch (this._queryMetadata.searchStrategy) {
      case 'exact_vocabulary':
        score += 30;
        break;
      case 'main_wordformat':
        score += 20;
        break;
      case 'derived_wordformat':
        score += 15;
        break;
      default:
        score += 0;
    }

    // 性能分数
    const perfLevel = this.getPerformanceLevel();
    switch (perfLevel) {
      case 'excellent':
        score += 15;
        break;
      case 'good':
        score += 10;
        break;
      case 'acceptable':
        score += 5;
        break;
      default:
        score += 0;
    }

    // 建议分数
    if (this.hasSuggestions()) {
      score += 5;
    }

    return Math.min(score, 100);
  }

  /**
   * 转换为API响应格式
   */
  toApiResponse() {
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-06-10 14:40:00 +08:00]
    // Reason: [实现新的API响应结构，移除顶层字段，并将智能过滤后的词形信息整合到单词对象中]
    // Principle_Applied: [API Design Best Practices, Encapsulation]
    // Optimization: [根据查询类型动态调整返回数据，减少不必要的负载]
    // Architectural_Note (AR): [将复杂的响应生成逻辑封装在值对象内部，保持服务层清洁]
    // Documentation_Note (DW): [实现新的API响应格式，包含智能词形过滤逻辑]
    // }}
    // {{START MODIFICATIONS}}
    const wordsWithForms = this._vocabulary
      ? [
          {
            ...this._vocabulary.toApiResponse(),
            forms: this.getMergedWordForms(), // 使用智能词形合并方法
          },
        ]
      : [];

    return {
      words: wordsWithForms,
      wordCount: this._vocabulary ? 1 : 0,
      queryMetadata: this._queryMetadata,
      // 移除了 wordList, wordRelationships, 和 relatedSuggestions
    };
    // {{END MODIFICATIONS}}
  }

  /**
   * 合并词形数据 - 智能返回策略
   * 根据查询策略决定返回的词形范围：
   * - 查询原型时：返回所有词形
   * - 查询派生词时：只返回当前词形和原型
   */
  private getMergedWordForms() {
    const wordForms: Array<{ name: string; form: string }> = [];

    // 1. 添加来自Vocabulary实体的formats数据（包含"不定式"等）
    if (this._vocabulary) {
      this._vocabulary.formats.forEach((format) => {
        wordForms.push({
          name: format.name,
          form: format.form,
        });
      });
    }

    // 2. 添加来自wordRelationships的数据
    const relationshipForms = convertWordRelationshipsToWordForms(this._wordRelationships);
    wordForms.push(...relationshipForms);

    // 3. 去重处理，避免重复的name-form组合
    const uniqueWordForms = wordForms.filter(
      (item, index, self) =>
        index === self.findIndex((t) => t.name === item.name && t.form === item.form)
    );

    // 4. 智能过滤策略
    return this.applySmartFilterStrategy(uniqueWordForms);
  }

  /**
   * 应用智能过滤策略
   * @param allForms 所有词形
   * @returns 根据查询策略过滤后的词形
   */
  private applySmartFilterStrategy(allForms: Array<{ name: string; form: string }>) {
    const searchTerm = this._queryMetadata.searchTerm;

    // 检查当前查询的词是否为派生词（通过检查Vocabulary的formats中是否有baseFormId）
    const isDerivedWordQuery = this.isDerivedWordQuery();

    // 如果是派生词查询，只返回当前词形和原型
    if (isDerivedWordQuery) {
      const filteredForms: Array<{ name: string; form: string }> = [];

      // 1. 添加原型（总是包含）
      const baseForm = allForms.find((form) => form.name === '原型');
      if (baseForm) {
        filteredForms.push(baseForm);
      }

      // 2. 添加当前查询词对应的词形
      const currentForms = allForms.filter((form) => form.form === searchTerm);
      currentForms.forEach((form) => {
        // 避免重复添加原型
        if (form.name !== '原型' || !baseForm) {
          filteredForms.push(form);
        }
      });

      // 3. 去重处理
      return filteredForms.filter(
        (item, index, self) =>
          index === self.findIndex((t) => t.name === item.name && t.form === item.form)
      );
    }

    // 如果是原型查询，返回所有词形
    return allForms;
  }

  /**
   * 检查当前查询是否为派生词查询
   * 通过检查Vocabulary的formats中是否有baseFormId来判断
   */
  private isDerivedWordQuery(): boolean {
    if (!this._vocabulary) {
      return false;
    }

    const searchTerm = this._queryMetadata.searchTerm;

    // 检查当前查询词在formats中是否有baseFormId
    const matchingFormat = this._vocabulary.formats.find((format) => format.form === searchTerm);

    // 如果找到匹配的format且有baseFormId，说明是派生词查询
    return matchingFormat ? !!matchingFormat.baseFormId : false;
  }

  /**
   * 添加缓存元数据
   */
  withCacheMetadata(cacheHit: boolean): SearchResult {
    const newMetadata = {
      ...this._queryMetadata,
      cacheHit,
    };

    return new SearchResult(
      this._vocabulary,
      this._wordRelationships,
      newMetadata,
      this._relatedSuggestions
    );
  }

  /**
   * 添加数据库查询统计
   */
  withDatabaseStats(queryCount: number): SearchResult {
    const newMetadata = {
      ...this._queryMetadata,
      databaseQueries: queryCount,
    };

    return new SearchResult(
      this._vocabulary,
      this._wordRelationships,
      newMetadata,
      this._relatedSuggestions
    );
  }

  /**
   * 合并相关建议
   */
  withAdditionalSuggestions(suggestions: string[]): SearchResult {
    const combinedSuggestions = [
      ...this._relatedSuggestions,
      ...suggestions.filter((s) => !this._relatedSuggestions.includes(s)),
    ];

    return new SearchResult(
      this._vocabulary,
      this._wordRelationships,
      this._queryMetadata,
      combinedSuggestions
    );
  }
}
