// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-05-29 04:30:00 +08:00]
// Reason: [导入转换函数以支持wordRelationships到wordForms的转换]
// Principle_Applied: [DRY - 复用转换逻辑]
// Optimization: [使用专门的转换函数处理数据格式转换]
// Architectural_Note (AR): [保持模块化设计，分离数据转换逻辑]
// Documentation_Note (DW): [更新导入以支持新的转换功能]
// }}
// {{START MODIFICATIONS}}
import { NextRequest, NextResponse } from 'next/server';
import { lookupWordWithRelationshipsOptimized } from '../../../../lib/lookup';
import {
  EnhancedDictionaryResponseType,
  convertWordRelationshipsToWordForms,
} from '../../../../lib/utils/dict-types';
import { Logger } from '../../../../lib/utils/logger';
import { randomUUID } from 'crypto';
// {{END MODIFICATIONS}}

/**
 * 字典API路由处理器
 * 路径: /api/dictionary/[lang]/[word]
 *
 * 支持的查询参数:
 * - fresh: boolean - 是否强制刷新缓存
 */

interface RouteParams {
  params: Promise<{
    lang: string;
    word: string;
  }>;
}

/**
 * 验证语言代码
 */
function validateLanguage(lang: string): boolean {
  // 目前主要支持英语，可扩展
  const supportedLanguages = ['en', 'english'];
  return supportedLanguages.includes(lang.toLowerCase());
}

/**
 * 验证单词格式
 */
function validateWord(word: string): boolean {
  // 基本单词格式验证：只允许字母、连字符、撇号
  const wordPattern = /^[a-zA-Z'-]+$/;
  return wordPattern.test(word) && word.length >= 1 && word.length <= 50;
}

/**
 * 创建错误响应
 */
function createErrorResponse(message: string, statusCode: number = 400, details?: any) {
  return NextResponse.json(
    {
      error: {
        message,
        statusCode,
        details,
        timestamp: new Date().toISOString(),
      },
    },
    { status: statusCode }
  );
}

/**
 * 创建成功响应（增强缓存信息）
 */
function createSuccessResponse(
  data: EnhancedDictionaryResponseType,
  cacheHit?: boolean,
  cacheSource?: string
) {
  const headers: Record<string, string> = {};

  // 添加缓存状态响应头
  if (cacheHit !== undefined) {
    headers['x-cache-status'] = cacheHit ? 'hit' : 'miss';
  }

  // 添加缓存来源响应头
  if (cacheSource) {
    headers['x-cache-source'] = cacheSource;
  }

  // 添加缓存命中率统计（如果可用）
  if ((data.queryMetadata as any)?.cacheHitTime !== undefined) {
    headers['x-cache-hit-time'] = `${(data.queryMetadata as any).cacheHitTime.toFixed(2)}ms`;
  }

  // 添加缓存层级信息
  if ((data.queryMetadata as any)?.cacheLayer) {
    headers['x-cache-layer'] = (data.queryMetadata as any).cacheLayer;
  }

  return NextResponse.json(
    {
      success: true,
      data,
      timestamp: new Date().toISOString(),
    },
    {
      status: 200,
      headers,
    }
  );
}

/**
 * GET /api/dictionary/[lang]/[word]
 * 查询指定语言的单词释义
 */
export async function GET(request: NextRequest, { params }: RouteParams) {
  const startTime = performance.now();
  const requestId = Math.random().toString(36).substring(2, 15);

  // 🚀 应用层并发控制 - 基于性能测试优化
  const { concurrencyLimiter } = await import('../../../../lib/utils/concurrency-limiter');

  return concurrencyLimiter.execute(async () => {
    // 🔍 DEBUG: 并发监控开始
    const { concurrencyMonitor } = await import('../../../../lib/utils/concurrency-monitor');
    concurrencyMonitor.startRequest(requestId, 'dictionary-api');

    // 🔍 DEBUG: 请求开始监控
    Logger.debug(`[REQ-${requestId}] 📥 API请求开始`, {
      requestId,
      url: request.url,
      method: request.method,
      timestamp: new Date().toISOString(),
      concurrency: concurrencyMonitor.getMetrics(),
      limiterStatus: concurrencyLimiter.getStatus(),
    });

    const perfTracker = {
      paramsExtraction: 0,
      validation: 0,
      lookup: 0,
      responseBuilding: 0,
      total: 0,
      // 新增详细时间追踪
      dbConnectionWait: 0,
      dbQueryExecution: 0,
      cacheOperations: 0,
      serialization: 0,
    };

    try {
      // 1. 参数提取
      const paramsStart = performance.now();
      const { lang, word } = await params;
      const { searchParams } = new URL(request.url);
      const fresh = searchParams.get('fresh') === 'true';
      perfTracker.paramsExtraction = performance.now() - paramsStart;

      // 2. 参数验证
      const validationStart = performance.now();
      if (!validateLanguage(lang)) {
        return createErrorResponse(`不支持的语言: ${lang}。目前支持: en, english`, 400, {
          supportedLanguages: ['en', 'english'],
        });
      }

      if (!validateWord(word)) {
        return createErrorResponse(
          `无效的单词格式: ${word}。单词只能包含字母、连字符和撇号，长度1-50字符`,
          400,
          {
            pattern: "^[a-zA-Z'-]+$",
            minLength: 1,
            maxLength: 50,
          }
        );
      }
      perfTracker.validation = performance.now() - validationStart;

      // 优化：使用信息日志，减少字符串拼接
      Logger.info(`Dictionary API: ${lang}/${word}`, { fresh, useCache: !fresh });

      // 3. 调用查询服务
      const lookupStart = performance.now();
      const lookupOptions = {
        useCache: !fresh,
        cacheTTL: fresh ? undefined : 3600, // 1小时缓存
      };

      // 🔍 DEBUG: 查询服务调用开始
      concurrencyMonitor.updateRequestStage(requestId, 'lookup-service');
      Logger.debug(`[REQ-${requestId}] 🔍 开始查询服务`, {
        requestId,
        word,
        lang,
        useCache: !fresh,
        cacheTTL: lookupOptions.cacheTTL,
      });

      const result = await lookupWordWithRelationshipsOptimized(word, lang, lookupOptions);
      perfTracker.lookup = performance.now() - lookupStart;

      // 🔍 DEBUG: 查询服务完成
      concurrencyMonitor.updateRequestStage(requestId, 'response-building');
      Logger.debug(`[REQ-${requestId}] ✅ 查询服务完成`, {
        requestId,
        lookupTime: Math.round(perfTracker.lookup),
        found: !!result,
        strategy: result?.queryMetadata?.searchStrategy,
        cacheHit: result?.queryMetadata?.cacheHit,
      });

      // 4. 检查查询结果
      if (!result) {
        return createErrorResponse(`查询服务返回空结果`, 500);
      }

      // 5. 响应构建
      const responseStart = performance.now();
      const apiResponse: EnhancedDictionaryResponseType = result;
      perfTracker.responseBuilding = performance.now() - responseStart;

      // 计算总时间
      perfTracker.total = performance.now() - startTime;

      // 优化：记录详细性能指标，减少字符串格式化
      const cacheHit = result.queryMetadata.cacheHit === true;
      const cacheSource = result.queryMetadata.cacheSource || 'database';

      // 优化：使用性能日志，避免toFixed()调用
      Logger.performance(`API ${word}`, {
        total: Math.round(perfTracker.total),
        params: Math.round(perfTracker.paramsExtraction),
        validation: Math.round(perfTracker.validation),
        lookup: Math.round(perfTracker.lookup),
        response: Math.round(perfTracker.responseBuilding),
        cacheHit,
        cacheSource,
        strategy: result.queryMetadata.searchStrategy,
      });

      // 如果未找到结果，返回200状态码和空的单词列表
      if (result.queryMetadata.searchStrategy === 'not_found') {
        const headers: Record<string, string> = {};

        // 添加缓存状态响应头
        if (cacheHit !== undefined) {
          headers['x-cache-status'] = cacheHit ? 'hit' : 'miss';
        }

        return NextResponse.json(
          {
            success: true,
            message: `未找到单词 "${word}" 的释义，返回空结果`,
            data: apiResponse, // apiResponse 已经包含空的 words 数组
            timestamp: new Date().toISOString(),
          },
          {
            status: 200,
            headers,
          }
        );
      }

      // 🔍 DEBUG: 请求成功完成
      concurrencyMonitor.endRequest(requestId);
      Logger.debug(`[REQ-${requestId}] 🎉 请求成功完成`, {
        requestId,
        totalTime: Math.round(perfTracker.total),
        cacheHit,
        cacheSource,
        finalConcurrency: concurrencyMonitor.getMetrics(),
      });

      return createSuccessResponse(apiResponse, cacheHit, cacheSource);
    } catch (error) {
      const processingTime = Date.now() - startTime;

      // 🔍 DEBUG: 请求失败
      concurrencyMonitor.endRequest(requestId);
      Logger.debug(`[REQ-${requestId}] ❌ 请求失败`, {
        requestId,
        processingTime: Math.round(processingTime),
        error: error instanceof Error ? error.message : String(error),
        finalConcurrency: concurrencyMonitor.getMetrics(),
      });

      // 优化：使用错误日志，保持错误信息完整
      Logger.error(`Dictionary API 查询失败`, {
        requestId,
        params,
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined,
        processingTime: Math.round(processingTime),
      });

      // 返回服务器错误
      return createErrorResponse(
        '服务器内部错误，请稍后重试',
        500,
        process.env.NODE_ENV === 'development'
          ? {
              error: error instanceof Error ? error.message : String(error),
              processingTime,
            }
          : undefined
      );
    }
  }, requestId); // 并发控制包装函数结束
}

/**
 * 处理不支持的HTTP方法
 */
export async function POST() {
  return createErrorResponse('不支持的HTTP方法: POST', 405);
}

export async function PUT() {
  return createErrorResponse('不支持的HTTP方法: PUT', 405);
}

export async function DELETE() {
  return createErrorResponse('不支持的HTTP方法: DELETE', 405);
}

export async function PATCH() {
  return createErrorResponse('不支持的HTTP方法: PATCH', 405);
}
