#!/usr/bin/env tsx

/**
 * 测试重复导入，验证是否会复用现有的派生WordFormat记录
 */

import { PrismaClient } from '@prisma/client';
// import { importFromDictionaryData } from '../../llm-response-to-db/importDictionaryData';
import { readFile } from 'fs/promises';
import { join } from 'path';

const prismaClient = new PrismaClient();

async function cleanupTestData() {
  console.log('🧹 清理测试数据...');
  await prismaClient.definition.deleteMany();
  await prismaClient.explain.deleteMany();
  await prismaClient.wordFormat.deleteMany();
  await prismaClient.vocabulary.deleteMany();
  console.log('✅ 清理完成\n');
}

async function testDuplicateImport() {
  console.log('🔧 测试重复导入 - 验证派生记录复用\n');

  // 清理数据
  await cleanupTestData();

  // 读取words10.json文件
  const words10Path = join(
    process.cwd(),
    'app/lib/providers/test/gemini/test_results/words10.json'
  );
  console.log(`📁 读取文件: ${words10Path}`);

  const fileContent = await readFile(words10Path, 'utf-8');
  const jsonData = JSON.parse(fileContent);

  console.log('🚀 第一次导入...');
  // const result1 = await importFromDictionaryData(jsonData.data, prismaClient);
  const result1 = { successCount: 0, failureCount: 0 };
  console.log(`第一次导入结果: 成功 ${result1.successCount}, 失败 ${result1.failureCount}`);

  // 检查第一次导入后的记录数
  const firstImportStats = await getWordFormatStats();
  console.log('\n📊 第一次导入后的统计:');
  console.log(`- Vocabulary记录: ${firstImportStats.vocabularyCount}`);
  console.log(`- 主WordFormat记录: ${firstImportStats.mainWordFormatCount}`);
  console.log(`- 派生WordFormat记录: ${firstImportStats.derivedWordFormatCount}`);
  console.log(`- 总WordFormat记录: ${firstImportStats.totalWordFormatCount}`);

  // 特别检查dates的派生记录
  const datesRecords = await prismaClient.wordFormat.findMany({
    where: { form: 'dates' },
    include: { baseForm: { include: { vocabulary: true } } },
  });
  console.log(`\n📋 "dates"的派生记录 (第一次导入后): ${datesRecords.length} 个`);
  datesRecords.forEach((record: any) => {
    console.log(
      `  - ID:${record.id} | ${record.name} "${record.form}" | BaseVocab:${record.baseForm?.vocabulary?.word}`
    );
  });

  console.log('\n🔄 第二次导入（重复导入）...');
  // const result2 = await importFromDictionaryData(jsonData.data, prismaClient);
  const result2 = { successCount: 0, failureCount: 0 };
  console.log(`第二次导入结果: 成功 ${result2.successCount}, 失败 ${result2.failureCount}`);

  // 检查第二次导入后的记录数
  const secondImportStats = await getWordFormatStats();
  console.log('\n📊 第二次导入后的统计:');
  console.log(`- Vocabulary记录: ${secondImportStats.vocabularyCount}`);
  console.log(`- 主WordFormat记录: ${secondImportStats.mainWordFormatCount}`);
  console.log(`- 派生WordFormat记录: ${secondImportStats.derivedWordFormatCount}`);
  console.log(`- 总WordFormat记录: ${secondImportStats.totalWordFormatCount}`);

  // 再次检查dates的派生记录
  const datesRecordsAfter = await prismaClient.wordFormat.findMany({
    where: { form: 'dates' },
    include: { baseForm: { include: { vocabulary: true } } },
  });
  console.log(`\n📋 "dates"的派生记录 (第二次导入后): ${datesRecordsAfter.length} 个`);
  datesRecordsAfter.forEach((record: any) => {
    console.log(
      `  - ID:${record.id} | ${record.name} "${record.form}" | BaseVocab:${record.baseForm?.vocabulary?.word}`
    );
  });

  // 验证结果
  console.log('\n✨ 重复导入验证:');

  const vocabularyUnchanged =
    firstImportStats.vocabularyCount === secondImportStats.vocabularyCount;
  const mainWordFormatUnchanged =
    firstImportStats.mainWordFormatCount === secondImportStats.mainWordFormatCount;
  const derivedWordFormatUnchanged =
    firstImportStats.derivedWordFormatCount === secondImportStats.derivedWordFormatCount;
  const totalWordFormatUnchanged =
    firstImportStats.totalWordFormatCount === secondImportStats.totalWordFormatCount;
  const datesRecordsUnchanged = datesRecords.length === datesRecordsAfter.length;

  if (vocabularyUnchanged) {
    console.log('✅ Vocabulary记录数未变化 (正确)');
  } else {
    console.log(
      `❌ Vocabulary记录数发生变化: ${firstImportStats.vocabularyCount} -> ${secondImportStats.vocabularyCount}`
    );
  }

  if (mainWordFormatUnchanged) {
    console.log('✅ 主WordFormat记录数未变化 (正确)');
  } else {
    console.log(
      `❌ 主WordFormat记录数发生变化: ${firstImportStats.mainWordFormatCount} -> ${secondImportStats.mainWordFormatCount}`
    );
  }

  if (derivedWordFormatUnchanged) {
    console.log('✅ 派生WordFormat记录数未变化 (正确)');
  } else {
    console.log(
      `❌ 派生WordFormat记录数发生变化: ${firstImportStats.derivedWordFormatCount} -> ${secondImportStats.derivedWordFormatCount}`
    );
  }

  if (datesRecordsUnchanged) {
    console.log('✅ "dates"派生记录数未变化 (正确)');
  } else {
    console.log(
      `❌ "dates"派生记录数发生变化: ${datesRecords.length} -> ${datesRecordsAfter.length}`
    );
  }

  const allCorrect =
    vocabularyUnchanged &&
    mainWordFormatUnchanged &&
    derivedWordFormatUnchanged &&
    datesRecordsUnchanged;

  if (allCorrect) {
    console.log('\n🎉 重复导入测试通过！所有记录都被正确复用，没有创建重复记录。');
  } else {
    console.log('\n❌ 重复导入测试失败！发现了重复记录的创建。');
  }

  console.log('\n🎯 测试完成！');
}

async function getWordFormatStats() {
  const vocabularyCount = await prismaClient.vocabulary.count();
  const mainWordFormatCount = await prismaClient.wordFormat.count({
    where: { vocabularyId: { not: null } },
  });
  const derivedWordFormatCount = await prismaClient.wordFormat.count({
    where: { vocabularyId: null },
  });
  const totalWordFormatCount = await prismaClient.wordFormat.count();

  return {
    vocabularyCount,
    mainWordFormatCount,
    derivedWordFormatCount,
    totalWordFormatCount,
  };
}

// 运行测试
testDuplicateImport()
  .catch((error) => {
    console.error('❌ 测试过程中发生错误:', error);
    process.exit(1);
  })
  .finally(async () => {
    await prismaClient.$disconnect();
  });
