import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TokenBlacklistService, TokenBlacklistEntry } from '../TokenBlacklistService';

// Mock Prisma
vi.mock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: {
    tokenBlacklist: {
      create: vi.fn(),
      createMany: vi.fn(),
      findUnique: vi.fn(),
      deleteMany: vi.fn(),  
      count: vi.fn(),
      groupBy: vi.fn(),
      findFirst: vi.fn(),
    },
    refreshToken: {
      findMany: vi.fn(),
      updateMany: vi.fn(),
    },
  },
}));

// 获取mock实例
import { prisma } from '@/lib/4-infrastructure/database/prisma';
const mockPrisma = prisma as any;

describe('TokenBlacklistService', () => {
  let tokenBlacklistService: TokenBlacklistService;
  let mockBlacklistEntry: TokenBlacklistEntry;

  beforeEach(() => {
    vi.clearAllMocks();
    tokenBlacklistService = TokenBlacklistService.getInstance();

    mockBlacklistEntry = {
      jti: 'jwt-abc-123',
      userId: 'user-123',
      tokenHash: 'hash-abc-123',
      reason: 'logout',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24小时后
      ipAddress: '127.0.0.1',
      userAgent: 'test-agent',
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('单例模式', () => {
    it('应该始终返回同一个实例', () => {
      // Act
      const instance1 = TokenBlacklistService.getInstance();
      const instance2 = TokenBlacklistService.getInstance();

      // Assert
      expect(instance1).toBe(instance2);
      expect(instance1).toBe(tokenBlacklistService);
    });

    it('应该在多次调用getInstance时保持状态', () => {
      // Arrange
      const instance1 = TokenBlacklistService.getInstance();
      const instance2 = TokenBlacklistService.getInstance();

      // Act & Assert
      expect(instance1.constructor).toBe(instance2.constructor);
      expect(Object.getPrototypeOf(instance1)).toBe(Object.getPrototypeOf(instance2));
    });
  });

  describe('令牌黑名单操作', () => {
    it('应该成功将令牌加入黑名单', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.create.mockResolvedValue({
        id: 'blacklist-123',
        ...mockBlacklistEntry,
        createdAt: new Date(),
      });

      // Act
      await tokenBlacklistService.blacklistToken(mockBlacklistEntry);

      // Assert
      expect(mockPrisma.tokenBlacklist.create).toHaveBeenCalledWith({
        data: {
          jti: 'jwt-abc-123',
          userId: 'user-123',
          tokenHash: 'hash-abc-123',
          reason: 'logout',
          expiresAt: mockBlacklistEntry.expiresAt,
          ipAddress: '127.0.0.1',
          userAgent: 'test-agent',
        },
      });
    });

    it('应该正确验证令牌是否在黑名单中', async () => {
      // Arrange
      const jti = 'jwt-blacklisted-123';
      mockPrisma.tokenBlacklist.findUnique.mockResolvedValue({
        id: 'blacklist-123',
        jti,
        userId: 'user-123',
        expiresAt: new Date(Date.now() + 60000), // 1分钟后过期
        createdAt: new Date(),
      });

      // Act
      const result = await tokenBlacklistService.isBlacklisted(jti);

      // Assert
      expect(result).toBe(true);
      expect(mockPrisma.tokenBlacklist.findUnique).toHaveBeenCalledWith({
        where: { jti },
      });
    });

    it('应该对不在黑名单中的令牌返回false', async () => {
      // Arrange
      const jti = 'jwt-not-blacklisted';
      mockPrisma.tokenBlacklist.findUnique.mockResolvedValue(null);

      // Act
      const result = await tokenBlacklistService.isBlacklisted(jti);

      // Assert
      expect(result).toBe(false);
    });

    it('应该支持令牌哈希黑名单检查', async () => {
      // Arrange
      const tokenHash = 'hash-test-123';
      mockPrisma.tokenBlacklist.findUnique.mockResolvedValue({
        id: 'blacklist-456',
        tokenHash,
        expiresAt: new Date(Date.now() + 60000),
        createdAt: new Date(),
      });

      // Act
      const result = await tokenBlacklistService.isTokenHashBlacklisted(tokenHash);

      // Assert
      expect(result).toBe(true);
      expect(mockPrisma.tokenBlacklist.findUnique).toHaveBeenCalledWith({
        where: { tokenHash },
      });
    });

    it('应该处理重复黑名单操作', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.create.mockRejectedValue(
        new Error('Unique constraint failed')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(
        tokenBlacklistService.blacklistToken(mockBlacklistEntry)
      ).rejects.toThrow('Failed to blacklist token');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to blacklist token:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('过期令牌处理', () => {
    it('应该自动清理过期的黑名单条目', async () => {
      // Arrange
      const jti = 'jwt-expired-123';
      const expiredEntry = {
        id: 'blacklist-expired',
        jti,
        userId: 'user-123',
        expiresAt: new Date(Date.now() - 60000), // 1分钟前过期
        createdAt: new Date(),
      };
      
      mockPrisma.tokenBlacklist.findUnique.mockResolvedValue(expiredEntry);
      mockPrisma.tokenBlacklist.deleteMany.mockResolvedValue({ count: 1 });

      // Act
      const result = await tokenBlacklistService.isBlacklisted(jti);

      // Assert
      expect(result).toBe(false);
      expect(mockPrisma.tokenBlacklist.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: { lte: expect.any(Date) },
        },
      });
    });

    it('应该在清理过期条目时返回清理数量', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.deleteMany.mockResolvedValue({ count: 5 });

      // Act
      const cleanedCount = await tokenBlacklistService.cleanupExpiredTokens();

      // Assert
      expect(cleanedCount).toBe(5);
      expect(mockPrisma.tokenBlacklist.deleteMany).toHaveBeenCalledWith({
        where: {
          expiresAt: { lte: expect.any(Date) },
        },
      });
    });

    it('应该处理清理操作中的错误', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.deleteMany.mockRejectedValue(
        new Error('Database connection failed')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      const result = await tokenBlacklistService.cleanupExpiredTokens();

      // Assert
      expect(result).toBe(0);
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to cleanup expired tokens:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('批量黑名单操作', () => {
    it('应该支持批量添加令牌到黑名单', async () => {
      // Arrange
      const batchEntries: TokenBlacklistEntry[] = [
        { ...mockBlacklistEntry, jti: 'jwt-1', tokenHash: 'hash-1' },
        { ...mockBlacklistEntry, jti: 'jwt-2', tokenHash: 'hash-2' },
        { ...mockBlacklistEntry, jti: 'jwt-3', tokenHash: 'hash-3' },
      ];

      mockPrisma.tokenBlacklist.createMany.mockResolvedValue({ count: 3 });

      // Act
      await tokenBlacklistService.blacklistTokens(batchEntries);

      // Assert
      expect(mockPrisma.tokenBlacklist.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({ jti: 'jwt-1', tokenHash: 'hash-1' }),
          expect.objectContaining({ jti: 'jwt-2', tokenHash: 'hash-2' }),
          expect.objectContaining({ jti: 'jwt-3', tokenHash: 'hash-3' }),
        ]),
        skipDuplicates: true,
      });
    });

    it('应该在批量操作中跳过重复项', async () => {
      // Arrange
      const batchEntries: TokenBlacklistEntry[] = [
        { ...mockBlacklistEntry, jti: 'jwt-duplicate' },
      ];

      mockPrisma.tokenBlacklist.createMany.mockResolvedValue({ count: 0 });

      // Act
      await tokenBlacklistService.blacklistTokens(batchEntries);

      // Assert
      expect(mockPrisma.tokenBlacklist.createMany).toHaveBeenCalledWith(
        expect.objectContaining({
          skipDuplicates: true,
        })
      );
    });

    it('应该正确处理批量操作中的部分失败', async () => {
      // Arrange
      const batchEntries: TokenBlacklistEntry[] = [
        { ...mockBlacklistEntry, jti: 'jwt-1' },
      ];

      mockPrisma.tokenBlacklist.createMany.mockRejectedValue(
        new Error('Batch operation failed')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(
        tokenBlacklistService.blacklistTokens(batchEntries)
      ).rejects.toThrow('Failed to blacklist tokens');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to blacklist tokens:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('用户令牌管理', () => {
    it('应该能够将用户的所有令牌加入黑名单', async () => {
      // Arrange
      const userId = 'user-123';
      const reason = 'password_change';
      const ipAddress = '***********';
      const userAgent = 'Mozilla/5.0';

      mockPrisma.refreshToken.findMany.mockResolvedValue([
        { id: 'refresh-1', userId, token: 'token-1' },
        { id: 'refresh-2', userId, token: 'token-2' },
      ]);
      mockPrisma.refreshToken.updateMany.mockResolvedValue({ count: 2 });
      mockPrisma.tokenBlacklist.create.mockResolvedValue({
        id: 'blacklist-user-revocation',
      });

      // Act
      await tokenBlacklistService.blacklistAllUserTokens(
        userId,
        reason,
        ipAddress,
        userAgent
      );

      // Assert
      expect(mockPrisma.refreshToken.findMany).toHaveBeenCalledWith({
        where: {
          userId,
          isRevoked: false,
          expiresAt: { gt: expect.any(Date) },
        },
      });

      expect(mockPrisma.refreshToken.updateMany).toHaveBeenCalledWith({
        where: { userId },
        data: {
          isRevoked: true,
          revokedAt: expect.any(Date),
          revokedBy: reason,
        },
      });

      expect(mockPrisma.tokenBlacklist.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId,
          reason,
          ipAddress,
          userAgent,
        }),
      });
    });

    it('应该正确撤销用户的refresh token', async () => {
      // Arrange
      const userId = 'user-456';
      const reason = 'security';

      mockPrisma.refreshToken.findMany.mockResolvedValue([
        { id: 'refresh-active', userId, isRevoked: false },
      ]);
      mockPrisma.refreshToken.updateMany.mockResolvedValue({ count: 1 });
      mockPrisma.tokenBlacklist.create.mockResolvedValue({ id: 'blacklist-123' });

      // Act
      await tokenBlacklistService.blacklistAllUserTokens(userId, reason);

      // Assert
      expect(mockPrisma.refreshToken.updateMany).toHaveBeenCalledWith({
        where: { userId },
        data: {
          isRevoked: true,
          revokedAt: expect.any(Date),
          revokedBy: reason,
        },
      });
    });

    it('应该记录撤销原因和时间戳', async () => {
      // Arrange
      const userId = 'user-789';
      const reason = 'password_change';
      const ipAddress = '********';

      mockPrisma.refreshToken.findMany.mockResolvedValue([]);
      mockPrisma.refreshToken.updateMany.mockResolvedValue({ count: 0 });
      mockPrisma.tokenBlacklist.create.mockResolvedValue({ id: 'blacklist-456' });

      // Act
      await tokenBlacklistService.blacklistAllUserTokens(userId, reason, ipAddress);

      // Assert
      expect(mockPrisma.tokenBlacklist.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId,
          reason,
          ipAddress,
          jti: expect.stringContaining(`user_${userId}_`),
          tokenHash: expect.any(String),
          expiresAt: expect.any(Date),
        }),
      });
    });

    it('应该处理用户令牌管理中的错误', async () => {
      // Arrange
      const userId = 'user-error';
      const reason = 'logout';

      mockPrisma.refreshToken.findMany.mockRejectedValue(
        new Error('Database query failed')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act & Assert
      await expect(
        tokenBlacklistService.blacklistAllUserTokens(userId, reason)
      ).rejects.toThrow('Failed to blacklist user tokens');

      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to blacklist all user tokens:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('强制过期检查', () => {
    it('应该正确判断令牌是否需要强制过期', async () => {
      // Arrange
      const userId = 'user-123';
      const tokenIssuedTime = Math.floor(Date.now() / 1000) - 3600; // 1小时前签发
      
      mockPrisma.tokenBlacklist.findFirst.mockResolvedValue({
        id: 'security-revocation',
        userId,
        reason: 'password_change',
        createdAt: new Date(Date.now() - 1800 * 1000), // 30分钟前创建
      });

      // Act
      const shouldExpire = await tokenBlacklistService.shouldForceExpireUserTokens(
        userId,
        tokenIssuedTime
      );

      // Assert
      expect(shouldExpire).toBe(true);
      expect(mockPrisma.tokenBlacklist.findFirst).toHaveBeenCalledWith({
        where: {
          userId,
          reason: { in: ['password_change', 'security'] },
          createdAt: { gt: new Date(tokenIssuedTime * 1000) },
        },
        orderBy: { createdAt: 'desc' },
      });
    });

    it('应该基于令牌签发时间进行判断', async () => {
      // Arrange
      const userId = 'user-456';
      const tokenIssuedTime = Math.floor(Date.now() / 1000) - 600; // 10分钟前签发
      
      // 没有在令牌签发后的安全事件
      mockPrisma.tokenBlacklist.findFirst.mockResolvedValue(null);

      // Act
      const shouldExpire = await tokenBlacklistService.shouldForceExpireUserTokens(
        userId,
        tokenIssuedTime
      );

      // Assert
      expect(shouldExpire).toBe(false);
    });

    it('应该处理多个安全事件的情况', async () => {
      // Arrange
      const userId = 'user-789';
      const tokenIssuedTime = Math.floor(Date.now() / 1000) - 7200; // 2小时前签发
      
      // 返回最近的安全事件
      mockPrisma.tokenBlacklist.findFirst.mockResolvedValue({
        id: 'latest-security-event',
        userId,
        reason: 'security',
        createdAt: new Date(Date.now() - 3600 * 1000), // 1小时前
      });

      // Act
      const shouldExpire = await tokenBlacklistService.shouldForceExpireUserTokens(
        userId,
        tokenIssuedTime
      );

      // Assert
      expect(shouldExpire).toBe(true);
      expect(mockPrisma.tokenBlacklist.findFirst).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { createdAt: 'desc' },
        })
      );
    });

    it('应该处理强制过期检查中的错误', async () => {
      // Arrange
      const userId = 'user-error';
      const tokenIssuedTime = Math.floor(Date.now() / 1000);

      mockPrisma.tokenBlacklist.findFirst.mockRejectedValue(
        new Error('Query execution failed')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      const result = await tokenBlacklistService.shouldForceExpireUserTokens(
        userId,
        tokenIssuedTime
      );

      // Assert
      expect(result).toBe(false); // 默认返回false保证安全
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to check force expiration:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('统计和监控', () => {
    it('应该返回准确的黑名单统计信息', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.count
        .mockResolvedValueOnce(100) // total
        .mockResolvedValueOnce(10); // expired

      mockPrisma.tokenBlacklist.groupBy.mockResolvedValue([
        { reason: 'logout', _count: { reason: 50 } },
        { reason: 'password_change', _count: { reason: 30 } },
        { reason: 'security', _count: { reason: 20 } },
      ]);

      // Act
      const stats = await tokenBlacklistService.getBlacklistStats();

      // Assert
      expect(stats).toEqual({
        total: 100,
        byReason: {
          logout: 50,
          password_change: 30,
          security: 20,
        },
        expiredCount: 10,
      });
    });

    it('应该按原因分组统计', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.count.mockResolvedValue(0);
      mockPrisma.tokenBlacklist.groupBy.mockResolvedValue([
        { reason: 'logout', _count: { reason: 25 } },
      ]);

      // Act
      const stats = await tokenBlacklistService.getBlacklistStats();

      // Assert
      expect(stats.byReason).toEqual({
        logout: 25,
      });
    });

    it('应该统计过期条目数量', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.count
        .mockResolvedValueOnce(50) // total
        .mockResolvedValueOnce(15); // expired
      mockPrisma.tokenBlacklist.groupBy.mockResolvedValue([]);

      // Act
      const stats = await tokenBlacklistService.getBlacklistStats();

      // Assert
      expect(stats.expiredCount).toBe(15);
      expect(mockPrisma.tokenBlacklist.count).toHaveBeenCalledWith({
        where: { expiresAt: { lte: expect.any(Date) } },
      });
    });

    it('应该处理统计查询中的错误', async () => {
      // Arrange
      mockPrisma.tokenBlacklist.count.mockRejectedValue(
        new Error('Statistics query failed')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      const stats = await tokenBlacklistService.getBlacklistStats();

      // Assert
      expect(stats).toEqual({
        total: 0,
        byReason: {},
        expiredCount: 0,
      });
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to get blacklist stats:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('边界条件和错误处理', () => {
    it('应该在数据库检查失败时采用安全策略', async () => {
      // Arrange
      const jti = 'jwt-db-error';
      mockPrisma.tokenBlacklist.findUnique.mockRejectedValue(
        new Error('Database connection lost')
      );
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Act
      const result = await tokenBlacklistService.isBlacklisted(jti);

      // Assert
      expect(result).toBe(true); // 安全起见，认为令牌已被列入黑名单
      expect(consoleSpy).toHaveBeenCalledWith(
        'Failed to check token blacklist:',
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });

    it('应该处理字符串哈希计算', () => {
      // Arrange
      const testString = 'test-input-for-hashing';

      // Act
      const hash1 = (tokenBlacklistService as any).hashString(testString);
      const hash2 = (tokenBlacklistService as any).hashString(testString);

      // Assert
      expect(hash1).toBe(hash2); // 相同输入应产生相同哈希
      expect(typeof hash1).toBe('string');
      expect(hash1.length).toBe(64); // SHA256输出长度
    });

    it('应该为不同输入生成不同哈希', () => {
      // Arrange
      const input1 = 'first-input';
      const input2 = 'second-input';

      // Act
      const hash1 = (tokenBlacklistService as any).hashString(input1);
      const hash2 = (tokenBlacklistService as any).hashString(input2);

      // Assert
      expect(hash1).not.toBe(hash2);
    });

    it('应该处理极大的批量操作', async () => {
      // Arrange
      const largeEntryCount = 10000;
      const largeEntries: TokenBlacklistEntry[] = Array.from(
        { length: largeEntryCount },
        (_, i) => ({
          ...mockBlacklistEntry,
          jti: `jwt-${i}`,
          tokenHash: `hash-${i}`,
        })
      );

      mockPrisma.tokenBlacklist.createMany.mockResolvedValue({
        count: largeEntryCount,
      });

      // Act
      await tokenBlacklistService.blacklistTokens(largeEntries);

      // Assert
      expect(mockPrisma.tokenBlacklist.createMany).toHaveBeenCalledWith({
        data: expect.arrayContaining([
          expect.objectContaining({ jti: 'jwt-0' }),
          expect.objectContaining({ jti: 'jwt-9999' }),
        ]),
        skipDuplicates: true,
      });
    });
  });
});