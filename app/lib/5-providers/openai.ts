import OpenAI from 'openai';
import { zodResponseFormat } from 'openai/helpers/zod';
import { z } from 'zod';
// import { isValidToken, validatePrompt } from './wordValidation';
import 'dotenv/config';

const DictionaryResponse = z.object({
  words: z.array(
    z.object({
      word: z.string().describe('单词本身'),
      explain: z
        .array(
          z.object({
            pos: z.string().describe('词性，如 noun、verb 等'),
            definitions: z
              .array(
                z.object({
                  definition: z.string().describe('definition in English'),
                  chinese: z.string().describe('explanation in Chinese'),
                  chinese_short: z
                    .string()
                    .describe(
                      '参考各个释义，选择最简短精准的中文解释 比如：世界，地球，苹果（水果）、'
                    ),
                })
              )
              .describe(
                '该词性的释义列表，如果单词有多个意思不要用逗号合并，每个意思返回一个释义，不要聚集，以常见度排序'
              ),
          })
        )
        .describe('词性–词意 列表，每项包含词性和该词性的释义列表'),
      wordFormats: z
        .array(
          z.object({
            name: z.string().describe('reply form name 用中文回复，例如：复数，第三人称单数'),
            form: z.string().describe('word in this form'),
          })
        )
        .describe('单词形式变化列表'),
      phonetic: z
        .object({
          us: z.string().describe('美式音标'),
          uk: z.string().describe('英式音标'),
        })
        .describe('音标列表'),
    })
  ),
});

const DictionaryResponseWithWordCount = DictionaryResponse.extend({
  wordCount: z.number().describe('单词数量'),
  wordList: z.array(z.string()).describe('单词列表'),
});

/**
 * 字典接口返回的数据结构
 * @typedef {DictionaryResponseType} DictionaryResponseType
 * @typedef {DictionaryResponseWithWordCountType} DictionaryResponseWithWordCountType
 */
// 从 Zod schema 中推导类型
export type DictionaryResponseType = z.infer<typeof DictionaryResponse>;
export type DictionaryResponseWithWordCountType = z.infer<typeof DictionaryResponseWithWordCount>;

/**
 * fetchDictionary 函数返回值类型
 */
export interface FetchDictionaryResult {
  /** 成功时返回的字典数据 */
  data?: DictionaryResponseWithWordCountType;
  /** 失败时返回的错误对象 */
  error?: unknown;
  /** 额外信息，如被剔除的非法词条 */
  info?: {
    invalidTokens?: string[];
  };
  /** HTTP 状态码 */
  statusCode: number;
}

/**
 * 默认示例词列表（逗号分隔）。
 * 可以根据需要替换成任意词列表以获取相应释义。
 */
const DEFAULT_PROMPT = 'world,apple,banana,cherry,date,elderberry,fig,grape,honeydew,wiki';

const DOMAIN: string = 'OPENAI';

const MODEL: string = 'gpt-4.1-mini';
// const MODEL = 'gpt-4.1-nano';
const defaultClient = new OpenAI({
  apiKey: process.env[`${DOMAIN}_API_KEY`],
  baseURL: process.env[`${DOMAIN}_BASE_URL`],
});

/**
 * 从 OpenAI API 获取单词释义
 * @param {string} [customPrompt=DEFAULT_PROMPT] - 自定义单词列表（逗号分隔），不传则使用 DEFAULT_PROMPT
 * @returns {Promise<FetchDictionaryResult>} 返回字典查询结果，包含数据、错误信息、状态码等
 * @throws {Error} 当 API 调用失败时抛出错误
 */

export const fetchDictionary = async (
  /** 自定义单词列表（逗号分隔），不传则使用 DEFAULT_PROMPT */
  customPrompt: string = DEFAULT_PROMPT,
  /** 自定义 OpenAI 客户端 */
  client: OpenAI = defaultClient,
  /** 自定义模型 */
  model: string = MODEL
): Promise<FetchDictionaryResult> => {
  /* 拆分并校验 prompt */
  const tokens = customPrompt
    .split(',')
    .map((t) => t.trim())
    .filter(Boolean);

  // const invalidTokens = tokens.filter((t) => !isValidToken(t));
  // const validTokens = tokens.filter(isValidToken);
  const validTokens = tokens; // Temporarily accept all tokens

  // 若全部非法，直接返回 400
  if (validTokens.length === 0) {
    return {
      error: { message: '全部词条非法，已取消查询' },
      info: { invalidTokens: [] },
      statusCode: 400,
    };
  }

  // 重新拼装合法 prompt
  const promptForQuery = validTokens.join(',');
  const startTime = Date.now();
  try {
    const completion = await client.beta.chat.completions.parse({
      model: model,
      messages: [
        {
          role: 'system',
          content:
            'You are a helpful dictionary. Only use the schema for dictionary responses. According to the frequency of use, list the definitions of the words in order , and try to provide all definitions. Separate words with commas, and if spaces can be considered as phrases, for example: "hello world" should be treated as a single phrase and not separated.',
        },
        { role: 'user', content: promptForQuery },
      ],
      response_format: zodResponseFormat(DictionaryResponse, 'dictionaryResponse'),
    });

    const message = completion.choices[0]?.message;

    // 若未解析出数据或解析结果为空，视为未找到词条，返回 404
    if (!message?.parsed) {
      return {
        error: { message: '未找到词条或解析失败' },
        statusCode: 404,
      };
    }

    const wordsParsed = (message.parsed as DictionaryResponseType).words;

    if (!Array.isArray(wordsParsed) || wordsParsed.length === 0) {
      return {
        error: { message: '未找到词条' },
        statusCode: 404,
      };
    }

    const wordList = wordsParsed.map((w) => w.word);
    const wordCount = wordList.length;

    const result: FetchDictionaryResult = {
      data: {
        ...message.parsed,
        wordList,
        wordCount,
      } as DictionaryResponseWithWordCountType,
      statusCode: 200,
    };

    const endTime = Date.now();
    console.log(`耗时: ${((endTime - startTime) / 1000).toFixed(2)} s `);

    // if (invalidTokens.length > 0) {
    //   result.info = { invalidTokens };
    // }

    return result;
  } catch (error: any) {
    console.error('Error fetching dictionary:', error);

    // 打印原始响应内容（如果存在）
    if (error.response) {
      console.error('原始响应内容:', error.response.data || error.response);
    }

    // 特别处理 ZodError
    if (error.name === 'ZodError' || (error.issues && Array.isArray(error.issues))) {
      console.error('Zod 验证失败，模型返回的数据结构不符合预期');

      // 尝试打印原始消息内容
      try {
        if (error.message && typeof error.message === 'string') {
          const rawContent = error.message.includes('content:')
            ? error.message.split('content:')[1]
            : error.message;
          console.error('模型原始返回:', rawContent);
        }
      } catch (e) {
        console.error('无法提取原始消息内容');
      }
    }

    return {
      error,
      statusCode: error.status || 500,
    };
  }
};
