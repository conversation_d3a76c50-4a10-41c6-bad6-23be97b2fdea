import DOMPurify from 'isomorphic-dompurify';
// import validator from 'validator';

// 净化配置选项
export interface SanitizationConfig {
  strictMode?: boolean;
  allowedTags?: string[];
  allowedAttributes?: Record<string, string[]>;
  maxLength?: number;
  preserveWhitespace?: boolean;
  removeEmptyElements?: boolean;
  customFilters?: Array<(input: string) => string>;
}

// 净化结果
export interface SanitizationResult {
  sanitized: string;
  threats: string[];
  confidence: number;
  originalLength: number;
  sanitizedLength: number;
  modifications: string[];
}

// 字段净化器
export class FieldSanitizer {
  private config: SanitizationConfig;

  constructor(config: SanitizationConfig = {}) {
    this.config = {
      strictMode: true,
      allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
      allowedAttributes: {
        a: ['href', 'title'],
        img: ['src', 'alt'],
      },
      maxLength: 10000,
      preserveWhitespace: false,
      removeEmptyElements: true,
      customFilters: [],
      ...config,
    };
  }

  // 文本净化
  sanitizeText(input: string): SanitizationResult {
    if (!input || typeof input !== 'string') {
      return {
        sanitized: '',
        threats: [],
        confidence: 1,
        originalLength: 0,
        sanitizedLength: 0,
        modifications: [],
      };
    }

    const originalLength = input.length;
    const modifications: string[] = [];
    const threats: string[] = [];
    let sanitized = input;

    // 1. 长度检查
    if (originalLength > (this.config.maxLength || 10000)) {
      sanitized = sanitized.substring(0, this.config.maxLength || 10000);
      modifications.push('截断过长文本');
      threats.push('OVERSIZED_INPUT');
    }

    // 2. HTML净化
    if (this.containsHtml(sanitized)) {
      const htmlResult = this.sanitizeHtml(sanitized);
      sanitized = htmlResult.sanitized;
      modifications.push(...htmlResult.modifications);
      threats.push(...htmlResult.threats);
    }

    // 3. 脚本注入检测和清理
    const scriptResult = this.removeScripts(sanitized);
    if (scriptResult.modified) {
      sanitized = scriptResult.sanitized;
      modifications.push('移除脚本代码');
      threats.push('SCRIPT_INJECTION');
    }

    // 4. SQL注入模式清理
    const sqlResult = this.sanitizeSqlInjection(sanitized);
    if (sqlResult.modified) {
      sanitized = sqlResult.sanitized;
      modifications.push('清理SQL注入模式');
      threats.push('SQL_INJECTION');
    }

    // 5. 路径遍历清理
    const pathResult = this.sanitizePathTraversal(sanitized);
    if (pathResult.modified) {
      sanitized = pathResult.sanitized;
      modifications.push('清理路径遍历');
      threats.push('PATH_TRAVERSAL');
    }

    // 6. 命令注入清理
    const commandResult = this.sanitizeCommandInjection(sanitized);
    if (commandResult.modified) {
      sanitized = commandResult.sanitized;
      modifications.push('清理命令注入');
      threats.push('COMMAND_INJECTION');
    }

    // 7. 特殊字符过滤
    const specialResult = this.filterSpecialCharacters(sanitized);
    if (specialResult.modified) {
      sanitized = specialResult.sanitized;
      modifications.push('过滤特殊字符');
    }

    // 8. 空白字符处理
    if (!this.config.preserveWhitespace) {
      const trimmedResult = this.normalizeWhitespace(sanitized);
      if (trimmedResult.modified) {
        sanitized = trimmedResult.sanitized;
        modifications.push('规范化空白字符');
      }
    }

    // 9. 自定义过滤器
    if (this.config.customFilters) {
      for (const filter of this.config.customFilters) {
        try {
          const filtered = filter(sanitized);
          if (filtered !== sanitized) {
            sanitized = filtered;
            modifications.push('应用自定义过滤器');
          }
        } catch (error) {
          console.warn('自定义过滤器错误:', error);
        }
      }
    }

    // 10. 空元素移除
    if (this.config.removeEmptyElements) {
      const emptyResult = this.removeEmptyElements(sanitized);
      if (emptyResult.modified) {
        sanitized = emptyResult.sanitized;
        modifications.push('移除空元素');
      }
    }

    const confidence = this.calculateConfidence(threats, modifications);

    return {
      sanitized,
      threats,
      confidence,
      originalLength,
      sanitizedLength: sanitized.length,
      modifications,
    };
  }

  // 数字净化
  sanitizeNumber(input: any): number | null {
    if (typeof input === 'number') {
      return isFinite(input) ? input : null;
    }

    if (typeof input === 'string') {
      const cleaned = input.replace(/[^0-9.-]/g, '');
      const parsed = parseFloat(cleaned);
      return isFinite(parsed) ? parsed : null;
    }

    return null;
  }

  // 邮箱净化
  sanitizeEmail(input: string): string | null {
    if (!input || typeof input !== 'string') return null;

    // 基础清理
    const cleaned = input.toLowerCase().trim();

    // 验证邮箱格式 (简单正则表达式验证)
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (emailRegex.test(cleaned)) {
      return cleaned;
    }

    return null;
  }

  // URL净化
  sanitizeUrl(input: string): string | null {
    if (!input || typeof input !== 'string') return null;

    try {
      // 移除危险协议
      const dangerousProtocols = ['javascript:', 'data:', 'vbscript:', 'file:'];
      const lower = input.toLowerCase();

      for (const protocol of dangerousProtocols) {
        if (lower.startsWith(protocol)) {
          return null;
        }
      }

      // 验证URL
      const url = new URL(input);

      // 只允许http和https
      if (!['http:', 'https:'].includes(url.protocol)) {
        return null;
      }

      return url.toString();
    } catch {
      return null;
    }
  }

  // 电话号码净化
  sanitizePhone(input: string): string | null {
    if (!input || typeof input !== 'string') return null;

    // 移除所有非数字字符
    const cleaned = input.replace(/\D/g, '');

    // 验证长度
    if (cleaned.length >= 10 && cleaned.length <= 15) {
      return cleaned;
    }

    return null;
  }

  // JSON净化
  sanitizeJson(input: string): any {
    try {
      const parsed = JSON.parse(input);
      return this.sanitizeObject(parsed);
    } catch {
      return null;
    }
  }

  // 对象净化
  sanitizeObject(obj: any, maxDepth: number = 10): any {
    if (maxDepth <= 0) return null;

    if (obj === null || obj === undefined) return obj;

    if (typeof obj === 'string') {
      return this.sanitizeText(obj).sanitized;
    }

    if (typeof obj === 'number') {
      return this.sanitizeNumber(obj);
    }

    if (typeof obj === 'boolean') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.sanitizeObject(item, maxDepth - 1));
    }

    if (typeof obj === 'object') {
      const sanitized: any = {};

      for (const [key, value] of Object.entries(obj)) {
        const sanitizedKey = this.sanitizeText(key).sanitized;
        sanitized[sanitizedKey] = this.sanitizeObject(value, maxDepth - 1);
      }

      return sanitized;
    }

    return null;
  }

  // 检查是否包含HTML
  private containsHtml(input: string): boolean {
    return /<[^>]+>/g.test(input);
  }

  // HTML净化
  private sanitizeHtml(input: string): {
    sanitized: string;
    modifications: string[];
    threats: string[];
  } {
    const modifications: string[] = [];
    const threats: string[] = [];

    // 使用DOMPurify进行HTML净化
    const config = {
      ALLOWED_TAGS: this.config.allowedTags || [],
      ALLOWED_ATTR: Object.keys(this.config.allowedAttributes || {}),
      KEEP_CONTENT: true,
      REMOVE_DATA_URI: true,
      SANITIZE_DOM: true,
    };

    const sanitized = DOMPurify.sanitize(input, config);

    if (sanitized !== input) {
      modifications.push('HTML净化');
      threats.push('HTML_CONTENT');
    }

    return { sanitized, modifications, threats };
  }

  // 移除脚本
  private removeScripts(input: string): { sanitized: string; modified: boolean } {
    const scriptPatterns = [
      /<script[^>]*>.*?<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /expression\s*\(/gi,
      /eval\s*\(/gi,
      /function\s*\(/gi,
    ];

    let sanitized = input;
    let modified = false;

    for (const pattern of scriptPatterns) {
      const newValue = sanitized.replace(pattern, '');
      if (newValue !== sanitized) {
        sanitized = newValue;
        modified = true;
      }
    }

    return { sanitized, modified };
  }

  // SQL注入净化
  private sanitizeSqlInjection(input: string): { sanitized: string; modified: boolean } {
    const sqlPatterns = [
      /['";]/g,
      /\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b/gi,
      /--/g,
      /\/\*/g,
      /\*\//g,
      /xp_/gi,
      /sp_/gi,
    ];

    let sanitized = input;
    let modified = false;

    for (const pattern of sqlPatterns) {
      const newValue = sanitized.replace(pattern, '');
      if (newValue !== sanitized) {
        sanitized = newValue;
        modified = true;
      }
    }

    return { sanitized, modified };
  }

  // 路径遍历净化
  private sanitizePathTraversal(input: string): { sanitized: string; modified: boolean } {
    const pathPatterns = [
      /\.\.[\/\\]/g,
      /\%2e\%2e/gi,
      /\%2f/gi,
      /\%5c/gi,
      /\.\.%2f/gi,
      /\.\.%5c/gi,
    ];

    let sanitized = input;
    let modified = false;

    for (const pattern of pathPatterns) {
      const newValue = sanitized.replace(pattern, '');
      if (newValue !== sanitized) {
        sanitized = newValue;
        modified = true;
      }
    }

    return { sanitized, modified };
  }

  // 命令注入净化
  private sanitizeCommandInjection(input: string): { sanitized: string; modified: boolean } {
    const commandPatterns = [
      /[;&|`$(){}[\]]/g,
      /\${[^}]*}/g,
      /`[^`]*`/g,
      /\b(rm|cat|ls|ps|kill|chmod|chown|wget|curl|nc|netcat)\b/gi,
    ];

    let sanitized = input;
    let modified = false;

    for (const pattern of commandPatterns) {
      const newValue = sanitized.replace(pattern, '');
      if (newValue !== sanitized) {
        sanitized = newValue;
        modified = true;
      }
    }

    return { sanitized, modified };
  }

  // 特殊字符过滤
  private filterSpecialCharacters(input: string): { sanitized: string; modified: boolean } {
    // 移除控制字符和不可见字符
    const sanitized = input.replace(/[\x00-\x1F\x7F-\x9F]/g, '');

    return {
      sanitized,
      modified: sanitized !== input,
    };
  }

  // 规范化空白字符
  private normalizeWhitespace(input: string): { sanitized: string; modified: boolean } {
    const sanitized = input
      .replace(/\s+/g, ' ') // 多个空白字符替换为单个空格
      .trim(); // 去除首尾空白

    return {
      sanitized,
      modified: sanitized !== input,
    };
  }

  // 移除空元素
  private removeEmptyElements(input: string): { sanitized: string; modified: boolean } {
    const sanitized = input
      .replace(/<[^>]*><\/[^>]*>/g, '') // 移除空HTML标签
      .replace(/\n\s*\n/g, '\n') // 移除空行
      .trim();

    return {
      sanitized,
      modified: sanitized !== input,
    };
  }

  // 计算置信度
  private calculateConfidence(threats: string[], modifications: string[]): number {
    const threatWeight = threats.length * 0.2;
    const modificationWeight = modifications.length * 0.1;

    return Math.max(0, 1 - threatWeight - modificationWeight);
  }
}

// 批量净化器
export class BatchSanitizer {
  private sanitizer: FieldSanitizer;

  constructor(config: SanitizationConfig = {}) {
    this.sanitizer = new FieldSanitizer(config);
  }

  // 批量净化对象
  sanitizeBatch(data: Record<string, any>): {
    sanitized: Record<string, any>;
    report: Record<string, SanitizationResult>;
    overallConfidence: number;
  } {
    const sanitized: Record<string, any> = {};
    const report: Record<string, SanitizationResult> = {};
    let totalConfidence = 0;
    let fieldCount = 0;

    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        const result = this.sanitizer.sanitizeText(value);
        sanitized[key] = result.sanitized;
        report[key] = result;
        totalConfidence += result.confidence;
        fieldCount++;
      } else if (typeof value === 'number') {
        const sanitizedValue = this.sanitizer.sanitizeNumber(value);
        sanitized[key] = sanitizedValue;
        report[key] = {
          sanitized: sanitizedValue?.toString() || '',
          threats: [],
          confidence: sanitizedValue !== null ? 1 : 0,
          originalLength: value.toString().length,
          sanitizedLength: sanitizedValue?.toString().length || 0,
          modifications: [],
        };
        totalConfidence += sanitizedValue !== null ? 1 : 0;
        fieldCount++;
      } else if (typeof value === 'object') {
        const sanitizedValue = this.sanitizer.sanitizeObject(value);
        sanitized[key] = sanitizedValue;
        report[key] = {
          sanitized: JSON.stringify(sanitizedValue),
          threats: [],
          confidence: sanitizedValue !== null ? 0.8 : 0,
          originalLength: JSON.stringify(value).length,
          sanitizedLength: JSON.stringify(sanitizedValue).length,
          modifications: ['对象净化'],
        };
        totalConfidence += sanitizedValue !== null ? 0.8 : 0;
        fieldCount++;
      } else {
        sanitized[key] = value;
        report[key] = {
          sanitized: String(value),
          threats: [],
          confidence: 1,
          originalLength: String(value).length,
          sanitizedLength: String(value).length,
          modifications: [],
        };
        totalConfidence += 1;
        fieldCount++;
      }
    }

    const overallConfidence = fieldCount > 0 ? totalConfidence / fieldCount : 0;

    return {
      sanitized,
      report,
      overallConfidence,
    };
  }
}

// 预定义净化器
export const presetSanitizers = {
  // 严格模式净化器
  strict: new FieldSanitizer({
    strictMode: true,
    allowedTags: [],
    allowedAttributes: {},
    maxLength: 1000,
    preserveWhitespace: false,
    removeEmptyElements: true,
  }),

  // 基础HTML净化器
  basicHtml: new FieldSanitizer({
    strictMode: false,
    allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br', 'a'],
    allowedAttributes: {
      a: ['href', 'title'],
    },
    maxLength: 5000,
    preserveWhitespace: true,
    removeEmptyElements: true,
  }),

  // 富文本净化器
  richText: new FieldSanitizer({
    strictMode: false,
    allowedTags: [
      'b',
      'i',
      'em',
      'strong',
      'p',
      'br',
      'a',
      'ul',
      'ol',
      'li',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
    ],
    allowedAttributes: {
      a: ['href', 'title', 'target'],
      img: ['src', 'alt', 'width', 'height'],
    },
    maxLength: 20000,
    preserveWhitespace: true,
    removeEmptyElements: false,
  }),

  // 表单输入净化器
  formInput: new FieldSanitizer({
    strictMode: true,
    allowedTags: [],
    allowedAttributes: {},
    maxLength: 500,
    preserveWhitespace: false,
    removeEmptyElements: true,
  }),
};

// 工厂函数
export function createSanitizer(config: SanitizationConfig = {}): FieldSanitizer {
  return new FieldSanitizer(config);
}

export function createBatchSanitizer(config: SanitizationConfig = {}): BatchSanitizer {
  return new BatchSanitizer(config);
}
