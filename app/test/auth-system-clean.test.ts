/**
 * 认证系统完整自动化测试套件（重构版）
 * 直接测试现有API端点，使用数据库操作进行清理
 * 测试注册 -> 登录验证 -> 删除用户的完整流程
 */

import { describe, it, expect, beforeEach, afterEach, afterAll } from 'vitest';
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

// 直接使用数据库客户端进行测试数据管理
const testDb = new PrismaClient();

/**
 * HTTP请求辅助函数
 */
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      signal: controller.signal,
      ...options,
    });

    clearTimeout(timeoutId);

    let data;
    try {
      data = await response.json();
    } catch {
      data = {};
    }

    return {
      response,
      data,
      status: response.status,
      ok: response.ok,
    };
  } catch (error) {
    // 如果请求失败（例如服务器未运行），返回模拟的响应
    console.error('API Request failed:', error);
    console.error('Endpoint:', endpoint);
    console.error('Options:', options);
    return {
      response: null,
      data: { error: 'Connection failed', message: 'Test server not available', details: error },
      status: 503,
      ok: false,
    };
  }
}

/**
 * 数据库清理函数
 */
async function cleanupUser(email: string) {
  try {
    const user = await testDb.user.findUnique({ where: { email } });
    if (user) {
      // 删除关联数据
      await testDb.$transaction(async (tx) => {
        await tx.account.deleteMany({ where: { userId: user.id } });
        await tx.session.deleteMany({ where: { userId: user.id } });
        await tx.refreshToken.deleteMany({ where: { userId: user.id } });
        await tx.rateLimit.deleteMany({ where: { userId: user.id } });
        await tx.userWordStat.deleteMany({ where: { userId: user.id } });
        await tx.user.delete({ where: { id: user.id } });
      });
    }
  } catch (error) {
    console.log(`清理用户失败 ${email}:`, error.message);
  }
}

/**
 * 直接在数据库中验证用户登录
 */
async function verifyUserCredentials(email: string, password: string) {
  const user = await testDb.user.findUnique({
    where: { email },
    select: { passwordHash: true, isActive: true },
  });

  if (!user || !user.passwordHash) return false;

  const isValid = await bcrypt.compare(password, user.passwordHash);
  return isValid && user.isActive;
}

describe('🔐 认证系统完整测试套件（重构版）', () => {
  const testUsers: string[] = [];

  // 记录测试用户，用于清理
  function trackUser(email: string) {
    testUsers.push(email);
  }

  // 每个测试后清理数据
  afterEach(async () => {
    for (const email of testUsers) {
      await cleanupUser(email);
    }
    testUsers.length = 0; // 清空数组
  });

  describe('📝 用户注册测试', () => {
    it('✅ 应该成功注册新用户', async () => {
      const testEmail = `test-register-${Date.now()}@example.com`;
      trackUser(testEmail);

      const { data, status, ok } = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'qwer1234',
          name: 'Test User',
        }),
      });

      expect(ok).toBe(true);
      expect(status).toBe(201);
      expect(data.message).toBe('User created successfully');
      expect(data.user.email).toBe(testEmail);

      // 验证用户确实存在于数据库中
      const dbUser = await testDb.user.findUnique({
        where: { email: testEmail },
      });
      expect(dbUser).toBeTruthy();
      expect(dbUser.email).toBe(testEmail);
    });

    it('❌ 应该拒绝无效邮箱', async () => {
      const { data, status, ok } = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: 'invalid-email',
          password: 'qwer1234',
        }),
      });

      expect(ok).toBe(false);
      expect(status).toBe(400);
      expect(data.message).toBe('Invalid input');
    });

    it('❌ 应该拒绝过短密码', async () => {
      const testEmail = `short-pwd-${Date.now()}@example.com`;

      const { data, status, ok } = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: '123',
        }),
      });

      expect(ok).toBe(false);
      expect(status).toBe(400);
      expect(data.message).toBe('Invalid input');
    });

    it('❌ 应该拒绝重复邮箱注册', async () => {
      const testEmail = `duplicate-${Date.now()}@example.com`;
      trackUser(testEmail);

      // 先创建用户
      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'qwer1234',
        }),
      });

      // 尝试再次注册相同邮箱
      const { data, status, ok } = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'different-password',
        }),
      });

      expect(ok).toBe(false);
      expect(status).toBe(400);
      expect(data.message).toBe('User already exists with this email');
      expect(data.type).toBe('user_exists');
    });

    it('✅ 智能登录：已注册邮箱+正确密码应该自动登录', async () => {
      const testEmail = `smart-login-${Date.now()}@example.com`;
      const testPassword = 'qwer1234';
      trackUser(testEmail);

      // 先创建用户
      const registerResponse = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: testPassword,
          name: 'Smart Login User',
        }),
      });

      expect(registerResponse.status).toBe(201);

      // 在注册入口使用已注册邮箱+正确密码（模拟用户点错入口）
      const { data, status, ok } = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: testPassword,
        }),
      });

      expect(ok).toBe(true);
      expect(status).toBe(200);
      expect(data.message).toBe('Login successful');
      expect(data.type).toBe('login');
      expect(data.user.email).toBe(testEmail);

      // 验证lastLoginAt已更新
      const dbUser = await testDb.user.findUnique({
        where: { email: testEmail },
      });
      expect(dbUser.lastLoginAt).toBeTruthy();
    });

    it('❌ 智能判断：已注册邮箱+错误密码应返回邮箱已注册', async () => {
      const testEmail = `smart-reject-${Date.now()}@example.com`;
      trackUser(testEmail);

      // 先创建用户
      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'qwer1234',
        }),
      });

      // 在注册入口使用已注册邮箱+错误密码
      const { data, status, ok } = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'wrong-password',
        }),
      });

      expect(ok).toBe(false);
      expect(status).toBe(400);
      expect(data.message).toBe('User already exists with this email');
      expect(data.type).toBe('user_exists');

      // 不应该提示密码错误，避免泄露账号存在信息
      expect(data.message).not.toContain('password');
      expect(data.message).not.toContain('invalid');
    });
  });

  describe('🔑 用户凭据验证测试', () => {
    it('✅ 应该验证正确的用户凭据', async () => {
      const testEmail = `login-test-${Date.now()}@example.com`;
      const testPassword = 'qwer1234';
      trackUser(testEmail);

      // 先注册用户
      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: testPassword,
        }),
      });

      // 验证凭据
      const isValid = await verifyUserCredentials(testEmail, testPassword);
      expect(isValid).toBe(true);
    });

    it('❌ 应该拒绝错误密码', async () => {
      const testEmail = `wrong-pwd-${Date.now()}@example.com`;
      trackUser(testEmail);

      // 先注册用户
      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'qwer1234',
        }),
      });

      // 验证错误密码
      const isValid = await verifyUserCredentials(testEmail, 'wrong-password');
      expect(isValid).toBe(false);
    });

    it('❌ 应该拒绝不存在的用户', async () => {
      const isValid = await verifyUserCredentials('<EMAIL>', 'qwer1234');
      expect(isValid).toBe(false);
    });
  });

  describe('🗑️ 用户删除测试', () => {
    it('✅ 应该成功删除已存在用户', async () => {
      const testEmail = `delete-test-${Date.now()}@example.com`;

      // 先创建用户
      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'qwer1234',
        }),
      });

      // 删除用户
      const { data, status, ok } = await apiRequest('/api/auth/delete-user', {
        method: 'DELETE',
        body: JSON.stringify({
          email: testEmail,
        }),
      });

      expect(ok).toBe(true);
      expect(status).toBe(200);
      expect(data.message).toBe('User deleted successfully');

      // 验证用户已被删除
      const dbUser = await testDb.user.findUnique({
        where: { email: testEmail },
      });
      expect(dbUser).toBeNull();
    });

    it('❌ 应该拒绝删除不存在的用户', async () => {
      const { data, status, ok } = await apiRequest('/api/auth/delete-user', {
        method: 'DELETE',
        body: JSON.stringify({
          email: '<EMAIL>',
        }),
      });

      expect(ok).toBe(false);
      expect(status).toBe(404);
      expect(data.message).toBe('User not found');
    });
  });

  describe('🔄 完整认证流程测试', () => {
    it('✅ 完整流程：注册 -> 验证凭据 -> 删除', async () => {
      const testEmail = `complete-flow-${Date.now()}@example.com`;
      const testPassword = 'qwer1234';

      // 1. 注册
      const registerResponse = await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: testPassword,
          name: 'Complete Flow Test',
        }),
      });

      expect(registerResponse.ok).toBe(true);
      expect(registerResponse.status).toBe(201);

      // 2. 验证凭据（模拟登录验证）
      const credentialsValid = await verifyUserCredentials(testEmail, testPassword);
      expect(credentialsValid).toBe(true);

      // 3. 验证用户存在于数据库
      const dbUser = await testDb.user.findUnique({
        where: { email: testEmail },
      });
      expect(dbUser).toBeTruthy();
      expect(dbUser.email).toBe(testEmail);

      // 4. 删除用户（数据清理）
      const deleteResponse = await apiRequest('/api/auth/delete-user', {
        method: 'DELETE',
        body: JSON.stringify({
          email: testEmail,
        }),
      });

      expect(deleteResponse.ok).toBe(true);
      expect(deleteResponse.status).toBe(200);

      // 5. 验证用户已被删除
      const deletedUser = await testDb.user.findUnique({
        where: { email: testEmail },
      });
      expect(deletedUser).toBeNull();
    });
  });

  describe('🔒 数据一致性测试', () => {
    it('✅ 应该正确处理密码哈希', async () => {
      const testEmail = `hash-test-${Date.now()}@example.com`;
      const testPassword = 'qwer1234';
      trackUser(testEmail);

      // 注册用户
      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: testPassword,
        }),
      });

      // 获取用户数据
      const user = await testDb.user.findUnique({
        where: { email: testEmail },
        select: { passwordHash: true },
      });

      expect(user).toBeTruthy();
      expect(user.passwordHash).toBeTruthy();
      expect(user.passwordHash).not.toBe(testPassword); // 密码应该被哈希

      // 验证哈希密码
      const isValid = await bcrypt.compare(testPassword, user.passwordHash);
      expect(isValid).toBe(true);
    });

    it('✅ 应该设置正确的用户属性', async () => {
      const testEmail = `props-test-${Date.now()}@example.com`;
      trackUser(testEmail);

      await apiRequest('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          email: testEmail,
          password: 'qwer1234',
          name: 'Props Test User',
        }),
      });

      const user = await testDb.user.findUnique({
        where: { email: testEmail },
      });

      expect(user.email).toBe(testEmail);
      expect(user.name).toBe('Props Test User');
      expect(user.provider).toBe('credentials');
      expect(user.isActive).toBe(true);
      expect(user.createdAt).toBeTruthy();
      expect(user.updatedAt).toBeTruthy();
    });
  });
});

// 测试完成后关闭数据库连接
afterAll(async () => {
  await testDb.$disconnect();
});
