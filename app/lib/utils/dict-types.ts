import { z } from 'zod';

/**
 * 词典数据结构定义模块
 * 提供通用的词典数据结构，可被多个provider使用
 */

// 最具代表性的词性枚举
export enum RepresentativePos {
  NOUN = 'n.', // 名词 - 最基础的词性
  VERB = 'v.', // 动词 - 最基础的词性
  ADJECTIVE = 'adj.', // 形容词 - 最基础的词性
  ADVERB = 'adv.', // 副词 - 最基础的词性
  PRONOUN = 'pron.', // 代词 - 基础词性
  PREPOSITION = 'prep.', // 介词 - 基础词性
  CONJUNCTION = 'conj.', // 连词 - 基础词性
  ARTICLE = 'art.', // 冠词 - 基础词性
  DETERMINER = 'det.', // 限定词 - 基础词性
  NUMERAL = 'num.', // 数词 - 基础词性
  INTERJECTION = 'int.', // 感叹词 - 基础词性
  ABBREVIATION = 'abbr.', // 缩写 - 现代常见
  SLANG = 'slang', // 俚语 - 现代常见
  PREFIX = 'pref.', // 前缀 - 构词成分
  SUFFIX = 'suff.', // 后缀 - 构词成分
  PHRASE = 'phr.', // 短语 - 语言单位
  PARTICLE = 'part.', // 小品词 - 语法功能词
  QUANTIFIER = 'quant.', // 量词 - 语法功能词
  COMBINING_FORM = 'comb.', // 组合形式 - 构词成分
}

// enums/WordForm.ts
// 单词的各种形式（中文描述）
export enum WordForm {
  BASE = '原型',
  PLURAL = '复数',
  THIRD_PERSON_SINGULAR = '第三人称单数',
  PAST = '过去式',
  PAST_PARTICIPLE = '过去分词',
  PRESENT_PARTICIPLE = '现在分词',
  PRESENT_CONTINUOUS = '现在进行时',
  GERUND = '动名词',
  INFINITIVE = '不定式',
  COMPARATIVE = '比较级',
  SUPERLATIVE = '最高级',
  NOMINATIVE = '主格',
  ACCUSATIVE = '宾格',
  DEPENDENT_POSSESSIVE = '依赖性所有格',
  INDEPENDENT_POSSESSIVE = '独立所有格',
  SINGULAR_REFLEXIVE = '单数反身',
  PLURAL_REFLEXIVE = '复数反身',
}

// 定义词条结构
export const WordDefinition = z.object({
  definition: z.string().describe('definition in English'),
  chinese: z.string().describe('explanation in Chinese'),
  chinese_short: z
    .string()
    .describe('参考各个释义，选择最简短精准的中文解释 比如：世界，地球，苹果（水果）、'),
});

// 定义词性结构
export const PartOfSpeech = z.object({
  // pos: z.string().describe('词性，如 noun、verb 等'),
  pos: z.nativeEnum(RepresentativePos).describe('词性'),
  definitions: z
    .array(WordDefinition)
    .describe(
      '该词性的释义列表，如果单词有多个意思不要用逗号合并，每个意思返回一个释义，不要聚集，以常见度排序'
    ),
});

// 定义单词形式结构
export const WordFormat = z
  .object({
    name: z.nativeEnum(WordForm).describe('单词形式'),
    form: z.string().describe('word in this form'),
  })
  .describe('word format 尽可能包含所有单词形式');

// 定义音标结构
export const Phonetic = z.object({
  us: z.string().describe('美式音标'),
  uk: z.string().describe('英式音标'),
});

// 定义单词结构
export const Word = z.object({
  word: z.string().describe('单词本身'),
  explain: z.array(PartOfSpeech).describe('词性–词意 列表，每项包含词性和该词性的释义列表'),
  wordFormats: z.array(WordFormat).describe('单词形式变化列表'),
  phonetic: Phonetic.describe('音标列表'),
});

// 定义基础字典响应结构
export const DictionaryResponse = z.object({
  words: z.array(Word),
  illegalWords: z.array(z.string()).optional().describe('由API识别的不合法或未找到的单词列表'),
  suggests: z
    .array(
      z.object({
        word: z.string().describe('单词本身'),
        explain: z.array(PartOfSpeech).describe('词性–词意 列表，每项包含词性和该词性的释义列表'),
        wordFormats: z.array(WordFormat).describe('单词形式变化列表'),
        phonetic: Phonetic.describe('音标列表'),
      })
    )
    .optional()
    .describe('不合法单词的相似词完整信息，结构与words相同'),
});

// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-05-29 04:30:00 +08:00]
// Reason: [替换复杂的词形关系结构为简单的name-form映射格式，满足用户API需求]
// Principle_Applied: [KISS - 简化数据结构，只保留核心信息]
// Optimization: [减少响应数据复杂度，提高API易用性]
// Architectural_Note (AR): [简化API响应格式，保持数据查询逻辑不变]
// Documentation_Note (DW): [更新类型定义以支持新的wordForms格式]
// }}
// {{START MODIFICATIONS}}
// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-05-29 04:35:00 +08:00]
// Reason: [修正WordFormItem类型定义，name字段应为形态名称而非原型词]
// Principle_Applied: [数据准确性 - 确保字段含义与数据库结构一致]
// Optimization: [使用数据库原生字段含义，避免混淆]
// Architectural_Note (AR): [保持数据模型与数据库结构的一致性]
// Documentation_Note (DW): [更新字段描述以反映正确的数据含义]
// }}
// {{START MODIFICATIONS}}
// 词形项结构 - 形态名称与具体形式的映射
export const WordFormItem = z.object({
  name: z.string().describe('形态名称，如"原型"、"过去式"、"第三人称单数"'),
  form: z.string().describe('具体形式，如"do", "does", "did", "done", "doing"'),
});
// {{END MODIFICATIONS}}

// 保留原有的词形关系结构（用于内部数据处理）
export const WordFormRelationship = z.object({
  baseForm: z.string().describe('原型形式'),
  baseFormId: z.number().describe('原型WordFormat的ID'),
  allForms: z
    .array(
      z.object({
        id: z.number().describe('WordFormat ID'),
        name: z.string().describe('形式名称，如"复数"、"过去式"'),
        form: z.string().describe('具体形式'),
        isMainForm: z.boolean().describe('是否为主要形式（直接关联vocabulary）'),
      })
    )
    .describe('所有相关的词形变化'),
  totalFormsCount: z.number().describe('总变化形式数量'),
});
// {{END MODIFICATIONS}}

// 查询元数据
export const QueryMetadata = z.object({
  searchTerm: z.string().describe('用户输入的查询词'),
  matchedForm: z.string().describe('实际匹配到的词形'),
  searchStrategy: z
    .enum(['exact_vocabulary', 'main_wordformat', 'derived_wordformat', 'not_found'])
    .describe('查找策略'),
  isBaseFormQuery: z.boolean().describe('是否通过原型查询'),
  processingTimeMs: z.number().describe('处理时间（毫秒）'),
  cacheHit: z.boolean().optional().describe('是否命中缓存'),
  databaseQueries: z.number().optional().describe('数据库查询次数'),
});

// 扩展字典响应，添加单词计数和列表
export const DictionaryResponseWithWordCount = DictionaryResponse.extend({
  wordCount: z.number().describe('单词数量'),
  wordList: z.array(z.string()).describe('单词列表'),
});

// 导出suggests类型
export type SuggestedWordsType = Array<{
  word: string;
  explain: ExplainType[];
  wordFormats: WordFormatType[];
  phonetic: PhoneticType;
}>;

// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-06-10 12:46:00 +08:00]
// Reason: [完全重构API响应结构，移除冗余字段，将词形信息整合到单词对象内部]
// Principle_Applied: [API Design Best Practices - 按需提供数据，减少顶层字段]
// Optimization: [精简API响应，提高性能和易用性]
// Architectural_Note (AR): [调整API contract，使其更简洁、更符合直觉]
// Documentation_Note (DW): [更新API响应结构定义，移除wordList、wordRelationships、relatedSuggestions]
// }}
// {{START MODIFICATIONS}}
// 增强的字典响应结构（最终API返回的格式）
export const EnhancedDictionaryResponse = z.object({
  words: z.array(
    Word.extend({
      // 在每个单词内部添加简化的词形信息
      wordForms: z.array(WordFormItem).optional().describe('该单词相关的所有词形变化'),
    })
  ),
  wordCount: z.number().describe('单词数量'),
  queryMetadata: QueryMetadata.describe('查询元数据'),
  // 移除 wordList, wordRelationships, relatedSuggestions
});
// {{END MODIFICATIONS}}

/**
 * 字典接口返回的数据结构类型
 */
// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-05-29 04:30:00 +08:00]
// Reason: [添加WordFormItem类型导出，更新类型导出列表]
// Principle_Applied: [DRY - 使用Zod作为类型定义的单一来源]
// Optimization: [保持类型定义的一致性]
// Architectural_Note (AR): [确保所有相关类型都被正确导出]
// Documentation_Note (DW): [更新类型导出以支持新的wordForms格式]
// }}
// {{START MODIFICATIONS}}
export type DictionaryResponseType = z.infer<typeof DictionaryResponse>;
export type DictionaryResponseWithWordCountType = z.infer<typeof DictionaryResponseWithWordCount>;
export type EnhancedDictionaryResponseType = z.infer<typeof EnhancedDictionaryResponse>;
export type WordFormItemType = z.infer<typeof WordFormItem>;
export type WordFormRelationshipType = z.infer<typeof WordFormRelationship>;
export type QueryMetadataType = z.infer<typeof QueryMetadata>;
// {{END MODIFICATIONS}}

// {{CHENGQI:
// Action: Added
// Timestamp: [2024-07-30 14:00:00 +00:00]
// Reason: [To infer and export types from Zod schemas, ensuring consistency and removing manual duplicates.]
// Principle_Applied: [DRY - using Zod as single source of truth for types.]
// Architectural_Note (AR): [Standardizing type generation from Zod schemas.]
// Documentation_Note (DW): [Added inferred types from Zod schemas.]
// }}
// {{START MODIFICATIONS}}
export type DefinitionType = z.infer<typeof WordDefinition>;
export type ExplainType = z.infer<typeof PartOfSpeech>;
export type WordFormatType = z.infer<typeof WordFormat>; // This refers to the Zod const WordFormat
export type PhoneticType = z.infer<typeof Phonetic>; // This refers to the Zod const Phonetic
export type WordDetailType = z.infer<typeof Word>; // This refers to the Zod const Word
// {{END MODIFICATIONS}}

/**
 * HTTP 状态码常量
 */
export enum HttpStatusCode {
  OK = 200,
  BAD_REQUEST = 400,
  NOT_FOUND = 404,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  SERVICE_UNAVAILABLE = 503,
}

/**
 * 词典查询结果接口
 */
export interface DictionaryQueryResult {
  /** 成功时返回的字典数据 */
  data?: DictionaryResponseWithWordCountType;
  /** 失败时返回的错误对象 */
  error?: Error | Record<string, any> | null;
  /** 额外信息，如被剔除的非法词条 */
  info?: {
    invalidTokens?: string[];
  };
  /** HTTP 状态码 */
  statusCode: number;
}

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-29 04:30:00 +08:00]
// Reason: [添加wordRelationships到wordForms的转换函数，实现数据格式转换]
// Principle_Applied: [单一职责原则 - 专门的转换函数处理数据格式转换]
// Optimization: [提供可复用的转换逻辑]
// Architectural_Note (AR): [核心数据转换逻辑，确保数据完整性]
// Documentation_Note (DW): [添加转换函数文档说明]
// }}
// {{START MODIFICATIONS}}
// {{CHENGQI:
// Action: Modified
// Timestamp: [2025-05-29 04:35:00 +08:00]
// Reason: [修正转换函数逻辑，name字段使用形态名称而非原型词]
// Principle_Applied: [数据准确性 - 直接使用数据库中的形态名称]
// Optimization: [简化转换逻辑，直接映射数据库字段]
// Architectural_Note (AR): [保持数据转换的准确性和一致性]
// Documentation_Note (DW): [更新函数文档以反映正确的转换逻辑]
// }}
// {{START MODIFICATIONS}}
/**
 * 将词形关系数据转换为简化的wordForms格式
 * @param relationships 词形关系数组
 * @returns 转换后的wordForms数组，格式为{name: 形态名称, form: 具体形式}
 */
export function convertWordRelationshipsToWordForms(
  relationships: WordFormRelationshipType[]
): WordFormItemType[] {
  if (!relationships || relationships.length === 0) {
    return [];
  }

  const wordForms: WordFormItemType[] = [];

  relationships.forEach((relationship) => {
    // 直接使用allForms中的name（形态名称）和form（具体形式）
    relationship.allForms.forEach((formItem) => {
      wordForms.push({
        name: formItem.name, // 形态名称：如"原型"、"过去式"、"第三人称单数"
        form: formItem.form, // 具体形式：如"do"、"did"、"does"
      });
    });
  });

  // 去重处理，避免重复的name-form组合
  const uniqueWordForms = wordForms.filter(
    (item, index, self) =>
      index === self.findIndex((t) => t.name === item.name && t.form === item.form)
  );

  return uniqueWordForms;
}
// {{END MODIFICATIONS}}
// {{END MODIFICATIONS}}

/**
 * 处理 API 响应，转换为标准格式
 * @param responseText API 响应文本
 * @param clientInvalidTokens 客户端预校验时的无效词条列表
 * @returns 处理后的字典查询结果
 */
export function processApiResponse(
  responseText: string,
  clientInvalidTokens: string[]
): DictionaryQueryResult {
  try {
    const parsedJson = JSON.parse(responseText);

    if (!parsedJson.words || !Array.isArray(parsedJson.words)) {
      if (!parsedJson.illegalWords || !Array.isArray(parsedJson.illegalWords)) {
        return {
          error: { message: 'API响应格式不正确，缺少 words 或 illegalWords 字段' },
          statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR,
        };
      }
      parsedJson.words = [];
    }

    const wordList = parsedJson.words.map((w: { word: string }) => w.word);
    const wordCount = wordList.length;

    const apiIllegalWords =
      parsedJson.illegalWords && Array.isArray(parsedJson.illegalWords)
        ? parsedJson.illegalWords
        : [];

    // 检查并处理suggests字段
    const suggests =
      parsedJson.suggests && Array.isArray(parsedJson.suggests) ? parsedJson.suggests : [];

    // 合并客户端校验的 invalidTokens 和 API 返回的 illegalWords
    const allInvalidOrIllegalTokens = Array.from(
      new Set([...clientInvalidTokens, ...apiIllegalWords])
    );

    const resultData: DictionaryResponseWithWordCountType = {
      words: parsedJson.words,
      illegalWords: apiIllegalWords, // 仍然在data中保留原始的apiIllegalWords，供需要区分的场景使用
      suggests: suggests, // 添加suggests字段到结果中
      wordList,
      wordCount,
    };

    // 如果API返回的words为空，但有suggests或illegalWords，则不应判断为NOT_FOUND
    // 只有当words、suggests和illegalWords都为空时，才认为是真正的NOT_FOUND
    if (
      wordCount === 0 &&
      suggests.length === 0 &&
      apiIllegalWords.length === 0 &&
      clientInvalidTokens.length === 0
    ) {
      return {
        error: { message: '未找到任何有效词条、相似词或非法词条报告' },
        info:
          allInvalidOrIllegalTokens.length > 0
            ? { invalidTokens: allInvalidOrIllegalTokens }
            : undefined,
        statusCode: HttpStatusCode.NOT_FOUND,
      };
    }

    const result: DictionaryQueryResult = {
      data: resultData,
      statusCode: HttpStatusCode.OK,
    };

    if (allInvalidOrIllegalTokens.length > 0) {
      result.info = { invalidTokens: allInvalidOrIllegalTokens };
    }

    return result;
  } catch (err) {
    console.error('Failed to parse JSON response:', err);
    return {
      error: { message: 'JSON 解析失败', details: err },
      statusCode: HttpStatusCode.INTERNAL_SERVER_ERROR,
    };
  }
}
