/**
 * 测试配置文件
 * 为词库功能测试提供通用配置和工具函数
 */
import { beforeAll, afterAll } from 'vitest';
import { prisma } from '@/lib/4-infrastructure/database/prisma';

/**
 * 测试数据库连接状态
 */
export async function ensureDatabaseConnection() {
  try {
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    throw error;
  }
}

/**
 * 清理测试用户数据
 * @param userIds 要清理的用户ID列表
 */
export async function cleanupTestUsers(userIds: string[]) {
  if (userIds.length === 0) return;

  try {
    await prisma.userWordProficiency.deleteMany({
      where: { userId: { in: userIds } }
    });

    await prisma.userWordStat.deleteMany({
      where: { userId: { in: userIds } }
    });

    console.log(`✅ 清理了 ${userIds.length} 个测试用户的数据`);
  } catch (error) {
    console.error('❌ 清理测试用户数据失败:', error);
    throw error;
  }
}

/**
 * 清理测试词库数据
 * @param wordListNames 要清理的词库名称列表
 */
export async function cleanupTestWordLists(wordListNames: string[]) {
  if (wordListNames.length === 0) return;

  try {
    // 获取要删除的词库ID
    const wordLists = await prisma.wordList.findMany({
      where: { name: { in: wordListNames } },
      select: { id: true }
    });

    const wordListIds = wordLists.map(wl => wl.id);

    if (wordListIds.length > 0) {
      // 删除词库条目
      await prisma.wordListEntry.deleteMany({
        where: { wordListId: { in: wordListIds } }
      });

      // 删除词库
      await prisma.wordList.deleteMany({
        where: { id: { in: wordListIds } }
      });
    }

    console.log(`✅ 清理了 ${wordListNames.length} 个测试词库`);
  } catch (error) {
    console.error('❌ 清理测试词库失败:', error);
    throw error;
  }
}

/**
 * 清理测试词汇数据
 * @param words 要清理的单词列表
 */
export async function cleanupTestVocabularies(words: string[]) {
  if (words.length === 0) return;

  try {
    await prisma.vocabulary.deleteMany({
      where: { word: { in: words } }
    });

    console.log(`✅ 清理了 ${words.length} 个测试词汇`);
  } catch (error) {
    console.error('❌ 清理测试词汇失败:', error);
    throw error;
  }
}

/**
 * 创建测试词库
 */
export async function createTestWordList(data: {
  name: string;
  description: string;
  difficulty: number;
  words: Array<{
    word: string;
    phonetics?: string[];
    freqRank?: number;
  }>;
}) {
  try {
    // 创建词库
    const wordList = await prisma.wordList.create({
      data: {
        name: data.name,
        description: data.description,
        difficulty: data.difficulty,
        totalWords: data.words.length,
        isActive: true
      }
    });

    // 创建词汇和词库条目
    for (const wordData of data.words) {
      // 创建或获取词汇
      let vocabulary = await prisma.vocabulary.findUnique({
        where: { word: wordData.word }
      });

      if (!vocabulary) {
        vocabulary = await prisma.vocabulary.create({
          data: {
            word: wordData.word,
            phonetics: wordData.phonetics || [],
            freqRank: wordData.freqRank || null
          }
        });
      }

      // 创建词库条目
      await prisma.wordListEntry.create({
        data: {
          word: wordData.word,
          wordListIds: [wordList.id],
          wordListId: wordList.id
        }
      });
    }

    console.log(`✅ 创建测试词库 "${data.name}"，包含 ${data.words.length} 个单词`);
    return wordList;
  } catch (error) {
    console.error(`❌ 创建测试词库 "${data.name}" 失败:`, error);
    throw error;
  }
}

/**
 * 创建测试用户练习数据
 */
export async function createTestUserProgress(
  userId: string,
  wordList: any,
  progressData: Array<{
    word: string;
    practiceCount: number;
    errorCount: number;
    averageTime: number;
    proficiencyScore: number;
    isMarked?: boolean;
  }>
) {
  try {
    for (const progress of progressData) {
      const vocabulary = await prisma.vocabulary.findUnique({
        where: { word: progress.word }
      });

      if (!vocabulary) {
        throw new Error(`词汇 "${progress.word}" 不存在`);
      }

      await prisma.userWordProficiency.create({
        data: {
          userId,
          wordId: vocabulary.id,
          practiceCount: progress.practiceCount,
          errorCount: progress.errorCount,
          averageTime: progress.averageTime,
          proficiencyScore: progress.proficiencyScore,
          isMarked: progress.isMarked || false,
          lastPracticed: new Date()
        }
      });
    }

    console.log(`✅ 为用户 ${userId} 创建了 ${progressData.length} 条练习记录`);
  } catch (error) {
    console.error(`❌ 创建用户 ${userId} 的练习数据失败:`, error);
    throw error;
  }
}

/**
 * 验证API响应格式
 */
export function validateApiResponse(response: any, expectedStructure: any) {
  function validateStructure(obj: any, structure: any, path = '') {
    for (const key in structure) {
      const fullPath = path ? `${path}.${key}` : key;
      
      if (!(key in obj)) {
        throw new Error(`Missing property: ${fullPath}`);
      }

      if (typeof structure[key] === 'object' && !Array.isArray(structure[key])) {
        validateStructure(obj[key], structure[key], fullPath);
      } else if (Array.isArray(structure[key])) {
        if (!Array.isArray(obj[key])) {
          throw new Error(`Property ${fullPath} should be an array`);
        }
        if (structure[key].length > 0 && obj[key].length > 0) {
          validateStructure(obj[key][0], structure[key][0], `${fullPath}[0]`);
        }
      } else if (typeof structure[key] === 'string') {
        const expectedType = structure[key];
        const actualType = typeof obj[key];
        if (actualType !== expectedType && obj[key] !== null) {
          throw new Error(`Property ${fullPath} should be ${expectedType}, got ${actualType}`);
        }
      }
    }
  }

  validateStructure(response, expectedStructure);
}

/**
 * 生成随机测试数据
 */
export class TestDataGenerator {
  static randomUserId(): string {
    return `test-user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  static randomWordListName(): string {
    return `测试词库-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`;
  }

  static randomWords(count: number): Array<{
    word: string;
    phonetics: string[];
    freqRank: number;
  }> {
    const words = ['test', 'sample', 'example', 'demo', 'mock', 'fake', 'dummy', 'placeholder'];
    const result = [];

    for (let i = 0; i < count; i++) {
      const baseWord = words[i % words.length];
      const word = count > words.length ? `${baseWord}${i}` : baseWord;
      
      result.push({
        word,
        phonetics: [`/${word}/`],
        freqRank: Math.floor(Math.random() * 1000) + 100
      });
    }

    return result;
  }

  static randomPracticeResult(): {
    isCorrect: boolean;
    timeTaken: number;
    errorCount: number;
  } {
    const isCorrect = Math.random() > 0.3; // 70% 正确率
    return {
      isCorrect,
      timeTaken: Math.floor(Math.random() * 4000) + 1000, // 1-5秒
      errorCount: isCorrect ? 0 : Math.floor(Math.random() * 3) + 1
    };
  }
}

/**
 * 测试环境设置
 */
export function setupTestEnvironment() {
  beforeAll(async () => {
    await ensureDatabaseConnection();
  });

  afterAll(async () => {
    await prisma.$disconnect();
  });
}

/**
 * 性能测试工具
 */
export class PerformanceProfiler {
  private startTime: number = 0;
  private measurements: Map<string, number[]> = new Map();

  start() {
    this.startTime = Date.now();
  }

  measure(operation: string): number {
    const duration = Date.now() - this.startTime;
    
    if (!this.measurements.has(operation)) {
      this.measurements.set(operation, []);
    }
    this.measurements.get(operation)!.push(duration);
    
    return duration;
  }

  getStats(operation: string) {
    const times = this.measurements.get(operation) || [];
    if (times.length === 0) return null;

    const sum = times.reduce((a, b) => a + b, 0);
    const avg = sum / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);

    return { avg, min, max, count: times.length };
  }

  reset() {
    this.measurements.clear();
  }
}

// 导出常用的测试数据结构
export const TEST_DATA_STRUCTURES = {
  WordListStats: {
    id: 'number',
    name: 'string',
    description: 'string',
    difficulty: 'number',
    totalWords: 'number',
    isActive: 'boolean',
    userProgress: 'object' // 可以为null
  },

  WordListPracticeWord: {
    id: 'number',
    word: 'string',
    phonetics: ['string'],
    freqRank: 'number',
    userProficiency: 'object' // 可以为null
  },

  PaginationInfo: {
    currentPage: 'number',
    pageSize: 'number',
    totalWords: 'number',
    totalPages: 'number',
    hasNextPage: 'boolean',
    hasPreviousPage: 'boolean'
  },

  ApiSuccessResponse: {
    success: 'boolean',
    data: 'object',
    message: 'string'
  },

  ApiErrorResponse: {
    success: 'boolean',
    error: 'string',
    message: 'string'
  }
};