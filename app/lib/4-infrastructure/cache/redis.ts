/**
 * Redis缓存客户端
 * 基础设施层 - 缓存连接管理
 */

import Redis from 'ioredis';
import { createPool, Pool } from 'generic-pool';
import { Logger } from '../../utils/logger';

/**
 * Redis配置选项
 */
interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  retryDelayOnFailover: number;
  lazyConnect: boolean;
  keepAlive: number;
  family: number;
  keyPrefix?: string;
  // 连接池配置
  maxRetriesPerRequest?: number;
  connectTimeout?: number;
  commandTimeout?: number;
  maxLoadingTimeout?: number;
  enableReadyCheck?: boolean;
}

/**
 * Redis连接池配置选项
 */
interface RedisPoolConfig {
  max: number; // 最大连接数
  min: number; // 最小连接数
  acquireTimeoutMillis: number; // 获取连接超时时间
  idleTimeoutMillis: number; // 空闲连接超时时间
  evictionRunIntervalMillis?: number; // 清理空闲连接的间隔
  testOnBorrow?: boolean; // 获取连接时是否测试
  testOnReturn?: boolean; // 归还连接时是否测试
}

/**
 * 获取Redis配置
 */
function getRedisConfig(): RedisConfig {
  const redisUrl = process.env.REDIS_URL;

  if (redisUrl) {
    // 解析Redis URL
    const url = new URL(redisUrl);
    return {
      host: url.hostname,
      port: parseInt(url.port) || 6379,
      password: url.password || undefined,
      db: parseInt(url.pathname.slice(1)) || 0,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keepAlive: 30000,
      family: 4,
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'lucid:',
    };
  }

  // 默认配置 - 优化高并发性能
  return {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD,
    db: parseInt(process.env.REDIS_DB || '0'),
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
    lazyConnect: true,
    keepAlive: 30000,
    family: 4,
    keyPrefix: process.env.REDIS_KEY_PREFIX || 'lucid:',
    // 高并发优化配置
    connectTimeout: 10000,
    commandTimeout: 5000,
    maxLoadingTimeout: 5000,
    enableReadyCheck: true,
  };
}

/**
 * 创建Redis客户端
 * 使用连接池优化高并发性能
 */
function createRedisClient(): Redis {
  const config = getRedisConfig();

  // 单例客户端配置 - 专为高并发缓存优化
  const optimizedConfig = {
    ...config,
    // 基础连接配置
    connectTimeout: 3000, // 3秒连接超时，快速失败
    commandTimeout: 2000, // 2秒命令超时，避免阻塞
    maxLoadingTimeout: 3000, // 3秒加载超时
    enableReadyCheck: true,
    // 高并发核心优化
    enableAutoPipelining: true, // 自动管道，关键优化
    maxRetriesPerRequest: 1, // 减少重试次数，快速失败
    // 连接保活
    keepAlive: 30000,
    // 快速重连策略
    retryStrategy: (times: number) => {
      const delay = Math.min(times * 25, 500); // 更快的重试
      return delay;
    },
    // 性能优化
    lazyConnect: false, // 立即连接
    family: 4, // 强制IPv4
  };

  const client = new Redis(optimizedConfig);

  // 连接事件监听
  client.on('connect', () => {
    console.log('Redis connected with optimized pool configuration');
  });

  client.on('ready', () => {
    console.log('Redis ready - autopipelining enabled for high concurrency');
  });

  client.on('error', (error) => {
    console.error('Redis error:', error);
  });

  client.on('close', () => {
    console.log('Redis connection closed');
  });

  client.on('reconnecting', () => {
    console.log('Redis reconnecting...');
  });

  return client;
}

/**
 * Redis客户端实例（单例）
 */
export const redis = createRedisClient();

/**
 * 缓存键生成器
 */
export class CacheKeys {
  static readonly VOCABULARY = 'vocab';
  static readonly WORD_FORMS = 'forms';
  static readonly SEARCH_RESULT = 'search';
  static readonly SUGGESTIONS = 'suggest';
  static readonly STATS = 'stats';

  static vocabulary(word: string): string {
    return `${this.VOCABULARY}:${word.toLowerCase()}`;
  }

  static wordForms(word: string): string {
    return `${this.WORD_FORMS}:${word.toLowerCase()}`;
  }

  static searchResult(term: string): string {
    return `${this.SEARCH_RESULT}:${term.toLowerCase()}`;
  }

  static suggestions(prefix: string): string {
    return `${this.SUGGESTIONS}:${prefix.toLowerCase()}`;
  }

  static userStats(userId: number): string {
    return `${this.STATS}:user:${userId}`;
  }

  static globalStats(): string {
    return `${this.STATS}:global`;
  }
}

/**
 * 缓存TTL常量（秒）
 */
export const CacheTTL = {
  VOCABULARY: 24 * 60 * 60, // 24小时
  WORD_FORMS: 12 * 60 * 60, // 12小时
  SEARCH_RESULT: 6 * 60 * 60, // 6小时
  SUGGESTIONS: 60 * 60, // 1小时
  STATS: 5 * 60, // 5分钟
  SHORT: 60, // 1分钟
} as const;

/**
 * Redis健康检查
 */
export async function checkRedisHealth(): Promise<boolean> {
  try {
    const result = await redis.ping();
    return result === 'PONG';
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
}

/**
 * 获取Redis统计信息
 */
export async function getRedisStats(): Promise<{
  connected: boolean;
  memoryUsage: number;
  keyCount: number;
  hitRate: number;
  clientConnections?: number;
  commandsProcessed?: number;
  autopipeliningEnabled?: boolean;
}> {
  try {
    const info = await redis.info();
    const lines = info.split('\r\n');

    let memoryUsage = 0;
    let keyCount = 0;
    let hits = 0;
    let misses = 0;
    let clientConnections = 0;
    let commandsProcessed = 0;

    for (const line of lines) {
      if (line.startsWith('used_memory:')) {
        memoryUsage = parseInt(line.split(':')[1]);
      } else if (line.startsWith('db0:keys=')) {
        const match = line.match(/keys=(\d+)/);
        if (match) keyCount = parseInt(match[1]);
      } else if (line.startsWith('keyspace_hits:')) {
        hits = parseInt(line.split(':')[1]);
      } else if (line.startsWith('keyspace_misses:')) {
        misses = parseInt(line.split(':')[1]);
      } else if (line.startsWith('connected_clients:')) {
        clientConnections = parseInt(line.split(':')[1]);
      } else if (line.startsWith('total_commands_processed:')) {
        commandsProcessed = parseInt(line.split(':')[1]);
      }
    }

    const hitRate = hits + misses > 0 ? hits / (hits + misses) : 0;

    return {
      connected: true,
      memoryUsage,
      keyCount,
      hitRate,
      clientConnections,
      commandsProcessed,
      autopipeliningEnabled: true, // 我们已启用自动管道
    };
  } catch (error) {
    console.error('Failed to get Redis stats:', error);
    return {
      connected: false,
      memoryUsage: 0,
      keyCount: 0,
      hitRate: 0,
    };
  }
}

/**
 * 清理过期的缓存键
 */
export async function cleanupExpiredKeys(pattern: string = '*'): Promise<number> {
  try {
    const keys = await redis.keys(pattern);
    let deletedCount = 0;

    for (const key of keys) {
      const ttl = await redis.ttl(key);
      if (ttl === -1) {
        // 没有设置过期时间的键
        // 可以根据业务逻辑决定是否删除
        continue;
      }
    }

    return deletedCount;
  } catch (error) {
    console.error('Failed to cleanup expired keys:', error);
    return 0;
  }
}

/**
 * 批量设置缓存
 */
export async function setBatch(
  items: Array<{ key: string; value: any; ttl?: number }>
): Promise<boolean> {
  try {
    const pipeline = redis.pipeline();

    for (const item of items) {
      const serialized = JSON.stringify(item.value);
      if (item.ttl) {
        pipeline.setex(item.key, item.ttl, serialized);
      } else {
        pipeline.set(item.key, serialized);
      }
    }

    await pipeline.exec();
    return true;
  } catch (error) {
    console.error('Batch set failed:', error);
    return false;
  }
}

/**
 * 批量获取缓存
 */
export async function getBatch<T>(keys: string[]): Promise<(T | null)[]> {
  try {
    if (keys.length === 0) return [];

    const values = await redis.mget(...keys);
    return values.map((value) => {
      if (!value) return null;
      try {
        return JSON.parse(value) as T;
      } catch {
        return null;
      }
    });
  } catch (error) {
    console.error('Batch get failed:', error);
    return keys.map(() => null);
  }
}

/**
 * 获取Redis连接池配置
 */
export function getRedisPoolConfig(): RedisPoolConfig {
  return {
    max: parseInt(process.env.REDIS_POOL_MAX || '200'), // 高并发优化：增加到200连接
    min: parseInt(process.env.REDIS_POOL_MIN || '20'), // 保持足够的最小连接数
    acquireTimeoutMillis: parseInt(process.env.REDIS_POOL_ACQUIRE_TIMEOUT || '2000'), // 2秒获取超时，给予更多时间
    idleTimeoutMillis: parseInt(process.env.REDIS_POOL_IDLE_TIMEOUT || '300000'), // 5分钟空闲超时，减少频繁创建/销毁
    evictionRunIntervalMillis: parseInt(process.env.REDIS_POOL_EVICTION_INTERVAL || '30000'), // 30秒清理间隔，减少清理频率
    testOnBorrow: process.env.REDIS_POOL_TEST_ON_BORROW !== 'false', // 默认启用连接测试
    testOnReturn: process.env.REDIS_POOL_TEST_ON_RETURN === 'true', // 默认禁用返回测试，提高性能
  };
}

/**
 * 创建Redis连接池
 */
function createRedisPool(): Pool<Redis> {
  const poolConfig = getRedisPoolConfig();

  const factory = {
    create: async (): Promise<Redis> => {
      // 创建Redis客户端，禁用lazyConnect以立即连接
      const config = getRedisConfig();
      const optimizedConfig = {
        ...config,
        lazyConnect: false, // 立即连接，避免手动连接的复杂性
        enableAutoPipelining: false, // 连接池模式下禁用AutoPipelining，避免冲突
        retryStrategy: (times: number) => {
          const delay = Math.min(times * 50, 1000); // 减少重试延迟
          return delay;
        },
        connectTimeout: 3000, // 3秒连接超时
        commandTimeout: 2000, // 2秒命令超时
      };

      const client = new Redis(optimizedConfig);

      // 快速等待连接就绪，减少超时时间
      await new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Redis连接超时'));
        }, 3000); // 减少到3秒

        client.once('ready', () => {
          clearTimeout(timeout);
          resolve();
        });

        client.once('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });
      });

      // 减少日志输出以提高性能
      if (process.env.NODE_ENV === 'development') {
        console.log('Redis连接池：创建新连接');
      }
      return client;
    },

    destroy: async (client: Redis): Promise<void> => {
      try {
        await client.quit();
        if (process.env.NODE_ENV === 'development') {
          console.log('Redis连接池：销毁连接');
        }
      } catch (error) {
        console.error('Redis连接池：销毁连接失败:', error);
      }
    },

    validate: async (client: Redis): Promise<boolean> => {
      try {
        // 真正验证连接可用性，使用快速ping命令
        const result = await Promise.race([
          client.ping(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('验证超时')), 500)),
        ]);
        return result === 'PONG';
      } catch (error) {
        console.error('Redis连接池：连接验证失败:', error);
        return false;
      }
    },
  };

  const pool = createPool(factory, poolConfig);

  // 连接池事件监听
  pool.on('factoryCreateError', (err) => {
    console.error('Redis连接池：创建连接失败:', err);
  });

  pool.on('factoryDestroyError', (err) => {
    console.error('Redis连接池：销毁连接失败:', err);
  });

  console.log(`Redis连接池已创建 - 最大连接数: ${poolConfig.max}, 最小连接数: ${poolConfig.min}`);
  return pool;
}

/**
 * Redis连接池实例
 */
export const redisPool = createRedisPool();

/**
 * 从连接池获取Redis连接
 */
export async function getRedisConnection(): Promise<Redis> {
  try {
    const connection = await redisPool.acquire();
    // 优化：使用新的日志系统，环境变量控制
    Logger.redisPool('获取连接', {
      borrowed: redisPool.borrowed,
      available: redisPool.available,
    });
    return connection;
  } catch (error) {
    Logger.error('Redis连接池：获取连接失败', error);
    throw error;
  }
}

/**
 * 释放Redis连接回连接池
 */
export async function releaseRedisConnection(connection: Redis): Promise<void> {
  try {
    await redisPool.release(connection);
    // 优化：使用新的日志系统，环境变量控制
    Logger.redisPool('释放连接', {
      borrowed: redisPool.borrowed,
      available: redisPool.available,
    });
  } catch (error) {
    Logger.error('Redis连接池：释放连接失败', error);
    throw error;
  }
}

/**
 * 获取Redis连接池统计信息
 */
export function getRedisPoolStats(): {
  size: number;
  available: number;
  borrowed: number;
  pending: number;
  max: number;
  min: number;
} {
  return {
    size: redisPool.size,
    available: redisPool.available,
    borrowed: redisPool.borrowed,
    pending: redisPool.pending,
    max: redisPool.max,
    min: redisPool.min,
  };
}

/**
 * 优雅关闭Redis连接
 */
export async function disconnectRedis(): Promise<void> {
  try {
    await redis.quit();
    console.log('Redis connection closed gracefully');
  } catch (error) {
    console.error('Error closing Redis connection:', error);
  }
}

/**
 * 优雅关闭Redis连接池
 */
export async function disconnectRedisPool(): Promise<void> {
  try {
    await redisPool.drain();
    await redisPool.clear();
    console.log('Redis连接池已关闭');
  } catch (error) {
    console.error('关闭Redis连接池失败:', error);
  }
}

// 进程退出时清理连接
process.on('beforeExit', async () => {
  await Promise.all([disconnectRedis(), disconnectRedisPool()]);
});

process.on('SIGINT', async () => {
  await Promise.all([disconnectRedis(), disconnectRedisPool()]);
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await Promise.all([disconnectRedis(), disconnectRedisPool()]);
  process.exit(0);
});
