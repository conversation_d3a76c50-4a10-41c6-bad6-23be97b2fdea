/**
 * 邮箱验证工具函数
 * 提供更严格的邮箱格式验证
 */

/**
 * 严格的邮箱验证函数
 * 验证规则：
 * - 用户名部分：1-64个字符，允许字母、数字、点号、连字符、下划线
 * - 域名部分：必须包含至少一个点号，域名和顶级域名都必须是有效格式
 * - 顶级域名：2-6个字母
 */
export function validateEmailStrict(email: string): { isValid: boolean; error?: string } {
  if (!email || typeof email !== 'string') {
    return { isValid: false, error: '邮箱地址不能为空' };
  }

  // 基础长度检查
  if (email.length > 254) {
    return { isValid: false, error: '邮箱地址过长' };
  }

  // 更严格的邮箱验证正则表达式
  const emailRegex =
    /^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?@[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]*[a-zA-Z0-9])?)*\.[a-zA-Z]{2,6}$/;

  if (!emailRegex.test(email)) {
    return { isValid: false, error: '请输入有效的邮箱地址' };
  }

  // 检查是否有连续的点号
  if (email.includes('..')) {
    return { isValid: false, error: '邮箱地址格式不正确' };
  }

  // 分割邮箱地址
  const [localPart, domainPart] = email.split('@');

  // 验证用户名部分
  if (!localPart || localPart.length === 0 || localPart.length > 64) {
    return { isValid: false, error: '邮箱用户名格式不正确' };
  }

  // 用户名不能以点号开头或结尾
  if (localPart.startsWith('.') || localPart.endsWith('.')) {
    return { isValid: false, error: '邮箱用户名不能以点号开头或结尾' };
  }

  // 验证域名部分
  if (!domainPart || domainPart.length === 0 || domainPart.length > 253) {
    return { isValid: false, error: '邮箱域名格式不正确' };
  }

  // 域名不能以点号开头或结尾
  if (domainPart.startsWith('.') || domainPart.endsWith('.')) {
    return { isValid: false, error: '邮箱域名格式不正确' };
  }

  // 检查顶级域名
  const domainParts = domainPart.split('.');
  const tld = domainParts[domainParts.length - 1];

  if (!tld || tld.length < 2 || tld.length > 6 || !/^[a-zA-Z]+$/.test(tld)) {
    return { isValid: false, error: '邮箱域名后缀不正确' };
  }

  // 检查域名部分是否包含数字结尾（如 .com1）
  if (/\d$/.test(tld)) {
    return { isValid: false, error: '邮箱域名后缀不能以数字结尾' };
  }

  return { isValid: true };
}

/**
 * 简单的邮箱验证函数（向后兼容）
 */
export function validateEmail(email: string): boolean {
  return validateEmailStrict(email).isValid;
}
