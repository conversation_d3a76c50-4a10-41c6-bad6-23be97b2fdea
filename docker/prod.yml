version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: Dockerfile
    container_name: lucid-bd-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=**************************************/dictionary
      - REDIS_URL=redis://redis:6379
      - NODE_ENV=production
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy

  db:
    image: postgres:16-alpine
    container_name: lucid-bd-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: dictionary
    volumes:
      - postgres_data_prod:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: lucid-bd-redis-prod
    restart: unless-stopped
    volumes:
      - redis_data_prod:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  redisinsight:
    image: redis/redisinsight:latest
    container_name: lucid-bd-redisinsight-prod
    restart: unless-stopped
    ports:
      - "5540:5540"
    volumes:
      - redisinsight_data_prod:/data
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - REDIS_HOSTS=local:redis:6379

volumes:
  postgres_data_prod:
  redis_data_prod:
  redisinsight_data_prod:
