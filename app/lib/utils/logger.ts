/**
 * 高性能日志系统
 * 专为减少高并发下的日志开销而设计
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

export interface LogConfig {
  level: LogLevel;
  samplingRate: number;
  enablePerformanceLogs: boolean;
  enableRedisPoolLogs: boolean;
  enableCacheLogs: boolean;
  debugMode: boolean;
  highConcurrencyMode: boolean;
  enablePerformanceMonitoring: boolean;
  enableErrorReporting: boolean;
}

/**
 * 高性能日志器
 * 特性：
 * - 日志级别控制
 * - 采样率控制（减少高频日志）
 * - 环境变量配置
 * - 结构化日志支持
 */
export class Logger {
  private static config: LogConfig = Logger.initializeConfig();

  /**
   * 初始化配置 - 支持环境变量和生产环境覆盖
   */
  private static initializeConfig(): LogConfig {
    const isProduction = process.env.NODE_ENV === 'production';
    const debugMode = process.env.DEBUG_MODE === 'true';
    const highConcurrencyMode = process.env.HIGH_CONCURRENCY_MODE === 'true';

    // 基础配置
    let level = parseInt(process.env.LOG_LEVEL || '2');
    let samplingRate = parseFloat(process.env.LOG_SAMPLING_RATE || '0.1');
    let enablePerformanceLogs = process.env.ENABLE_PERFORMANCE_LOGS === 'true';

    // 生产环境覆盖
    if (isProduction) {
      level = parseInt(process.env.PRODUCTION_LOG_LEVEL || '1');
      samplingRate = parseFloat(process.env.PRODUCTION_LOG_SAMPLING_RATE || '0.01');
      enablePerformanceLogs = process.env.PRODUCTION_ENABLE_PERFORMANCE_LOGS === 'true';
    }

    // 高并发模式覆盖
    if (highConcurrencyMode) {
      level = LogLevel.WARN;
      samplingRate = 0.01;
      enablePerformanceLogs = false;
    }

    // 调试模式覆盖
    if (debugMode) {
      level = LogLevel.DEBUG;
      samplingRate = 1.0;
      enablePerformanceLogs = true;
    }

    return {
      level,
      samplingRate,
      enablePerformanceLogs,
      enableRedisPoolLogs: process.env.ENABLE_REDIS_POOL_LOGS === 'true',
      enableCacheLogs: process.env.ENABLE_CACHE_LOGS !== 'false', // 默认启用
      debugMode,
      highConcurrencyMode,
      enablePerformanceMonitoring: process.env.ENABLE_PERFORMANCE_MONITORING !== 'false',
      enableErrorReporting: process.env.ENABLE_ERROR_REPORTING !== 'false',
    };
  }

  /**
   * 判断是否应该输出日志
   */
  private static shouldLog(level: LogLevel, category?: string): boolean {
    // 级别检查
    if (level > this.config.level) return false;

    // 特定类别的开关检查
    if (category === 'performance' && !this.config.enablePerformanceLogs) return false;
    if (category === 'redis-pool' && !this.config.enableRedisPoolLogs) return false;
    if (category === 'cache' && !this.config.enableCacheLogs) return false;

    // 采样率检查（主要用于DEBUG和TRACE级别）
    if (level >= LogLevel.DEBUG && Math.random() > this.config.samplingRate) return false;

    return true;
  }

  /**
   * 错误日志 - 始终输出
   */
  static error(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      if (data) {
        console.error(`[ERROR] ${message}`, data);
      } else {
        console.error(`[ERROR] ${message}`);
      }
    }
  }

  /**
   * 警告日志
   */
  static warn(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.WARN)) {
      if (data) {
        console.warn(`[WARN] ${message}`, data);
      } else {
        console.warn(`[WARN] ${message}`);
      }
    }
  }

  /**
   * 信息日志
   */
  static info(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.INFO)) {
      if (data) {
        console.log(`[INFO] ${message}`, data);
      } else {
        console.log(`[INFO] ${message}`);
      }
    }
  }

  /**
   * 调试日志 - 支持采样
   */
  static debug(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      if (data) {
        console.log(`[DEBUG] ${message}`, data);
      } else {
        console.log(`[DEBUG] ${message}`);
      }
    }
  }

  /**
   * 性能日志 - 专门用于性能相关的日志
   */
  static performance(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG, 'performance')) {
      const formattedData = data ? this.formatPerformanceData(data) : '';
      console.log(`[PERF] ${message}${formattedData}`);
    }
  }

  /**
   * 缓存日志 - 专门用于缓存相关的日志
   */
  static cache(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG, 'cache')) {
      const formattedData = data ? this.formatCacheData(data) : '';
      console.log(`[CACHE] ${message}${formattedData}`);
    }
  }

  /**
   * Redis连接池日志
   */
  static redisPool(message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG, 'redis-pool')) {
      const formattedData = data ? ` | ${JSON.stringify(data)}` : '';
      console.log(`[REDIS-POOL] ${message}${formattedData}`);
    }
  }

  /**
   * 格式化性能数据 - 避免toFixed()调用
   */
  private static formatPerformanceData(data: any): string {
    if (!data) return '';

    const formatted: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'number') {
        // 使用Math.round替代toFixed，减少字符串格式化开销
        formatted[key] =
          key.includes('Time') || key.includes('time')
            ? `${Math.round(value as number)}ms`
            : Math.round(value as number);
      } else {
        formatted[key] = value;
      }
    }

    return ` | ${JSON.stringify(formatted)}`;
  }

  /**
   * 格式化缓存数据
   */
  private static formatCacheData(data: any): string {
    if (!data) return '';

    const formatted: any = {};
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'number' && (key.includes('Time') || key.includes('time'))) {
        formatted[key] = `${Math.round(value as number)}ms`;
      } else {
        formatted[key] = value;
      }
    }

    return ` | ${JSON.stringify(formatted)}`;
  }

  /**
   * 更新配置 - 运行时动态调整
   */
  static updateConfig(newConfig: Partial<LogConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  static getConfig(): LogConfig {
    return { ...this.config };
  }

  /**
   * 快速禁用所有性能日志 - 用于高并发场景
   */
  static disablePerformanceLogs(): void {
    this.config.enablePerformanceLogs = false;
    this.config.enableCacheLogs = false;
    this.config.enableRedisPoolLogs = false;
  }

  /**
   * 启用调试模式 - 用于问题排查
   */
  static enableDebugMode(): void {
    this.config.level = LogLevel.DEBUG;
    this.config.samplingRate = 1.0;
    this.config.enablePerformanceLogs = true;
    this.config.enableCacheLogs = true;
    this.config.enableRedisPoolLogs = true;
    this.config.debugMode = true;
    this.config.highConcurrencyMode = false;
  }

  /**
   * 启用生产模式 - 最小化日志输出
   */
  static enableProductionMode(): void {
    this.config.level = LogLevel.WARN;
    this.config.samplingRate = 0.01;
    this.config.enablePerformanceLogs = false;
    this.config.enableCacheLogs = true; // 保留重要的缓存日志
    this.config.enableRedisPoolLogs = false;
    this.config.debugMode = false;
    this.config.highConcurrencyMode = false;
  }

  /**
   * 重新初始化配置 - 重新读取环境变量
   */
  static reinitializeConfig(): void {
    this.config = this.initializeConfig();
  }

  /**
   * 获取当前环境信息
   */
  static getEnvironmentInfo(): {
    nodeEnv: string;
    debugMode: boolean;
    highConcurrencyMode: boolean;
    isProduction: boolean;
  } {
    return {
      nodeEnv: process.env.NODE_ENV || 'development',
      debugMode: this.config.debugMode,
      highConcurrencyMode: this.config.highConcurrencyMode,
      isProduction: process.env.NODE_ENV === 'production',
    };
  }

  /**
   * 性能监控日志 - 专门用于性能指标收集
   */
  static performanceMetric(metric: string, value: number, unit: string = 'ms'): void {
    if (this.config.enablePerformanceMonitoring && this.shouldLog(LogLevel.INFO)) {
      console.log(`[METRIC] ${metric}: ${Math.round(value)}${unit}`);
    }
  }

  /**
   * 错误报告 - 专门用于错误收集和报告
   */
  static errorReport(error: Error, context?: any): void {
    if (this.config.enableErrorReporting) {
      const errorData = {
        message: error.message,
        stack: error.stack,
        context,
        timestamp: new Date().toISOString(),
        environment: this.getEnvironmentInfo(),
      };
      console.error(`[ERROR-REPORT]`, errorData);
    }
  }

  /**
   * 安全事件日志 - 专门用于安全相关事件
   */
  static security(event: string, details: {
    userId?: string;
    ipAddress?: string;
    userAgent?: string;
    endpoint?: string;
    threat?: string;
    action?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
  }): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const securityData = {
        event,
        ...details,
        timestamp: new Date().toISOString(),
        securityEvent: true,
      };
      console.warn(`[SECURITY] Security event: ${event}`, securityData);
    }
  }

  /**
   * 认证事件日志
   */
  static auth(
    event: 'login' | 'logout' | 'refresh' | 'failed_login' | 'token_expired' | 'token_revoked',
    context: {
      userId?: string;
      method?: 'session' | 'jwt';
      clientType?: string;
      ipAddress?: string;
      success: boolean;
      reason?: string;
      duration?: number;
    }
  ): void {
    const level = context.success ? LogLevel.INFO : LogLevel.WARN;
    if (this.shouldLog(level)) {
      const authData = {
        event,
        ...context,
        timestamp: new Date().toISOString(),
        authEvent: true,
      };
      const message = `Authentication ${event}: ${context.success ? 'success' : 'failed'}`;
      
      if (level === LogLevel.WARN) {
        console.warn(`[AUTH] ${message}`, authData);
      } else {
        console.log(`[AUTH] ${message}`, authData);
      }
    }
  }

  /**
   * 速率限制事件日志
   */
  static rateLimit(
    event: 'check' | 'exceeded' | 'reset',
    details: {
      key: string;
      ipAddress: string;
      endpoint: string;
      current: number;
      limit: number;
      resetTime?: number;
    }
  ): void {
    const level = event === 'exceeded' ? LogLevel.WARN : LogLevel.DEBUG;
    if (this.shouldLog(level)) {
      const rateLimitData = {
        event,
        ...details,
        timestamp: new Date().toISOString(),
        rateLimitEvent: true,
      };
      
      const message = `Rate limit ${event} - ${details.current}/${details.limit}`;
      if (level === LogLevel.WARN) {
        console.warn(`[RATE-LIMIT] ${message}`, rateLimitData);
      } else {
        console.log(`[RATE-LIMIT] ${message}`, rateLimitData);
      }
    }
  }
}

/**
 * 便捷的日志函数 - 向后兼容
 */
export const log = Logger;

/**
 * 高并发模式 - 最小化日志输出
 */
export function enableHighConcurrencyMode(): void {
  Logger.updateConfig({
    level: LogLevel.WARN, // 只输出警告和错误
    samplingRate: 0.01, // 1%采样率
    enablePerformanceLogs: false,
    enableRedisPoolLogs: false,
    enableCacheLogs: false,
    debugMode: false,
    highConcurrencyMode: true,
    enablePerformanceMonitoring: false,
    enableErrorReporting: true, // 保留错误报告
  });
}

/**
 * 开发模式 - 详细日志输出
 */
export function enableDevelopmentMode(): void {
  Logger.updateConfig({
    level: LogLevel.DEBUG,
    samplingRate: 0.1, // 10%采样率，平衡性能和调试需求
    enablePerformanceLogs: true,
    enableRedisPoolLogs: true,
    enableCacheLogs: true,
    debugMode: true,
    highConcurrencyMode: false,
    enablePerformanceMonitoring: true,
    enableErrorReporting: true,
  });
}

/**
 * 根据环境变量自动配置日志系统
 */
export function autoConfigureLogger(): void {
  const envInfo = Logger.getEnvironmentInfo();

  if (envInfo.highConcurrencyMode) {
    enableHighConcurrencyMode();
    Logger.info('Logger configured for high concurrency mode');
  } else if (envInfo.debugMode) {
    Logger.enableDebugMode();
    Logger.info('Logger configured for debug mode');
  } else if (envInfo.isProduction) {
    Logger.enableProductionMode();
    Logger.info('Logger configured for production mode');
  } else {
    enableDevelopmentMode();
    Logger.info('Logger configured for development mode');
  }
}
