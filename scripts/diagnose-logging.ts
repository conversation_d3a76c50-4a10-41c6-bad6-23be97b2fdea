#!/usr/bin/env tsx

/**
 * 日志系统诊断脚本
 * 检查当前日志配置和测试各种日志输出
 */

async function main() {
  console.log('🔧 日志系统诊断工具\n');
  console.log('='.repeat(60));

  // 1. 检查环境变量
  console.log('📋 环境变量配置:');
  console.log(`   NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`   LOG_LEVEL: ${process.env.LOG_LEVEL}`);
  console.log(`   LOG_SAMPLING_RATE: ${process.env.LOG_SAMPLING_RATE}`);
  console.log(`   ENABLE_PERFORMANCE_LOGS: ${process.env.ENABLE_PERFORMANCE_LOGS}`);
  console.log(`   ENABLE_REDIS_POOL_LOGS: ${process.env.ENABLE_REDIS_POOL_LOGS}`);
  console.log(`   ENABLE_CACHE_LOGS: ${process.env.ENABLE_CACHE_LOGS}`);
  console.log(`   DEBUG_MODE: ${process.env.DEBUG_MODE}`);
  console.log(`   HIGH_CONCURRENCY_MODE: ${process.env.HIGH_CONCURRENCY_MODE}`);
  console.log(`   ENABLE_PERFORMANCE_MONITORING: ${process.env.ENABLE_PERFORMANCE_MONITORING}`);

  // 2. 导入并检查Logger配置
  console.log('\n🔧 Logger配置:');
  try {
    const { Logger } = await import('../app/lib/utils/logger');
    const config = Logger.getConfig();
    const envInfo = Logger.getEnvironmentInfo();

    console.log('   当前配置:', JSON.stringify(config, null, 2));
    console.log('   环境信息:', JSON.stringify(envInfo, null, 2));

    // 3. 测试各种日志级别
    console.log('\n🧪 测试日志输出:');
    console.log('   测试ERROR级别:');
    Logger.error('这是一个错误日志测试', { test: 'error' });

    console.log('   测试WARN级别:');
    Logger.warn('这是一个警告日志测试', { test: 'warn' });

    console.log('   测试INFO级别:');
    Logger.info('这是一个信息日志测试', { test: 'info' });

    console.log('   测试DEBUG级别:');
    Logger.debug('这是一个调试日志测试', { test: 'debug' });

    console.log('   测试性能日志:');
    Logger.performance('性能日志测试', { responseTime: 123, cacheHit: true });

    console.log('   测试缓存日志:');
    Logger.cache('缓存日志测试', { key: 'test', hitTime: 5.5 });

    console.log('   测试Redis连接池日志:');
    Logger.redisPool('Redis连接池日志测试', { borrowed: 1, available: 19 });
  } catch (error) {
    console.error('❌ 导入Logger失败:', error);
  }

  // 4. 检查缓存日志系统
  console.log('\n🎯 缓存日志系统配置:');
  try {
    const { CacheLogConfig, CacheLogger } = await import('../app/lib/utils/cache-types');

    console.log('   缓存日志配置:');
    console.log(`     ENABLE_CACHE_LOGS: ${process.env.ENABLE_CACHE_LOGS}`);
    console.log(`     ENABLE_DETAILED_CACHE_LOGS: ${process.env.ENABLE_DETAILED_CACHE_LOGS}`);
    console.log(`     HIGH_CONCURRENCY_MODE: ${process.env.HIGH_CONCURRENCY_MODE}`);
    console.log(`     shouldLog(): ${CacheLogConfig.shouldLog()}`);
    console.log(`     shouldLogDetailed(): ${CacheLogConfig.shouldLogDetailed()}`);

    console.log('\n   测试缓存日志:');
    const { CacheSource } = await import('../app/lib/utils/cache-types');

    CacheLogger.logHit(
      CacheSource.DATABASE,
      {
        key: 'test:word',
        hitTime: 25.5,
        layer: 'database',
      },
      'test'
    );

    CacheLogger.logHit(
      CacheSource.REDIS_CACHE,
      {
        key: 'test:word',
        hitTime: 2.1,
        layer: 'redis-distributed',
      },
      'test'
    );

    CacheLogger.logMiss('test', [CacheSource.MEMORY_CACHE]);

    CacheLogger.logStats();
  } catch (error) {
    console.error('❌ 导入缓存日志系统失败:', error);
  }

  // 5. 建议修复方案
  console.log('\n💡 建议修复方案:');

  if (process.env.ENABLE_PERFORMANCE_LOGS === 'false') {
    console.log('   ⚠️  ENABLE_PERFORMANCE_LOGS=false 会禁用性能日志');
    console.log('   🔧 建议设置: ENABLE_PERFORMANCE_LOGS=true');
  }

  if (process.env.LOG_SAMPLING_RATE === '0.1') {
    console.log('   ⚠️  LOG_SAMPLING_RATE=0.1 只显示10%的DEBUG日志');
    console.log('   🔧 建议设置: LOG_SAMPLING_RATE=1.0 (显示所有日志)');
  }

  if (process.env.DEBUG_MODE === 'false') {
    console.log('   ⚠️  DEBUG_MODE=false 可能限制调试日志');
    console.log('   🔧 建议设置: DEBUG_MODE=true');
  }

  if (!process.env.ENABLE_DETAILED_CACHE_LOGS) {
    console.log('   ⚠️  ENABLE_DETAILED_CACHE_LOGS 未设置');
    console.log('   🔧 建议设置: ENABLE_DETAILED_CACHE_LOGS=true');
  }

  console.log('\n🔧 推荐的环境变量配置:');
  console.log('   LOG_LEVEL=4');
  console.log('   LOG_SAMPLING_RATE=1.0');
  console.log('   ENABLE_PERFORMANCE_LOGS=true');
  console.log('   ENABLE_REDIS_POOL_LOGS=true');
  console.log('   ENABLE_CACHE_LOGS=true');
  console.log('   ENABLE_DETAILED_CACHE_LOGS=true');
  console.log('   DEBUG_MODE=true');
  console.log('   HIGH_CONCURRENCY_MODE=false');

  console.log('\n✅ 诊断完成');
}

main().catch(console.error);
