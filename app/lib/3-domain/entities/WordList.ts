/**
 * WordList Domain Entity
 * 代表词库实体，包含词库的基本信息和业务逻辑
 */
export interface WordList {
  id: number;
  name: string; // 词库名称，如 "CET-4", "CET-6", "TOEFL"
  description: string; // 词库描述
  difficulty: number; // 难度级别 1-10
  totalWords: number; // 词库包含的总单词数
  isActive: boolean; // 是否启用
  createdAt: Date;
  updatedAt: Date;
}

/**
 * 创建词库的数据传输对象
 */
export interface CreateWordListData {
  name: string;
  description: string;
  difficulty: number;
  totalWords?: number;
  isActive?: boolean;
}

/**
 * 更新词库的数据传输对象
 */
export interface UpdateWordListData {
  name?: string;
  description?: string;
  difficulty?: number;
  totalWords?: number;
  isActive?: boolean;
}

/**
 * 词库查询参数
 */
export interface WordListQueryOptions {
  isActive?: boolean;
  difficulty?: number;
  minDifficulty?: number;
  maxDifficulty?: number;
}

/**
 * 词库统计信息
 */
export interface WordListStats {
  id: number;
  name: string;
  totalWords: number;
  userPracticedWords?: number; // 用户已练习的单词数
  userProgress?: number; // 用户进度百分比 (0-100)
  averageProficiency?: number; // 用户在此词库的平均熟练度
}