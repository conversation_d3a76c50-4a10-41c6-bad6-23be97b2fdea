#!/usr/bin/env tsx

/**
 * 重新匹配未找到单词的脚本
 *
 * 功能：
 * 1. 查找在频率排名设置过程中未找到的单词
 * 2. 使用Gemini API重新查询这些单词
 * 3. 导入数据库并设置频率排名
 * 4. 提供详细的失败分析报告
 */

import { PrismaClient } from '@prisma/client';
import { fetchDictionary } from '../../5-providers/gemini.js';
import { importFromDictionaryData } from '../llm-response-to-db/importDictionaryData.js';
import {
  findMissingWords,
  getWordsToRetry,
  getWordsToUpdateRank,
} from './utils/missing-words-finder.js';
import {
  batchAnalyzeFailures,
  generateFailureReport,
  analyzeApiResponse,
} from './utils/failure-analyzer.js';
import type { DictionaryQueryResult } from '../../utils/dict-types.js';

// 配置常量
const TOTAL_WORDS = 500; // 总单词数
const WORDS_PER_REQUEST = 50; // 每个请求处理的单词数
const MAX_RETRIES = 2; // 最大重试次数
const RETRY_DELAY = 3000; // 重试延迟（毫秒）

// 初始化Prisma客户端
const prismaClient = new PrismaClient();

/**
 * 将单词数组分割成指定大小的块
 */
function chunkWords(words: string[], chunkSize: number): string[][] {
  const chunks: string[][] = [];
  for (let i = 0; i < words.length; i += chunkSize) {
    chunks.push(words.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * 合并多个词典查询结果
 */
function mergeDictionaryResults(results: DictionaryQueryResult[]): any {
  const mergedWords: any[] = [];
  const mergedIllegalWords: string[] = [];

  results.forEach((result) => {
    if (result.statusCode === 200 && result.data) {
      if (result.data.words) {
        mergedWords.push(...result.data.words);
      }
      if (result.data.illegalWords) {
        mergedIllegalWords.push(...result.data.illegalWords);
      }
    }
  });

  return {
    words: mergedWords,
    wordCount: mergedWords.length,
    wordList: mergedWords.map((w) => w.word),
    illegalWords: mergedIllegalWords,
  };
}

/**
 * 重新查询单词
 */
async function retryWords(words: string[]): Promise<{
  results: DictionaryQueryResult[];
  successWords: string[];
  failedWords: string[];
}> {
  console.log(`🔄 开始重新查询 ${words.length} 个单词...`);

  const wordChunks = chunkWords(words, WORDS_PER_REQUEST);
  const results: DictionaryQueryResult[] = [];
  const successWords: string[] = [];
  const failedWords: string[] = [];

  for (let i = 0; i < wordChunks.length; i++) {
    const chunk = wordChunks[i];
    const wordList = chunk.join(',');

    console.log(`📤 查询第 ${i + 1}/${wordChunks.length} 批: ${chunk.length} 个单词`);
    console.log(`   单词: ${chunk.slice(0, 5).join(', ')}${chunk.length > 5 ? '...' : ''}`);

    let retryCount = 0;
    let result: DictionaryQueryResult | null = null;

    // 重试机制
    while (retryCount <= MAX_RETRIES && !result) {
      try {
        if (retryCount > 0) {
          console.log(`   🔄 第 ${retryCount} 次重试...`);
          await new Promise((resolve) => setTimeout(resolve, RETRY_DELAY));
        }

        result = await fetchDictionary(wordList);

        if (result.statusCode === 200) {
          console.log(`   ✅ 成功: ${result.data?.wordCount || 0} 个单词`);

          // 分析响应
          const analysis = analyzeApiResponse(chunk, result);
          successWords.push(...analysis.successfulWords);

          // 记录失败的单词
          analysis.illegalWords.forEach((word) => {
            if (!failedWords.includes(word)) {
              failedWords.push(word);
            }
          });
          analysis.missingWords.forEach((word) => {
            if (!failedWords.includes(word)) {
              failedWords.push(word);
            }
          });
        } else {
          console.log(`   ❌ 失败: ${result.error?.message || '未知错误'}`);
          failedWords.push(...chunk);
        }

        results.push(result);
      } catch (error) {
        console.error(`   💥 请求异常:`, error);
        retryCount++;

        if (retryCount > MAX_RETRIES) {
          console.log(`   ❌ 达到最大重试次数，放弃该批次`);
          failedWords.push(...chunk);
          results.push({
            error: error,
            statusCode: 500,
          } as DictionaryQueryResult);
        }
      }
    }

    // 批次间延迟
    if (i < wordChunks.length - 1) {
      console.log('   ⏳ 等待2秒后处理下一批...');
      await new Promise((resolve) => setTimeout(resolve, 2000));
    }
  }

  return { results, successWords, failedWords };
}

/**
 * 更新频率排名
 */
async function updateFrequencyRanks(
  wordsToUpdate: Array<{ word: string; rank: number }>
): Promise<void> {
  if (wordsToUpdate.length === 0) {
    console.log('📊 没有需要更新排名的单词');
    return;
  }

  console.log(`📊 开始更新 ${wordsToUpdate.length} 个单词的频率排名...`);

  let updatedCount = 0;
  let failedCount = 0;

  for (const { word, rank } of wordsToUpdate) {
    try {
      const result = await prismaClient.vocabulary.updateMany({
        where: { word: word },
        data: { freqRank: rank },
      });

      if (result.count > 0) {
        updatedCount++;
        console.log(`📊 更新排名: "${word}" -> ${rank}`);
      } else {
        failedCount++;
        console.warn(`⚠️  更新失败: "${word}" (未找到记录)`);
      }
    } catch (error) {
      failedCount++;
      console.error(`❌ 更新排名失败 "${word}":`, error);
    }
  }

  console.log(`📊 排名更新完成: ${updatedCount} 成功, ${failedCount} 失败`);
}

/**
 * 主函数
 */
export async function main(): Promise<void> {
  console.log('🚀 开始重新匹配未找到的单词...\n');

  try {
    // 1. 查找未匹配的单词
    console.log('🔍 步骤1: 查找未匹配的单词');
    const missingWords = await findMissingWords(TOTAL_WORDS, prismaClient);

    if (missingWords.length === 0) {
      console.log('🎉 所有单词都已正确匹配，无需重新处理！');
      return;
    }

    // 2. 分类处理
    const wordsToRetry = getWordsToRetry(missingWords);
    const wordsToUpdateRank = getWordsToUpdateRank(missingWords);

    console.log(`\n📋 处理计划:`);
    console.log(`  🔄 需要重新查询: ${wordsToRetry.length} 个`);
    console.log(`  📊 需要更新排名: ${wordsToUpdateRank.length} 个`);

    // 3. 重新查询单词
    if (wordsToRetry.length > 0) {
      console.log('\n🌐 步骤2: 重新查询单词');
      const { results, successWords, failedWords } = await retryWords(wordsToRetry);

      console.log(`\n📈 查询结果:`);
      console.log(`  ✅ 成功: ${successWords.length} 个`);
      console.log(`  ❌ 失败: ${failedWords.length} 个`);

      // 4. 导入成功查询的数据
      if (results.some((r) => r.statusCode === 200 && r.data)) {
        console.log('\n💾 步骤3: 导入数据到数据库');
        const mergedData = mergeDictionaryResults(results);

        if (mergedData.wordCount > 0) {
          const importResult = await importFromDictionaryData(mergedData, prismaClient, {
            forceOverwrite: true,
          });

          console.log(`📈 导入结果:`);
          console.log(`  ✅ 成功: ${importResult.successCount} 个单词`);
          console.log(`  ❌ 失败: ${importResult.failureCount} 个单词`);

          if (importResult.errors.length > 0) {
            console.log('  🔍 导入错误详情:');
            importResult.errors.forEach((error, index) => {
              console.log(`    ${index + 1}. ${error.word}: ${error.error.message}`);
            });
          }

          // 5. 设置新导入单词的频率排名
          console.log('\n🏆 步骤4: 设置频率排名');
          const successfulImports = successWords.filter(
            (word) => !importResult.errors.some((e) => e.word === word)
          );

          for (const word of successfulImports) {
            const missingWord = missingWords.find((m) => m.word === word);
            if (missingWord) {
              try {
                const result = await prismaClient.vocabulary.updateMany({
                  where: { word: word },
                  data: { freqRank: missingWord.expectedRank },
                });

                if (result.count > 0) {
                  console.log(`📊 设置排名: "${word}" -> ${missingWord.expectedRank}`);
                } else {
                  console.warn(`⚠️  设置排名失败: "${word}" (未找到记录)`);
                }
              } catch (error) {
                console.error(`❌ 设置排名失败 "${word}":`, error);
              }
            }
          }
        }
      }

      // 6. 生成失败分析报告
      if (failedWords.length > 0) {
        console.log('\n📋 步骤5: 生成失败分析报告');
        const importErrors = results
          .filter((r) => r.statusCode === 200 && r.data)
          .flatMap((r) => r.data?.words || [])
          .filter((word) => failedWords.includes(word.word))
          .map((word) => ({ word: word.word, error: new Error('导入失败') }));

        const analyses = batchAnalyzeFailures(failedWords, results, importErrors);
        const report = generateFailureReport(analyses);

        console.log('\n' + report);
      }
    }

    // 7. 更新已存在但缺少排名的单词
    if (wordsToUpdateRank.length > 0) {
      console.log('\n📊 步骤6: 更新频率排名');
      await updateFrequencyRanks(wordsToUpdateRank);
    }

    console.log('\n🎉 重新匹配完成！');
  } catch (error) {
    console.error('\n💥 重新匹配失败:', error);
    throw error;
  } finally {
    await prismaClient.$disconnect();
  }
}

// 如果直接运行此文件
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
    .then(() => {
      console.log('\n✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ 脚本执行失败:', error);
      process.exit(1);
    });
}
