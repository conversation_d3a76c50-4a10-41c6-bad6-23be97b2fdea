'use client';

import { PlatformDetector } from './storage';

export interface OAuthResult {
  accessToken: string;
  refreshToken?: string;
  user?: any;
  error?: string;
}

export interface OAuthConfig {
  provider: 'google' | 'github';
  clientId?: string;
  redirectUri?: string;
  scope?: string;
  state?: string;
}

// Platform-specific OAuth handler interface
export interface OAuthAdapter {
  login(provider: 'google' | 'github'): Promise<OAuthResult>;
  handleCallback?(params: URLSearchParams): void;
}

// Web OAuth adapter (popup-based)
export class WebOAuthAdapter implements OAuthAdapter {
  private static readonly POPUP_OPTIONS = {
    width: 600,
    height: 600,
    scrollbars: 'yes',
    resizable: 'yes',
  };

  async login(provider: 'google' | 'github'): Promise<OAuthResult> {
    const popup = this.openOAuthPopup(provider);

    if (!popup) {
      throw new Error('Popup blocked. Please allow popups for this site.');
    }

    return new Promise((resolve, reject) => {
      const checkClosed = setInterval(() => {
        if (popup.closed) {
          clearInterval(checkClosed);
          reject(new Error('OAuth cancelled by user'));
        }
      }, 1000);

      const messageHandler = (event: MessageEvent) => {
        if (event.origin !== window.location.origin) return;

        if (event.data.type === 'oauth-success') {
          clearInterval(checkClosed);
          popup.close();
          window.removeEventListener('message', messageHandler);
          resolve({
            accessToken: event.data.tokens.accessToken,
            refreshToken: event.data.tokens.refreshToken,
            user: event.data.user,
          });
        } else if (event.data.type === 'oauth-error') {
          clearInterval(checkClosed);
          popup.close();
          window.removeEventListener('message', messageHandler);
          reject(new Error(event.data.error || 'OAuth failed'));
        }
      };

      window.addEventListener('message', messageHandler);
    });
  }

  private openOAuthPopup(provider: 'google' | 'github'): Window | null {
    const { width, height } = WebOAuthAdapter.POPUP_OPTIONS;
    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;

    const features = [
      `width=${width}`,
      `height=${height}`,
      `left=${left}`,
      `top=${top}`,
      'scrollbars=yes',
      'resizable=yes',
    ].join(',');

    return window.open(`/api/auth/token/${provider}`, `oauth-${provider}`, features);
  }

  handleCallback(params: URLSearchParams): void {
    const error = params.get('error');
    const code = params.get('code');

    if (error) {
      this.postMessageToParent({
        type: 'oauth-error',
        error: error,
      });
      return;
    }

    if (!code) {
      this.postMessageToParent({
        type: 'oauth-error',
        error: 'No authorization code received',
      });
      return;
    }

    // The actual token exchange should be handled by the server
    this.postMessageToParent({
      type: 'oauth-success',
      code: code,
    });
  }

  private postMessageToParent(message: any): void {
    if (window.opener) {
      window.opener.postMessage(message, window.location.origin);
    } else if (window.parent !== window) {
      window.parent.postMessage(message, window.location.origin);
    }
  }
}

// Browser extension OAuth adapter
export class ExtensionOAuthAdapter implements OAuthAdapter {
  async login(provider: 'google' | 'github'): Promise<OAuthResult> {
    if (!this.isExtensionEnvironment()) {
      throw new Error('Extension OAuth adapter can only be used in extension environment');
    }

    try {
      const authUrl = await this.getAuthURL(provider);

      return new Promise((resolve, reject) => {
        (window as any).chrome.identity.launchWebAuthFlow(
          {
            url: authUrl,
            interactive: true,
          },
          (redirectUrl: any) => {
            if ((window as any).chrome.runtime.lastError) {
              reject(new Error((window as any).chrome.runtime.lastError.message));
              return;
            }

            if (!redirectUrl) {
              reject(new Error('OAuth cancelled'));
              return;
            }

            // Parse tokens from redirect URL
            const tokens = this.parseTokensFromUrl(redirectUrl);
            resolve(tokens);
          }
        );
      });
    } catch (error) {
      throw new Error(
        `Extension OAuth failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );
    }
  }

  private isExtensionEnvironment(): boolean {
    return (
      typeof window !== 'undefined' &&
      (window as any).chrome &&
      (window as any).chrome.identity &&
      (window as any).chrome.runtime
    );
  }

  private async getAuthURL(provider: 'google' | 'github'): Promise<string> {
    const response = await fetch(`/api/auth/oauth/${provider}/url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: this.generateSessionId(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get ${provider} auth URL`);
    }

    const data = await response.json();
    return data.url;
  }

  private generateSessionId(): string {
    return `ext-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private parseTokensFromUrl(url: string): OAuthResult {
    const urlParams = new URLSearchParams(new URL(url).search);

    const accessToken = urlParams.get('access_token');
    const refreshToken = urlParams.get('refresh_token');
    const error = urlParams.get('error');

    if (error) {
      throw new Error(error);
    }

    if (!accessToken) {
      throw new Error('No access token received');
    }

    return {
      accessToken,
      refreshToken: refreshToken || undefined,
    };
  }
}

// Mobile OAuth adapter (deep links)
export class MobileOAuthAdapter implements OAuthAdapter {
  async login(provider: 'google' | 'github'): Promise<OAuthResult> {
    const authUrl = await this.getAuthURL(provider);

    // For mobile web, we'll use a full page redirect instead of popup
    if (this.isMobileWeb()) {
      window.location.href = authUrl;

      // This promise will never resolve as the page redirects
      return new Promise(() => {});
    }

    // For React Native, this would use Linking.openURL
    throw new Error('Native mobile OAuth not implemented in web context');
  }

  private isMobileWeb(): boolean {
    return (
      typeof window !== 'undefined' &&
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
    );
  }

  private async getAuthURL(provider: 'google' | 'github'): Promise<string> {
    const response = await fetch(`/api/auth/oauth/${provider}/url`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        sessionId: this.generateSessionId(),
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to get ${provider} auth URL`);
    }

    const data = await response.json();
    return data.url;
  }

  private generateSessionId(): string {
    return `mobile-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  handleCallback(params: URLSearchParams): void {
    // Handle deep link callback in mobile apps
    const error = params.get('error');
    const code = params.get('code');

    if (error) {
      // In a real mobile app, this would emit an event or call a callback
      console.error('OAuth error:', error);
      return;
    }

    if (code) {
      // Exchange code for tokens
      this.exchangeCodeForTokens(code);
    }
  }

  private async exchangeCodeForTokens(code: string): Promise<void> {
    // This would be handled by the app's OAuth callback mechanism
    console.log('Mobile OAuth code received:', code);
  }
}

// OAuth adapter factory
export class OAuthAdapterFactory {
  static createAdapter(): OAuthAdapter {
    const platform = PlatformDetector.platform;

    switch (platform) {
      case 'extension':
        return new ExtensionOAuthAdapter();
      case 'mobile':
        return new MobileOAuthAdapter();
      case 'web':
        return new WebOAuthAdapter();
      default:
        console.warn('Unknown platform, falling back to web OAuth');
        return new WebOAuthAdapter();
    }
  }
}

// Universal OAuth handler
export class UniversalOAuthHandler {
  private adapter: OAuthAdapter;

  constructor() {
    this.adapter = OAuthAdapterFactory.createAdapter();
  }

  async login(provider: 'google' | 'github'): Promise<OAuthResult> {
    try {
      return await this.adapter.login(provider);
    } catch (error) {
      console.error(`OAuth login failed for ${provider}:`, error);
      throw error;
    }
  }

  handleCallback(params: URLSearchParams): void {
    if (this.adapter.handleCallback) {
      this.adapter.handleCallback(params);
    }
  }

  // Provider-specific configurations
  static getProviderConfig(provider: 'google' | 'github'): OAuthConfig {
    const configs = {
      google: {
        provider: 'google' as const,
        scope: 'openid email profile',
      },
      github: {
        provider: 'github' as const,
        scope: 'user:email',
      },
    };

    return configs[provider];
  }

  // Generate OAuth state for CSRF protection (DEPRECATED - use oauthCSRF)
  static generateState(): string {
    console.warn(
      '[DEPRECATED] UniversalOAuthHandler.generateState() is deprecated. Use oauthCSRF.generateOAuthState() instead.'
    );
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
  }

  // Validate OAuth state (DEPRECATED - use oauthCSRF)
  static validateState(receivedState: string, expectedState: string): boolean {
    console.warn(
      '[DEPRECATED] UniversalOAuthHandler.validateState() is deprecated. Use oauthCSRF.validateOAuthState() instead.'
    );
    return receivedState === expectedState;
  }
}

// OAuth utility functions
export function isOAuthCallback(): boolean {
  if (typeof window === 'undefined') return false;

  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.has('code') || urlParams.has('error');
}

export function getOAuthCallbackParams(): URLSearchParams {
  if (typeof window === 'undefined') return new URLSearchParams();

  return new URLSearchParams(window.location.search);
}

export function clearOAuthParams(): void {
  if (typeof window === 'undefined') return;

  const url = new URL(window.location.href);
  url.searchParams.delete('code');
  url.searchParams.delete('state');
  url.searchParams.delete('error');
  url.searchParams.delete('error_description');

  window.history.replaceState({}, '', url.toString());
}

// OAuth provider configurations for multi-platform
export const OAUTH_PROVIDERS = {
  google: {
    name: 'Google',
    icon: 'google',
    color: '#4285f4',
    scopes: {
      web: ['openid', 'email', 'profile'],
      extension: ['openid', 'email', 'profile'],
      mobile: ['openid', 'email', 'profile'],
    },
  },
  github: {
    name: 'GitHub',
    icon: 'github',
    color: '#333',
    scopes: {
      web: ['user:email'],
      extension: ['user:email'],
      mobile: ['user:email'],
    },
  },
} as const;
