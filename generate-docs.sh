#!/bin/bash

# 如果任何命令失败，立即退出脚本
set -e
set -o pipefail

echo "🚀 开始生成 Lucid 项目文档..."

# --- 配置 ---
OUTPUT_DIR="docs-output"

# 通用排除项
COMMON_EXCLUDE="node_modules/**,dist/**,.next/**,docs-output/**,.wxt/**,*.log,pnpm-lock.yaml,**/*.spec.ts,public/**,app/generated/**"

# 核心应用代码
CORE_APP_INCLUDE="app/api/**/*.ts,app/lib/**/*.ts,app/components/**/*.tsx,app/hooks/**/*.ts,app/test/**/*.ts,**/*.prisma" 

# 配置文件
CONFIG_INCLUDE="package.json,tsconfig.json,next.config.ts,vitest.config.ts,postcss.config.mjs,prisma/schema.prisma,docker-compose.yml,Dockerfile,generate-docs.sh"

# 项目内文档
DOCS_INCLUDE="doc/**/*.md,README.md"

# --- 执行 ---

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 1. 生成完整项目文档 (不含测试和依赖)
echo "📄 1/5: 生成完整项目文档 (lucid-full.md)..."
code2prompt . \
  --include="app/**/*.ts,app/**/*.tsx,*.ts,*.mjs,*.js,*.css,*.md,*.json,*.sh,prisma/schema.prisma" \
  --exclude="$COMMON_EXCLUDE" \
  --output-file="$OUTPUT_DIR/lucid-full.md" \
  --line-numbers \
  --full-directory-tree \
  --tokens=format

# 2. 生成核心源码文档
echo "💻 2/5: 生成核心源码文档 (lucid-source.md)..."
code2prompt . \
  --include="$CORE_APP_INCLUDE" \
  --exclude="$COMMON_EXCLUDE" \
  --output-file="$OUTPUT_DIR/lucid-source.md" \
  --line-numbers \
  --tokens=format

# 3. 生成配置文档
echo "⚙️ 3/5: 生成配置文档 (lucid-config.md)..."
code2prompt . \
  --include="$CONFIG_INCLUDE" \
  --output-file="$OUTPUT_DIR/lucid-config.md" \
  --line-numbers

# 4. 生成文档集合
echo "📚 4/5: 生成项目内文档集合 (lucid-docs.md)..."
code2prompt . \
  --include="$DOCS_INCLUDE" \
  --output-file="$OUTPUT_DIR/lucid-docs.md"

# 5. 生成核心源码的 JSON 格式
echo "🔧 5/5: 生成核心源码 JSON 格式 (lucid-source.json)..."
code2prompt . \
  --include="$CORE_APP_INCLUDE" \
  --exclude="$COMMON_EXCLUDE" \
  --output-format=json \
  --output-file="$OUTPUT_DIR/lucid-source.json" \
  --tokens=format

echo "✅ 文档生成完成！输出目录：$OUTPUT_DIR/"
echo "📊 文件列表："
ls -la "$OUTPUT_DIR/"
