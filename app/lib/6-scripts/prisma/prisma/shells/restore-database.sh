#!/bin/bash
#
# PostgreSQL数据库恢复脚本 (Shell版本)
# 支持从各种格式的备份文件恢复PostgreSQL数据库
#

set -euo pipefail

# 默认配置
CONTAINER_NAME="lucid-bd-postgres"
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_NAME="dictionary"
BACKUP_DIR="${PWD}/backups"
DROP_DATABASE=false
CREATE_DATABASE=false
SKIP_DATA=false
SKIP_SCHEMA=false
VERBOSE=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
PostgreSQL数据库恢复脚本

用法:
    $0 <备份文件> [选项]
    $0 --list                   # 列出可用备份

选项:
    --drop-database             恢复前删除目标数据库
    --create-database           恢复前创建目标数据库
    --target=<database>         指定目标数据库名称
    --skip-data                 只恢复结构，跳过数据
    --skip-schema               只恢复数据，跳过结构
    --container=<name>          Docker容器名称 (默认: lucid-bd-postgres)
    --db-user=<user>            数据库用户名 (默认: postgres)
    --db-password=<pwd>         数据库密码 (默认: postgres)
    --db-name=<name>            数据库名称 (默认: dictionary)
    --backup-dir=<dir>          备份目录 (默认: ./backups)
    --verbose                   详细输出
    --list                      列出可用的备份文件
    --help                      显示帮助信息

示例:
    $0 backups/lucid-bd-full-2024-01-01.backup
    $0 backup.sql --drop-database --create-database
    $0 --list
EOF
}

# 解析命令行参数
parse_args() {
    if [[ $# -eq 0 ]]; then
        show_help
        exit 0
    fi
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --drop-database)
                DROP_DATABASE=true
                shift
                ;;
            --create-database)
                CREATE_DATABASE=true
                shift
                ;;
            --target=*)
                TARGET_DB="${1#*=}"
                shift
                ;;
            --skip-data)
                SKIP_DATA=true
                shift
                ;;
            --skip-schema)
                SKIP_SCHEMA=true
                shift
                ;;
            --container=*)
                CONTAINER_NAME="${1#*=}"
                shift
                ;;
            --db-user=*)
                DB_USER="${1#*=}"
                shift
                ;;
            --db-password=*)
                DB_PASSWORD="${1#*=}"
                shift
                ;;
            --db-name=*)
                DB_NAME="${1#*=}"
                shift
                ;;
            --backup-dir=*)
                BACKUP_DIR="${1#*=}"
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --list)
                LIST_BACKUPS=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            -*)
                log_error "未知参数: $1"
                echo "使用 --help 查看可用选项"
                exit 1
                ;;
            *)
                if [[ -z "${BACKUP_FILE:-}" ]]; then
                    BACKUP_FILE="$1"
                else
                    log_error "意外的参数: $1"
                    exit 1
                fi
                shift
                ;;
        esac
    done
}

# 检查Docker是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查容器状态
check_container() {
    if ! docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_error "容器 ${CONTAINER_NAME} 不存在"
        exit 1
    fi
    
    if ! docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_info "启动容器 ${CONTAINER_NAME}..."
        if ! docker start "${CONTAINER_NAME}" &> /dev/null; then
            log_error "无法启动容器 ${CONTAINER_NAME}"
            exit 1
        fi
        
        # 等待容器启动
        sleep 3
    fi
    
    log_success "容器 ${CONTAINER_NAME} 运行正常"
}

# 测试数据库连接
test_database_connection() {
    log_info "测试数据库连接..."
    
    if ! docker exec "${CONTAINER_NAME}" env PGPASSWORD="${DB_PASSWORD}" psql -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT version();" &> /dev/null; then
        log_error "数据库连接失败"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 验证备份文件
validate_backup_file() {
    local backup_file="$1"
    
    if [[ ! -f "${backup_file}" ]]; then
        log_error "备份文件不存在: ${backup_file}"
        exit 1
    fi
    
    # 检查文件大小
    local file_size
    if command -v stat &> /dev/null; then
        if [[ "$(uname)" == "Darwin" ]]; then
            file_size=$(stat -f%z "${backup_file}")
        else
            file_size=$(stat -c%s "${backup_file}")
        fi
    else
        file_size=$(wc -c < "${backup_file}")
    fi
    
    if [[ ${file_size} -lt 100 ]]; then
        log_error "备份文件太小，可能损坏: ${backup_file}"
        exit 1
    fi
    
    # 格式化文件大小
    local size_human
    if command -v numfmt &> /dev/null; then
        size_human=$(numfmt --to=iec-i --suffix=B "${file_size}")
    else
        size_human="${file_size} bytes"
    fi
    
    log_success "备份文件验证通过: ${size_human}"
}

# 检测备份格式
detect_backup_format() {
    local backup_file="$1"
    local filename=$(basename "${backup_file}")
    
    case "${filename}" in
        *.sql|*.sql.gz)
            echo "sql"
            ;;
        *.tar|*.tar.gz)
            echo "tar"
            ;;
        *.backup)
            echo "custom"
            ;;
        *)
            echo "custom"  # 默认格式
            ;;
    esac
}

# 删除数据库
drop_database() {
    local database="$1"
    
    log_info "删除数据库: ${database}"
    
    if ! docker exec "${CONTAINER_NAME}" env PGPASSWORD="${DB_PASSWORD}" psql -U "${DB_USER}" -d postgres -c "DROP DATABASE IF EXISTS ${database};" &> /dev/null; then
        log_warning "删除数据库失败或数据库不存在"
    else
        log_success "数据库已删除"
    fi
}

# 创建数据库
create_database() {
    local database="$1"
    
    log_info "创建数据库: ${database}"
    
    if ! docker exec "${CONTAINER_NAME}" env PGPASSWORD="${DB_PASSWORD}" psql -U "${DB_USER}" -d postgres -c "CREATE DATABASE ${database};" &> /dev/null; then
        log_error "创建数据库失败"
        exit 1
    fi
    
    log_success "数据库已创建"
}

# 执行恢复
perform_restore() {
    local backup_file="$1"
    local target_db="${TARGET_DB:-${DB_NAME}}"
    local filename=$(basename "${backup_file}")
    local temp_file="restore-$(date +%s)-${filename}"
    local backup_format=$(detect_backup_format "${backup_file}")
    
    log_info "开始恢复数据库: ${target_db}"
    log_info "备份文件: ${backup_file}"
    log_info "备份格式: ${backup_format}"
    
    local start_time=$(date +%s)
    
    # 复制备份文件到容器内
    if ! docker cp "${backup_file}" "${CONTAINER_NAME}:/tmp/${temp_file}"; then
        log_error "复制备份文件到容器失败"
        exit 1
    fi
    
    # 构建恢复命令
    local restore_cmd=""
    
    if [[ "${backup_format}" == "sql" ]]; then
        # SQL格式使用psql恢复
        restore_cmd="env PGPASSWORD=\"${DB_PASSWORD}\" psql -U \"${DB_USER}\" -d \"${target_db}\""
        
        if [[ "${VERBOSE}" == true ]]; then
            restore_cmd="${restore_cmd} -v ON_ERROR_STOP=1"
        fi
        
        restore_cmd="${restore_cmd} -f /tmp/${temp_file}"
        
    else
        # 其他格式使用pg_restore恢复
        restore_cmd="env PGPASSWORD=\"${DB_PASSWORD}\" pg_restore -U \"${DB_USER}\" -d \"${target_db}\""
        
        if [[ "${VERBOSE}" == true ]]; then
            restore_cmd="${restore_cmd} --verbose"
        fi
        
        if [[ "${SKIP_DATA}" == true ]]; then
            restore_cmd="${restore_cmd} --schema-only"
        elif [[ "${SKIP_SCHEMA}" == true ]]; then
            restore_cmd="${restore_cmd} --data-only"
        fi
        
        restore_cmd="${restore_cmd} /tmp/${temp_file}"
    fi
    
    if [[ "${VERBOSE}" == true ]]; then
        log_info "执行命令: ${restore_cmd}"
    fi
    
    # 执行恢复命令
    if ! docker exec "${CONTAINER_NAME}" bash -c "${restore_cmd}"; then
        log_error "数据库恢复失败"
        # 清理临时文件
        docker exec "${CONTAINER_NAME}" rm -f "/tmp/${temp_file}" &> /dev/null || true
        exit 1
    fi
    
    # 清理容器内临时文件
    docker exec "${CONTAINER_NAME}" rm -f "/tmp/${temp_file}" &> /dev/null || true
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 获取恢复后的表数量
    local table_count
    if table_count=$(docker exec "${CONTAINER_NAME}" env PGPASSWORD="${DB_PASSWORD}" psql -U "${DB_USER}" -d "${target_db}" -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" 2>/dev/null); then
        table_count=$(echo "${table_count}" | tr -d ' \n')
    else
        table_count="未知"
    fi
    
    log_success "恢复完成"
    log_info "恢复表数量: ${table_count}"
    log_info "耗时: ${duration}秒"
}

# 列出可用备份文件
list_backup_files() {
    log_info "扫描备份目录: ${BACKUP_DIR}"
    
    if [[ ! -d "${BACKUP_DIR}" ]]; then
        log_error "备份目录不存在: ${BACKUP_DIR}"
        exit 1
    fi
    
    local backup_files
    backup_files=$(find "${BACKUP_DIR}" -name "lucid-bd-*" -type f 2>/dev/null | sort -r || true)
    
    if [[ -z "${backup_files}" ]]; then
        log_warning "没有找到备份文件"
        return
    fi
    
    echo -e "\n📋 可用备份文件:\n"
    
    local count=0
    while IFS= read -r file; do
        if [[ -n "${file}" ]]; then
            count=$((count + 1))
            local filename=$(basename "${file}")
            local file_size
            
            if command -v stat &> /dev/null; then
                if [[ "$(uname)" == "Darwin" ]]; then
                    file_size=$(stat -f%z "${file}")
                else
                    file_size=$(stat -c%s "${file}")
                fi
            else
                file_size=$(wc -c < "${file}")
            fi
            
            local size_human
            if command -v numfmt &> /dev/null; then
                size_human=$(numfmt --to=iec-i --suffix=B "${file_size}")
            else
                size_human="${file_size} bytes"
            fi
            
            local format=$(detect_backup_format "${file}")
            local mtime
            if command -v stat &> /dev/null; then
                if [[ "$(uname)" == "Darwin" ]]; then
                    mtime=$(stat -f%Sm -t "%Y-%m-%d %H:%M:%S" "${file}")
                else
                    mtime=$(stat -c%y "${file}" | cut -d. -f1)
                fi
            else
                mtime=$(ls -l "${file}" | awk '{print $6, $7, $8}')
            fi
            
            echo "${count}. ${filename}"
            echo "   📁 路径: ${file}"
            echo "   📊 大小: ${size_human}"
            echo "   📅 时间: ${mtime}"
            echo "   🗜️ 格式: ${format}"
            echo ""
        fi
    done <<< "${backup_files}"
    
    if [[ ${count} -eq 0 ]]; then
        log_warning "没有找到有效的备份文件"
    else
        log_info "找到 ${count} 个备份文件"
    fi
}

# 主函数
main() {
    # 解析命令行参数
    parse_args "$@"
    
    # 如果是列出备份文件
    if [[ "${LIST_BACKUPS:-}" == true ]]; then
        list_backup_files
        exit 0
    fi
    
    # 检查是否指定了备份文件
    if [[ -z "${BACKUP_FILE:-}" ]]; then
        log_error "请指定备份文件路径"
        echo "使用 --help 查看使用方法"
        exit 1
    fi
    
    log_info "开始PostgreSQL数据库恢复"
    
    # 环境检查
    check_docker
    check_container
    test_database_connection
    validate_backup_file "${BACKUP_FILE}"
    
    # 准备数据库环境
    local target_db="${TARGET_DB:-${DB_NAME}}"
    
    if [[ "${DROP_DATABASE}" == true ]]; then
        drop_database "${target_db}"
    fi
    
    if [[ "${CREATE_DATABASE}" == true ]]; then
        create_database "${target_db}"
    fi
    
    # 执行恢复
    perform_restore "${BACKUP_FILE}"
    
    log_success "恢复流程完成"
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi