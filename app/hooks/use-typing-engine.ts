'use client';

import { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { TYPING_CONFIG } from '@/config/typing';

// 打字状态枚举
export enum State {
  Start,
  Run,
  Finish,
}

// Hook 的返回类型接口
export interface TypingEngineResult {
  state: State;
  typed: string;
  timeLeft: number;
  wpm: number;
  accuracy: number;
  errors: number;
  restart: () => void;
  fullText: string;
  skipWord: () => void;
  previousWord: () => void;
}

/**
 * 核心打字引擎 Hook
 * 管理打字练习的所有核心逻辑，包括状态管理、计时器、输入处理等
 *
 * @param words - 用于练习的单词数组
 * @param timeLimit - 练习时间限制（秒）
 * @returns TypingEngineResult - 包含所有打字状态和控制方法的对象
 */
export const useTypingEngine = (words: string[], timeLimit: number): TypingEngineResult => {
  const [state, setState] = useState<State>(State.Start);
  const [typed, setTyped] = useState('');
  const [timeLeft, setTimeLeft] = useState(timeLimit);
  const [errors, setErrors] = useState(0);

  const timer = useRef<NodeJS.Timeout | null>(null);
  const currentWordIndexRef = useRef(0);
  const handleKeyDownRef = useRef<((key: string) => void) | null>(null);
  const fullText = useMemo(() => words.join(' '), [words]);

  const restart = useCallback(() => {
    if (timer.current) {
      clearInterval(timer.current);
    }
    setState(State.Start);
    setTimeLeft(timeLimit);
    setTyped('');
    setErrors(0);
    timer.current = null;
    currentWordIndexRef.current = 0;
  }, [timeLimit]);

  useEffect(() => {
    restart();
  }, [words, restart]);

  // 清理定时器，防止内存泄漏
  useEffect(() => {
    return () => {
      if (timer.current) {
        clearInterval(timer.current);
      }
    };
  }, []);

  const previousWord = useCallback(() => {
    if (state !== State.Run) return;
    const targetWordIndex = Math.max(0, currentWordIndexRef.current - 1);

    if (currentWordIndexRef.current > 0) {
      const wordsUpToPrevious = words.slice(0, targetWordIndex);
      const newTyped = wordsUpToPrevious.join(' ') + (wordsUpToPrevious.length > 0 ? ' ' : '');

      setTyped(newTyped);
      currentWordIndexRef.current = targetWordIndex;
    }
  }, [words, state]);

  const skipWord = useCallback(() => {
    if (state !== State.Run || currentWordIndexRef.current >= words.length - 1) {
      return;
    }

    const typedWords = typed.split(' ');
    const completedTypedPart = typedWords.slice(0, currentWordIndexRef.current).join(' ');
    const wordToSkip = words[currentWordIndexRef.current];
    const newTyped = (completedTypedPart ? completedTypedPart + ' ' : '') + wordToSkip + ' ';

    setTyped(newTyped);
    currentWordIndexRef.current++;
  }, [words, typed, state]);

  const handleKeyDown = useCallback(
    (key: string) => {
      if (state === State.Finish) return;

      if (state === State.Start) {
        setState(State.Run);
        timer.current = setInterval(() => {
          setTimeLeft((prevTime) => {
            if (prevTime <= 1) {
              setState(State.Finish);
              if (timer.current) clearInterval(timer.current);
              return 0;
            }
            return prevTime - 1;
          });
        }, TYPING_CONFIG.COUNTDOWN_INTERVAL);
      }

      if (key === 'Backspace') {
        // If we backspace over a space, we are moving to the previous word
        if (typed.endsWith(' ')) {
          currentWordIndexRef.current = Math.max(0, currentWordIndexRef.current - 1);
        }
        setTyped((prev) => prev.slice(0, -1));
      } else if (key.length === 1) {
        if (key !== fullText[typed.length]) {
          setErrors((prev) => prev + 1);
        }

        // Word completion check (moving to next word by typing a space)
        if (key === ' ') {
          const currentWordTyped = typed.split(' ')[currentWordIndexRef.current];
          if (
            currentWordTyped &&
            currentWordTyped.length > 0 &&
            currentWordIndexRef.current < words.length - 1
          ) {
            currentWordIndexRef.current++;
          }
        }

        setTyped((prev) => prev + key);
      }
    },
    [state, typed, fullText, words]
  );

  // 更新 ref 以保持最新的回调函数
  handleKeyDownRef.current = handleKeyDown;

  useEffect(() => {
    const keydownListener = (e: KeyboardEvent) => {
      if (e.key.length === 1 || e.key === 'Backspace') {
        e.preventDefault();
        handleKeyDownRef.current?.(e.key);
      }
    };
    window.addEventListener('keydown', keydownListener);
    return () => window.removeEventListener('keydown', keydownListener);
  }, []); // 空依赖数组，只绑定一次

  useEffect(() => {
    if (typed.length === fullText.length && state === State.Run) {
      setState(State.Finish);
      if (timer.current) clearInterval(timer.current);
    }
  }, [typed, fullText, state]);

  const wpm = useMemo(() => {
    if (state !== State.Finish || timeLeft === timeLimit) return 0;
    const minutes = (timeLimit - timeLeft) / 60;
    const wordsTyped = typed.length / TYPING_CONFIG.AVERAGE_WORD_LENGTH;
    return Math.round(wordsTyped / minutes);
  }, [state, typed, timeLeft, timeLimit]);

  const accuracy = useMemo(() => {
    if (typed.length === 0) return 100;
    return Math.round(((typed.length - errors) / typed.length) * 100);
  }, [typed, errors]);

  return {
    state,
    typed,
    timeLeft,
    wpm,
    accuracy,
    errors,
    restart,
    fullText,
    skipWord,
    previousWord,
  };
};
