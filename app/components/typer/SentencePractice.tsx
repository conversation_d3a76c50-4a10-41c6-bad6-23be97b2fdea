'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { useTypingEngine, State } from '@/hooks/use-typing-engine';
import { Character, Caret } from './shared';

// 常量定义
const SENTENCE_PRACTICE_TIME_LIMIT = 120;

// 类型定义
interface SentenceExample {
  sentence: string;
  translation: string;
}

// 句子练习数据
const SENTENCE_EXAMPLES: SentenceExample[] = [
  {
    sentence: 'The ubiquitous smartphone has changed how we communicate.',
    translation: '无处不在的智能手机改变了我们的交流方式。',
  },
  {
    sentence: "Life's ephemeral moments deserve our full attention.",
    translation: '生命中短暂的时刻值得我们全神贯注。',
  },
  {
    sentence: 'Her mellifluous voice captivated the entire audience.',
    translation: '她悦耳的声音迷住了整个观众。',
  },
  {
    sentence: 'Finding that book was pure serendipity.',
    translation: '找到那本书纯粹是意外发现。',
  },
  {
    sentence: 'The petrichor after rain brings childhood memories.',
    translation: '雨后泥土的气味带来童年的回忆。',
  },
  {
    sentence: 'His eloquence impressed everyone at the meeting.',
    translation: '他的口才给会议上的每个人留下了深刻印象。',
  },
  {
    sentence: "The villain's nefarious plan was finally exposed.",
    translation: '反派的邪恶计划终于被揭露了。',
  },
  {
    sentence: 'The cacophony of traffic disturbed her concentration.',
    translation: '交通的刺耳声音干扰了她的注意力。',
  },
  {
    sentence: "She had an epiphany about her life's purpose.",
    translation: '她对自己人生目标有了顿悟。',
  },
  {
    sentence: 'The languid summer afternoon made everyone sleepy.',
    translation: '慵懒的夏日午后让每个人都昏昏欲睡。',
  },
  {
    sentence: "There's no panacea for all of life's problems.",
    translation: '没有解决所有人生问题的万能药。',
  },
  {
    sentence: 'Her gregarious nature made her popular at parties.',
    translation: '她合群的性格让她在聚会上很受欢迎。',
  },
  {
    sentence: 'They decided to ostracize him from the group.',
    translation: '他们决定将他从群体中排斥出去。',
  },
  {
    sentence: 'His quixotic dreams seemed impossible to achieve.',
    translation: '他不切实际的梦想似乎无法实现。',
  },
  {
    sentence: 'Her resilience helped her overcome many challenges.',
    translation: '她的恢复力帮助她克服了许多挑战。',
  },
  {
    sentence: 'The sycophant always agreed with the boss.',
    translation: '那个马屁精总是赞同老板的意见。',
  },
  {
    sentence: 'His verbose explanations confused the students.',
    translation: '他啰嗦的解释让学生们感到困惑。',
  },
  {
    sentence: 'She showed zealous dedication to her work.',
    translation: '她对工作表现出热情的奉献精神。',
  },
  {
    sentence: 'His business acumen led to great success.',
    translation: '他的商业敏锐度带来了巨大的成功。',
  },
  {
    sentence: 'The benevolent king cared deeply for his people.',
    translation: '仁慈的国王深深关心他的人民。',
  },
];

/**
 * 获取随机句子
 */
const getRandomSentence = (): SentenceExample => {
  return SENTENCE_EXAMPLES[Math.floor(Math.random() * SENTENCE_EXAMPLES.length)];
};

/**
 * 句子练习组件
 * 提供完整英语句子的打字练习，包含中文翻译提示
 */
export const SentencePractice: React.FC = () => {
  const [currentSentence, setCurrentSentence] = useState<SentenceExample>(() =>
    getRandomSentence()
  );
  const sentenceWords = currentSentence.sentence.split(' ');

  const { state, typed, timeLeft, wpm, accuracy, errors, restart, fullText } = useTypingEngine(
    sentenceWords,
    SENTENCE_PRACTICE_TIME_LIMIT
  );

  const isSentenceComplete = typed === currentSentence.sentence;

  const handleNext = useCallback(() => {
    setCurrentSentence(getRandomSentence());
  }, []);

  useEffect(() => {
    if (isSentenceComplete && state === State.Run) {
      const timer = setTimeout(() => {
        handleNext();
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [isSentenceComplete, state, handleNext]);

  useEffect(() => {
    restart();
  }, [currentSentence, restart]);

  const characters = useMemo(() => {
    return currentSentence.sentence
      .split('')
      .map((char: string, index: number) => (
        <Character
          key={`${char}-${index}`}
          expected={char}
          actual={typed[index]}
          isTyped={index < typed.length}
        />
      ));
  }, [currentSentence.sentence, typed]);

  return (
    <div className="space-y-6">
      <div className="flex justify-around items-center p-4 bg-muted rounded-lg">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Time Left</p>
          <p className="text-2xl font-bold text-primary">{timeLeft}</p>
        </div>
        {state === State.Finish ? (
          <>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">WPM</p>
              <p className="text-2xl font-bold">{wpm}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Accuracy</p>
              <p className="text-2xl font-bold">{accuracy}%</p>
            </div>
          </>
        ) : null}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Errors</p>
          <p className="text-2xl font-bold text-destructive">{errors}</p>
        </div>
      </div>

      <div className="text-center space-y-4">
        <div className="text-lg text-muted-foreground mb-4">{currentSentence.translation}</div>

        <div className="text-2xl leading-relaxed p-6 border rounded-md relative select-none font-mono">
          {typed.length === 0 && state === State.Start && <Caret />}
          {characters.map((charComponent, index) => (
            <React.Fragment key={index}>
              {charComponent}
              {index === typed.length - 1 && state === State.Run && <Caret />}
            </React.Fragment>
          ))}
          {state === State.Start && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <p className="text-muted-foreground animate-pulse">Start typing the sentence...</p>
            </div>
          )}
          {isSentenceComplete && state === State.Run && (
            <div className="absolute inset-0 flex items-center justify-center bg-green-500/10">
              <p className="text-green-500 font-bold text-xl animate-pulse">
                Correct! Next sentence loading...
              </p>
            </div>
          )}
        </div>
      </div>

      <div className="flex gap-4 justify-center">
        <Button onClick={handleNext}>Next Sentence</Button>
      </div>
    </div>
  );
};
