/**
 * @fileoverview Auth工具函数测试套件
 *
 * 测试认证相关的工具类和函数，包括：
 * - TokenStorage: 令牌存储管理
 * - JWTUtils: JWT令牌工具
 * - AuthenticatedAPI: 认证API请求
 * - PlatformUtils: 平台检测工具
 * - AuthError: 错误处理
 * - 安全性和边界情况测试
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  TokenStorage,
  JWTUtils,
  AuthenticatedAPI,
  PlatformUtils,
  AuthError,
  AUTH_ERROR_CODES,
  handleAuthError,
} from '../auth';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Mock fetch
const mockFetch = vi.fn();

// Mock valid JWT token
const createMockJWT = (payload: any = {}, expired = false) => {
  const header = { alg: 'HS256', typ: 'JWT' };
  const defaultPayload = {
    sub: 'user-123',
    email: '<EMAIL>',
    exp: expired ? Math.floor(Date.now() / 1000) - 3600 : Math.floor(Date.now() / 1000) + 3600,
    iat: Math.floor(Date.now() / 1000),
    ...payload,
  };

  const encodedHeader = btoa(JSON.stringify(header));
  const encodedPayload = btoa(JSON.stringify(defaultPayload));
  const signature = 'mock-signature';

  return `${encodedHeader}.${encodedPayload}.${signature}`;
};

describe('Auth Utilities', () => {
  beforeEach(() => {
    // Mock global objects
    Object.defineProperty(window, 'localStorage', {
      value: mockLocalStorage,
      writable: true,
    });

    global.fetch = mockFetch;
    global.btoa = vi.fn((str) => Buffer.from(str).toString('base64'));
    global.atob = vi.fn((str) => Buffer.from(str, 'base64').toString());

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('TokenStorage', () => {
    describe('基础存储操作', () => {
      it('应该正确存储和获取访问令牌', () => {
        const token = 'test-access-token';
        mockLocalStorage.getItem.mockReturnValue(token);

        TokenStorage.set(token);
        const retrieved = TokenStorage.get();

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('accessToken', token);
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith('accessToken');
        expect(retrieved).toBe(token);
      });

      it('应该正确存储和获取刷新令牌', () => {
        const refreshToken = 'test-refresh-token';
        mockLocalStorage.getItem.mockReturnValue(refreshToken);

        TokenStorage.setRefresh(refreshToken);
        const retrieved = TokenStorage.getRefresh();

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshToken', refreshToken);
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith('refreshToken');
        expect(retrieved).toBe(refreshToken);
      });

      it('应该正确存储和获取用户数据', () => {
        const user = { id: '123', email: '<EMAIL>', name: 'Test User' };
        const userJson = JSON.stringify(user);
        mockLocalStorage.getItem.mockReturnValue(userJson);

        TokenStorage.setUser(user);
        const retrieved = TokenStorage.getUser();

        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('user', userJson);
        expect(mockLocalStorage.getItem).toHaveBeenCalledWith('user');
        expect(retrieved).toEqual(user);
      });

      it('应该正确删除令牌', () => {
        TokenStorage.remove();
        TokenStorage.removeRefresh();
        TokenStorage.removeUser();

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
      });

      it('应该清除所有认证数据', () => {
        TokenStorage.clear();

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
      });
    });

    describe('浏览器环境检测', () => {
      it('应该在非浏览器环境中返回null', () => {
        // Mock non-browser environment
        Object.defineProperty(window, 'localStorage', {
          value: undefined,
          writable: true,
        });

        expect(TokenStorage.get()).toBeNull();
        expect(TokenStorage.getRefresh()).toBeNull();
        expect(TokenStorage.getUser()).toBeNull();
      });

      it('应该在非浏览器环境中静默处理设置操作', () => {
        Object.defineProperty(window, 'localStorage', {
          value: undefined,
          writable: true,
        });

        // 这些操作不应该抛出错误
        expect(() => {
          TokenStorage.set('token');
          TokenStorage.setRefresh('refresh');
          TokenStorage.setUser({ id: '123' });
          TokenStorage.remove();
          TokenStorage.clear();
        }).not.toThrow();
      });
    });

    describe('令牌验证', () => {
      it('应该验证有效的JWT令牌', () => {
        const validToken = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(validToken);

        expect(TokenStorage.isValid()).toBe(true);
      });

      it('应该拒绝过期的JWT令牌', () => {
        const expiredToken = createMockJWT({}, true);
        mockLocalStorage.getItem.mockReturnValue(expiredToken);

        expect(TokenStorage.isValid()).toBe(false);
      });

      it('应该拒绝无效格式的令牌', () => {
        mockLocalStorage.getItem.mockReturnValue('invalid.token');

        expect(TokenStorage.isValid()).toBe(false);
      });

      it('应该在没有令牌时返回false', () => {
        mockLocalStorage.getItem.mockReturnValue(null);

        expect(TokenStorage.isValid()).toBe(false);
      });
    });

    describe('错误处理', () => {
      it('应该处理JSON解析错误', () => {
        mockLocalStorage.getItem.mockReturnValue('invalid-json');

        expect(TokenStorage.getUser()).toBeNull();
      });

      it('应该处理localStorage访问错误', () => {
        mockLocalStorage.getItem.mockImplementation(() => {
          throw new Error('Storage error');
        });

        expect(TokenStorage.get()).toBeNull();
      });
    });
  });

  describe('JWTUtils', () => {
    describe('令牌解码', () => {
      it('应该正确解码有效的JWT令牌', () => {
        const payload = { sub: 'user-123', email: '<EMAIL>' };
        const token = createMockJWT(payload);

        const decoded = JWTUtils.decode(token);

        expect(decoded).toMatchObject(payload);
      });

      it('应该处理无效的JWT令牌', () => {
        const invalidToken = 'invalid.token.format';

        const decoded = JWTUtils.decode(invalidToken);

        expect(decoded).toBeNull();
      });

      it('应该处理格式错误的JWT令牌', () => {
        const malformedToken = 'header.invalid-base64.signature';

        const decoded = JWTUtils.decode(malformedToken);

        expect(decoded).toBeNull();
      });
    });

    describe('过期检查', () => {
      it('应该正确识别未过期的令牌', () => {
        const token = createMockJWT({ exp: Math.floor(Date.now() / 1000) + 3600 });

        expect(JWTUtils.isExpired(token)).toBe(false);
      });

      it('应该正确识别过期的令牌', () => {
        const token = createMockJWT({ exp: Math.floor(Date.now() / 1000) - 3600 });

        expect(JWTUtils.isExpired(token)).toBe(true);
      });

      it('应该处理没有过期时间的令牌', () => {
        const token = createMockJWT({ sub: 'user' }); // 没有exp字段

        // 移除exp字段
        const parts = token.split('.');
        const payload = JSON.parse(atob(parts[1]));
        delete payload.exp;
        const newPayload = btoa(JSON.stringify(payload));
        const newToken = `${parts[0]}.${newPayload}.${parts[2]}`;

        expect(JWTUtils.isExpired(newToken)).toBe(true);
      });
    });

    describe('时间计算', () => {
      it('应该正确计算到期剩余时间', () => {
        const expTime = Math.floor(Date.now() / 1000) + 1800; // 30分钟后
        const token = createMockJWT({ exp: expTime });

        const timeUntilExpiry = JWTUtils.getTimeUntilExpiry(token);

        expect(timeUntilExpiry).toBeGreaterThan(1790);
        expect(timeUntilExpiry).toBeLessThan(1810);
      });

      it('应该为过期令牌返回0', () => {
        const token = createMockJWT({}, true);

        const timeUntilExpiry = JWTUtils.getTimeUntilExpiry(token);

        expect(timeUntilExpiry).toBe(0);
      });

      it('应该正确判断是否需要刷新', () => {
        // 令牌在200秒后过期，默认缓冲时间300秒
        const expTime = Math.floor(Date.now() / 1000) + 200;
        const token = createMockJWT({ exp: expTime });

        expect(JWTUtils.shouldRefresh(token)).toBe(true);

        // 令牌在400秒后过期
        const expTime2 = Math.floor(Date.now() / 1000) + 400;
        const token2 = createMockJWT({ exp: expTime2 });

        expect(JWTUtils.shouldRefresh(token2)).toBe(false);
      });

      it('应该支持自定义缓冲时间', () => {
        const expTime = Math.floor(Date.now() / 1000) + 100;
        const token = createMockJWT({ exp: expTime });

        expect(JWTUtils.shouldRefresh(token, 50)).toBe(false);
        expect(JWTUtils.shouldRefresh(token, 150)).toBe(true);
      });
    });
  });

  describe('AuthenticatedAPI', () => {
    beforeEach(() => {
      mockFetch.mockClear();
    });

    describe('基础请求', () => {
      it('应该发送带有Authorization头的请求', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.get('/api/test');

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        });
      });

      it('应该在没有令牌时发送不带Authorization头的请求', async () => {
        mockLocalStorage.getItem.mockReturnValue(null);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.get('/api/test');

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });
      });

      it('应该支持POST请求', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        const data = { key: 'value' };
        await AuthenticatedAPI.post('/api/test', data);

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(data),
        });
      });

      it('应该支持PUT请求', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        const data = { key: 'updated' };
        await AuthenticatedAPI.put('/api/test', data);

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(data),
        });
      });

      it('应该支持DELETE请求', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.delete('/api/test');

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
        });
      });
    });

    describe('令牌刷新', () => {
      it('应该在令牌即将过期时自动刷新', async () => {
        // 创建即将过期的令牌（100秒后过期，小于默认缓冲时间300秒）
        const expTime = Math.floor(Date.now() / 1000) + 100;
        const oldToken = createMockJWT({ exp: expTime });
        const newToken = createMockJWT();
        const refreshToken = 'refresh-token';

        mockLocalStorage.getItem
          .mockReturnValueOnce(oldToken) // 第一次获取访问令牌
          .mockReturnValueOnce(refreshToken) // 获取刷新令牌
          .mockReturnValueOnce(newToken); // 获取新的访问令牌

        // Mock refresh API response
        mockFetch
          .mockResolvedValueOnce(
            new Response(
              JSON.stringify({
                accessToken: newToken,
                refreshToken: 'new-refresh-token',
              }),
              { status: 200 }
            )
          )
          .mockResolvedValueOnce(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.get('/api/test');

        // 应该调用刷新端点
        expect(mockFetch).toHaveBeenCalledWith('/api/auth/token/refresh', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refreshToken }),
        });

        // 应该存储新令牌
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('accessToken', newToken);
        expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refreshToken', 'new-refresh-token');
      });

      it('应该在刷新失败时清除令牌', async () => {
        const expTime = Math.floor(Date.now() / 1000) + 100;
        const oldToken = createMockJWT({ exp: expTime });
        const refreshToken = 'refresh-token';

        mockLocalStorage.getItem.mockReturnValueOnce(oldToken).mockReturnValueOnce(refreshToken);

        // Mock refresh API failure
        mockFetch.mockResolvedValueOnce(new Response('', { status: 401 }));

        await expect(AuthenticatedAPI.get('/api/test')).rejects.toThrow();

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
      });
    });

    describe('401响应处理', () => {
      it('应该在收到401响应时尝试刷新令牌', async () => {
        const token = createMockJWT();
        const refreshToken = 'refresh-token';
        const newToken = createMockJWT();

        mockLocalStorage.getItem
          .mockReturnValueOnce(token) // 初始令牌获取
          .mockReturnValueOnce(refreshToken) // 刷新时获取refresh token
          .mockReturnValueOnce(newToken); // 获取新令牌

        // Mock 401 response, then refresh success, then retry success
        mockFetch
          .mockResolvedValueOnce(new Response('', { status: 401 })) // 初始请求401
          .mockResolvedValueOnce(
            new Response(
              JSON.stringify({
                accessToken: newToken,
              }),
              { status: 200 }
            )
          ) // 刷新成功
          .mockResolvedValueOnce(new Response('{}', { status: 200 })); // 重试成功

        const response = await AuthenticatedAPI.get('/api/test');

        expect(response.status).toBe(200);
        expect(mockFetch).toHaveBeenCalledTimes(3);
      });

      it('应该在刷新失败时抛出认证错误', async () => {
        const token = createMockJWT();
        const refreshToken = 'refresh-token';

        mockLocalStorage.getItem.mockReturnValueOnce(token).mockReturnValueOnce(refreshToken);

        mockFetch
          .mockResolvedValueOnce(new Response('', { status: 401 }))
          .mockResolvedValueOnce(new Response('', { status: 401 })); // 刷新也失败

        await expect(AuthenticatedAPI.get('/api/test')).rejects.toThrow('Authentication required');

        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('accessToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refreshToken');
        expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
      });
    });

    describe('自定义请求选项', () => {
      it('应该合并自定义headers', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.get('/api/test', {
          headers: {
            'Custom-Header': 'custom-value',
            'X-Request-ID': 'request-123',
          },
        });

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
            'Custom-Header': 'custom-value',
            'X-Request-ID': 'request-123',
          },
        });
      });

      it('应该保留其他请求选项', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.get('/api/test', {
          credentials: 'include',
          cache: 'no-cache',
        });

        expect(mockFetch).toHaveBeenCalledWith('/api/test', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${token}`,
          },
          credentials: 'include',
          cache: 'no-cache',
        });
      });
    });
  });

  describe('PlatformUtils', () => {
    describe('平台检测', () => {
      it('应该检测Web环境', () => {
        Object.defineProperty(window, 'chrome', {
          value: undefined,
          writable: true,
        });

        expect(PlatformUtils.isWeb).toBe(true);
        expect(PlatformUtils.isExtension).toBe(false);
      });

      it('应该检测Chrome扩展环境', () => {
        Object.defineProperty(window, 'chrome', {
          value: {
            runtime: {
              id: 'extension-id',
            },
          },
          writable: true,
        });

        expect(PlatformUtils.isExtension).toBe(true);
        expect(PlatformUtils.isWeb).toBe(false);
      });

      it('应该检测移动设备', () => {
        Object.defineProperty(navigator, 'userAgent', {
          value: 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)',
          writable: true,
        });

        expect(PlatformUtils.isMobile).toBe(true);
        expect(PlatformUtils.isIOS).toBe(true);
      });

      it('应该检测Android设备', () => {
        Object.defineProperty(navigator, 'userAgent', {
          value: 'Mozilla/5.0 (Linux; Android 11; SM-G991B)',
          writable: true,
        });

        expect(PlatformUtils.isMobile).toBe(true);
        expect(PlatformUtils.isIOS).toBe(false);
      });
    });

    describe('存储选择', () => {
      it('应该在扩展环境中使用chrome.storage', () => {
        const mockChromeStorage = { get: vi.fn(), set: vi.fn() };
        Object.defineProperty(window, 'chrome', {
          value: {
            runtime: { id: 'extension-id' },
            storage: { local: mockChromeStorage },
          },
          writable: true,
        });

        expect(PlatformUtils.storage).toBe(mockChromeStorage);
      });

      it('应该在Web环境中使用localStorage', () => {
        Object.defineProperty(window, 'chrome', {
          value: undefined,
          writable: true,
        });

        expect(PlatformUtils.storage).toBe(localStorage);
      });

      it('应该在不支持的环境中返回null', () => {
        // Mock environment without window
        const originalWindow = global.window;
        delete (global as any).window;

        expect(PlatformUtils.storage).toBeNull();

        global.window = originalWindow;
      });
    });
  });

  describe('AuthError', () => {
    describe('错误创建', () => {
      it('应该创建带有正确属性的错误', () => {
        const error = new AuthError('Test error', AUTH_ERROR_CODES.INVALID_CREDENTIALS, 401);

        expect(error.message).toBe('Test error');
        expect(error.code).toBe(AUTH_ERROR_CODES.INVALID_CREDENTIALS);
        expect(error.statusCode).toBe(401);
        expect(error.name).toBe('AuthError');
        expect(error instanceof Error).toBe(true);
      });

      it('应该支持不带状态码的错误', () => {
        const error = new AuthError('Test error', AUTH_ERROR_CODES.NETWORK_ERROR);

        expect(error.message).toBe('Test error');
        expect(error.code).toBe(AUTH_ERROR_CODES.NETWORK_ERROR);
        expect(error.statusCode).toBeUndefined();
      });
    });

    describe('错误处理函数', () => {
      it('应该直接返回AuthError实例', () => {
        const originalError = new AuthError('Original', AUTH_ERROR_CODES.TOKEN_EXPIRED);
        const handled = handleAuthError(originalError);

        expect(handled).toBe(originalError);
      });

      it('应该处理401响应错误', () => {
        const responseError = {
          response: { status: 401 },
        };

        const handled = handleAuthError(responseError);

        expect(handled).toBeInstanceOf(AuthError);
        expect(handled.code).toBe(AUTH_ERROR_CODES.UNAUTHORIZED);
        expect(handled.statusCode).toBe(401);
        expect(handled.message).toBe('Authentication required');
      });

      it('应该处理429响应错误', () => {
        const responseError = {
          response: { status: 429 },
        };

        const handled = handleAuthError(responseError);

        expect(handled).toBeInstanceOf(AuthError);
        expect(handled.code).toBe(AUTH_ERROR_CODES.RATE_LIMITED);
        expect(handled.statusCode).toBe(429);
        expect(handled.message).toBe('Too many requests. Please try again later.');
      });

      it('应该处理服务器错误', () => {
        const responseError = {
          response: { status: 500 },
        };

        const handled = handleAuthError(responseError);

        expect(handled).toBeInstanceOf(AuthError);
        expect(handled.code).toBe(AUTH_ERROR_CODES.NETWORK_ERROR);
        expect(handled.statusCode).toBe(500);
        expect(handled.message).toBe('Server error. Please try again later.');
      });

      it('应该处理通用错误', () => {
        const genericError = new Error('Generic error message');

        const handled = handleAuthError(genericError);

        expect(handled).toBeInstanceOf(AuthError);
        expect(handled.code).toBe(AUTH_ERROR_CODES.NETWORK_ERROR);
        expect(handled.message).toBe('Generic error message');
        expect(handled.statusCode).toBeUndefined();
      });

      it('应该处理无消息的错误', () => {
        const unknownError = {};

        const handled = handleAuthError(unknownError);

        expect(handled).toBeInstanceOf(AuthError);
        expect(handled.code).toBe(AUTH_ERROR_CODES.NETWORK_ERROR);
        expect(handled.message).toBe('An unexpected error occurred');
      });
    });
  });

  describe('安全性测试', () => {
    describe('令牌安全', () => {
      it('应该安全处理恶意JWT载荷', () => {
        // 尝试注入恶意代码的JWT
        const maliciousPayload = {
          sub: '<script>alert("xss")</script>',
          eval: 'malicious code',
        };
        const token = createMockJWT(maliciousPayload);

        const decoded = JWTUtils.decode(token);

        expect(decoded.sub).toBe('<script>alert("xss")</script>');
        expect(decoded.eval).toBe('malicious code');
        // 验证不会执行恶意代码，只是作为字符串存储
      });

      it('应该防止原型污染攻击', () => {
        const maliciousUser = JSON.stringify({
          __proto__: { admin: true },
          constructor: { prototype: { admin: true } },
          id: 'user-123',
        });

        mockLocalStorage.getItem.mockReturnValue(maliciousUser);

        const user = TokenStorage.getUser();

        // 验证原型没有被污染
        expect(user.__proto__).not.toHaveProperty('admin');
        expect(user.constructor.prototype).not.toHaveProperty('admin');
      });
    });

    describe('存储安全', () => {
      it('应该处理localStorage被禁用的情况', () => {
        Object.defineProperty(window, 'localStorage', {
          get() {
            throw new Error('localStorage is disabled');
          },
        });

        expect(() => TokenStorage.get()).not.toThrow();
        expect(TokenStorage.get()).toBeNull();
      });

      it('应该处理存储容量限制', () => {
        mockLocalStorage.setItem.mockImplementation(() => {
          throw new Error('QuotaExceededError');
        });

        expect(() => TokenStorage.set('token')).not.toThrow();
      });
    });

    describe('网络安全', () => {
      it('应该防止CSRF攻击', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);
        mockFetch.mockResolvedValue(new Response('{}', { status: 200 }));

        await AuthenticatedAPI.get('/api/test');

        // 验证请求包含CSRF防护头
        const callArgs = mockFetch.mock.calls[0];
        const headers = callArgs[1].headers;
        expect(headers['Content-Type']).toBe('application/json');
      });

      it('应该验证响应内容类型', async () => {
        const token = createMockJWT();
        mockLocalStorage.getItem.mockReturnValue(token);

        // Mock response with unexpected content type
        const response = new Response('<html>Not JSON</html>', {
          status: 200,
          headers: { 'Content-Type': 'text/html' },
        });
        mockFetch.mockResolvedValue(response);

        const result = await AuthenticatedAPI.get('/api/test');

        // API不应该自动解析非JSON响应
        expect(result.headers.get('Content-Type')).toBe('text/html');
      });
    });
  });

  describe('性能和内存测试', () => {
    it('应该有效处理大量令牌操作', () => {
      const iterations = 1000;

      for (let i = 0; i < iterations; i++) {
        const token = createMockJWT({ iteration: i });
        TokenStorage.set(token);
        TokenStorage.get();
        TokenStorage.isValid();
      }

      // 验证操作完成且没有内存泄漏
      expect(mockLocalStorage.setItem).toHaveBeenCalledTimes(iterations);
    });

    it('应该缓存解码结果以提高性能', () => {
      const token = createMockJWT();

      // 多次解码同一令牌
      const decode1 = JWTUtils.decode(token);
      const decode2 = JWTUtils.decode(token);
      const decode3 = JWTUtils.decode(token);

      expect(decode1).toEqual(decode2);
      expect(decode2).toEqual(decode3);
    });
  });

  describe('边界情况', () => {
    it('应该处理空字符串令牌', () => {
      expect(JWTUtils.decode('')).toBeNull();
      expect(JWTUtils.isExpired('')).toBe(true);
      expect(JWTUtils.getTimeUntilExpiry('')).toBe(0);
    });

    it('应该处理null/undefined输入', () => {
      expect(JWTUtils.decode(null as any)).toBeNull();
      expect(JWTUtils.decode(undefined as any)).toBeNull();
      expect(JWTUtils.isExpired(null as any)).toBe(true);
      expect(JWTUtils.isExpired(undefined as any)).toBe(true);
    });

    it('应该处理极值时间戳', () => {
      // 测试时间戳为0的情况
      const token1 = createMockJWT({ exp: 0 });
      expect(JWTUtils.isExpired(token1)).toBe(true);

      // 测试极大时间戳
      const token2 = createMockJWT({ exp: 2147483647 }); // 2038年
      expect(JWTUtils.isExpired(token2)).toBe(false);
    });

    it('应该处理网络中断的情况', async () => {
      const token = createMockJWT();
      mockLocalStorage.getItem.mockReturnValue(token);
      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(AuthenticatedAPI.get('/api/test')).rejects.toThrow('Network error');
    });
  });
});
