'use client';

// Comprehensive platform detection and utilities
export class PlatformDetection {
  // Basic platform checks
  static get isClient(): boolean {
    return typeof window !== 'undefined';
  }

  static get isServer(): boolean {
    return typeof window === 'undefined';
  }

  static get isWeb(): boolean {
    return this.isClient && !this.isExtension && !this.isNativeMobile;
  }

  static get isExtension(): boolean {
    return (
      this.isClient &&
      typeof (window as any).chrome !== 'undefined' &&
      typeof (window as any).chrome.runtime !== 'undefined' &&
      (window as any).chrome.runtime.id !== undefined
    );
  }

  static get isMobileWeb(): boolean {
    return (
      this.isClient &&
      /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent) &&
      !this.isNativeMobile
    );
  }

  static get isNativeMobile(): boolean {
    // This would be true in React Native environment
    return this.isClient && typeof (window as any).ReactNativeWebView !== 'undefined';
  }

  static get isIOS(): boolean {
    return this.isClient && /iPad|iPhone|iPod/.test(navigator.userAgent);
  }

  static get isAndroid(): boolean {
    return this.isClient && /Android/.test(navigator.userAgent);
  }

  static get isMacOS(): boolean {
    return this.isClient && /Macintosh|MacIntel|MacPPC|Mac68K/.test(navigator.platform);
  }

  static get isWindows(): boolean {
    return this.isClient && /Win32|Win64|Windows|WinCE/.test(navigator.platform);
  }

  static get isLinux(): boolean {
    return this.isClient && /Linux/.test(navigator.platform);
  }

  // Browser detection
  static get isChrome(): boolean {
    return this.isClient && /Chrome/.test(navigator.userAgent) && !/Edge/.test(navigator.userAgent);
  }

  static get isFirefox(): boolean {
    return this.isClient && /Firefox/.test(navigator.userAgent);
  }

  static get isSafari(): boolean {
    return (
      this.isClient && /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
    );
  }

  static get isEdge(): boolean {
    return this.isClient && /Edge/.test(navigator.userAgent);
  }

  // Feature detection
  static get supportsLocalStorage(): boolean {
    try {
      return (
        this.isClient && typeof window.localStorage !== 'undefined' && window.localStorage !== null
      );
    } catch {
      return false;
    }
  }

  static get supportsSessionStorage(): boolean {
    try {
      return (
        this.isClient &&
        typeof window.sessionStorage !== 'undefined' &&
        window.sessionStorage !== null
      );
    } catch {
      return false;
    }
  }

  static get supportsIndexedDB(): boolean {
    return this.isClient && typeof window.indexedDB !== 'undefined';
  }

  static get supportsWebCrypto(): boolean {
    return (
      this.isClient &&
      typeof window.crypto !== 'undefined' &&
      typeof window.crypto.subtle !== 'undefined'
    );
  }

  static get supportsPushNotifications(): boolean {
    return (
      this.isClient &&
      'Notification' in window &&
      'serviceWorker' in navigator &&
      'PushManager' in window
    );
  }

  static get supportsClipboard(): boolean {
    return this.isClient && typeof navigator.clipboard !== 'undefined';
  }

  static get supportsGeolocation(): boolean {
    return this.isClient && typeof navigator.geolocation !== 'undefined';
  }

  static get supportsCamera(): boolean {
    return (
      this.isClient &&
      typeof navigator.mediaDevices !== 'undefined' &&
      typeof navigator.mediaDevices.getUserMedia !== 'undefined'
    );
  }

  // Network detection
  static get isOnline(): boolean {
    return this.isClient ? navigator.onLine : true;
  }

  static get connectionType(): string {
    if (!this.isClient) return 'unknown';

    const connection =
      (navigator as any).connection ||
      (navigator as any).mozConnection ||
      (navigator as any).webkitConnection;

    return connection?.effectiveType || 'unknown';
  }

  // Performance capabilities
  static get deviceMemory(): number {
    if (!this.isClient) return 0;
    return (navigator as any).deviceMemory || 0;
  }

  static get hardwareConcurrency(): number {
    if (!this.isClient) return 1;
    return navigator.hardwareConcurrency || 1;
  }

  // Screen information
  static get screenSize(): { width: number; height: number } {
    if (!this.isClient) return { width: 0, height: 0 };
    return {
      width: screen.width,
      height: screen.height,
    };
  }

  static get viewportSize(): { width: number; height: number } {
    if (!this.isClient) return { width: 0, height: 0 };
    return {
      width: window.innerWidth,
      height: window.innerHeight,
    };
  }

  static get pixelRatio(): number {
    if (!this.isClient) return 1;
    return window.devicePixelRatio || 1;
  }

  // Platform-specific capabilities
  static get authCapabilities(): {
    popup: boolean;
    redirect: boolean;
    deepLink: boolean;
    biometric: boolean;
  } {
    return {
      popup: this.isWeb && !this.isMobileWeb,
      redirect: true,
      deepLink: this.isNativeMobile,
      biometric: this.isNativeMobile,
    };
  }

  static get storageCapabilities(): {
    localStorage: boolean;
    sessionStorage: boolean;
    indexedDB: boolean;
    chromeStorage: boolean;
    secureStorage: boolean;
  } {
    return {
      localStorage: this.supportsLocalStorage,
      sessionStorage: this.supportsSessionStorage,
      indexedDB: this.supportsIndexedDB,
      chromeStorage: this.isExtension,
      secureStorage: this.isNativeMobile,
    };
  }

  static get notificationCapabilities(): {
    push: boolean;
    local: boolean;
    badge: boolean;
  } {
    return {
      push: this.supportsPushNotifications,
      local: this.isClient && 'Notification' in window,
      badge: this.isExtension || this.isNativeMobile,
    };
  }

  // Environment identification
  static get environment(): 'web' | 'extension' | 'mobile-web' | 'mobile-native' | 'server' {
    if (this.isServer) return 'server';
    if (this.isExtension) return 'extension';
    if (this.isNativeMobile) return 'mobile-native';
    if (this.isMobileWeb) return 'mobile-web';
    return 'web';
  }

  // Platform-specific configurations
  static getAuthConfig(): {
    method: 'popup' | 'redirect' | 'deep-link';
    supportsMultipleProviders: boolean;
    requiresUserAgent: boolean;
  } {
    const env = this.environment;

    switch (env) {
      case 'web':
        return {
          method: 'popup',
          supportsMultipleProviders: true,
          requiresUserAgent: false,
        };
      case 'mobile-web':
        return {
          method: 'redirect',
          supportsMultipleProviders: true,
          requiresUserAgent: true,
        };
      case 'extension':
        return {
          method: 'popup',
          supportsMultipleProviders: true,
          requiresUserAgent: false,
        };
      case 'mobile-native':
        return {
          method: 'deep-link',
          supportsMultipleProviders: true,
          requiresUserAgent: false,
        };
      default:
        return {
          method: 'redirect',
          supportsMultipleProviders: false,
          requiresUserAgent: true,
        };
    }
  }

  static getStorageConfig(): {
    primary: 'localStorage' | 'chromeStorage' | 'secureStorage' | 'memory';
    fallback: 'sessionStorage' | 'memory';
    encrypted: boolean;
  } {
    const env = this.environment;

    switch (env) {
      case 'web':
      case 'mobile-web':
        return {
          primary: 'localStorage',
          fallback: 'sessionStorage',
          encrypted: false,
        };
      case 'extension':
        return {
          primary: 'chromeStorage',
          fallback: 'memory',
          encrypted: false,
        };
      case 'mobile-native':
        return {
          primary: 'secureStorage',
          fallback: 'memory',
          encrypted: true,
        };
      default:
        return {
          primary: 'memory',
          fallback: 'memory',
          encrypted: false,
        };
    }
  }

  // Utility methods
  static isMobile(): boolean {
    return this.isMobileWeb || this.isNativeMobile;
  }

  static isDesktop(): boolean {
    return this.isWeb && !this.isMobileWeb;
  }

  static canUsePopups(): boolean {
    return this.isDesktop() || this.isExtension;
  }

  static canUseNotifications(): boolean {
    return this.supportsPushNotifications || this.isExtension || this.isNativeMobile;
  }

  static getOptimalImageSize(): { width: number; height: number } {
    const viewport = this.viewportSize;
    const pixelRatio = this.pixelRatio;

    return {
      width: Math.floor(viewport.width * pixelRatio),
      height: Math.floor(viewport.height * pixelRatio),
    };
  }

  static getRecommendedCacheSize(): number {
    const memory = this.deviceMemory;

    if (memory >= 8) return 100; // MB
    if (memory >= 4) return 50;
    if (memory >= 2) return 25;
    return 10;
  }

  // Debug information
  static getDebugInfo(): Record<string, any> {
    if (this.isServer) {
      return { environment: 'server' };
    }

    return {
      environment: this.environment,
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      screenSize: this.screenSize,
      viewportSize: this.viewportSize,
      pixelRatio: this.pixelRatio,
      deviceMemory: this.deviceMemory,
      hardwareConcurrency: this.hardwareConcurrency,
      connectionType: this.connectionType,
      isOnline: this.isOnline,
      capabilities: {
        auth: this.authCapabilities,
        storage: this.storageCapabilities,
        notifications: this.notificationCapabilities,
      },
    };
  }
}
