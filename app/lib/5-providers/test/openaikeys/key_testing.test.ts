import { fetchDictionary } from '../../openai';
import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import { describe, it, expect } from 'vitest';

const apikeysPath = path.join(__dirname, 'apikeys.txt');
const apikeys = fs
  .readFileSync(apikeysPath, 'utf-8')
  .split('\n')
  .map((line) => line.trim())
  .filter((line) => line && !line.startsWith('#'));

const prompt100 =
  'apple,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla,watermelon,xigua,yuzu,zucchini,apricot,blueberry,cantaloupe,durian,enterprise,feijoa,guava,huckleberry,imbe,jackfruit,kumquat,lime,lychee,mulberry,nance,olive,peach,pear,persimmon,pineapple,plantain,plum,pomegranate,pomelo,qiwi,rambutan,sapodilla,starfruit,sugar,tamarillo,tamarind,tomato,cloud,computer,forest,mountain,ocean,river,sun,moon,star,tree,flower,grass,sky,book,pen,pencil,table,chair,window,door,house,car,bicycle,train,bus,boat,ship,road,street,city,town,village,country,world,earth,space,time,love,hate,happy,sad,good';

describe('APIKEY 批量测试', () => {
  apikeys.forEach((key, idx) => {
    it(`第${idx + 1}个key`, async () => {
      const startTime = Date.now();
      const client = new OpenAI({ apiKey: key });

      const promises = Array(5)
        .fill(null)
        .map(async () => {
          const result = await fetchDictionary('apple', client, 'gpt-4.1-mini');
          const endTime = Date.now();
          // console.log(result);
          console.log(`第${idx + 1}个key:`, result.statusCode, result.error?.message);
          console.log(`耗时: ${((endTime - startTime) / 1000).toFixed(2)} s`);

          const results = await Promise.all(promises);
          const allSuccess = results.every(
            (r) =>
              r.statusCode === 200 ||
              (r.statusCode === 429 && r.error?.message?.includes('Limit 100000, Used 100000'))
          );

          if (allSuccess) {
            const prokeysPath = path.join(__dirname, 'prokeys.txt');
            fs.appendFileSync(prokeysPath, key + '\n');
            console.log(`✅ Key ${key} 已添加到 prokeys.txt`);
          }

          return result;
        });

      await Promise.all(promises);
    });
  });
});
