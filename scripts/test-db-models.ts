#!/usr/bin/env tsx
/**
 * 测试数据库模型是否正确配置
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});

async function testModels() {
  try {
    console.log('🔍 Testing Prisma models...');

    // 测试User模型
    console.log('Testing User model...');
    const userCount = await prisma.user.count();
    console.log('✅ User model works, count:', userCount);

    // 测试PasswordResetToken模型
    console.log('Testing PasswordResetToken model...');
    const tokenCount = await prisma.passwordResetToken.count();
    console.log('✅ PasswordResetToken model works, count:', tokenCount);

    // 测试创建令牌
    console.log('Testing token creation...');
    const testUser = await prisma.user.findFirst();
    if (testUser) {
      const token = await prisma.passwordResetToken.create({
        data: {
          token: 'test-token-' + Date.now(),
          userId: testUser.id,
          expires: new Date(Date.now() + 60000), // 1分钟后过期
        },
      });

      console.log('✅ Token created:', token.id);

      // 清理测试令牌
      await prisma.passwordResetToken.delete({
        where: { id: token.id },
      });

      console.log('✅ Token cleaned up');
    }

    console.log('🎉 All models working correctly!');
  } catch (error) {
    console.error('❌ Model test failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testModels();
