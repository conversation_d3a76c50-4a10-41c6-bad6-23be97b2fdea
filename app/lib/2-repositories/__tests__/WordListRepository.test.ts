/**
 * WordListRepository 单元测试
 */
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { WordListRepository } from '../WordListRepository';
import { prisma } from '@/lib/4-infrastructure/database/prisma';

vi.mock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: {
    wordList: {
      findMany: vi.fn(),
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
      delete: vi.fn(),
      count: vi.fn()
    },
    wordListEntry: {
      findMany: vi.fn(),
      count: vi.fn()
    },
    vocabulary: {
      findMany: vi.fn()
    },
    userWordProficiency: {
      findMany: vi.fn(),
      count: vi.fn()
    },
    $queryRaw: vi.fn()
  }
}));

describe('WordListRepository', () => {
  let repository: WordListRepository;

  beforeEach(() => {
    repository = new WordListRepository();
    vi.clearAllMocks();
  });

  describe('findAll', () => {
    it('应该返回所有激活的词库', async () => {
      const mockWordLists = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: 2,
          name: 'CET-4',
          description: '大学英语四级词汇',
          difficulty: 4,
          totalWords: 2000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordList.findMany as any).mockResolvedValue(mockWordLists);

      const result = await repository.findAll();

      expect(prisma.wordList.findMany).toHaveBeenCalledWith({
        where: {},
        orderBy: [
          { difficulty: 'asc' },
          { name: 'asc' }
        ]
      });
      expect(result).toEqual(mockWordLists);
    });

    it('应该支持自定义过滤条件', async () => {
      const mockWordLists = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordList.findMany as any).mockResolvedValue(mockWordLists);

      const options = {
        difficulty: 2,
        isActive: true
      };

      const result = await repository.findAll(options);

      expect(prisma.wordList.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          difficulty: 2
        },
        orderBy: [
          { difficulty: 'asc' },
          { name: 'asc' }
        ]
      });
      expect(result).toEqual(mockWordLists);
    });
  });

  describe('findById', () => {
    it('应该返回指定ID的词库', async () => {
      const mockWordList = {
        id: 1,
        name: '初中词汇',
        description: '初中英语词汇',
        difficulty: 2,
        totalWords: 1000,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.wordList.findUnique as any).mockResolvedValue(mockWordList);

      const result = await repository.findById(1);

      expect(prisma.wordList.findUnique).toHaveBeenCalledWith({
        where: { id: 1 }
      });
      expect(result).toEqual(mockWordList);
    });

    it('应该在词库不存在时返回null', async () => {
      (prisma.wordList.findUnique as any).mockResolvedValue(null);

      const result = await repository.findById(999);

      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    it('应该成功创建新词库', async () => {
      const createData = {
        name: '测试词库',
        description: '用于测试的词库',
        difficulty: 3,
        totalWords: 500,
        isActive: true
      };

      const mockCreatedWordList = {
        id: 3,
        ...createData,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.wordList.create as any).mockResolvedValue(mockCreatedWordList);

      const result = await repository.create(createData);

      expect(prisma.wordList.create).toHaveBeenCalledWith({
        data: createData
      });
      expect(result).toEqual(mockCreatedWordList);
    });
  });

  describe('update', () => {
    it('应该成功更新词库信息', async () => {
      const updateData = {
        name: '更新后的词库名',
        description: '更新后的描述',
        totalWords: 1500
      };

      const mockUpdatedWordList = {
        id: 1,
        name: '更新后的词库名',
        description: '更新后的描述',
        difficulty: 2,
        totalWords: 1500,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      (prisma.wordList.update as any).mockResolvedValue(mockUpdatedWordList);

      const result = await repository.update(1, updateData);

      expect(prisma.wordList.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateData
      });
      expect(result).toEqual(mockUpdatedWordList);
    });
  });

  describe('delete', () => {
    it('应该成功删除词库', async () => {
      (prisma.wordList.delete as any).mockResolvedValue(undefined);

      await repository.delete(1);

      expect(prisma.wordList.delete).toHaveBeenCalledWith({
        where: { id: 1 }
      });
    });
  });

  describe('getStatsWithUserProgress', () => {
    it('应该返回不带用户进度的词库统计', async () => {
      const mockWordLists = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordList.findMany as any).mockResolvedValue(mockWordLists);

      const result = await repository.getStatsWithUserProgress();

      expect(result).toEqual([
        {
          id: 1,
          name: '初中词汇',
          totalWords: 1000
        }
      ]);
    });

    it('应该返回带用户进度的词库统计', async () => {
      const mockWordLists = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockWordListEntries = [
        { word: 'hello' },
        { word: 'world' },
        { word: 'test' }
      ];

      const mockUserProficiencies = [
        { proficiencyScore: 0.8, vocabulary: { word: 'hello' } },
        { proficiencyScore: 0.6, vocabulary: { word: 'world' } }
      ];

      (prisma.wordList.findMany as any).mockResolvedValue(mockWordLists);
      (prisma.wordListEntry.findMany as any).mockResolvedValue(mockWordListEntries);
      (prisma.userWordProficiency.findMany as any).mockResolvedValue(mockUserProficiencies);

      const result = await repository.getStatsWithUserProgress('user-123');

      expect(result).toEqual([
        {
          id: 1,
          name: '初中词汇',
          totalWords: 1000,
          userPracticedWords: 2,
          userProgress: 67, // Math.round((2/3) * 100)
          averageProficiency: 0.7 // (0.8 + 0.6) / 2
        }
      ]);
    });

    it('应该处理无进度数据的情况', async () => {
      const mockWordLists = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      (prisma.wordList.findMany as any).mockResolvedValue(mockWordLists);
      (prisma.wordListEntry.findMany as any).mockResolvedValue([]);
      (prisma.userWordProficiency.findMany as any).mockResolvedValue([]);

      const result = await repository.getStatsWithUserProgress('user-123');

      expect(result).toEqual([
        {
          id: 1,
          name: '初中词汇',
          totalWords: 1000,
          userPracticedWords: 0,
          userProgress: 0,
          averageProficiency: 0
        }
      ]);
    });

    it('应该正确处理私有方法调用', async () => {
      const mockWordLists = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      // Mock所有可能的Prisma调用
      (prisma.wordList.findMany as any).mockResolvedValue(mockWordLists);
      (prisma.wordListEntry.findMany as any).mockResolvedValue([]);
      (prisma.userWordProficiency.findMany as any).mockResolvedValue([]);

      const result = await repository.getStatsWithUserProgress();

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBe(1);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('name');
      expect(result[0]).toHaveProperty('totalWords');
    });
  });
});