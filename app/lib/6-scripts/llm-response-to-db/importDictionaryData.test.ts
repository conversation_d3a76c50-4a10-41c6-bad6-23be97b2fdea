import { describe, it, expect, beforeAll } from 'vitest';
import path from 'path';
import fs from 'fs/promises';
import { PrismaClient } from '@prisma/client';
import { importFromDictionaryData } from './importDictionaryData.js';

const word1path = path.join(
  __dirname,
  '..',
  '..',
  '5-providers',
  'test',
  'gemini',
  'test_results',
  'words1.json'
);
const word10path = path.join(
  __dirname,
  '..',
  '..',
  '5-providers',
  'test',
  'gemini',
  'test_results',
  'words10.json'
);
const word100path = path.join(
  __dirname,
  '..',
  '..',
  '5-providers',
  'test',
  'gemini',
  'test_results',
  'words100.json'
);

// Initialize Prisma client
const prismaClient = new PrismaClient();

// Function to read dictionary data from file
async function readDictionaryDataFromFile(filePath: string) {
  try {
    const fileContent = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(fileContent);
  } catch (error) {
    console.error(`Error reading dictionary data from ${filePath}:`, error);
    throw error;
  }
}

// Function to get dictionary data from the word paths
async function getDictionaryData(filePath: string) {
  const parsedJson = await readDictionaryDataFromFile(filePath);
  // 确保返回正确的数据格式，包含data字段
  if (parsedJson.data) {
    return parsedJson.data; // 返回data字段
  } else if (parsedJson.words) {
    return parsedJson; // 如果已经是正确格式，直接返回
  } else {
    console.error('JSON文件格式不正确，缺少data或words字段');
    // 创建一个最小化的符合格式要求的对象
    return {
      words: [],
      wordCount: 0,
    };
  }
}

describe('清空数据库', () => {
  it('should clear database', async () => {
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-01-27 19:15:00 +08:00]
    // Reason: [修复数据库清空顺序，避免外键约束违反错误]
    // Principle_Applied: [Data Integrity, Correct Deletion Order]
    // Optimization: [按照外键依赖关系正确排序删除操作]
    // Architectural_Note (AR): [遵循数据库关系层次，从最底层开始删除]
    // Documentation_Note (DW): [修复了测试中的外键约束违反问题]
    // }}
    // {{START MODIFICATIONS}}
    // 按照外键依赖关系的正确顺序删除：
    // 1. Definition (依赖 Explain)
    // 2. Explain (依赖 Vocabulary)
    // 3. WordFormat (可能依赖 Vocabulary 和其他 WordFormat)
    // 4. Vocabulary (最顶层)
    await prismaClient.definition.deleteMany();
    await prismaClient.explain.deleteMany();
    await prismaClient.wordFormat.deleteMany();
    await prismaClient.vocabulary.deleteMany();
    // {{END MODIFICATIONS}}
  });
});

describe('importDictionaryData', () => {
  it('should import dictionary data 1 word', async () => {
    const dictionaryData = await getDictionaryData(word1path);
    const result = await importFromDictionaryData(dictionaryData, prismaClient);
    expect(result).toBeDefined();
    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-01-27 19:20:00 +08:00]
    // Reason: [修正测试期望值，words1.json实际包含3个单词而不是1个]
    // Principle_Applied: [Test Accuracy, Data Validation]
    // Optimization: [根据实际数据内容调整测试断言]
    // Architectural_Note (AR): [确保测试反映真实的数据结构]
    // Documentation_Note (DW): [修正了测试期望值以匹配实际数据]
    // }}
    // {{START MODIFICATIONS}}
    // words1.json实际包含3个单词：["d", "didn't", "i'm"]
    expect(result.successCount).toBe(3);
    // {{END MODIFICATIONS}}
  });

  it('should import dictionary data 10 words', async () => {
    const dictionaryData = await getDictionaryData(word10path);
    const result = await importFromDictionaryData(dictionaryData, prismaClient, {
      forceOverwrite: true,
    });
    expect(result).toBeDefined();
    expect(result.successCount).toBeGreaterThan(0);
  });

  it('should import dictionary data 100 words', async () => {
    const dictionaryData = await getDictionaryData(word100path);
    const result = await importFromDictionaryData(dictionaryData, prismaClient, {
      forceOverwrite: true,
    });
    expect(result).toBeDefined();
    expect(result.successCount).toBeGreaterThan(0);
  });
});
