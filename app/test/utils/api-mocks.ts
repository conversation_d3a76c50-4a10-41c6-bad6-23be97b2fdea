/**
 * @fileoverview API Mock工具集
 *
 * 提供认证系统API端点的完整模拟解决方案，包括：
 * - 认证端点模拟
 * - 用户管理端点模拟
 * - OAuth流程模拟
 * - 错误场景模拟
 * - MSW集成
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import { vi, MockedFunction, expect } from 'vitest';
import { setupServer } from 'msw/node';
import { http, HttpResponse } from 'msw';
import { User } from '@/components/providers/AuthProvider';
import { createMockUser } from './auth-test-utils';

/**
 * API响应类型定义
 */
export interface ApiResponse<T = any> {
  data?: T;
  message?: string;
  error?: string;
  status?: number;
}

export interface LoginResponse {
  accessToken: string;
  refreshToken: string;
  user: User;
  userId: string;
}

export interface SignupResponse extends LoginResponse {}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken: string;
}

/**
 * Mock API响应生成器
 */
export const createApiResponse = <T>(
  data: T,
  status: number = 200,
  message?: string
): ApiResponse<T> => ({
  data,
  status,
  message,
});

export const createApiError = (
  message: string,
  status: number = 400,
  error?: string
): ApiResponse => ({
  message,
  status,
  error: error || 'Error',
});

/**
 * 认证API端点模拟数据
 */
export const authApiMocks = {
  // 用户数据
  users: {
    testUser: createMockUser({
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User',
    }),
    adminUser: createMockUser({
      id: 'admin-1',
      email: '<EMAIL>',
      name: 'Admin User',
      role: 'admin',
    }),
    newUser: createMockUser({
      id: 'user-2',
      email: '<EMAIL>',
      name: 'New User',
    }),
  },

  // 令牌数据
  tokens: {
    accessToken: 'mock-access-token-12345',
    refreshToken: 'mock-refresh-token-67890',
    expiredToken: 'expired-access-token',
  },

  // 错误消息
  errors: {
    invalidCredentials: 'Invalid email or password',
    emailExists: 'Email already exists',
    userNotFound: 'User not found',
    tokenExpired: 'Token has expired',
    tokenInvalid: 'Invalid token',
    networkError: 'Network connection failed',
    serverError: 'Internal server error',
  },
};

/**
 * MSW请求处理器
 */
export const authHandlers = [
  // POST /api/auth/token/login
  http.post('/api/auth/token/login', async ({ request }) => {
    const body = (await request.json()) as { email: string; password: string };

    // 模拟登录逻辑
    if (body.email === '<EMAIL>' && body.password === 'password123') {
      return HttpResponse.json({
        accessToken: authApiMocks.tokens.accessToken,
        refreshToken: authApiMocks.tokens.refreshToken,
        user: authApiMocks.users.testUser,
        userId: authApiMocks.users.testUser.id,
      } as LoginResponse);
    }

    if (body.email === '<EMAIL>' && body.password === 'admin123') {
      return HttpResponse.json({
        accessToken: authApiMocks.tokens.accessToken,
        refreshToken: authApiMocks.tokens.refreshToken,
        user: authApiMocks.users.adminUser,
        userId: authApiMocks.users.adminUser.id,
      } as LoginResponse);
    }

    // 模拟无效凭据
    return HttpResponse.json(
      { message: authApiMocks.errors.invalidCredentials, error: 'Unauthorized' },
      { status: 401 }
    );
  }),

  // POST /api/auth/token/signup
  http.post('/api/auth/token/signup', async ({ request }) => {
    const body = (await request.json()) as {
      email: string;
      password: string;
      name?: string;
    };

    // 模拟邮箱已存在
    if (body.email === '<EMAIL>') {
      return HttpResponse.json(
        { message: authApiMocks.errors.emailExists, error: 'Conflict' },
        { status: 409 }
      );
    }

    // 模拟成功注册
    const newUser = createMockUser({
      id: 'new-user-' + Date.now(),
      email: body.email,
      name: body.name || body.email.split('@')[0],
    });

    return HttpResponse.json(
      {
        accessToken: authApiMocks.tokens.accessToken,
        refreshToken: authApiMocks.tokens.refreshToken,
        user: newUser,
        userId: newUser.id,
      } as SignupResponse,
      { status: 201 }
    );
  }),

  // GET /api/user/profile
  http.get('/api/user/profile', ({ request }) => {
    const authHeader = request.headers.get('authorization');

    // 检查认证头
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(
        { message: 'Authentication required', error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const token = authHeader.replace('Bearer ', '');

    // 模拟过期令牌
    if (token === authApiMocks.tokens.expiredToken) {
      return HttpResponse.json(
        { message: authApiMocks.errors.tokenExpired, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 模拟有效令牌
    if (token === authApiMocks.tokens.accessToken) {
      return HttpResponse.json(authApiMocks.users.testUser);
    }

    // 模拟无效令牌
    return HttpResponse.json(
      { message: authApiMocks.errors.tokenInvalid, error: 'Unauthorized' },
      { status: 401 }
    );
  }),

  // POST /api/auth/token/refresh
  http.post('/api/auth/token/refresh', async ({ request }) => {
    const body = (await request.json()) as { refreshToken: string };

    // 模拟有效刷新令牌
    if (body.refreshToken === authApiMocks.tokens.refreshToken) {
      return HttpResponse.json({
        accessToken: 'new-access-token-' + Date.now(),
        refreshToken: 'new-refresh-token-' + Date.now(),
      } as RefreshTokenResponse);
    }

    // 模拟无效刷新令牌
    return HttpResponse.json(
      { message: authApiMocks.errors.tokenInvalid, error: 'Unauthorized' },
      { status: 401 }
    );
  }),

  // DELETE /api/auth/secure-token
  http.delete('/api/auth/secure-token', () => {
    return HttpResponse.json({ message: 'Logged out successfully' });
  }),

  // OAuth端点模拟
  // GET /api/auth/token/google
  http.get('/api/auth/token/google', () => {
    // OAuth重定向模拟
    return HttpResponse.json({ redirectUrl: '/auth/google/callback' });
  }),

  // GET /api/auth/token/github
  http.get('/api/auth/token/github', () => {
    // OAuth重定向模拟
    return HttpResponse.json({ redirectUrl: '/auth/github/callback' });
  }),
];

/**
 * 测试服务器设置
 */
export const testServer = setupServer(...authHandlers);

/**
 * Fetch API Mock工具
 */
export class FetchMock {
  private static instance: FetchMock;
  private mockResponses: Map<string, any> = new Map();

  static getInstance(): FetchMock {
    if (!FetchMock.instance) {
      FetchMock.instance = new FetchMock();
    }
    return FetchMock.instance;
  }

  /**
   * 设置特定URL的模拟响应
   */
  mockEndpoint(url: string, response: any, options: { status?: number; delay?: number } = {}) {
    const { status = 200, delay = 0 } = options;

    this.mockResponses.set(url, {
      ok: status >= 200 && status < 300,
      status,
      json: vi.fn().mockResolvedValue(response),
      text: vi.fn().mockResolvedValue(JSON.stringify(response)),
      delay,
    });

    return this;
  }

  /**
   * 模拟网络错误
   */
  mockNetworkError(url: string) {
    this.mockResponses.set(url, () => {
      throw new Error('Network error');
    });

    return this;
  }

  /**
   * 应用所有模拟
   */
  apply() {
    const mockFetch = vi.fn().mockImplementation(async (url: string, options?: RequestInit) => {
      const mockResponse = this.mockResponses.get(url);

      if (typeof mockResponse === 'function') {
        return mockResponse();
      }

      if (mockResponse) {
        if (mockResponse.delay > 0) {
          await new Promise((resolve) => setTimeout(resolve, mockResponse.delay));
        }
        return mockResponse;
      }

      // 默认404响应
      return {
        ok: false,
        status: 404,
        json: vi.fn().mockResolvedValue({ message: 'Not found' }),
        text: vi.fn().mockResolvedValue('Not found'),
      };
    });

    global.fetch = mockFetch as any;
    return mockFetch;
  }

  /**
   * 清除所有模拟
   */
  clear() {
    this.mockResponses.clear();
    vi.restoreAllMocks();
  }

  /**
   * 重置实例
   */
  static reset() {
    if (FetchMock.instance) {
      FetchMock.instance.clear();
      FetchMock.instance = null as any;
    }
  }
}

/**
 * 快速API模拟设置
 */
export const quickApiMocks = {
  /**
   * 设置成功登录模拟
   */
  setupSuccessfulLogin: (user: User = authApiMocks.users.testUser) => {
    return FetchMock.getInstance()
      .mockEndpoint('/api/auth/token/login', {
        accessToken: authApiMocks.tokens.accessToken,
        refreshToken: authApiMocks.tokens.refreshToken,
        user,
        userId: user.id,
      })
      .mockEndpoint('/api/user/profile', user)
      .apply();
  },

  /**
   * 设置失败登录模拟
   */
  setupFailedLogin: (errorMessage: string = authApiMocks.errors.invalidCredentials) => {
    return FetchMock.getInstance()
      .mockEndpoint(
        '/api/auth/token/login',
        { message: errorMessage, error: 'Unauthorized' },
        { status: 401 }
      )
      .apply();
  },

  /**
   * 设置成功注册模拟
   */
  setupSuccessfulSignup: (user: User = authApiMocks.users.newUser) => {
    return FetchMock.getInstance()
      .mockEndpoint(
        '/api/auth/token/signup',
        {
          accessToken: authApiMocks.tokens.accessToken,
          refreshToken: authApiMocks.tokens.refreshToken,
          user,
          userId: user.id,
        },
        { status: 201 }
      )
      .mockEndpoint('/api/user/profile', user)
      .apply();
  },

  /**
   * 设置网络错误模拟
   */
  setupNetworkError: () => {
    return FetchMock.getInstance()
      .mockNetworkError('/api/auth/token/login')
      .mockNetworkError('/api/auth/token/signup')
      .mockNetworkError('/api/user/profile')
      .apply();
  },

  /**
   * 清除所有模拟
   */
  cleanup: () => {
    FetchMock.reset();
  },
};

/**
 * 测试工具函数
 */
export const apiTestUtils = {
  /**
   * 等待异步操作完成
   */
  waitForAsync: (ms: number = 0) => new Promise((resolve) => setTimeout(resolve, ms)),

  /**
   * 验证API调用
   */
  expectApiCall: (mockFetch: MockedFunction<any>, url: string, options?: Partial<RequestInit>) => {
    expect(mockFetch).toHaveBeenCalledWith(url, expect.objectContaining(options || {}));
  },

  /**
   * 验证API调用次数
   */
  expectApiCallCount: (mockFetch: MockedFunction<any>, count: number) => {
    expect(mockFetch).toHaveBeenCalledTimes(count);
  },

  /**
   * 验证请求头
   */
  expectRequestHeaders: (mockFetch: MockedFunction<any>, headers: Record<string, string>) => {
    const calls = mockFetch.mock.calls;
    const lastCall = calls[calls.length - 1];
    const requestOptions = lastCall[1] as RequestInit;

    if (requestOptions.headers) {
      Object.entries(headers).forEach(([key, value]) => {
        expect((requestOptions.headers as any)[key]).toBe(value);
      });
    }
  },
};
