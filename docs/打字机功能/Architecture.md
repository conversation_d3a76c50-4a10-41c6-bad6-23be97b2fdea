# 架构文档: lucid-bd 项目 - 打字练习功能 2.0 增强

## 1. 介绍 (Introduction)

本文档为`lucid-bd`项目的“打字练习功能2.0”增强提供技术架构方案。方案基于对现有代码库的分析，并遵循“模块化集成”的核心策略，以确保新功能开发的灵活性和对现有系统的最小化影响。

## 2. 高层集成策略 (High-Level Integration Strategy)

- **核心策略**: **模块化集成 (Loosely Coupled)**。
- **实施方案**: 新的打字练习功能将被构建为一个独立的`PracticeService` (练习服务)模块。该模块将拥有自己的数据模型和API，通过明确定义的接口与现有的用户系统和词库进行交互，以隔离变化并降低耦合度。

## 3. 组件架构 (Component Architecture)

- **新增组件**: `PracticeService` (练习服务)
- **职责**:
  - 管理所有与打字练习相关的业务逻辑。
  - 处理用户与单词/句子之间的关系（熟练度、收藏等）。
  - 计算和存储练习后的统计数据。
  - 为前端提供一套独立的、专门用于练习功能的API (`/api/practice/...`)。

## 4. 数据模型与变更 (Data Models and Schema Changes)

为支持新功能，将新增以下数据表，现有核心表（如User）无需重大修改。

- **`Sentence` (句子表)**
  - **用途**: 存储所有句子的中央“公共句库”。
  - **关键字段**: `id`, `content`, `source`, `favoriteCount`, `createdAt`。
- **`UserSavedSentence` (用户收藏句子表)**
  - **用途**: 建立用户和句子之间的多对多“收藏”关系 (Join Table)。
  - **关键字段**: `userId`, `sentenceId`, `savedAt`。
- **`UserWordProficiency` (用户单词熟练度表)**
  - **用途**: 追踪每个用户对每个单词的掌握情况。
  - **关键字段**: `userId`, `wordId`, `practiceCount`, `errorCount`, `averageTime`, `isMarked`, `proficiencyScore`, `lastPracticed`。
- **`PracticeHistory` (练习历史表)**
  - **用途**: 记录每次练习的总结，用于生成WPM历史图表。
  - **关键字段**: `userId`, `wpm`, `accuracy`, `practiceMode`, `completedAt`。

## 5. 核心API接口设计 (Core API Interface Design)

所有新接口都将以 `/api/practice/` 或资源化的 `/api/word` 为前缀。

- **词汇练习API**:
  - `GET /api/practice/wordlists`: 获取所有可用的词库列表。
  - `GET /api/practice/wordlist/:listId`: 获取特定词库中的一批练习单词（支持分页）。
  - `POST /api/practice/progress`: 更新用户在某个词库中的练习进度。
- **生词本管理API**:
  - `GET /api/word`: 获取用户的生词列表。
  - `POST /api/word`: 批量向生词本添加单词。
  - `DELETE /api/word`: 批量从生词本移除单词。
- **生词练习API**:
  - `GET /api/practice/type-raw-words`: 获取由智能算法排序后的生词列表用于练习。
  - `POST /api/practice/session-result`: **异步**提交练习结果以更新后台熟练度指标。
- **句子练习API**:
  - `GET /api/practice/sentences`: 获取一条练习句子。
  - `POST /api/practice/sentences/favorites`: 收藏一个新句子。
  - `DELETE /api/practice/sentences/favorites/:sentenceId`: 取消收藏一个句子。
- **统计图表API**:
  - `GET /api/practice/stats/wpm-history`: 获取用户历史打字速度数据。

## 6. 风险管理与待办事项 (Risk Management & Action Items)

- **回滚策略**: 在产品正式上线前，主要依赖**Git版本控制**作为回滚手段。
- **功能开关 (建议)**: 建议为“打字机2.0”功能设置一个配置级别的功能开关（如 `FEATURE_TYPER_V2_ENABLED`），以便在紧急情况下快速禁用新功能。
- **[任务 -> 架构师]**: 补充一份**生产环境密钥管理策略**。
- **[任务 -> 架构师]**: 设计一套**外部API的Mock方案方法论**，供未来使用。
- **[待办]**: 在未来集成付费API时，进行**成本与配额分析**。
