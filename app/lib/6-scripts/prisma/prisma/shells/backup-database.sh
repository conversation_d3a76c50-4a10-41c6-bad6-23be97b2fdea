#!/bin/bash
#
# PostgreSQL数据库备份脚本 (Shell版本)
# 支持Docker容器化的PostgreSQL数据库备份
#

set -euo pipefail

# 默认配置
CONTAINER_NAME="lucid-bd-postgres"
DB_USER="postgres"
DB_PASSWORD="postgres"
DB_NAME="dictionary"
BACKUP_DIR="${PWD}/backups"
BACKUP_PREFIX="lucid-bd"
BACKUP_TYPE="full"
BACKUP_FORMAT="custom"
COMPRESS=true
VERBOSE=false

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    cat << EOF
PostgreSQL数据库备份脚本

用法:
    $0 [选项]

选项:
    --type=<type>        备份类型: full|schema|data (默认: full)
    --format=<format>    备份格式: sql|custom|tar (默认: custom)
    --no-compress        禁用压缩
    --output=<path>      指定输出文件路径
    --exclude=<tables>   排除的表名 (逗号分隔)
    --include=<tables>   包含的表名 (逗号分隔)
    --container=<name>   Docker容器名称 (默认: lucid-bd-postgres)
    --db-user=<user>     数据库用户名 (默认: postgres)
    --db-password=<pwd>  数据库密码 (默认: postgres)
    --db-name=<name>     数据库名称 (默认: dictionary)
    --backup-dir=<dir>   备份目录 (默认: ./backups)
    --verbose            详细输出
    --help               显示帮助信息

示例:
    $0
    $0 --type=schema --format=sql
    $0 --exclude=logs,temp --verbose
    $0 --output=/path/to/backup.sql
EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --type=*)
                BACKUP_TYPE="${1#*=}"
                shift
                ;;
            --format=*)
                BACKUP_FORMAT="${1#*=}"
                shift
                ;;
            --no-compress)
                COMPRESS=false
                shift
                ;;
            --output=*)
                OUTPUT_PATH="${1#*=}"
                shift
                ;;
            --exclude=*)
                EXCLUDE_TABLES="${1#*=}"
                shift
                ;;
            --include=*)
                INCLUDE_TABLES="${1#*=}"
                shift
                ;;
            --container=*)
                CONTAINER_NAME="${1#*=}"
                shift
                ;;
            --db-user=*)
                DB_USER="${1#*=}"
                shift
                ;;
            --db-password=*)
                DB_PASSWORD="${1#*=}"
                shift
                ;;
            --db-name=*)
                DB_NAME="${1#*=}"
                shift
                ;;
            --backup-dir=*)
                BACKUP_DIR="${1#*=}"
                shift
                ;;
            --verbose)
                VERBOSE=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                echo "使用 --help 查看可用选项"
                exit 1
                ;;
        esac
    done
}

# 检查Docker是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker服务未运行"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查容器状态
check_container() {
    if ! docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_error "容器 ${CONTAINER_NAME} 不存在"
        exit 1
    fi
    
    if ! docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"; then
        log_info "启动容器 ${CONTAINER_NAME}..."
        if ! docker start "${CONTAINER_NAME}" &> /dev/null; then
            log_error "无法启动容器 ${CONTAINER_NAME}"
            exit 1
        fi
        
        # 等待容器启动
        sleep 3
    fi
    
    log_success "容器 ${CONTAINER_NAME} 运行正常"
}

# 测试数据库连接
test_database_connection() {
    log_info "测试数据库连接..."
    
    if ! docker exec "${CONTAINER_NAME}" env PGPASSWORD="${DB_PASSWORD}" psql -U "${DB_USER}" -d "${DB_NAME}" -c "SELECT version();" &> /dev/null; then
        log_error "数据库连接失败"
        exit 1
    fi
    
    log_success "数据库连接正常"
}

# 创建备份目录
ensure_backup_directory() {
    if [[ ! -d "${BACKUP_DIR}" ]]; then
        log_info "创建备份目录: ${BACKUP_DIR}"
        mkdir -p "${BACKUP_DIR}"
    fi
    
    log_success "备份目录已准备"
}

# 生成备份文件名
generate_filename() {
    local timestamp=$(date +"%Y-%m-%d-%H-%M-%S")
    local extension=""
    
    case "${BACKUP_FORMAT}" in
        sql)
            extension=".sql"
            ;;
        custom)
            extension=".backup"
            ;;
        tar)
            extension=".tar"
            ;;
    esac
    
    if [[ "${COMPRESS}" == true && "${BACKUP_FORMAT}" != "custom" ]]; then
        extension="${extension}.gz"
    fi
    
    echo "${BACKUP_PREFIX}-${BACKUP_TYPE}-${timestamp}${extension}"
}

# 构建pg_dump命令
build_pg_dump_command() {
    local temp_file="$1"
    local cmd="env PGPASSWORD=\"${DB_PASSWORD}\" pg_dump -U \"${DB_USER}\" -d \"${DB_NAME}\""
    
    # 设置格式
    case "${BACKUP_FORMAT}" in
        sql)
            cmd="${cmd} --format=plain"
            ;;
        custom)
            cmd="${cmd} --format=custom"
            ;;
        tar)
            cmd="${cmd} --format=tar"
            ;;
    esac
    
    # 压缩选项
    if [[ "${COMPRESS}" == true ]]; then
        cmd="${cmd} --compress=9"
    fi
    
    # 备份类型
    case "${BACKUP_TYPE}" in
        schema)
            cmd="${cmd} --schema-only"
            ;;
        data)
            cmd="${cmd} --data-only"
            ;;
    esac
    
    # 排除表
    if [[ -n "${EXCLUDE_TABLES:-}" ]]; then
        IFS=',' read -ra TABLES <<< "${EXCLUDE_TABLES}"
        for table in "${TABLES[@]}"; do
            cmd="${cmd} --exclude-table=${table}"
        done
    fi
    
    # 包含表
    if [[ -n "${INCLUDE_TABLES:-}" ]]; then
        IFS=',' read -ra TABLES <<< "${INCLUDE_TABLES}"
        for table in "${TABLES[@]}"; do
            cmd="${cmd} --table=${table}"
        done
    fi
    
    # 输出文件
    cmd="${cmd} --file=/tmp/${temp_file}"
    
    echo "${cmd}"
}

# 执行备份
perform_backup() {
    local filename="$1"
    local output_path="${OUTPUT_PATH:-${BACKUP_DIR}/${filename}}"
    local temp_file="backup-$(date +%s)-${filename}"
    
    log_info "开始备份数据库: ${DB_NAME}"
    log_info "备份类型: ${BACKUP_TYPE}"
    log_info "备份格式: ${BACKUP_FORMAT}"
    log_info "输出文件: ${output_path}"
    
    # 构建并执行pg_dump命令
    local pg_dump_cmd=$(build_pg_dump_command "${temp_file}")
    
    if [[ "${VERBOSE}" == true ]]; then
        log_info "执行命令: ${pg_dump_cmd}"
    fi
    
    local start_time=$(date +%s)
    
    if ! docker exec "${CONTAINER_NAME}" bash -c "${pg_dump_cmd}"; then
        log_error "pg_dump执行失败"
        # 清理临时文件
        docker exec "${CONTAINER_NAME}" rm -f "/tmp/${temp_file}" &> /dev/null || true
        exit 1
    fi
    
    # 从容器复制文件到宿主机
    if ! docker cp "${CONTAINER_NAME}:/tmp/${temp_file}" "${output_path}"; then
        log_error "从容器复制备份文件失败"
        # 清理临时文件
        docker exec "${CONTAINER_NAME}" rm -f "/tmp/${temp_file}" &> /dev/null || true
        exit 1
    fi
    
    # 清理容器内临时文件
    docker exec "${CONTAINER_NAME}" rm -f "/tmp/${temp_file}" &> /dev/null || true
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    # 获取文件大小
    local file_size
    if command -v stat &> /dev/null; then
        if [[ "$(uname)" == "Darwin" ]]; then
            file_size=$(stat -f%z "${output_path}")
        else
            file_size=$(stat -c%s "${output_path}")
        fi
    else
        file_size=$(wc -c < "${output_path}")
    fi
    
    # 格式化文件大小
    local size_human
    if command -v numfmt &> /dev/null; then
        size_human=$(numfmt --to=iec-i --suffix=B "${file_size}")
    else
        size_human="${file_size} bytes"
    fi
    
    log_success "备份完成"
    log_info "文件路径: ${output_path}"
    log_info "文件大小: ${size_human}"
    log_info "耗时: ${duration}秒"
    
    # 计算校验和
    if command -v sha256sum &> /dev/null; then
        local checksum=$(sha256sum "${output_path}" | cut -d' ' -f1)
        log_info "SHA256校验和: ${checksum}"
    elif command -v shasum &> /dev/null; then
        local checksum=$(shasum -a 256 "${output_path}" | cut -d' ' -f1)
        log_info "SHA256校验和: ${checksum}"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    local max_backups=10
    local backup_pattern="${BACKUP_PREFIX}-*"
    
    log_info "清理旧备份文件..."
    
    # 找到所有备份文件并按时间排序
    local backup_files
    if ! backup_files=$(find "${BACKUP_DIR}" -name "${backup_pattern}" -type f -printf '%T@ %p\n' 2>/dev/null | sort -nr | cut -d' ' -f2-); then
        # 如果find不支持-printf，使用ls (less reliable)
        backup_files=$(ls -t "${BACKUP_DIR}"/${backup_pattern} 2>/dev/null || true)
    fi
    
    if [[ -z "${backup_files}" ]]; then
        return
    fi
    
    local count=0
    while IFS= read -r file; do
        count=$((count + 1))
        if [[ ${count} -gt ${max_backups} ]]; then
            log_info "删除旧备份: $(basename "${file}")"
            rm -f "${file}"
        fi
    done <<< "${backup_files}"
}

# 主函数
main() {
    log_info "开始PostgreSQL数据库备份"
    
    # 解析命令行参数
    parse_args "$@"
    
    # 环境检查
    check_docker
    check_container
    test_database_connection
    ensure_backup_directory
    
    # 执行备份
    local filename=$(generate_filename)
    perform_backup "${filename}"
    
    # 清理旧备份
    cleanup_old_backups
    
    log_success "备份流程完成"
}

# 如果脚本被直接执行
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi