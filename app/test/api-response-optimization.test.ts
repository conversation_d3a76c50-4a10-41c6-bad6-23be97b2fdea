/**
 * API响应优化测试
 * 验证API响应结构的精简化和智能词形过滤效果
 */

import { describe, it, expect } from 'vitest';

describe('API响应优化测试', () => {
  const API_BASE_URL = 'http://localhost:3000/api/dictionary/en';

  async function makeRequest(word: string) {
    const response = await fetch(`${API_BASE_URL}/${word}?fresh=true`);
    return response.json();
  }

  it('1. 应该返回精简的API响应结构，移除顶层冗余字段', async () => {
    const result = await makeRequest('word');

    expect(result.success).toBe(true);
    expect(result.data).toBeDefined();

    const topLevelKeys = Object.keys(result.data);
    expect(topLevelKeys).toEqual(expect.arrayContaining(['words', 'wordCount', 'queryMetadata']));
    expect(topLevelKeys).not.toContain('wordList');
    expect(topLevelKeys).not.toContain('wordRelationships');
    expect(topLevelKeys).not.toContain('relatedSuggestions');
  });

  it('2. 单词对象内部应该包含`forms`字段，而非`wordFormats`', async () => {
    const result = await makeRequest('go');

    expect(result.data.words.length).toBeGreaterThan(0);
    const wordObject = result.data.words[0];

    expect(wordObject).toHaveProperty('forms');
    expect(wordObject).not.toHaveProperty('wordFormats'); // 验证旧字段已移除
    expect(Array.isArray(wordObject.forms)).toBe(true);
  });

  it('3. 查询原型词时, `forms` 应该包含所有相关词形', async () => {
    const result = await makeRequest('go');
    const forms = result.data.words[0].forms as Array<{ name: string; form: string }>;

    const formNames = forms.map((f) => f.form);
    expect(formNames).toContain('go');
    expect(formNames).toContain('went');
    expect(formNames).toContain('gone');
    expect(formNames).toContain('goes');
    expect(formNames).toContain('going');
  });

  it('4. 查询派生词时, `forms` 应该只包含当前词形和原型', async () => {
    const result = await makeRequest('went');

    if (result.data.words.length > 0) {
      const forms = result.data.words[0].forms as Array<{ name: string; form: string }>;
      const formNames = forms.map((f) => f.form);

      // 验证包含当前词形
      expect(formNames).toContain('went');

      // 如果有原型，应该包含原型
      const hasBaseForm = forms.some((f) => f.name === '原型');
      if (hasBaseForm) {
        expect(formNames).toContain('go');
      }

      // 验证不包含其他派生形式（如果智能过滤生效）
      // 注意：这个测试可能需要根据实际数据库内容调整
      console.log('Went forms:', forms);
    } else {
      // 如果没找到went，跳过这个测试
      console.log('Word "went" not found in database, skipping derived word test');
    }
  });

  it('5. 对于没有复杂变化的单词，应正确返回', async () => {
    const result = await makeRequest('computer');
    const forms = result.data.words[0].forms as Array<{ name: string; form: string }>;

    expect(forms.length).toBeGreaterThan(0);
    const formNames = forms.map((f) => f.form);
    expect(formNames).toContain('computer');
    expect(formNames).toContain('computers');
  });

  it('6. API响应应该保持高性能', async () => {
    const startTime = Date.now();
    const result = await makeRequest('do');
    const endTime = Date.now();

    const responseTime = endTime - startTime;

    // 验证响应时间合理（应该在合理范围内）
    expect(responseTime).toBeLessThan(5000); // 5秒内

    // 验证查询元数据
    expect(result.data.queryMetadata).toBeDefined();
    expect(result.data.queryMetadata.processingTimeMs).toBeDefined();
    expect(typeof result.data.queryMetadata.processingTimeMs).toBe('number');
  });

  it('7. 不存在的单词应该返回空结果', async () => {
    const result = await makeRequest('nonexistentword');

    expect(result.success).toBe(true);
    expect(result.data.words).toEqual([]);
    expect(result.data.wordCount).toBe(0);
    expect(result.data.queryMetadata.searchStrategy).toBe('not_found');
  });
});
