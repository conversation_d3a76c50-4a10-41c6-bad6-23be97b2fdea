# 数据库备份与恢复系统

为Lucid BD项目提供的完整PostgreSQL数据库备份和恢复解决方案，专为Docker容器化部署环境设计。

## 🚀 快速开始

### 基本备份

```bash
# 完整备份
pnpm run db:backup

# 仅备份结构
pnpm run db:backup:schema

# 仅备份数据
pnpm run db:backup:data
```

### 基本恢复

```bash
# 列出可用备份
pnpm run db:restore:list

# 恢复备份
pnpm run db:restore backups/lucid-bd-full-2024-01-01.backup
```

### 备份管理

```bash
# 查看备份统计
pnpm run db:backup:stats

# 列出所有备份
pnpm run db:backup:list

# 验证备份完整性
pnpm run db:backup:verify

# 清理旧备份
pnpm run db:backup:cleanup
```

## 📋 功能特性

### 🔄 备份功能

- **多种备份类型**: 完整备份、结构备份、数据备份
- **多种格式**: SQL、Custom、TAR格式
- **压缩支持**: 自动压缩备份文件减少存储空间
- **Docker集成**: 无缝对接Docker Compose环境
- **自动清理**: 按时间和数量自动清理旧备份
- **校验和验证**: SHA256校验确保备份完整性

### 🔙 恢复功能

- **灵活恢复**: 支持完整恢复或选择性恢复（仅结构/仅数据）
- **数据库管理**: 自动创建/删除目标数据库
- **格式自动识别**: 自动检测备份文件格式
- **进度监控**: 详细的恢复进度和统计信息

### 🛠️ 管理工具

- **备份列表**: 查看所有可用备份文件
- **统计信息**: 备份数量、大小、分布统计
- **完整性验证**: 验证备份文件是否完整可用
- **智能清理**: 基于时间和数量的清理策略

## 📁 目录结构

```
app/lib/6-scripts/database/prisma/
├── backup-database.ts      # 主备份脚本
├── restore-database.ts     # 恢复脚本
├── backup-manager.ts       # 备份管理工具
├── utils/
│   ├── backup-types.ts     # 类型定义
│   ├── backup-config.ts    # 配置管理
│   └── docker-utils.ts     # Docker工具类
├── shells/
│   ├── backup-database.sh  # Shell版本备份脚本
│   └── restore-database.sh # Shell版本恢复脚本
└── README.md               # 本文档
```

## ⚙️ 配置说明

### 默认配置

```typescript
{
  database: {
    containerName: 'lucid-bd-postgres',
    host: 'localhost',
    port: 5432,
    username: 'postgres',
    password: 'postgres',
    database: 'dictionary',
  },
  backup: {
    outputDir: './backups',
    compression: true,
    format: 'custom',
    filenamePrefix: 'lucid-bd',
    retentionDays: 30,
    maxBackups: 10,
  },
  docker: {
    useDocker: true,
    containerName: 'lucid-bd-postgres',
  }
}
```

### 环境变量支持

可以通过环境变量覆盖默认配置：

```bash
export DB_CONTAINER_NAME="my-postgres"
export DB_PASSWORD="my-password"
export BACKUP_DIR="/path/to/backups"
```

## 🔧 命令详解

### 备份命令

#### 基本备份

```bash
# 完整备份（默认）
tsx backup-database.ts

# 指定备份类型
tsx backup-database.ts --type=full|schema|data

# 指定备份格式
tsx backup-database.ts --format=sql|custom|tar

# 禁用压缩
tsx backup-database.ts --no-compress

# 指定输出路径
tsx backup-database.ts --output=/path/to/backup.sql

# 排除特定表
tsx backup-database.ts --exclude=logs,temp

# 只包含特定表
tsx backup-database.ts --include=vocabulary,explain

# 详细输出
tsx backup-database.ts --verbose
```

#### 使用npm脚本

```bash
# 基本备份命令
pnpm run db:backup                 # 完整备份
pnpm run db:backup:schema          # 结构备份
pnpm run db:backup:data            # 数据备份

# 其他管理命令
pnpm run db:backup:list            # 列出备份文件
pnpm run db:backup:stats           # 备份统计
pnpm run db:backup:verify          # 验证备份
pnpm run db:backup:cleanup         # 清理旧备份
```

### 恢复命令

#### 基本恢复

```bash
# 恢复指定备份文件
tsx restore-database.ts /path/to/backup.sql

# 删除并重建数据库
tsx restore-database.ts backup.sql --drop-database --create-database

# 指定目标数据库
tsx restore-database.ts backup.sql --target=test_db

# 只恢复结构
tsx restore-database.ts backup.sql --skip-data

# 只恢复数据
tsx restore-database.ts backup.sql --skip-schema

# 详细输出
tsx restore-database.ts backup.sql --verbose

# 列出可用备份
tsx restore-database.ts --list
```

#### 使用npm脚本

```bash
# 恢复命令
pnpm run db:restore <备份文件>     # 恢复指定文件
pnpm run db:restore:list           # 列出可用备份
```

### 管理命令

#### 备份管理器

```bash
# 列出所有备份
tsx backup-manager.ts list

# 显示统计信息
tsx backup-manager.ts stats

# 清理旧备份
tsx backup-manager.ts cleanup

# 模拟清理（不实际删除）
tsx backup-manager.ts cleanup --dry-run

# 自定义清理规则
tsx backup-manager.ts cleanup --retention=7 --max-backups=5

# 验证备份完整性
tsx backup-manager.ts verify

# 查看特定备份信息
tsx backup-manager.ts info /path/to/backup.sql
```

## 🐚 Shell脚本版本

除了TypeScript版本，还提供了Shell脚本版本，无需Node.js环境：

### 备份

```bash
# 基本备份
./shells/backup-database.sh

# 自定义配置
./shells/backup-database.sh \
  --type=schema \
  --format=sql \
  --container=my-postgres \
  --db-name=my_database \
  --backup-dir=/backups

# 查看帮助
./shells/backup-database.sh --help
```

### 恢复

```bash
# 基本恢复
./shells/restore-database.sh backup.sql

# 列出备份
./shells/restore-database.sh --list

# 删除重建数据库
./shells/restore-database.sh backup.sql --drop-database --create-database

# 查看帮助
./shells/restore-database.sh --help
```

## 📊 备份文件格式

### 文件命名规则

```
{前缀}-{类型}-{时间戳}.{扩展名}
```

示例：

- `lucid-bd-full-2024-01-01-12-30-45.backup`
- `lucid-bd-schema-2024-01-01-12-30-45.sql.gz`
- `lucid-bd-data-2024-01-01-12-30-45.tar`

### 支持的格式

| 格式   | 扩展名    | 压缩 | 特点                     |
| ------ | --------- | ---- | ------------------------ |
| SQL    | `.sql`    | 可选 | 纯文本，可编辑，兼容性好 |
| Custom | `.backup` | 内置 | 二进制，速度快，体积小   |
| TAR    | `.tar`    | 可选 | 归档格式，支持并行恢复   |

## 🔍 故障排除

### 常见问题

#### 1. Docker连接失败

**错误**: `Docker不可用，请确保Docker已安装并运行`

**解决方案**:

```bash
# 检查Docker状态
docker --version
docker info

# 启动Docker服务
sudo systemctl start docker  # Linux
open -a Docker              # macOS
```

#### 2. 容器未运行

**错误**: `容器 lucid-bd-postgres 不存在`

**解决方案**:

```bash
# 检查容器状态
docker ps -a

# 启动容器
docker start lucid-bd-postgres

# 或启动完整服务
docker compose up -d
```

#### 3. 数据库连接失败

**错误**: `数据库连接失败`

**解决方案**:

```bash
# 检查数据库配置
docker exec lucid-bd-postgres env PGPASSWORD=postgres psql -U postgres -l

# 检查容器日志
docker logs lucid-bd-postgres

# 重启数据库容器
docker restart lucid-bd-postgres
```

#### 4. 备份文件权限问题

**错误**: `复制备份文件失败`

**解决方案**:

```bash
# 确保备份目录有写权限
chmod 755 backups/

# 检查Docker用户权限
docker exec lucid-bd-postgres whoami
```

#### 5. 磁盘空间不足

**错误**: `备份失败，磁盘空间不足`

**解决方案**:

```bash
# 检查磁盘空间
df -h

# 清理旧备份
pnpm run db:backup:cleanup --retention=7

# 使用压缩备份
tsx backup-database.ts --compress
```

### 日志调试

启用详细日志输出：

```bash
# TypeScript版本
tsx backup-database.ts --verbose

# Shell版本
./shells/backup-database.sh --verbose
```

### 性能优化

#### 大数据库备份

```bash
# 使用custom格式（更快）
tsx backup-database.ts --format=custom

# 仅备份结构进行测试
tsx backup-database.ts --type=schema

# 排除大表
tsx backup-database.ts --exclude=logs,audit_log
```

#### 网络环境

```bash
# 本地Docker网络问题
docker network ls
docker network inspect bridge

# 容器间通信检查
docker exec lucid-bd-postgres ping host.docker.internal
```

## 📈 最佳实践

### 1. 备份策略

**推荐的备份频率**:

- **每日完整备份**: 适用于生产环境
- **每周结构备份**: 用于开发环境同步
- **关键操作前备份**: 数据迁移、升级前

**自动化备份**:

```bash
# 添加到crontab
0 2 * * * cd /path/to/project && pnpm run db:backup >> /var/log/backup.log 2>&1
```

### 2. 存储管理

**备份保留策略**:

```bash
# 短期项目（保留7天，最多5个）
tsx backup-manager.ts cleanup --retention=7 --max-backups=5

# 生产环境（保留90天，最多30个）
tsx backup-manager.ts cleanup --retention=90 --max-backups=30
```

**异地备份**:

```bash
# 定期同步到远程存储
rsync -av backups/ user@remote-server:/backups/lucid-bd/
```

### 3. 安全考虑

**敏感数据处理**:

```bash
# 排除敏感表
tsx backup-database.ts --exclude=user_sessions,api_keys

# 加密备份文件
gpg --symmetric --cipher-algo AES256 backup.sql
```

**访问控制**:

```bash
# 限制备份文件权限
chmod 600 backups/*

# 使用专用备份用户
createuser --no-createdb --no-createrole backup_user
```

### 4. 测试恢复

**定期测试**:

```bash
# 创建测试数据库
tsx restore-database.ts backup.sql --target=test_db --create-database

# 验证数据完整性
docker exec lucid-bd-postgres psql -U postgres -d test_db -c "SELECT COUNT(*) FROM vocabulary;"

# 清理测试数据库
docker exec lucid-bd-postgres psql -U postgres -c "DROP DATABASE test_db;"
```

## 🔗 相关链接

- [PostgreSQL 备份文档](https://www.postgresql.org/docs/current/backup.html)
- [Docker Compose 配置](../../../../../../docker-compose.yml)
- [Prisma Schema](../../../../../../prisma/schema.prisma)
- [项目主要文档](../../../../../../CLAUDE.md)

## 📞 技术支持

如遇问题，请检查：

1. **环境要求**: Docker、Node.js 20+、pnpm
2. **容器状态**: `docker ps` 确认PostgreSQL容器运行
3. **权限设置**: 确保备份目录可写
4. **磁盘空间**: 确保有足够存储空间
5. **网络连接**: 确保容器网络正常

**调试步骤**:

1. 使用 `--verbose` 选项获取详细输出
2. 检查Docker容器日志: `docker logs lucid-bd-postgres`
3. 验证数据库连接: `docker exec lucid-bd-postgres psql -U postgres -l`
4. 测试简单备份: `pnpm run db:backup:schema`

---

_最后更新: 2024-01-01_
_版本: v1.0.0_
