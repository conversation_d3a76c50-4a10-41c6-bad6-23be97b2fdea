/**
 * @fileoverview OAuth适配器简化测试套件
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  WebOAuthAdapter,
  ExtensionOAuthAdapter,
  MobileOAuthAdapter,
  isOAuthCallback,
  getOAuthCallbackParams,
  clearOAuthParams,
  OAUTH_PROVIDERS,
} from '../oauth';

// Mock window.open
const mockWindowOpen = vi.fn();

describe('OAuth Adapters - 简化测试', () => {
  beforeEach(() => {
    // Mock global objects
    global.window = {
      open: mockWindowOpen,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      location: {
        origin: 'https://test.example.com',
        href: 'https://test.example.com/app',
        search: '',
      },
      history: {
        replaceState: vi.fn(),
      },
      screenX: 100,
      screenY: 100,
      outerWidth: 1200,
      outerHeight: 800,
      chrome: undefined,
      navigator: { userAgent: '' },
    } as any;

    global.fetch = vi.fn();
    global.URL = URL;
    global.URLSearchParams = URLSearchParams;

    // Reset mocks
    vi.clearAllMocks();
  });

  describe('WebOAuthAdapter', () => {
    let adapter: WebOAuthAdapter;

    beforeEach(() => {
      adapter = new WebOAuthAdapter();
    });

    it('应该正确计算弹窗位置', () => {
      mockWindowOpen.mockReturnValue(null);

      adapter.login('google').catch(() => {}); // 忽略错误

      const expectedLeft = 100 + (1200 - 600) / 2;
      const expectedTop = 100 + (800 - 600) / 2;

      expect(mockWindowOpen).toHaveBeenCalledWith(
        '/api/auth/token/google',
        'oauth-google',
        `width=600,height=600,left=${expectedLeft},top=${expectedTop},scrollbars=yes,resizable=yes`
      );
    });

    it('应该处理弹窗被阻止的情况', async () => {
      mockWindowOpen.mockReturnValue(null);

      await expect(adapter.login('google')).rejects.toThrow('Popup blocked');
    });
  });

  describe('ExtensionOAuthAdapter', () => {
    let adapter: ExtensionOAuthAdapter;

    beforeEach(() => {
      adapter = new ExtensionOAuthAdapter();
    });

    it('应该在非扩展环境中抛出错误', async () => {
      global.window.chrome = undefined;

      await expect(adapter.login('google')).rejects.toThrow(
        'Extension OAuth adapter can only be used in extension environment'
      );
    });
  });

  describe('MobileOAuthAdapter', () => {
    let adapter: MobileOAuthAdapter;

    beforeEach(() => {
      adapter = new MobileOAuthAdapter();
    });

    it('应该在非移动环境中抛出错误', async () => {
      global.window.navigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)';

      // Mock fetch to return a valid response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({ url: 'test-url' }),
      });

      await expect(adapter.login('google')).rejects.toThrow(
        'Native mobile OAuth not implemented in web context'
      );
    });
  });

  describe('OAuth工具函数', () => {
    it('应该检测OAuth回调URL', () => {
      global.window.location.search = '?code=auth_code_123&state=test_state';
      expect(isOAuthCallback()).toBe(true);

      global.window.location.search = '?error=access_denied';
      expect(isOAuthCallback()).toBe(true);

      global.window.location.search = '?foo=bar';
      expect(isOAuthCallback()).toBe(false);
    });

    it('应该返回OAuth回调参数', () => {
      global.window.location.search = '?code=auth_code_123&state=test_state';

      const params = getOAuthCallbackParams();

      expect(params.get('code')).toBe('auth_code_123');
      expect(params.get('state')).toBe('test_state');
    });
  });

  describe('OAuth提供商配置', () => {
    it('应该包含Google配置', () => {
      const googleConfig = OAUTH_PROVIDERS.google;

      expect(googleConfig).toEqual({
        name: 'Google',
        icon: 'google',
        color: '#4285f4',
        scopes: {
          web: ['openid', 'email', 'profile'],
          extension: ['openid', 'email', 'profile'],
          mobile: ['openid', 'email', 'profile'],
        },
      });
    });

    it('应该包含GitHub配置', () => {
      const githubConfig = OAUTH_PROVIDERS.github;

      expect(githubConfig).toEqual({
        name: 'GitHub',
        icon: 'github',
        color: '#333',
        scopes: {
          web: ['user:email'],
          extension: ['user:email'],
          mobile: ['user:email'],
        },
      });
    });
  });
});
