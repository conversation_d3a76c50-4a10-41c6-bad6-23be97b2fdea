/**
 * @fileoverview 组件测试工具集
 *
 * 提供React组件测试的完整工具链，包括：
 * - 多Provider包装器
 * - 表单测试工具
 * - 用户交互模拟
 * - 异步组件测试
 * - 路由测试工具
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import React, { ReactElement, ReactNode } from 'react';
import { render, RenderOptions, RenderResult, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, expect, test } from 'vitest';
import { MockAuthProvider, MockAuthContextType, createMockAuthContext } from './auth-test-utils';

/**
 * Theme Provider Mock
 */
const MockThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  return <div data-testid="mock-theme-provider">{children}</div>;
};

/**
 * Toast Provider Mock
 */
const MockToastProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  return (
    <div data-testid="mock-toast-provider">
      {children}
      <div data-testid="toast-container" />
    </div>
  );
};

/**
 * 完整的应用Provider包装器
 */
interface AllProvidersProps {
  children: ReactNode;
  authValue?: MockAuthContextType;
  themeValue?: any;
}

export const AllProviders: React.FC<AllProvidersProps> = ({
  children,
  authValue = {},
  themeValue = {},
}) => {
  return (
    <MockThemeProvider {...themeValue}>
      <MockToastProvider>
        <MockAuthProvider value={authValue}>{children}</MockAuthProvider>
      </MockToastProvider>
    </MockThemeProvider>
  );
};

/**
 * 扩展的渲染选项
 */
interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  authValue?: MockAuthContextType;
  themeValue?: any;
  wrapper?: React.ComponentType<{ children: ReactNode }>;
}

/**
 * 带完整Provider的组件渲染工具
 */
export const renderWithProviders = (
  ui: ReactElement,
  options: ExtendedRenderOptions = {}
): RenderResult & {
  user: ReturnType<typeof userEvent.setup>;
  authContext: any;
} => {
  const { authValue = {}, themeValue = {}, wrapper: Wrapper, ...renderOptions } = options;

  const mockAuthContext = createMockAuthContext(authValue);

  // Create wrapper component with all providers
  const ComponentWrapper: React.FC<{ children: ReactNode }> = ({ children }) => {
    const content = (
      <AllProviders authValue={authValue} themeValue={themeValue}>
        {children}
      </AllProviders>
    );

    return Wrapper ? <Wrapper>{content}</Wrapper> : content;
  };

  const user = userEvent.setup();

  const result = render(ui, {
    wrapper: ComponentWrapper,
    ...renderOptions,
  });

  return {
    ...result,
    user,
    authContext: mockAuthContext,
  };
};

/**
 * 表单测试工具
 */
export const formTestUtils = {
  /**
   * 填写登录表单
   */
  fillLoginForm: async (
    user: ReturnType<typeof userEvent.setup>,
    credentials: {
      email: string;
      password: string;
    }
  ) => {
    const emailInput = screen.getByLabelText(/email/i) || screen.getByPlaceholderText(/email/i);
    const passwordInput =
      screen.getByLabelText(/password/i) || screen.getByPlaceholderText(/password/i);

    await user.clear(emailInput);
    await user.type(emailInput, credentials.email);

    await user.clear(passwordInput);
    await user.type(passwordInput, credentials.password);
  },

  /**
   * 填写注册表单
   */
  fillSignupForm: async (
    user: ReturnType<typeof userEvent.setup>,
    credentials: {
      email: string;
      password: string;
      confirmPassword: string;
      name?: string;
    }
  ) => {
    const emailInput = screen.getByLabelText(/email/i) || screen.getByPlaceholderText(/email/i);
    const passwordInput =
      screen.getByLabelText(/^password/i) || screen.getByPlaceholderText(/^password/i);
    const confirmPasswordInput =
      screen.getByLabelText(/confirm.*password/i) ||
      screen.getByPlaceholderText(/confirm.*password/i);

    await user.clear(emailInput);
    await user.type(emailInput, credentials.email);

    await user.clear(passwordInput);
    await user.type(passwordInput, credentials.password);

    await user.clear(confirmPasswordInput);
    await user.type(confirmPasswordInput, credentials.confirmPassword);

    if (credentials.name) {
      const nameInput = screen.getByLabelText(/name/i) || screen.getByPlaceholderText(/name/i);
      await user.clear(nameInput);
      await user.type(nameInput, credentials.name);
    }
  },

  /**
   * 提交表单
   */
  submitForm: async (user: ReturnType<typeof userEvent.setup>, submitText: string = 'submit') => {
    const submitButton = screen.getByRole('button', { name: submitText });
    await user.click(submitButton);
  },

  /**
   * 验证表单错误
   */
  expectFormError: (errorMessage: string | RegExp) => {
    expect(screen.getByText(errorMessage)).toBeInTheDocument();
  },

  /**
   * 验证表单字段值
   */
  expectFieldValue: (fieldName: string | RegExp, value: string) => {
    const field = screen.getByLabelText(fieldName) || screen.getByPlaceholderText(fieldName);
    expect(field).toHaveValue(value);
  },

  /**
   * 验证表单字段必填
   */
  expectFieldRequired: (fieldName: string | RegExp) => {
    const field = screen.getByLabelText(fieldName) || screen.getByPlaceholderText(fieldName);
    expect(field).toBeRequired();
  },
};

/**
 * 异步组件测试工具
 */
export const asyncTestUtils = {
  /**
   * 等待组件加载完成
   */
  waitForComponentLoad: async (testId?: string) => {
    if (testId) {
      await waitFor(() => {
        expect(screen.getByTestId(testId)).toBeInTheDocument();
      });
    } else {
      await waitFor(() => {
        expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
      });
    }
  },

  /**
   * 等待错误消息出现
   */
  waitForError: async (errorMessage: string | RegExp) => {
    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  },

  /**
   * 等待成功消息出现
   */
  waitForSuccess: async (successMessage: string | RegExp) => {
    await waitFor(() => {
      expect(screen.getByText(successMessage)).toBeInTheDocument();
    });
  },

  /**
   * 等待路由导航
   */
  waitForNavigation: async (path: string) => {
    await waitFor(() => {
      expect(window.location.pathname).toBe(path);
    });
  },

  /**
   * 等待API调用完成
   */
  waitForApiCall: async (mockFetch: any, timeout: number = 5000) => {
    await waitFor(
      () => {
        expect(mockFetch).toHaveBeenCalled();
      },
      { timeout }
    );
  },
};

/**
 * 用户交互模拟工具
 */
export const interactionUtils = {
  /**
   * 模拟键盘导航
   */
  navigateWithKeyboard: async (
    user: ReturnType<typeof userEvent.setup>,
    key: string,
    times: number = 1
  ) => {
    for (let i = 0; i < times; i++) {
      await user.keyboard(`{${key}}`);
    }
  },

  /**
   * 模拟鼠标悬停
   */
  hoverElement: async (user: ReturnType<typeof userEvent.setup>, element: HTMLElement) => {
    await user.hover(element);
  },

  /**
   * 模拟焦点事件
   */
  focusElement: async (user: ReturnType<typeof userEvent.setup>, element: HTMLElement) => {
    await user.click(element);
    expect(element).toHaveFocus();
  },

  /**
   * 模拟文件上传
   */
  uploadFile: async (user: ReturnType<typeof userEvent.setup>, input: HTMLElement, file: File) => {
    await user.upload(input, file);
  },

  /**
   * 模拟剪贴板操作
   */
  copyToClipboard: async (user: ReturnType<typeof userEvent.setup>, text: string) => {
    await (user as any).clipboard.copy(text);
  },
};

/**
 * 组件状态验证工具
 */
export const stateVerificationUtils = {
  /**
   * 验证组件可见性
   */
  expectVisible: (element: HTMLElement | null) => {
    expect(element).toBeInTheDocument();
    expect(element).toBeVisible();
  },

  /**
   * 验证组件隐藏
   */
  expectHidden: (testId: string) => {
    const element = screen.queryByTestId(testId);
    expect(element).not.toBeInTheDocument();
  },

  /**
   * 验证按钮状态
   */
  expectButtonEnabled: (buttonText: string | RegExp) => {
    const button = screen.getByRole('button', { name: buttonText });
    expect(button).toBeEnabled();
  },

  expectButtonDisabled: (buttonText: string | RegExp) => {
    const button = screen.getByRole('button', { name: buttonText });
    expect(button).toBeDisabled();
  },

  /**
   * 验证加载状态
   */
  expectLoadingState: () => {
    expect(
      screen.getByText(/loading/i) || screen.getByTestId('loading-spinner')
    ).toBeInTheDocument();
  },

  expectNoLoadingState: () => {
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
    expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
  },

  /**
   * 验证模态框状态
   */
  expectModalOpen: (modalTitle?: string | RegExp) => {
    if (modalTitle) {
      expect(screen.getByRole('dialog', { name: modalTitle })).toBeInTheDocument();
    } else {
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    }
  },

  expectModalClosed: () => {
    expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
  },
};

/**
 * 可访问性测试工具
 */
export const a11yTestUtils = {
  /**
   * 验证ARIA标签
   */
  expectAriaLabel: (element: HTMLElement, expectedLabel: string) => {
    expect(element).toHaveAttribute('aria-label', expectedLabel);
  },

  /**
   * 验证角色属性
   */
  expectRole: (element: HTMLElement, expectedRole: string) => {
    expect(element).toHaveAttribute('role', expectedRole);
  },

  /**
   * 验证焦点管理
   */
  expectFocusable: (element: HTMLElement) => {
    expect(element).toHaveAttribute('tabindex');
  },

  /**
   * 验证语义化HTML
   */
  expectSemanticStructure: () => {
    expect(
      screen.getByRole('main') || screen.getByRole('banner') || screen.getByRole('navigation')
    ).toBeInTheDocument();
  },

  /**
   * 验证键盘导航
   */
  testKeyboardNavigation: async (user: ReturnType<typeof userEvent.setup>) => {
    // Tab键导航测试
    await user.keyboard('{Tab}');
    const focusedElement = document.activeElement;
    expect(focusedElement).not.toBe(document.body);

    // Enter键激活测试
    if (
      focusedElement &&
      (focusedElement.tagName === 'BUTTON' || focusedElement.getAttribute('role') === 'button')
    ) {
      await user.keyboard('{Enter}');
    }
  },
};

/**
 * 快速测试模板
 */
export const testTemplates = {
  /**
   * 基础组件渲染测试
   */
  basicRenderTest: (Component: React.ComponentType, props: any = {}) => {
    test('renders without crashing', () => {
      const { container } = renderWithProviders(<Component {...props} />);
      expect(container.firstChild).toBeInTheDocument();
    });
  },

  /**
   * 认证状态测试模板
   */
  authStateTest: (
    Component: React.ComponentType,
    scenarios: Array<{
      name: string;
      authValue: MockAuthContextType;
      expectedBehavior: string;
      test: (result: RenderResult) => void;
    }>
  ) => {
    scenarios.forEach((scenario) => {
      test(`${scenario.name} - ${scenario.expectedBehavior}`, () => {
        const result = renderWithProviders(<Component />, { authValue: scenario.authValue });
        scenario.test(result);
      });
    });
  },

  /**
   * 表单提交测试模板
   */
  formSubmissionTest: (Component: React.ComponentType, formData: any, expectedOutcome: string) => {
    test(`form submission - ${expectedOutcome}`, async () => {
      const { user } = renderWithProviders(<Component />);

      // 填写表单
      if (formData.email && formData.password) {
        await formTestUtils.fillLoginForm(user, formData);
      }

      // 提交表单
      await formTestUtils.submitForm(user);

      // 验证结果 - 这里需要根据具体组件调整
    });
  },
};

/**
 * 性能测试工具
 */
export const performanceTestUtils = {
  /**
   * 测量渲染时间
   */
  measureRenderTime: async (Component: React.ComponentType, props: any = {}) => {
    const startTime = performance.now();
    const { unmount } = renderWithProviders(<Component {...props} />);
    const endTime = performance.now();
    unmount();

    return endTime - startTime;
  },

  /**
   * 测试组件重新渲染
   */
  testReRender: (Component: React.ComponentType, initialProps: any, updatedProps: any) => {
    const { rerender } = renderWithProviders(<Component {...initialProps} />);

    // 重新渲染
    rerender(<Component {...updatedProps} />);

    // 验证更新后的状态
    expect(screen.getByTestId('component-container')).toBeInTheDocument();
  },
};
