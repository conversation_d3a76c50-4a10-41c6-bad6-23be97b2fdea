/**
 * 词库API集成测试
 */
import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import { GET as getWordLists } from '@/app/api/practice/wordlists/route';
import { GET as getWordListWords } from '@/app/api/practice/wordlist/[listId]/route';
import { POST as recordProgress } from '@/app/api/practice/progress/route';
import { prisma } from '@/lib/4-infrastructure/database/prisma';

describe('词库练习API集成测试', () => {
  let testWordList: any;
  let testWords: string[] = [];

  beforeAll(async () => {
    // 清理测试数据
    await cleanupTestData();

    // 创建测试词库
    testWordList = await prisma.wordList.create({
      data: {
        name: '测试词库',
        description: '用于集成测试的词库',
        difficulty: 3,
        totalWords: 0,
        isActive: true
      }
    });

    // 创建测试词汇
    const testVocabularies = [
      { word: 'integration', phonetics: ['/ˌɪntɪˈɡreɪʃn/'], freqRank: 1000 },
      { word: 'testing', phonetics: ['/ˈtestɪŋ/'], freqRank: 500 },
      { word: 'vocabulary', phonetics: ['/vəˈkæbjʊləri/'], freqRank: 800 }
    ];

    for (const vocab of testVocabularies) {
      const vocabulary = await prisma.vocabulary.create({
        data: vocab
      });

      await prisma.wordListEntry.create({
        data: {
          word: vocab.word,
          wordListIds: [testWordList.id],
          wordListId: testWordList.id
        }
      });

      testWords.push(vocab.word);
    }

    // 更新词库总词数
    await prisma.wordList.update({
      where: { id: testWordList.id },
      data: { totalWords: testVocabularies.length }
    });
  });

  afterAll(async () => {
    await cleanupTestData();
  });

  async function cleanupTestData() {
    // 清理测试数据
    await prisma.userWordProficiency.deleteMany({
      where: { userId: 'temp-user-id' }
    });
    await prisma.userWordStat.deleteMany({
      where: { userId: 'temp-user-id' }
    });
    await prisma.wordListEntry.deleteMany({
      where: { word: { in: ['integration', 'testing', 'vocabulary'] } }
    });
    await prisma.vocabulary.deleteMany({
      where: { word: { in: ['integration', 'testing', 'vocabulary'] } }
    });
    await prisma.wordList.deleteMany({
      where: { name: '测试词库' }
    });
  }

  describe('GET /api/practice/wordlists', () => {
    it('应该返回词库列表', async () => {
      const request = new Request('http://localhost:3000/api/practice/wordlists');
      const response = await getWordLists(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.wordLists).toBeInstanceOf(Array);
      expect(data.data.total).toBeGreaterThan(0);

      // 检查测试词库是否在列表中
      const testWordListInResponse = data.data.wordLists.find(
        (wl: any) => wl.name === '测试词库'
      );
      expect(testWordListInResponse).toBeDefined();
      expect(testWordListInResponse.difficulty).toBe(3);
      expect(testWordListInResponse.totalWords).toBe(3);
    });

    it('应该支持查询参数过滤', async () => {
      const request = new Request('http://localhost:3000/api/practice/wordlists?isActive=true');
      const response = await getWordLists(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.wordLists).toBeInstanceOf(Array);
      // 所有返回的词库都应该是激活状态
      data.data.wordLists.forEach((wl: any) => {
        expect(wl.isActive).toBe(true);
      });
    });

    it('应该处理无效查询参数', async () => {
      const request = new Request('http://localhost:3000/api/practice/wordlists?difficulty=invalid');
      const response = await getWordLists(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid query parameters');
    });
  });

  describe('GET /api/practice/wordlist/[listId]', () => {
    it('应该返回指定词库的单词', async () => {
      const request = new Request(`http://localhost:3000/api/practice/wordlist/${testWordList.id}`);
      const response = await getWordListWords(
        request,
        { params: { listId: testWordList.id.toString() } }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.wordList.id).toBe(testWordList.id);
      expect(data.data.wordList.name).toBe('测试词库');
      expect(data.data.words).toBeInstanceOf(Array);
      expect(data.data.words.length).toBe(3);

      // 检查单词数据结构
      const firstWord = data.data.words[0];
      expect(firstWord).toHaveProperty('id');
      expect(firstWord).toHaveProperty('word');
      expect(firstWord).toHaveProperty('phonetics');
      expect(firstWord).toHaveProperty('freqRank');
      expect(firstWord).toHaveProperty('userProficiency');
    });

    it('应该支持分页查询', async () => {
      const request = new Request(
        `http://localhost:3000/api/practice/wordlist/${testWordList.id}?page=1&limit=2`
      );
      const response = await getWordListWords(
        request,
        { params: { listId: testWordList.id.toString() } }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.words.length).toBe(2);
      expect(data.data.pagination.currentPage).toBe(1);
      expect(data.data.pagination.pageSize).toBe(2);
      expect(data.data.pagination.totalWords).toBe(3);
      expect(data.data.pagination.totalPages).toBe(2);
      expect(data.data.pagination.hasNextPage).toBe(true);
      expect(data.data.pagination.hasPreviousPage).toBe(false);
    });

    it('应该支持随机排序', async () => {
      const request = new Request(
        `http://localhost:3000/api/practice/wordlist/${testWordList.id}?random=true`
      );
      const response = await getWordListWords(
        request,
        { params: { listId: testWordList.id.toString() } }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.config.isRandomized).toBe(true);
      expect(data.data.words).toBeInstanceOf(Array);
    });

    it('应该支持单词搜索', async () => {
      const request = new Request(
        `http://localhost:3000/api/practice/wordlist/${testWordList.id}?word=test`
      );
      const response = await getWordListWords(
        request,
        { params: { listId: testWordList.id.toString() } }
      );
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      // 应该只返回包含"test"的单词
      data.data.words.forEach((word: any) => {
        expect(word.word.toLowerCase()).toContain('test');
      });
    });

    it('应该在词库不存在时返回404', async () => {
      const request = new Request('http://localhost:3000/api/practice/wordlist/99999');
      const response = await getWordListWords(
        request,
        { params: { listId: '99999' } }
      );
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Word list not found');
    });

    it('应该处理无效的listId参数', async () => {
      const request = new Request('http://localhost:3000/api/practice/wordlist/invalid');
      const response = await getWordListWords(
        request,
        { params: { listId: 'invalid' } }
      );
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid parameters');
    });
  });

  describe('POST /api/practice/progress', () => {
    it('应该成功记录正确答案的练习进度', async () => {
      const requestBody = {
        wordListId: testWordList.id,
        word: 'testing',
        practiceResult: {
          isCorrect: true,
          timeTaken: 2000,
          errorCount: 0,
          sessionType: 'typing'
        },
        addToWordBook: false
      };

      const request = new Request('http://localhost:3000/api/practice/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const response = await recordProgress(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.word).toBe('testing');
      expect(data.data.proficiency.practiceCount).toBe(1);
      expect(data.data.proficiency.errorCount).toBe(0);
      expect(data.data.proficiency.averageTime).toBe(2000);
      expect(data.data.proficiency.proficiencyScore).toBeGreaterThan(0);
      expect(data.data.practiceResult.improvement).toBe(0); // 第一次练习无改进度
      expect(data.data.practiceResult.accuracy).toBe(1);
    });

    it('应该成功记录错误答案的练习进度', async () => {
      const requestBody = {
        wordListId: testWordList.id,
        word: 'vocabulary',
        practiceResult: {
          isCorrect: false,
          timeTaken: 5000,
          errorCount: 2,
          sessionType: 'typing'
        },
        addToWordBook: true
      };

      const request = new Request('http://localhost:3000/api/practice/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const response = await recordProgress(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.word).toBe('vocabulary');
      expect(data.data.proficiency.practiceCount).toBe(1);
      expect(data.data.proficiency.errorCount).toBe(2);
      expect(data.data.proficiency.averageTime).toBe(5000);
      expect(data.data.proficiency.isMarked).toBe(true); // 加入生词本
      expect(data.data.practiceResult.accuracy).toBeLessThan(1);
    });

    it('应该更新已存在的练习记录', async () => {
      // 第二次练习同一个单词
      const requestBody = {
        wordListId: testWordList.id,
        word: 'testing',
        practiceResult: {
          isCorrect: true,
          timeTaken: 1500,
          errorCount: 0,
          sessionType: 'typing'
        },
        addToWordBook: false
      };

      const request = new Request('http://localhost:3000/api/practice/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const response = await recordProgress(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.proficiency.practiceCount).toBe(2); // 练习次数增加
      expect(data.data.proficiency.averageTime).toBeLessThan(2000); // 平均时间应该更新
      expect(data.data.practiceResult.improvement).toBeGreaterThan(0); // 应该有改进
    });

    it('应该处理无效的请求数据', async () => {
      const invalidRequestBody = {
        wordListId: 'invalid', // 无效的ID
        word: '',
        practiceResult: {
          isCorrect: 'true', // 应该是布尔值
          timeTaken: -100 // 负数
        }
      };

      const request = new Request('http://localhost:3000/api/practice/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidRequestBody)
      });

      const response = await recordProgress(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request data');
      expect(data.details).toBeInstanceOf(Array);
    });

    it('应该处理不存在的单词', async () => {
      const requestBody = {
        wordListId: testWordList.id,
        word: 'nonexistent',
        practiceResult: {
          isCorrect: true,
          timeTaken: 2000,
          errorCount: 0,
          sessionType: 'typing'
        }
      };

      const request = new Request('http://localhost:3000/api/practice/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const response = await recordProgress(request);
      const data = await response.json();

      expect(response.status).toBe(200); // 应该成功创建新的词汇记录
      expect(data.success).toBe(true);
      expect(data.data.word).toBe('nonexistent');
    });
  });

  describe('完整工作流测试', () => {
    it('应该支持完整的词库练习工作流', async () => {
      // 1. 获取词库列表
      const wordListsRequest = new Request('http://localhost:3000/api/practice/wordlists');
      const wordListsResponse = await getWordLists(wordListsRequest);
      const wordListsData = await wordListsResponse.json();

      expect(wordListsResponse.status).toBe(200);
      expect(wordListsData.data.wordLists.length).toBeGreaterThan(0);

      // 2. 获取特定词库的单词
      const wordsRequest = new Request(
        `http://localhost:3000/api/practice/wordlist/${testWordList.id}?limit=1`
      );
      const wordsResponse = await getWordListWords(
        wordsRequest,
        { params: { listId: testWordList.id.toString() } }
      );
      const wordsData = await wordsResponse.json();

      expect(wordsResponse.status).toBe(200);
      expect(wordsData.data.words.length).toBe(1);

      const practiceWord = wordsData.data.words[0];

      // 3. 记录练习进度
      const progressRequest = new Request('http://localhost:3000/api/practice/progress', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          wordListId: testWordList.id,
          word: practiceWord.word,
          practiceResult: {
            isCorrect: true,
            timeTaken: 1800,
            errorCount: 0,
            sessionType: 'typing'
          },
          addToWordBook: false
        })
      });

      const progressResponse = await recordProgress(progressRequest);
      const progressData = await progressResponse.json();

      expect(progressResponse.status).toBe(200);
      expect(progressData.success).toBe(true);
      expect(progressData.data.word).toBe(practiceWord.word);

      // 4. 再次获取同一单词，验证用户进度已更新
      const updatedWordsRequest = new Request(
        `http://localhost:3000/api/practice/wordlist/${testWordList.id}?word=${practiceWord.word}`
      );
      const updatedWordsResponse = await getWordListWords(
        updatedWordsRequest,
        { params: { listId: testWordList.id.toString() } }
      );
      const updatedWordsData = await updatedWordsResponse.json();

      expect(updatedWordsResponse.status).toBe(200);
      expect(updatedWordsData.data.words.length).toBe(1);
      
      const updatedWord = updatedWordsData.data.words[0];
      expect(updatedWord.userProficiency).not.toBeNull();
      expect(updatedWord.userProficiency.practiceCount).toBeGreaterThan(0);
    });
  });
});