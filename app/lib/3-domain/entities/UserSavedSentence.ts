/**
 * UserSavedSentence Domain Entity
 * 
 * Represents the many-to-many relationship between users and their saved sentences.
 * Contains practice statistics and user-specific sentence data.
 */

export interface UserSavedSentence {
  id: string;
  userId: string;
  sentenceId: number;
  savedAt: Date;
  practiceCount: number;
  lastPracticedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateUserSavedSentenceInput {
  userId: string;
  sentenceId: number;
  practiceContext?: string; // Optional context about why the sentence was saved
}

export interface UpdateUserSavedSentenceInput {
  practiceCount?: number;
  lastPracticedAt?: Date;
}

export interface UserSavedSentenceWithSentence extends UserSavedSentence {
  sentence: {
    id: number;
    content: string;
    source?: string;
    difficulty: number;
    category?: string;
    length: number;
  };
}

export interface UserSentencePracticeStats {
  totalSavedSentences: number;
  totalPracticeCount: number;
  averagePracticeCount: number;
  mostPracticedSentences: UserSavedSentenceWithSentence[];
  recentlyPracticedSentences: UserSavedSentenceWithSentence[];
  difficultySummary: {
    [difficulty: number]: {
      count: number;
      totalPractices: number;
    };
  };
}

/**
 * Practice frequency categories
 */
export enum PracticeFrequency {
  NEVER_PRACTICED = 'never_practiced',    // practiceCount = 0
  RARELY_PRACTICED = 'rarely_practiced',  // practiceCount 1-2
  SOMETIMES_PRACTICED = 'sometimes_practiced', // practiceCount 3-5
  OFTEN_PRACTICED = 'often_practiced',    // practiceCount 6-10
  FREQUENTLY_PRACTICED = 'frequently_practiced' // practiceCount > 10
}

/**
 * Business logic methods for UserSavedSentence entity
 */
export class UserSavedSentenceEntity {
  /**
   * Determine practice frequency based on practice count
   */
  static getPracticeFrequency(practiceCount: number): PracticeFrequency {
    if (practiceCount === 0) return PracticeFrequency.NEVER_PRACTICED;
    if (practiceCount <= 2) return PracticeFrequency.RARELY_PRACTICED;
    if (practiceCount <= 5) return PracticeFrequency.SOMETIMES_PRACTICED;
    if (practiceCount <= 10) return PracticeFrequency.OFTEN_PRACTICED;
    return PracticeFrequency.FREQUENTLY_PRACTICED;
  }

  /**
   * Calculate practice score based on frequency and recency
   */
  static calculatePracticeScore(
    practiceCount: number,
    lastPracticedAt?: Date,
    savedAt?: Date
  ): number {
    let score = 0;
    
    // Base score from practice count (0-50 points)
    score += Math.min(50, practiceCount * 5);
    
    // Recency bonus (0-30 points)
    if (lastPracticedAt) {
      const daysSinceLastPractice = Math.floor(
        (Date.now() - lastPracticedAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (daysSinceLastPractice <= 1) score += 30;
      else if (daysSinceLastPractice <= 7) score += 20;
      else if (daysSinceLastPractice <= 30) score += 10;
    }
    
    // Engagement bonus (0-20 points)
    if (savedAt) {
      const daysSinceSaved = Math.floor(
        (Date.now() - savedAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      if (practiceCount > 0 && daysSinceSaved > 0) {
        const practiceRate = practiceCount / daysSinceSaved;
        score += Math.min(20, practiceRate * 10);
      }
    }
    
    return Math.min(100, score);
  }

  /**
   * Determine if a sentence needs more practice
   */
  static needsMorePractice(
    practiceCount: number,
    lastPracticedAt?: Date,
    difficulty?: number
  ): boolean {
    const targetPracticeCount = Math.max(3, (difficulty || 5) - 2);
    
    if (practiceCount < targetPracticeCount) {
      return true;
    }
    
    // If last practiced more than a week ago, suggest more practice
    if (lastPracticedAt) {
      const daysSinceLastPractice = Math.floor(
        (Date.now() - lastPracticedAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      
      return daysSinceLastPractice > 7;
    }
    
    return false;
  }

  /**
   * Get practice recommendation based on user's sentence stats
   */
  static getPracticeRecommendation(
    userSavedSentence: UserSavedSentence,
    sentenceDifficulty?: number
  ): {
    priority: 'high' | 'medium' | 'low';
    reason: string;
    suggestedAction: string;
  } {
    const { practiceCount, lastPracticedAt, savedAt } = userSavedSentence;
    const frequency = this.getPracticeFrequency(practiceCount);
    const needsPractice = this.needsMorePractice(practiceCount, lastPracticedAt, sentenceDifficulty);
    
    if (frequency === PracticeFrequency.NEVER_PRACTICED) {
      return {
        priority: 'high',
        reason: 'You saved this sentence but haven\'t practiced it yet',
        suggestedAction: 'Try practicing this sentence to improve your typing skills'
      };
    }
    
    if (needsPractice) {
      const daysSinceLastPractice = lastPracticedAt
        ? Math.floor((Date.now() - lastPracticedAt.getTime()) / (1000 * 60 * 60 * 24))
        : 0;
      
      if (daysSinceLastPractice > 14) {
        return {
          priority: 'high',
          reason: `It's been ${daysSinceLastPractice} days since you last practiced this sentence`,
          suggestedAction: 'Review this sentence to maintain your progress'
        };
      }
      
      return {
        priority: 'medium',
        reason: 'This sentence could benefit from more practice',
        suggestedAction: 'Practice a few more times to build muscle memory'
      };
    }
    
    return {
      priority: 'low',
      reason: 'You\'ve practiced this sentence well',
      suggestedAction: 'Consider exploring new sentences or reviewing occasionally'
    };
  }

  /**
   * Validate user saved sentence input
   */
  static validateInput(input: CreateUserSavedSentenceInput): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (!input.userId || typeof input.userId !== 'string') {
      errors.push('Valid user ID is required');
    }
    
    if (!input.sentenceId || typeof input.sentenceId !== 'number' || input.sentenceId <= 0) {
      errors.push('Valid sentence ID is required');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
}