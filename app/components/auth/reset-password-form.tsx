'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface FormData {
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  newPassword?: string;
  confirmPassword?: string;
  general?: string;
}

export function ResetPasswordForm({ className, ...props }: React.ComponentProps<'div'>) {
  const searchParams = useSearchParams();
  const token = searchParams.get('token');

  const [formData, setFormData] = useState<FormData>({
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isValidatingToken, setIsValidatingToken] = useState(true);
  const [tokenValid, setTokenValid] = useState(false);
  const [userEmail, setUserEmail] = useState('');

  // 验证令牌有效性
  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setErrors({ general: '缺少重置令牌，请重新请求密码重置' });
        setIsValidatingToken(false);
        return;
      }

      try {
        console.log('🔍 验证重置令牌:', token);

        const response = await fetch(`/api/auth/reset-password?token=${token}`, {
          method: 'GET',
        });

        const data = await response.json();
        console.log('🔍 令牌验证结果:', data);

        if (response.ok && data.valid) {
          setTokenValid(true);
          setUserEmail(data.email || '');
          console.log('✅ 令牌有效，邮箱:', data.email);
        } else {
          setTokenValid(false);
          setErrors({
            general: data.message || '重置令牌无效或已过期，请重新请求密码重置',
          });
          console.log('❌ 令牌无效:', data.message);
        }
      } catch (error) {
        console.error('💥 令牌验证错误:', error);
        setTokenValid(false);
        setErrors({ general: '验证令牌时发生错误，请稍后重试' });
      } finally {
        setIsValidatingToken(false);
      }
    };

    validateToken();
  }, [token]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.newPassword) {
      newErrors.newPassword = '请输入新密码';
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = '密码长度至少6位';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = '请确认新密码';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsLoading(true);
    setErrors({});

    try {
      console.log('🔐 提交密码重置');

      const response = await fetch('/api/auth/reset-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          newPassword: formData.newPassword,
        }),
      });

      const data = await response.json();
      console.log('🔍 密码重置结果:', data);

      if (response.ok) {
        console.log('✅ 密码重置成功');
        setIsSuccess(true);
      } else {
        console.log('❌ 密码重置失败:', data.message);
        setErrors({ general: data.message || '密码重置失败，请稍后重试' });
      }
    } catch (error) {
      console.error('💥 重置密码错误:', error);
      setErrors({ general: '网络错误，请检查连接后重试' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    // 清除相关错误
    if (errors[name as keyof FormErrors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // 令牌验证中
  if (isValidatingToken) {
    return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl">验证中...</CardTitle>
            <CardDescription>正在验证重置令牌的有效性</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 令牌无效
  if (!tokenValid) {
    return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl text-red-600">令牌无效</CardTitle>
            <CardDescription>重置链接无效或已过期</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              {errors.general && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {errors.general}
                </div>
              )}

              <Button asChild className="w-full">
                <Link href="/forgot-password">重新请求密码重置</Link>
              </Button>

              <div className="text-center text-sm">
                想起密码了？{' '}
                <Link href="/login" className="underline underline-offset-4">
                  立即登录
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 重置成功
  if (isSuccess) {
    return (
      <div className={cn('flex flex-col gap-6', className)} {...props}>
        <Card>
          <CardHeader className="text-center">
            <CardTitle className="text-xl">密码重置成功</CardTitle>
            <CardDescription>您的密码已成功重置，现在可以使用新密码登录</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6">
              <div className="text-center space-y-2">
                <p className="text-sm text-muted-foreground">
                  账户: <strong>{userEmail}</strong>
                </p>
                <p className="text-sm text-muted-foreground">密码已更新，您将收到确认邮件</p>
              </div>

              <Button asChild className="w-full">
                <Link href="/login">立即登录</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // 重置表单
  return (
    <div className={cn('flex flex-col gap-6', className)} {...props}>
      <Card>
        <CardHeader className="text-center">
          <CardTitle className="text-xl">重置密码</CardTitle>
          <CardDescription>为您的账户设置新密码</CardDescription>
          {userEmail && (
            <p className="text-sm text-muted-foreground">
              账户: <strong>{userEmail}</strong>
            </p>
          )}
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-6">
              {errors.general && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md">
                  {errors.general}
                </div>
              )}

              <div className="grid gap-3">
                <Label htmlFor="newPassword">新密码</Label>
                <Input
                  id="newPassword"
                  name="newPassword"
                  type="password"
                  placeholder="请输入新密码"
                  value={formData.newPassword}
                  onChange={handleInputChange}
                  className={errors.newPassword ? 'border-red-500' : ''}
                  required
                />
                {errors.newPassword && <p className="text-sm text-red-600">{errors.newPassword}</p>}
              </div>

              <div className="grid gap-3">
                <Label htmlFor="confirmPassword">确认新密码</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  placeholder="请再次输入新密码"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={errors.confirmPassword ? 'border-red-500' : ''}
                  required
                />
                {errors.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? '重置中...' : '重置密码'}
              </Button>

              <div className="text-center text-sm">
                想起密码了？{' '}
                <Link href="/login" className="underline underline-offset-4">
                  立即登录
                </Link>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      <div className="text-muted-foreground text-center text-xs text-balance">
        出现问题？{' '}
        <Link href="/forgot-password" className="underline underline-offset-4 hover:text-primary">
          重新请求密码重置
        </Link>
        。
      </div>
    </div>
  );
}
