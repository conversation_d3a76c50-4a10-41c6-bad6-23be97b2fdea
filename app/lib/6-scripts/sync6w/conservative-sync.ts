#!/usr/bin/env tsx

/**
 * 保守的同步脚本 - 专门处理网络不稳定的情况
 * 分批执行：考虑将6万条数据分成多个较小批次（如每次5000-10000条）
 *
 * 特点：
 * 1. 单个请求，无并发
 * 2. 小批次处理（50个单词/批）
 * 3. 长延迟和强重试机制
 * 4. 详细的网络错误处理
 */

import { join } from 'path';
import XLSX from 'xlsx';
import { PrismaClient } from '@prisma/client';
import { fetchDictionary } from '../../5-providers/gemini.js';
import { importFromDictionaryData } from '../llm-response-to-db/importDictionaryData.js';
import type { DictionaryQueryResult } from '../../utils/dict-types.js';

// 保守配置
const WORDS_TO_EXTRACT = 60000;
const WORDS_PER_BATCH = 100; // 小批次
const MAX_RETRIES = 5; // 更多重试
const BATCH_DELAY = 10000; // 10秒延迟

const prismaClient = new PrismaClient();

/**
 * 超级保守的重试机制
 */
async function conservativeRetry(
  wordList: string,
  batchIndex: number,
  maxRetries: number = MAX_RETRIES
): Promise<DictionaryQueryResult> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 批次 ${batchIndex} 尝试 ${attempt}/${maxRetries}`);

      // 每次尝试前都等待一下，让网络稳定
      if (attempt > 1) {
        const preDelay = 5000 * attempt; // 递增延迟
        console.log(`⏳ 尝试前等待 ${preDelay / 1000} 秒...`);
        await new Promise((resolve) => setTimeout(resolve, preDelay));
      }

      const result = await fetchDictionary(wordList);

      if (result.statusCode === 200 && result.data) {
        if (attempt > 1) {
          console.log(`✅ 批次 ${batchIndex} 重试成功 (第${attempt}次尝试)`);
        }
        return result;
      } else {
        throw new Error(`API返回错误状态: ${result.statusCode}`);
      }
    } catch (error) {
      lastError = error;
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.warn(`⚠️  批次 ${batchIndex} 第${attempt}次尝试失败:`, errorMessage);

      if (attempt < maxRetries) {
        // 网络错误时使用更长的延迟
        let delay = 10000; // 基础10秒
        if (errorMessage.includes('fetch failed')) {
          delay = 20000 + attempt * 10000; // 网络错误：20秒起步，递增
        } else {
          delay = 5000 + attempt * 5000; // 其他错误：5秒起步，递增
        }

        console.log(`⏳ 等待 ${delay / 1000} 秒后重试...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

/**
 * 从Excel读取单词
 */
async function extractWords(count: number): Promise<string[]> {
  const excelPath = join(process.cwd(), 'app/lib/6-scripts/sync6w/SUBTLEXusfrequencyabove1.xls');
  const workbook = XLSX.readFile(excelPath);
  const worksheet = workbook.Sheets[workbook.SheetNames[0]];
  const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

  const words: string[] = [];
  let extractedCount = 0;

  for (let i = 1; i < jsonData.length && extractedCount < count; i++) {
    const row = jsonData[i] as any[];
    if (row && row[0] && typeof row[0] === 'string') {
      const word = row[0].trim().toLowerCase();
      if (word && /^[a-zA-Z]+$/.test(word)) {
        words.push(word);
        extractedCount++;
      }
    }
  }

  return words;
}

/**
 * 分批处理单词
 */
function chunkWords(words: string[], chunkSize: number): string[][] {
  const chunks: string[][] = [];
  for (let i = 0; i < words.length; i += chunkSize) {
    chunks.push(words.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * 主函数
 */
async function main() {
  console.log('🐌 开始保守同步（网络友好模式）...\n');

  try {
    // 1. 读取单词
    console.log('📖 步骤1: 读取Excel文件');
    const words = await extractWords(WORDS_TO_EXTRACT);
    console.log(`✅ 提取 ${words.length} 个单词`);

    // 2. 分批处理
    const batches = chunkWords(words, WORDS_PER_BATCH);
    console.log(`📦 分为 ${batches.length} 批，每批 ${WORDS_PER_BATCH} 个单词`);
    console.log(
      `⏰ 预计总耗时: ${Math.ceil((batches.length * (BATCH_DELAY + 60000)) / 60000)} 分钟\n`
    );

    let totalSuccess = 0;
    let totalFailed = 0;
    const allResults: any[] = [];

    // 3. 逐批处理（无并发）
    for (let i = 0; i < batches.length; i++) {
      const batch = batches[i];
      const batchIndex = i + 1;

      console.log(`\n🔄 处理第 ${batchIndex}/${batches.length} 批`);
      console.log(`📝 单词: ${batch.slice(0, 5).join(', ')}${batch.length > 5 ? '...' : ''}`);

      try {
        const result = await conservativeRetry(batch.join(','), batchIndex);

        if (result.statusCode === 200 && result.data) {
          console.log(`✅ 批次 ${batchIndex} 成功: ${result.data.wordCount} 个单词`);
          allResults.push(result.data);
          totalSuccess += result.data.wordCount;
        } else {
          console.error(`❌ 批次 ${batchIndex} 失败: ${result.error}`);
          totalFailed += batch.length;
        }
      } catch (error) {
        console.error(`❌ 批次 ${batchIndex} 最终失败:`, error);
        totalFailed += batch.length;
      }

      // 批次间延迟（除了最后一批）
      if (i < batches.length - 1) {
        console.log(`⏳ 等待 ${BATCH_DELAY / 1000} 秒后处理下一批...`);
        await new Promise((resolve) => setTimeout(resolve, BATCH_DELAY));
      }
    }

    // 4. 合并结果并导入
    if (allResults.length > 0) {
      console.log(`\n💾 步骤4: 导入数据到数据库`);

      const mergedData = {
        words: allResults.flatMap((r) => r.words),
        wordList: allResults.flatMap((r) => r.wordList),
        wordCount: allResults.reduce((sum, r) => sum + r.wordCount, 0),
        illegalWords: allResults.flatMap((r) => r.illegalWords || []),
      };

      console.log(`📊 合并结果: ${mergedData.wordCount} 个单词`);

      const importResult = await importFromDictionaryData(mergedData, prismaClient, {
        forceOverwrite: true,
        verbose: false, // 关闭详细日志
      });

      console.log(`📈 导入结果:`);
      console.log(`  ✅ 成功: ${importResult.successCount} 个单词`);
      console.log(`  ❌ 失败: ${importResult.failureCount} 个单词`);

      // 5. 设置频率排名
      console.log('\n🏆 步骤5: 设置频率排名');
      let rankUpdated = 0;
      for (let i = 0; i < words.length; i++) {
        const word = words[i];
        const rank = i + 1;

        try {
          const result = await prismaClient.vocabulary.updateMany({
            where: { word: word },
            data: { freqRank: rank },
          });

          if (result.count > 0) {
            rankUpdated++;
          }
        } catch (error) {
          console.error(`❌ 设置排名失败 "${word}":`, error);
        }
      }

      console.log(`🏆 排名更新: ${rankUpdated} 个单词`);
    }

    console.log(`\n📊 最终统计:`);
    console.log(`  ✅ 成功: ${totalSuccess} 个单词`);
    console.log(`  ❌ 失败: ${totalFailed} 个单词`);
    console.log(
      `  📈 成功率: ${Math.round((totalSuccess / (totalSuccess + totalFailed)) * 100 * 10) / 10}%`
    );

    console.log('\n🎉 保守同步完成！');
  } catch (error) {
    console.error('\n💥 同步失败:', error);
    process.exit(1);
  } finally {
    await prismaClient.$disconnect();
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('未处理的错误:', error);
    process.exit(1);
  });
}

export { main };
