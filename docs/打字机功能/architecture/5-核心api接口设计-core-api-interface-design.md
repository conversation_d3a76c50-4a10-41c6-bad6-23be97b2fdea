# 5. 核心API接口设计 (Core API Interface Design)

所有新接口都将以 `/api/practice/` 或资源化的 `/api/word` 为前缀。

- **词汇练习API**:
  - `GET /api/practice/wordlists`: 获取所有可用的词库列表。
  - `GET /api/practice/wordlist/:listId`: 获取特定词库中的一批练习单词（支持分页）。
  - `POST /api/practice/progress`: 更新用户在某个词库中的练习进度。
- **生词本管理API**:
  - `GET /api/word`: 获取用户的生词列表。
  - `POST /api/word`: 批量向生词本添加单词。
  - `DELETE /api/word`: 批量从生词本移除单词。
- **生词练习API**:
  - `GET /api/practice/type-raw-words`: 获取由智能算法排序后的生词列表用于练习。
  - `POST /api/practice/session-result`: **异步**提交练习结果以更新后台熟练度指标。
- **句子练习API**:
  - `GET /api/practice/sentences`: 获取一条练习句子。
  - `POST /api/practice/sentences/favorites`: 收藏一个新句子。
  - `DELETE /api/practice/sentences/favorites/:sentenceId`: 取消收藏一个句子。
- **统计图表API**:
  - `GET /api/practice/stats/wpm-history`: 获取用户历史打字速度数据。
