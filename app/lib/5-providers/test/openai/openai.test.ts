import { describe, it, expect, beforeAll, vi } from 'vitest';
import { fetchDictionary, DictionaryResponseType, FetchDictionaryResult } from '../../openai';
import OpenAI from 'openai';
import { z } from 'zod';
import { zodResponseFormat } from 'openai/helpers/zod';
import 'dotenv/config';

const prompt100 =
  'apple,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla,watermelon,xigua,yuzu,zucchini,apricot,blueberry,cantaloupe,durian,enterprise,feijoa,guava,huckleberry,imbe,jackfruit,kumquat,lime,lychee,mulberry,nance,olive,peach,pear,persimmon,pineapple,plantain,plum,pomegranate,pomelo,qiwi,rambutan,sapodilla,starfruit,sugar,tamarillo,tamarind,tomato,cloud,computer,forest,mountain,ocean,river,sun,moon,star,tree,flower,grass,sky,book,pen,pencil,table,chair,window,door,house,car,bicycle,train,bus,boat,ship,road,street,city,town,village,country,world,earth,space,time,love,hate,happy,sad,good';

const prompt10 = 'world,apple,banana,cherry,date,elderberry,fig,grape,honeydew,wiki';
const prompt15 =
  'serendipity,ephemeral,quintessential,melancholy,eloquent,resilient,enigmatic,ubiquitous,paradigm,serene,apple,banana,cherry,date,elderberry';
const prompt20 =
  'love,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla';
const prompt50 =
  'apple,banana,cherry,date,elderberry,fig,grape,honeydew,kiwi,lemon,mango,nectarine,orange,papaya,quince,raspberry,strawberry,tangerine,ugli,vanilla,watermelon,xigua,yuzu,zucchini,apricot,blueberry,cantaloupe,durian,enterprise,feijoa,guava,huckleberry,imbe,jackfruit,kumquat,lime,lychee,mulberry,nance,olive,peach,pear,persimmon,pineapple,plantain,plum,pomegranate,pomelo,qiwi,rambutan';

function writeToFile(result: FetchDictionaryResult, prefix: string = 'openai') {
  const fs = require('fs');
  const path = require('path');
  const filePath = path.join(__dirname, `${prefix}_response.json`);
  // 清空文件内容
  fs.writeFileSync(filePath, '');
  // 写入新的JSON数据
  fs.writeFileSync(filePath, JSON.stringify(result, null, 2));
}

describe('OpenAI Dictionary API', () => {
  beforeAll(() => {
    // 检查环境变量
    if (!process.env.OPENAI_API_KEY) {
      console.warn('⚠️ OPENAI_API_KEY 环境变量未设置，测试可能会失败');
    }
  });

  const client = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    baseURL: process.env.OPENAI_BASE_URL,
  });

  it('处理无效的单词应返回错误信息但不抛出异常', async () => {
    // 发送一个很可能不存在的单词或无效的请求
    const result = await fetchDictionary('hello world, good, job, ooooog');

    // 验证我们得到了响应（没有异常）
    expect(result).toBeDefined();

    // 可能是API错误或者返回空结果，但不应抛出未处理的异常
    if (result.error) {
      const err = result.error as { message?: string };
      console.log('预期中的错误:', err.message);
    } else if (result.data) {
      // API可能返回空词组列表或只有一个条目说明未找到
      if (result.data.words.length === 0) {
        console.log('API返回空词组列表');
      } else {
        console.log(result);
        console.log(`API返回了 ${result.data.words.length} 个词条`);
      }
    }
  });

  it('处理10个单词', async () => {
    const prompt10 = 'world,apple,banana,cherry,date,elderberry,fig,grape,honeydew,wiki';
    const prompt = prompt10;

    const result = await fetchDictionary(prompt);

    if (result.error) {
      const err = result.error as { message?: string };
      console.log('预期中的错误:', err.message);
    } else if (result.data) {
      // API可能返回空词组列表或只有一个条目说明未找到
      if (result.data.words.length === 0) {
        console.log('API返回空词组列表');
      } else {
        console.log(`API返回了 ${result.data.words.length} 个词条`);
      }
    }
    if (result.data) {
      const fs = require('fs');
      const path = require('path');
      const filePath = path.join(__dirname, 'openai_response.json');

      // 清空文件内容
      fs.writeFileSync(filePath, '');

      // 写入新的JSON数据
      fs.writeFileSync(filePath, JSON.stringify(result.data, null, 2));
    }

    // 如果API返回错误，跳过验证
    if (result.error) {
      const err = result.error as { message?: string };
      console.log('API错误，跳过格式验证:', err.message);
      return;
    }

    // 验证状态码
    expect(result.statusCode).toBe(200);

    // 验证数据存在
    expect(result.data).toBeDefined();

    if (result.data) {
      // 验证数据结构符合预期
      expect(Array.isArray(result.data.words)).toBe(true);

      // 至少有一个单词
      expect(result.data.words.length).toBeGreaterThan(0);

      // 验证第一个单词的结构
      const word = result.data.words[0];
      expect(Array.isArray(word.explain)).toBe(true);
      expect(Array.isArray(word.wordFormats)).toBe(true);
      expect(word.phonetic).toHaveProperty('us');
      expect(word.phonetic).toHaveProperty('uk');

      // 验证解释结构
      if (word.explain.length > 0) {
        const explain = word.explain[0];
        expect(explain).toHaveProperty('pos');
        expect(Array.isArray(explain.definitions)).toBe(true);

        if (explain.definitions.length > 0) {
          const definition = explain.definitions[0];
          expect(definition).toHaveProperty('definition');
          expect(definition).toHaveProperty('chinese');
          expect(definition).toHaveProperty('chinese_short');
        }
      }
    }
  });

  it('处理100个单词', async () => {
    const result = await fetchDictionary(prompt100);
    if (result.error) {
      const err = result.error as { message?: string };
      console.log('预期中的错误:', err.message);
    } else if (result.data) {
      console.log(result);
      writeToFile(result);
    }
  });
});

const FuckClient = new OpenAI({
  apiKey: process.env.FUCK_API_KEY,
  baseURL: process.env.FUCK_BASE_URL,
});

const L654600Client = new OpenAI({
  apiKey: process.env.L654600_API_KEY,
  baseURL: process.env.L654600_BASE_URL,
});

const PromptLayerClient = new OpenAI({
  apiKey: process.env.PROMPTLAYER_API_KEY,
  baseURL: process.env.PROMPTLAYER_BASE_URL,
});

const OpenRouterClient = new OpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: process.env.OPENROUTER_BASE_URL,
});

const OpenAIClient = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_BASE_URL,
});

const OpenAIProy = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  baseURL: process.env.OPENAI_PROXY_URL,
});

const LocalClient = new OpenAI({
  apiKey: process.env.LOCAL_API_KEY,
  baseURL: process.env.LOCAL_BASE_URL,
});

describe('Different Provider Test', () => {
  it('FuckProvider 处理1个单词', async () => {
    const result = await fetchDictionary(prompt20, FuckClient);
    writeToFile(result, 'fuck');
  });

  it('L654600Client 处理1个单词', async () => {
    const result = await fetchDictionary('hello', L654600Client);
    writeToFile(result, 'l654600');
  });

  it('PromptLayerClient 处理1个单词', async () => {
    const result = await fetchDictionary('hello', PromptLayerClient);
    writeToFile(result, 'promptlayer');
  });

  it('OpenRouterClient 处理1个单词', async () => {
    const result = await fetchDictionary('hello', OpenRouterClient);
    writeToFile(result, 'openrouter');
  });

  it('LocalClient 处理1个单词', async () => {
    const result = await fetchDictionary('love', LocalClient, 'gpt-4.1-mini');
    writeToFile(result, 'local');
  });
});

describe('Different Provider Pressure Test', () => {
  it('FuckProvider 处理100个单词', async () => {
    const result = await fetchDictionary(prompt100, FuckClient);
    writeToFile(result, 'fuck');
  });

  it('L654600Provider 处理100个单词', async () => {
    const result = await fetchDictionary(prompt100, L654600Client);
    writeToFile(result, 'l654600');
  });

  it('PromptLayerProvider 处理100个单词', async () => {
    const result = await fetchDictionary(prompt100, PromptLayerClient);
    writeToFile(result, 'promptlayer');
  });

  it('OpenAIProvider 处理100个单词', async () => {
    const result = await fetchDictionary(prompt100, OpenAIClient);
    writeToFile(result, 'openai');
  });

  it('OpenAIProxyProvider 处理100个单词', async () => {
    const result = await fetchDictionary(prompt100, OpenAIProy);
    writeToFile(result, 'openai_proxy');
  });

  it('LocalClient 处理100个单词', async () => {
    const result = await fetchDictionary(prompt100, LocalClient, 'gpt-4.1-mini');
    writeToFile(result, 'local');
  });
});
