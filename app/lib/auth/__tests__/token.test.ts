/**
 * JWT Token Utilities Unit Tests
 * JWT令牌工具单元测试
 *
 * Following TDD approach with persona-qa mindset
 * Comprehensive testing with security focus
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { randomBytes, createHash } from 'crypto';
import { PrismaClient } from '@prisma/client';
import {
  generateAccessToken,
  generateRefreshToken,
  verifyAccessToken,
  verifyRefreshToken,
  createTokenResponse,
  refreshAccessToken,
  revokeRefreshToken,
  revokeAllUserTokens,
  cleanupExpiredTokens,
  generateSecureToken,
  hashPassword,
  verifyPassword,
  generatePasswordResetToken,
  verifyPasswordResetToken,
  extractBearerToken,
  validateTokenFormat,
  calculateTokenExpiration,
  isTokenExpiringSoon,
} from '../token';
import { TOKEN_CONSTANTS, SECURITY_CONFIG } from '../../types/auth';

// Mock external dependencies
vi.mock('jsonwebtoken');
vi.mock('bcryptjs');
vi.mock('crypto');

// Mock the Prisma client module with inline mock
vi.mock('@prisma/client', () => {
  const mockPrismaClient = {
    refreshToken: {
      create: vi.fn(),
      findUnique: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
      deleteMany: vi.fn(),
    },
    user: {
      findUnique: vi.fn(),
    },
  };

  return {
    PrismaClient: vi.fn(() => mockPrismaClient),
  };
});

// Create mocked instances
const mockJwt = jwt as vi.Mocked<typeof jwt>;
const mockBcrypt = bcrypt as vi.Mocked<typeof bcrypt>;
const mockRandomBytes = randomBytes as vi.MockedFunction<typeof randomBytes>;
const mockCreateHash = createHash as vi.MockedFunction<typeof createHash>;

describe('JWT Token Utilities', () => {
  const mockUserId = 'user123';
  const mockEmail = '<EMAIL>';
  const mockName = 'Test User';
  const mockTokenId = 'token123';
  const mockAccessToken =
    'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************.signature';
  const mockRefreshToken =
    'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************.signature';
  const mockCurrentTime = 1670000000; // Mock timestamp

  let mockPrismaClient: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    vi.useFakeTimers();
    vi.setSystemTime(new Date(mockCurrentTime * 1000));

    // Get the mocked Prisma client
    const { PrismaClient } = (await vi.importMock('@prisma/client')) as any;
    mockPrismaClient = new PrismaClient();

    // Setup default mocks
    mockJwt.sign.mockReturnValue(mockAccessToken);
    mockJwt.verify.mockReturnValue({
      sub: mockUserId,
      email: mockEmail,
      type: 'access',
      iat: mockCurrentTime,
      exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
    });

    mockRandomBytes.mockReturnValue(Buffer.from('mock-random-bytes'));
    mockCreateHash.mockReturnValue({
      update: vi.fn().mockReturnThis(),
      digest: vi.fn().mockReturnValue('mock-hash'),
    } as any);

    mockBcrypt.hash.mockResolvedValue('hashed-password');
    mockBcrypt.compare.mockResolvedValue(true);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('generateAccessToken', () => {
    it('should generate access token with correct payload structure', async () => {
      const payload = {
        sub: mockUserId,
        email: mockEmail,
        name: mockName,
      };

      const token = await generateAccessToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          ...payload,
          type: 'access',
          iat: mockCurrentTime,
          exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
        },
        expect.any(String),
        {
          algorithm: TOKEN_CONSTANTS.JWT_ALGORITHM,
          issuer: TOKEN_CONSTANTS.JWT_ISSUER,
          audience: TOKEN_CONSTANTS.JWT_AUDIENCE,
        }
      );
      expect(token).toBe(mockAccessToken);
    });

    it('should generate access token without optional name field', async () => {
      const payload = {
        sub: mockUserId,
        email: mockEmail,
      };

      await generateAccessToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          ...payload,
          type: 'access',
          iat: mockCurrentTime,
          exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
        },
        expect.any(String),
        expect.any(Object)
      );
    });

    it('should use correct JWT signing options', async () => {
      const payload = { sub: mockUserId, email: mockEmail };

      await generateAccessToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(expect.any(Object), expect.any(String), {
        algorithm: TOKEN_CONSTANTS.JWT_ALGORITHM,
        issuer: TOKEN_CONSTANTS.JWT_ISSUER,
        audience: TOKEN_CONSTANTS.JWT_AUDIENCE,
      });
    });

    it('should generate token with correct expiration time', async () => {
      const payload = { sub: mockUserId, email: mockEmail };

      await generateAccessToken(payload);

      const signCall = mockJwt.sign.mock.calls[0];
      const tokenPayload = signCall[0] as any;

      expect(tokenPayload.exp).toBe(mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN);
      expect(tokenPayload.iat).toBe(mockCurrentTime);
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate refresh token with correct payload structure', async () => {
      const payload = {
        sub: mockUserId,
        email: mockEmail,
        tokenId: mockTokenId,
      };

      mockJwt.sign.mockReturnValue(mockRefreshToken);

      const token = await generateRefreshToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        {
          ...payload,
          type: 'refresh',
          iat: mockCurrentTime,
          exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
        },
        expect.any(String),
        {
          algorithm: TOKEN_CONSTANTS.JWT_ALGORITHM,
          issuer: TOKEN_CONSTANTS.JWT_ISSUER,
          audience: TOKEN_CONSTANTS.JWT_AUDIENCE,
        }
      );
      expect(token).toBe(mockRefreshToken);
    });

    it('should use refresh token secret for signing', async () => {
      const payload = { sub: mockUserId, email: mockEmail };
      const expectedSecret = process.env.JWT_REFRESH_SECRET || 'development-refresh-secret';

      await generateRefreshToken(payload);

      expect(mockJwt.sign).toHaveBeenCalledWith(
        expect.any(Object),
        expectedSecret,
        expect.any(Object)
      );
    });

    it('should generate token with correct long expiration time', async () => {
      const payload = { sub: mockUserId, email: mockEmail };

      await generateRefreshToken(payload);

      const signCall = mockJwt.sign.mock.calls[0];
      const tokenPayload = signCall[0] as any;

      expect(tokenPayload.exp).toBe(mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN);
      expect(tokenPayload.type).toBe('refresh');
    });
  });

  describe('verifyAccessToken', () => {
    it('should verify valid access token successfully', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'access',
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);

      const result = await verifyAccessToken(mockAccessToken);

      expect(mockJwt.verify).toHaveBeenCalledWith(mockAccessToken, expect.any(String), {
        algorithms: [TOKEN_CONSTANTS.JWT_ALGORITHM],
        issuer: TOKEN_CONSTANTS.JWT_ISSUER,
        audience: TOKEN_CONSTANTS.JWT_AUDIENCE,
      });
      expect(result).toEqual(mockPayload);
    });

    it('should throw error for invalid token type', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh', // Wrong type
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);

      await expect(verifyAccessToken(mockAccessToken)).rejects.toThrow('Invalid access token');
    });

    it('should throw error for JWT verification failure', async () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('JWT verification failed');
      });

      await expect(verifyAccessToken(mockAccessToken)).rejects.toThrow('Invalid access token');
    });

    it('should use correct verification options', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'access',
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);

      await verifyAccessToken(mockAccessToken);

      expect(mockJwt.verify).toHaveBeenCalledWith(mockAccessToken, expect.any(String), {
        algorithms: [TOKEN_CONSTANTS.JWT_ALGORITHM],
        issuer: TOKEN_CONSTANTS.JWT_ISSUER,
        audience: TOKEN_CONSTANTS.JWT_AUDIENCE,
      });
    });
  });

  describe('verifyRefreshToken', () => {
    const mockRefreshTokenData = {
      token: mockRefreshToken,
      userId: mockUserId,
      isRevoked: false,
      expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
      createdAt: new Date(),
      lastUsedAt: new Date(),
    };

    it('should verify valid refresh token successfully', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue(mockRefreshTokenData);

      const result = await verifyRefreshToken(mockRefreshToken);

      expect(mockJwt.verify).toHaveBeenCalledWith(mockRefreshToken, expect.any(String), {
        algorithms: [TOKEN_CONSTANTS.JWT_ALGORITHM],
        issuer: TOKEN_CONSTANTS.JWT_ISSUER,
        audience: TOKEN_CONSTANTS.JWT_AUDIENCE,
      });
      expect(mockPrismaClient.refreshToken.findUnique).toHaveBeenCalledWith({
        where: { token: mockRefreshToken },
      });
      expect(result).toEqual(mockPayload);
    });

    it('should throw error for invalid token type', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'access', // Wrong type
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);

      await expect(verifyRefreshToken(mockRefreshToken)).rejects.toThrow('Invalid refresh token');
    });

    it('should throw error for revoked token', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        ...mockRefreshTokenData,
        isRevoked: true,
      });

      await expect(verifyRefreshToken(mockRefreshToken)).rejects.toThrow('Invalid refresh token');
    });

    it('should throw error for expired token', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        ...mockRefreshTokenData,
        expiresAt: new Date(Date.now() - 1000), // Expired
      });

      await expect(verifyRefreshToken(mockRefreshToken)).rejects.toThrow('Invalid refresh token');
    });

    it('should throw error for non-existent token', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue(null);

      await expect(verifyRefreshToken(mockRefreshToken)).rejects.toThrow('Invalid refresh token');
    });
  });

  describe('createTokenResponse', () => {
    it('should create complete token response successfully', async () => {
      mockJwt.sign.mockReturnValueOnce(mockAccessToken).mockReturnValueOnce(mockRefreshToken);

      mockPrismaClient.refreshToken.create.mockResolvedValue({
        token: mockRefreshToken,
        userId: mockUserId,
        isRevoked: false,
        expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
        createdAt: new Date(),
        lastUsedAt: new Date(),
      });

      const result = await createTokenResponse(mockUserId, mockEmail, mockName);

      expect(result).toEqual({
        accessToken: mockAccessToken,
        refreshToken: mockRefreshToken,
        tokenType: TOKEN_CONSTANTS.TOKEN_TYPE,
        expiresIn: TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      });
    });

    it('should store refresh token in database', async () => {
      mockJwt.sign.mockReturnValueOnce(mockAccessToken).mockReturnValueOnce(mockRefreshToken);

      await createTokenResponse(mockUserId, mockEmail, mockName);

      expect(mockPrismaClient.refreshToken.create).toHaveBeenCalledWith({
        data: {
          token: mockRefreshToken,
          userId: mockUserId,
          expiresAt: expect.any(Date),
        },
      });
    });

    it('should work without optional name parameter', async () => {
      mockJwt.sign.mockReturnValueOnce(mockAccessToken).mockReturnValueOnce(mockRefreshToken);

      const result = await createTokenResponse(mockUserId, mockEmail);

      expect(result.accessToken).toBe(mockAccessToken);
      expect(result.refreshToken).toBe(mockRefreshToken);
    });

    it('should generate tokens with correct payloads', async () => {
      mockJwt.sign.mockReturnValueOnce(mockAccessToken).mockReturnValueOnce(mockRefreshToken);

      await createTokenResponse(mockUserId, mockEmail, mockName);

      // Check access token generation
      expect(mockJwt.sign).toHaveBeenNthCalledWith(
        1,
        expect.objectContaining({
          sub: mockUserId,
          email: mockEmail,
          name: mockName,
          type: 'access',
        }),
        expect.any(String),
        expect.any(Object)
      );

      // Check refresh token generation
      expect(mockJwt.sign).toHaveBeenNthCalledWith(
        2,
        expect.objectContaining({
          sub: mockUserId,
          email: mockEmail,
          type: 'refresh',
        }),
        expect.any(String),
        expect.any(Object)
      );
    });
  });

  describe('refreshAccessToken', () => {
    const mockUser = {
      id: mockUserId,
      email: mockEmail,
      name: mockName,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    it('should refresh access token successfully', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        token: mockRefreshToken,
        userId: mockUserId,
        isRevoked: false,
        expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
        createdAt: new Date(),
        lastUsedAt: new Date(),
      });
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);
      mockPrismaClient.refreshToken.update.mockResolvedValue({} as any);
      mockJwt.sign.mockReturnValue(mockAccessToken);

      const result = await refreshAccessToken(mockRefreshToken);

      expect(result).toEqual({
        accessToken: mockAccessToken,
        refreshToken: mockRefreshToken,
        tokenType: TOKEN_CONSTANTS.TOKEN_TYPE,
        expiresIn: TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      });
    });

    it('should update refresh token last used time', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        token: mockRefreshToken,
        userId: mockUserId,
        isRevoked: false,
        expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
        createdAt: new Date(),
        lastUsedAt: new Date(),
      });
      mockPrismaClient.user.findUnique.mockResolvedValue(mockUser);

      await refreshAccessToken(mockRefreshToken);

      expect(mockPrismaClient.refreshToken.update).toHaveBeenCalledWith({
        where: { token: mockRefreshToken },
        data: { lastUsedAt: expect.any(Date) },
      });
    });

    it('should throw error for inactive user', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        token: mockRefreshToken,
        userId: mockUserId,
        isRevoked: false,
        expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
        createdAt: new Date(),
        lastUsedAt: new Date(),
      });
      mockPrismaClient.user.findUnique.mockResolvedValue({
        ...mockUser,
        isActive: false,
      });

      await expect(refreshAccessToken(mockRefreshToken)).rejects.toThrow(
        'User not found or inactive'
      );
    });

    it('should throw error for non-existent user', async () => {
      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);
      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        token: mockRefreshToken,
        userId: mockUserId,
        isRevoked: false,
        expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
        createdAt: new Date(),
        lastUsedAt: new Date(),
      });
      mockPrismaClient.user.findUnique.mockResolvedValue(null);

      await expect(refreshAccessToken(mockRefreshToken)).rejects.toThrow(
        'User not found or inactive'
      );
    });
  });

  describe('revokeRefreshToken', () => {
    it('should revoke refresh token successfully', async () => {
      mockPrismaClient.refreshToken.updateMany.mockResolvedValue({ count: 1 });

      await revokeRefreshToken(mockRefreshToken);

      expect(mockPrismaClient.refreshToken.updateMany).toHaveBeenCalledWith({
        where: { token: mockRefreshToken },
        data: { isRevoked: true },
      });
    });

    it('should handle non-existent token gracefully', async () => {
      mockPrismaClient.refreshToken.updateMany.mockResolvedValue({ count: 0 });

      await expect(revokeRefreshToken(mockRefreshToken)).resolves.not.toThrow();
    });
  });

  describe('revokeAllUserTokens', () => {
    it('should revoke all user tokens successfully', async () => {
      mockPrismaClient.refreshToken.updateMany.mockResolvedValue({ count: 3 });

      await revokeAllUserTokens(mockUserId);

      expect(mockPrismaClient.refreshToken.updateMany).toHaveBeenCalledWith({
        where: { userId: mockUserId },
        data: { isRevoked: true },
      });
    });

    it('should handle user with no tokens gracefully', async () => {
      mockPrismaClient.refreshToken.updateMany.mockResolvedValue({ count: 0 });

      await expect(revokeAllUserTokens(mockUserId)).resolves.not.toThrow();
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should cleanup expired and revoked tokens', async () => {
      mockPrismaClient.refreshToken.deleteMany.mockResolvedValue({ count: 5 });

      await cleanupExpiredTokens();

      expect(mockPrismaClient.refreshToken.deleteMany).toHaveBeenCalledWith({
        where: {
          OR: [{ expiresAt: { lt: expect.any(Date) } }, { isRevoked: true }],
        },
      });
    });

    it('should handle no expired tokens gracefully', async () => {
      mockPrismaClient.refreshToken.deleteMany.mockResolvedValue({ count: 0 });

      await expect(cleanupExpiredTokens()).resolves.not.toThrow();
    });
  });

  describe('generateSecureToken', () => {
    it('should generate secure token with default length', () => {
      const mockBuffer = Buffer.from('test-random-bytes');
      mockRandomBytes.mockReturnValue(mockBuffer);

      const token = generateSecureToken();

      expect(mockRandomBytes).toHaveBeenCalledWith(32);
      expect(token).toBe(mockBuffer.toString('hex'));
    });

    it('should generate secure token with custom length', () => {
      const mockBuffer = Buffer.from('test-random-bytes');
      mockRandomBytes.mockReturnValue(mockBuffer);

      const token = generateSecureToken(64);

      expect(mockRandomBytes).toHaveBeenCalledWith(64);
      expect(token).toBe(mockBuffer.toString('hex'));
    });

    it('should use crypto.randomBytes for secure generation', () => {
      generateSecureToken();

      expect(mockRandomBytes).toHaveBeenCalled();
    });
  });

  describe('hashPassword', () => {
    it('should hash password with correct salt rounds', async () => {
      const password = 'test-password';
      const expectedHash = 'hashed-password';

      mockBcrypt.hash.mockResolvedValue(expectedHash);

      const result = await hashPassword(password);

      expect(mockBcrypt.hash).toHaveBeenCalledWith(password, SECURITY_CONFIG.BCRYPT_ROUNDS);
      expect(result).toBe(expectedHash);
    });

    it('should handle empty password', async () => {
      const password = '';

      await hashPassword(password);

      expect(mockBcrypt.hash).toHaveBeenCalledWith(password, SECURITY_CONFIG.BCRYPT_ROUNDS);
    });
  });

  describe('verifyPassword', () => {
    it('should verify correct password', async () => {
      const password = 'test-password';
      const hash = 'hashed-password';

      mockBcrypt.compare.mockResolvedValue(true);

      const result = await verifyPassword(password, hash);

      expect(mockBcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(true);
    });

    it('should reject incorrect password', async () => {
      const password = 'wrong-password';
      const hash = 'hashed-password';

      mockBcrypt.compare.mockResolvedValue(false);

      const result = await verifyPassword(password, hash);

      expect(mockBcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(false);
    });
  });

  describe('generatePasswordResetToken', () => {
    it('should generate password reset token', async () => {
      const email = '<EMAIL>';
      const mockToken = 'mock-random-bytes';

      mockRandomBytes.mockReturnValue(Buffer.from(mockToken));

      const result = await generatePasswordResetToken(email);

      expect(mockRandomBytes).toHaveBeenCalledWith(32);
      expect(result).toBe(mockToken);
    });
  });

  describe('verifyPasswordResetToken', () => {
    it('should verify valid token format', async () => {
      const validToken = 'a'.repeat(64); // 64 hex characters

      const result = await verifyPasswordResetToken(validToken);

      expect(result).toBe(true);
    });

    it('should reject invalid token format', async () => {
      const invalidToken = 'short-token';

      const result = await verifyPasswordResetToken(invalidToken);

      expect(result).toBe(false);
    });
  });

  describe('extractBearerToken', () => {
    it('should extract valid Bearer token', () => {
      const authorization = `Bearer ${mockAccessToken}`;

      const result = extractBearerToken(authorization);

      expect(result).toBe(mockAccessToken);
    });

    it('should return null for invalid Bearer format', () => {
      const authorization = `Basic ${mockAccessToken}`;

      const result = extractBearerToken(authorization);

      expect(result).toBeNull();
    });

    it('should return null for undefined authorization', () => {
      const result = extractBearerToken(undefined);

      expect(result).toBeNull();
    });

    it('should return null for empty authorization', () => {
      const result = extractBearerToken('');

      expect(result).toBeNull();
    });

    it('should return null for Bearer without token', () => {
      const authorization = 'Bearer ';

      const result = extractBearerToken(authorization);

      expect(result).toBe('');
    });
  });

  describe('validateTokenFormat', () => {
    it('should validate correct JWT format', () => {
      const validToken = 'header.payload.signature';

      const result = validateTokenFormat(validToken);

      expect(result).toBe(true);
    });

    it('should reject invalid JWT format with too few parts', () => {
      const invalidToken = 'header.payload';

      const result = validateTokenFormat(invalidToken);

      expect(result).toBe(false);
    });

    it('should reject invalid JWT format with too many parts', () => {
      const invalidToken = 'header.payload.signature.extra';

      const result = validateTokenFormat(invalidToken);

      expect(result).toBe(false);
    });

    it('should reject empty token', () => {
      const result = validateTokenFormat('');

      expect(result).toBe(false);
    });
  });

  describe('calculateTokenExpiration', () => {
    it('should calculate correct expiration date', () => {
      const expiresIn = 3600; // 1 hour
      const expectedExpiration = new Date(Date.now() + expiresIn * 1000);

      const result = calculateTokenExpiration(expiresIn);

      expect(result.getTime()).toBeCloseTo(expectedExpiration.getTime(), -2);
    });

    it('should handle zero expiration', () => {
      const expiresIn = 0;
      const expectedExpiration = new Date(Date.now());

      const result = calculateTokenExpiration(expiresIn);

      expect(result.getTime()).toBeCloseTo(expectedExpiration.getTime(), -2);
    });
  });

  describe('isTokenExpiringSoon', () => {
    it('should detect token expiring soon with default buffer', () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const exp = currentTime + 300; // 5 minutes from now

      const result = isTokenExpiringSoon(exp);

      expect(result).toBe(true);
    });

    it('should detect token not expiring soon', () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const exp = currentTime + 3600; // 1 hour from now

      const result = isTokenExpiringSoon(exp);

      expect(result).toBe(false);
    });

    it('should use custom buffer time', () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const exp = currentTime + 600; // 10 minutes from now
      const bufferMinutes = 15;

      const result = isTokenExpiringSoon(exp, bufferMinutes);

      expect(result).toBe(true);
    });

    it('should detect expired token', () => {
      const currentTime = Math.floor(Date.now() / 1000);
      const exp = currentTime - 3600; // 1 hour ago

      const result = isTokenExpiringSoon(exp);

      expect(result).toBe(true);
    });
  });

  describe('Security Edge Cases', () => {
    it('should handle malformed JWT payloads gracefully', async () => {
      mockJwt.verify.mockImplementation(() => {
        throw new Error('Malformed JWT');
      });

      await expect(verifyAccessToken('malformed-token')).rejects.toThrow('Invalid access token');
    });

    it('should handle database connection errors gracefully', async () => {
      mockPrismaClient.refreshToken.findUnique.mockRejectedValue(
        new Error('Database connection failed')
      );

      const mockPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValue(mockPayload);

      await expect(verifyRefreshToken(mockRefreshToken)).rejects.toThrow('Invalid refresh token');
    });

    it('should handle token payload without required fields', async () => {
      const incompletePayload = {
        sub: mockUserId,
        // Missing email and type
      };

      mockJwt.verify.mockReturnValue(incompletePayload);

      await expect(verifyAccessToken(mockAccessToken)).rejects.toThrow('Invalid access token');
    });

    it('should handle concurrent token revocation', async () => {
      // Simulate concurrent revocation
      mockPrismaClient.refreshToken.updateMany.mockImplementation(async () => {
        // Simulate delay
        await new Promise((resolve) => setTimeout(resolve, 100));
        return { count: 1 };
      });

      const promises = [revokeRefreshToken(mockRefreshToken), revokeRefreshToken(mockRefreshToken)];

      await Promise.all(promises);

      expect(mockPrismaClient.refreshToken.updateMany).toHaveBeenCalledTimes(2);
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle multiple token generation requests', async () => {
      const requests = Array(100)
        .fill(null)
        .map((_, i) =>
          generateAccessToken({
            sub: `user${i}`,
            email: `user${i}@example.com`,
          })
        );

      const results = await Promise.all(requests);

      expect(results).toHaveLength(100);
      expect(mockJwt.sign).toHaveBeenCalledTimes(100);
    });

    it('should handle batch token cleanup efficiently', async () => {
      mockPrismaClient.refreshToken.deleteMany.mockResolvedValue({ count: 1000 });

      const startTime = Date.now();
      await cleanupExpiredTokens();
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(mockPrismaClient.refreshToken.deleteMany).toHaveBeenCalledTimes(1);
    });
  });

  describe('Integration-like Tests', () => {
    it('should complete full token lifecycle', async () => {
      // Create tokens
      mockJwt.sign.mockReturnValueOnce(mockAccessToken).mockReturnValueOnce(mockRefreshToken);

      const tokenResponse = await createTokenResponse(mockUserId, mockEmail, mockName);

      // Verify tokens
      const accessPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'access',
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      };

      const refreshPayload = {
        sub: mockUserId,
        email: mockEmail,
        type: 'refresh',
        tokenId: mockTokenId,
        iat: mockCurrentTime,
        exp: mockCurrentTime + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      mockJwt.verify.mockReturnValueOnce(accessPayload).mockReturnValueOnce(refreshPayload);

      mockPrismaClient.refreshToken.findUnique.mockResolvedValue({
        token: mockRefreshToken,
        userId: mockUserId,
        isRevoked: false,
        expiresAt: new Date(Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN * 1000),
        createdAt: new Date(),
        lastUsedAt: new Date(),
      });

      const verifiedAccess = await verifyAccessToken(tokenResponse.accessToken);
      const verifiedRefresh = await verifyRefreshToken(tokenResponse.refreshToken);

      expect(verifiedAccess.sub).toBe(mockUserId);
      expect(verifiedRefresh.sub).toBe(mockUserId);

      // Revoke token
      await revokeRefreshToken(tokenResponse.refreshToken);

      expect(mockPrismaClient.refreshToken.updateMany).toHaveBeenCalledWith({
        where: { token: tokenResponse.refreshToken },
        data: { isRevoked: true },
      });
    });
  });
});
