# Sync6w - 频率词汇同步工具

高效的词汇同步工具，从SUBTLEX频率词汇表同步高频词汇到数据库。

## 🚀 快速开始

### 基本使用

```bash
# 同步60000个高频词汇（推荐）
pnpm run sync:freq-words

# 小规模测试（100个单词）
pnpm run sync:freq-words:test

# 保守同步（网络不稳定时使用）
pnpm run sync:freq-words:safe

# 重试失败的单词
pnpm run sync:retry-missing

# 分析失败原因
pnpm run sync:analyze-failures
```

### 数据完整性管理

```bash
# 检查数据完整性
npx tsx app/lib/6-scripts/sync6w/debug/data-integrity.ts

# 测试Gemini API连接
npx tsx app/lib/6-scripts/sync6w/debug/gemini-connection-test.ts
```

## 📁 文件结构

### 核心脚本

- **`sync-frequency-words.ts`** - 主同步脚本（核心）
- **`conservative-sync.ts`** - 保守同步模式
- **`retry-missing-words.ts`** - 重试失败的单词
- **`analyze-failures.ts`** - 失败分析工具

### 运行脚本

- **`run-sync.ts`** - 同步入口点
- **`test-small-sync.ts`** - 小规模测试

### 工具模块

- **`utils/missing-words-finder.ts`** - 查找未匹配单词
- **`utils/failure-analyzer.ts`** - 失败原因分析

### 调试工具

- **`debug/data-integrity.ts`** - 数据完整性检查
- **`debug/gemini-connection-test.ts`** - API连接测试

### 测试文件

- **`test/import/words1-import.ts`** - 基本导入测试
- **`test/duplicate/duplicate-import.ts`** - 重复数据处理测试
- **`test/excel/excel-read.ts`** - Excel读取测试

## 🛠️ 数据完整性工具

新的统一工具 `data-integrity.ts` 提供以下命令：

```bash
# 检查数据完整性
npx tsx data-integrity.ts check

# 清理孤立记录
npx tsx data-integrity.ts cleanup

# 修复缺失的主WordFormat
npx tsx data-integrity.ts fix

# 测试删除逻辑
npx tsx data-integrity.ts test

# 自动检查并修复所有问题（推荐）
npx tsx data-integrity.ts auto
```

### 状态说明

- **HEALTHY** ✅ - 数据完整性良好
- **WARNING** ⚠️ - 发现少量问题，可自动修复
- **ERROR** ❌ - 发现严重问题，需要手动处理

## 📊 频率词汇同步

从SUBTLEX频率词汇表读取高频词汇，调用Gemini API获取详细释义，导入数据库并设置频率排名。

### 配置参数

```typescript
const WORDS_TO_EXTRACT = 100; // 提取词汇数量
const PARALLEL_REQUESTS = 2; // 并行请求数
const WORDS_PER_REQUEST = 50; // 每请求词汇数
```

### 使用方法

```bash
# 方法1: npm脚本（推荐）
pnpm run sync:freq-words

# 方法2: 直接运行
npx tsx app/lib/scripts/sync6w/run-sync.ts
```

## 🔄 重新匹配未找到的单词 ⭐ 新功能

当同步过程中出现未找到的单词时（如：`398 个成功, 102 个未找到`），使用此功能重新处理。

### 功能特性

- 🔍 自动查找未匹配的单词
- 🌐 重新查询Gemini API
- 💾 导入数据库并设置排名
- 📋 生成详细的失败分析报告
- 🔄 智能重试机制

### 使用方法

```bash
# 重新匹配所有未找到的单词
pnpm run sync:retry-missing
```

### 输出示例

```
🔍 步骤1: 查找未匹配的单词
📋 找到 102 个未匹配的单词

📋 处理计划:
  🔄 需要重新查询: 85 个
  📊 需要更新排名: 17 个

🌐 步骤2: 重新查询单词
📤 查询第 1/2 批: 50 个单词
   ✅ 成功: 45 个单词

💾 步骤3: 导入数据到数据库
📈 导入结果:
  ✅ 成功: 75 个单词
  ❌ 失败: 10 个单词

🎉 重新匹配完成！
```

## 🔍 失败分析工具 ⭐ 新功能

深度分析为什么某些单词无法成功处理，提供详细的失败原因和解决建议。

### 功能特性

- 📊 快速分析所有未匹配单词
- 🔍 深度分析具体单词（API测试、数据库状态）
- 📋 生成可操作的分析报告
- 💡 提供具体的解决建议

### 使用方法

```bash
# 分析所有失败的单词
pnpm run sync:analyze-failures
```

### 分析报告示例

```
📋 失败原因分析报告
==================================================

📊 失败类型统计:
  无效单词: 15 个
  API错误: 5 个
  数据库错误: 2 个

🔄 可重试: 7 个
❌ 不可重试: 15 个

📝 详细分析:
1. "xyz123"
   类型: invalid_word
   原因: API认为这是无效或无法识别的单词
   建议: 检查单词拼写，或者这可能是专有名词、缩写或非标准词汇
   可重试: 否
```

## 🔧 故障排除

### 数据完整性问题

```bash
# 一键解决所有问题
npx tsx data-integrity.ts auto
```

### API调用问题

- 检查网络连接
- 确认Gemini API密钥配置
- 减少并行请求数量

### 数据库问题

- 检查数据库连接
- 确认表结构正确
- 运行数据完整性检查

## 📝 最佳实践

1. **定期检查**: 每日运行 `data-integrity.ts check`
2. **自动修复**: 发现问题时运行 `data-integrity.ts auto`
3. **备份数据**: 重要操作前备份数据库
4. **监控日志**: 关注脚本输出的错误信息

## 🔗 相关文件

- `app/lib/scripts/importDictionaryData.ts` - 数据导入核心逻辑
- `app/lib/providers/gemini.ts` - Gemini API调用
- `prisma/schema.prisma` - 数据库模式定义

---

**注意**: 这个简化版本替代了之前的多个文档文件，提供了更清晰的使用指南。
