#!/usr/bin/env tsx
/**
 * 数据库备份脚本
 * 支持Docker容器化的PostgreSQL数据库备份
 */

import { promises as fs } from 'fs';
import { join, resolve } from 'path';
import { createHash } from 'crypto';
import { stat } from 'fs/promises';
import {
  BackupConfig,
  BackupOptions,
  BackupResult,
  BackupType,
  BackupFormat,
} from './utils/backup-types.js';
import { getBackupConfig, validateBackupConfig } from './utils/backup-config.js';
import { DockerUtils } from './utils/docker-utils.js';

export class DatabaseBackup {
  private config: BackupConfig;
  private dockerUtils: DockerUtils;

  constructor(config?: Partial<BackupConfig>) {
    this.config = { ...getBackupConfig(), ...config };
    this.dockerUtils = new DockerUtils(this.config.docker.containerName);

    // 验证配置
    const validation = validateBackupConfig(this.config);
    if (!validation.valid) {
      throw new Error(`配置验证失败: ${validation.errors.join(', ')}`);
    }
  }

  /**
   * 执行备份
   */
  async backup(options: BackupOptions): Promise<BackupResult> {
    const startTime = Date.now();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);

    console.log(`🚀 开始备份数据库: ${this.config.database.database}`);
    console.log(`📅 时间戳: ${timestamp}`);
    console.log(`📋 备份类型: ${options.type}`);
    console.log(`🗜️ 格式: ${options.format}`);

    try {
      // 1. 检查环境
      await this.checkEnvironment();

      // 2. 确保输出目录存在
      await this.ensureOutputDirectory();

      // 3. 生成备份文件名
      const filename = this.generateFilename(options.type, options.format, timestamp);
      const outputPath = options.outputPath || join(this.config.backup.outputDir, filename);

      console.log(`📁 输出路径: ${outputPath}`);

      // 4. 执行备份
      const backupSuccess = await this.performBackup(options, outputPath, timestamp);

      if (!backupSuccess) {
        throw new Error('备份执行失败');
      }

      // 5. 获取文件信息
      const fileStats = await stat(outputPath);
      const fileSize = fileStats.size;

      // 6. 计算校验和
      const checksum = await this.calculateChecksum(outputPath);

      // 7. 清理旧备份
      await this.cleanupOldBackups();

      const duration = Date.now() - startTime;

      const result: BackupResult = {
        success: true,
        filePath: outputPath,
        fileSize,
        duration,
        backupType: options.type,
        timestamp: new Date().toISOString(),
        checksum,
      };

      console.log(`✅ 备份完成`);
      console.log(`📊 文件大小: ${this.formatFileSize(fileSize)}`);
      console.log(`⏱️ 耗时: ${duration}ms`);
      console.log(`🔒 校验和: ${checksum}`);

      return result;
    } catch (error: any) {
      const duration = Date.now() - startTime;
      console.error(`❌ 备份失败: ${error.message}`);

      return {
        success: false,
        duration,
        backupType: options.type,
        timestamp: new Date().toISOString(),
        error: error.message,
      };
    }
  }

  /**
   * 检查环境
   */
  private async checkEnvironment(): Promise<void> {
    // 检查Docker是否可用
    if (this.config.docker.useDocker) {
      if (!(await this.dockerUtils.isDockerAvailable())) {
        throw new Error('Docker不可用，请确保Docker已安装并运行');
      }

      // 检查容器是否存在且运行
      if (!(await this.dockerUtils.containerExists())) {
        throw new Error(`容器 ${this.config.docker.containerName} 不存在`);
      }

      if (!(await this.dockerUtils.isContainerRunning())) {
        console.log(`🔄 启动容器: ${this.config.docker.containerName}`);
        const started = await this.dockerUtils.startContainer();
        if (!started) {
          throw new Error(`无法启动容器: ${this.config.docker.containerName}`);
        }
      }

      // 测试数据库连接
      const connection = await this.dockerUtils.testDatabaseConnection(
        this.config.database.database,
        this.config.database.username,
        this.config.database.password
      );

      if (!connection.isConnected) {
        throw new Error(`数据库连接失败: ${connection.error}`);
      }

      console.log(`✅ 数据库连接正常 (PostgreSQL ${connection.version})`);
    }
  }

  /**
   * 确保输出目录存在
   */
  private async ensureOutputDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.config.backup.outputDir, { recursive: true });
    } catch (error: any) {
      throw new Error(`无法创建输出目录: ${error.message}`);
    }
  }

  /**
   * 生成备份文件名
   */
  private generateFilename(type: BackupType, format: BackupFormat, timestamp: string): string {
    const extension = this.getFileExtension(format, this.config.backup.compression);
    return `${this.config.backup.filenamePrefix}-${type}-${timestamp}${extension}`;
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(format: BackupFormat, compressed: boolean): string {
    const extensions: Record<BackupFormat, string> = {
      sql: '.sql',
      custom: '.backup',
      tar: '.tar',
      directory: '',
    };

    let ext = extensions[format];
    if (compressed && format !== 'custom') {
      ext += '.gz';
    }

    return ext;
  }

  /**
   * 执行备份操作
   */
  private async performBackup(
    options: BackupOptions,
    outputPath: string,
    timestamp: string
  ): Promise<boolean> {
    const tempFilename = `lucid-bd-backup-${timestamp}${this.getFileExtension(options.format, options.compress)}`;

    try {
      // 在容器内执行pg_dump
      const result = await this.dockerUtils.executePgDump(
        this.config.database.database,
        this.config.database.username,
        this.config.database.password,
        tempFilename,
        {
          format: options.format === 'sql' ? 'plain' : options.format,
          compress: options.compress,
          schemaOnly: options.type === 'schema',
          dataOnly: options.type === 'data',
          excludeTables: options.excludeTables,
          includeTables: options.includeTables,
        }
      );

      if (!result.success) {
        throw new Error(`pg_dump执行失败: ${result.error}`);
      }

      // 从容器复制文件到宿主机
      const copySuccess = await this.dockerUtils.copyFromContainer(
        `/tmp/${tempFilename}`,
        outputPath
      );

      if (!copySuccess) {
        throw new Error('从容器复制备份文件失败');
      }

      // 清理容器内临时文件
      await this.dockerUtils.cleanupTempFiles(`/tmp/${tempFilename}`);

      return true;
    } catch (error) {
      // 清理失败时也要尝试删除临时文件
      await this.dockerUtils.cleanupTempFiles(`/tmp/${tempFilename}`);
      throw error;
    }
  }

  /**
   * 计算文件校验和
   */
  private async calculateChecksum(filePath: string): Promise<string> {
    try {
      const data = await fs.readFile(filePath);
      return createHash('sha256').update(data).digest('hex');
    } catch {
      return '';
    }
  }

  /**
   * 清理旧备份
   */
  private async cleanupOldBackups(): Promise<void> {
    try {
      const files = await fs.readdir(this.config.backup.outputDir);
      const backupFiles = files
        .filter((file) => file.startsWith(this.config.backup.filenamePrefix))
        .map((file) => join(this.config.backup.outputDir, file));

      if (backupFiles.length <= this.config.backup.maxBackups) {
        return;
      }

      // 按修改时间排序，删除最旧的文件
      const fileStats = await Promise.all(
        backupFiles.map(async (file) => ({
          path: file,
          mtime: (await stat(file)).mtime,
        }))
      );

      fileStats.sort((a, b) => a.mtime.getTime() - b.mtime.getTime());

      const filesToDelete = fileStats
        .slice(0, fileStats.length - this.config.backup.maxBackups)
        .map((f) => f.path);

      for (const file of filesToDelete) {
        await fs.unlink(file);
        console.log(`🗑️ 删除旧备份: ${file}`);
      }
    } catch (error: any) {
      console.warn(`⚠️ 清理旧备份时出错: ${error.message}`);
    }
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }
}

/**
 * 命令行入口
 */
async function main() {
  const args = process.argv.slice(2);

  // 解析命令行参数
  const options: BackupOptions = {
    type: (args.find((arg) => arg.startsWith('--type='))?.split('=')[1] as BackupType) || 'full',
    format:
      (args.find((arg) => arg.startsWith('--format='))?.split('=')[1] as BackupFormat) || 'custom',
    compress: !args.includes('--no-compress'),
    verbose: args.includes('--verbose'),
    outputPath: args.find((arg) => arg.startsWith('--output='))?.split('=')[1],
    excludeTables: args
      .find((arg) => arg.startsWith('--exclude='))
      ?.split('=')[1]
      ?.split(','),
    includeTables: args
      .find((arg) => arg.startsWith('--include='))
      ?.split('=')[1]
      ?.split(','),
  };

  if (args.includes('--help')) {
    console.log(`
数据库备份工具

用法:
  tsx backup-database.ts [选项]

选项:
  --type=<type>        备份类型: full|schema|data (默认: full)
  --format=<format>    备份格式: sql|custom|tar (默认: custom)
  --no-compress        禁用压缩
  --output=<path>      指定输出文件路径
  --exclude=<tables>   排除的表名 (逗号分隔)
  --include=<tables>   包含的表名 (逗号分隔)
  --verbose            详细输出
  --help               显示帮助信息

示例:
  tsx backup-database.ts
  tsx backup-database.ts --type=schema --format=sql
  tsx backup-database.ts --exclude=logs,temp
    `);
    return;
  }

  try {
    const backup = new DatabaseBackup();
    const result = await backup.backup(options);

    if (result.success) {
      console.log(`\n✅ 备份成功完成!`);
      console.log(`📁 文件路径: ${result.filePath}`);
      process.exit(0);
    } else {
      console.error(`\n❌ 备份失败: ${result.error}`);
      process.exit(1);
    }
  } catch (error: any) {
    console.error(`\n💥 程序错误: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
