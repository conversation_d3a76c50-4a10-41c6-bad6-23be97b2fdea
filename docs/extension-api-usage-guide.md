# 浏览器扩展 API 使用说明

## 🎯 概述

这些专用 API 端点为浏览器扩展提供了完整的认证解决方案，绕过了 NextAuth.js 的重定向机制，直接返回 JSON 响应。

## 📋 API 端点列表

### 1. 扩展登录 API
- **端点**: `POST /api/auth/extension-signin`
- **功能**: 用户登录，返回 JWT 令牌
- **响应**: JSON 格式（非重定向）

### 2. 扩展令牌刷新 API
- **端点**: `POST /api/auth/extension-refresh`
- **功能**: 使用刷新令牌获取新的访问令牌
- **响应**: JSON 格式的新访问令牌

### 3. 扩展用户验证 API
- **端点**: `GET/POST /api/auth/extension-verify`
- **功能**: 验证访问令牌并返回用户信息
- **响应**: JSON 格式的用户信息

## 🔐 API 详细说明

### 1. 登录 API

#### 请求格式

```bash
POST /api/auth/extension-signin
Content-Type: application/json
```

```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "clientType": "extension"  // 可选，标识客户端类型
}
```

#### 成功响应 (HTTP 200)

```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "User Name",
    "avatar": "https://example.com/avatar.jpg"
  },
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refreshToken": "refresh-token-here",
  "expiresIn": 3600
}
```

#### 失败响应示例

**用户不存在 (HTTP 401):**
```json
{
  "success": false,
  "error": "用户不存在",
  "message": "User not found",
  "code": "USER_NOT_FOUND"
}
```

**密码错误 (HTTP 401):**
```json
{
  "success": false,
  "error": "用户名或密码错误",
  "message": "Invalid email or password",
  "code": "INVALID_CREDENTIALS"
}
```

**缺少字段 (HTTP 400):**
```json
{
  "success": false,
  "error": "缺少必填字段",
  "message": "Missing required fields: email, password",
  "code": "MISSING_FIELDS"
}
```

### 2. 令牌刷新 API

#### 请求格式

```bash
POST /api/auth/extension-refresh
Content-Type: application/json
```

```json
{
  "refreshToken": "your-refresh-token-here"
}
```

#### 成功响应 (HTTP 200)

```json
{
  "success": true,
  "accessToken": "new-access-token-here",
  "expiresIn": 3600
}
```

#### 失败响应 (HTTP 401)

```json
{
  "success": false,
  "error": "无效的刷新令牌",
  "message": "Invalid refresh token",
  "code": "INVALID_REFRESH_TOKEN"
}
```

### 3. 用户验证 API

#### 请求格式 (GET 方式)

```bash
GET /api/auth/extension-verify
Authorization: Bearer your-access-token-here
```

#### 请求格式 (POST 方式)

```bash
POST /api/auth/extension-verify
Content-Type: application/json
Authorization: Bearer your-access-token-here
```

或者在请求体中传递令牌：

```json
{
  "accessToken": "your-access-token-here"
}
```

#### 成功响应 (HTTP 200)

```json
{
  "success": true,
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "User Name",
    "avatar": "https://example.com/avatar.jpg"
  }
}
```

#### 失败响应 (HTTP 401)

```json
{
  "success": false,
  "error": "无效的访问令牌",
  "message": "Invalid access token",
  "code": "INVALID_ACCESS_TOKEN"
}
```

## 💻 前端集成示例

### JavaScript/TypeScript 示例

```typescript
class ExtensionAuthService {
  private baseUrl = 'http://localhost:4001'; // 或你的服务器地址
  
  /**
   * 用户登录
   */
  async signIn(email: string, password: string) {
    try {
      const response = await fetch(`${this.baseUrl}/api/auth/extension-signin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          clientType: 'extension',
        }),
      });

      const data = await response.json();

      if (data.success) {
        // 保存令牌到扩展存储
        await this.saveTokens(data.accessToken, data.refreshToken);
        return { success: true, user: data.user };
      } else {
        return { success: false, error: data.error, code: data.code };
      }
    } catch (error) {
      console.error('Login error:', error);
      return { success: false, error: '网络错误' };
    }
  }

  /**
   * 刷新访问令牌
   */
  async refreshToken() {
    try {
      const refreshToken = await this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token found');
      }

      const response = await fetch(`${this.baseUrl}/api/auth/extension-refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken,
        }),
      });

      const data = await response.json();

      if (data.success) {
        await this.saveAccessToken(data.accessToken);
        return { success: true, accessToken: data.accessToken };
      } else {
        return { success: false, error: data.error, code: data.code };
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      return { success: false, error: '令牌刷新失败' };
    }
  }

  /**
   * 验证用户状态
   */
  async verifyUser() {
    try {
      const accessToken = await this.getAccessToken();
      if (!accessToken) {
        return { success: false, error: 'No access token found' };
      }

      const response = await fetch(`${this.baseUrl}/api/auth/extension-verify`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        return { success: true, user: data.user };
      } else {
        // 如果令牌无效，尝试刷新
        if (data.code === 'INVALID_ACCESS_TOKEN') {
          const refreshResult = await this.refreshToken();
          if (refreshResult.success) {
            // 重新尝试验证
            return await this.verifyUser();
          }
        }
        return { success: false, error: data.error, code: data.code };
      }
    } catch (error) {
      console.error('User verification error:', error);
      return { success: false, error: '验证失败' };
    }
  }

  /**
   * 保存令牌到扩展存储
   */
  private async saveTokens(accessToken: string, refreshToken: string) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.set({
        accessToken,
        refreshToken,
        tokenSavedAt: Date.now(),
      });
    } else {
      // Fallback to localStorage for testing
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('refreshToken', refreshToken);
      localStorage.setItem('tokenSavedAt', Date.now().toString());
    }
  }

  /**
   * 保存访问令牌
   */
  private async saveAccessToken(accessToken: string) {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.set({
        accessToken,
        tokenSavedAt: Date.now(),
      });
    } else {
      localStorage.setItem('accessToken', accessToken);
      localStorage.setItem('tokenSavedAt', Date.now().toString());
    }
  }

  /**
   * 获取访问令牌
   */
  private async getAccessToken(): Promise<string | null> {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get('accessToken');
      return result.accessToken || null;
    } else {
      return localStorage.getItem('accessToken');
    }
  }

  /**
   * 获取刷新令牌
   */
  private async getRefreshToken(): Promise<string | null> {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      const result = await chrome.storage.local.get('refreshToken');
      return result.refreshToken || null;
    } else {
      return localStorage.getItem('refreshToken');
    }
  }

  /**
   * 清除所有令牌（登出）
   */
  async signOut() {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      await chrome.storage.local.remove(['accessToken', 'refreshToken', 'tokenSavedAt']);
    } else {
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('tokenSavedAt');
    }
  }
}

// 使用示例
const authService = new ExtensionAuthService();

// 登录
const loginResult = await authService.signIn('<EMAIL>', 'password123');
if (loginResult.success) {
  console.log('登录成功:', loginResult.user);
} else {
  console.error('登录失败:', loginResult.error);
}

// 验证用户
const verifyResult = await authService.verifyUser();
if (verifyResult.success) {
  console.log('用户验证成功:', verifyResult.user);
} else {
  console.error('用户验证失败:', verifyResult.error);
}
```

### React Hook 示例

```typescript
import { useState, useEffect, useCallback } from 'react';

interface User {
  id: string;
  email: string;
  name: string | null;
  avatar: string | null;
}

interface AuthState {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export function useExtensionAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  });

  const authService = new ExtensionAuthService();

  // 检查用户认证状态
  const checkAuth = useCallback(async () => {
    setAuthState(prev => ({ ...prev, isLoading: true }));
    
    const result = await authService.verifyUser();
    
    setAuthState({
      user: result.success ? result.user : null,
      isLoading: false,
      isAuthenticated: result.success,
    });
  }, []);

  // 登录
  const signIn = useCallback(async (email: string, password: string) => {
    const result = await authService.signIn(email, password);
    
    if (result.success) {
      setAuthState({
        user: result.user,
        isLoading: false,
        isAuthenticated: true,
      });
    }
    
    return result;
  }, []);

  // 登出
  const signOut = useCallback(async () => {
    await authService.signOut();
    setAuthState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
    });
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  return {
    ...authState,
    signIn,
    signOut,
    checkAuth,
  };
}
```

## 🧪 测试命令

### 1. 测试登录 API

```bash
# 测试错误密码
curl -X POST http://localhost:4001/api/auth/extension-signin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword",
    "clientType": "extension"
  }'

# 测试缺少字段
curl -X POST http://localhost:4001/api/auth/extension-signin \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>"}'

# 测试 GET 请求（应该被拒绝）
curl -X GET http://localhost:4001/api/auth/extension-signin
```

### 2. 测试令牌刷新 API

```bash
# 测试无效刷新令牌
curl -X POST http://localhost:4001/api/auth/extension-refresh \
  -H "Content-Type: application/json" \
  -d '{"refreshToken": "invalid-token"}'

# 测试缺少刷新令牌
curl -X POST http://localhost:4001/api/auth/extension-refresh \
  -H "Content-Type: application/json" \
  -d '{}'
```

### 3. 测试用户验证 API

```bash
# 测试无效访问令牌 (GET)
curl -X GET http://localhost:4001/api/auth/extension-verify \
  -H "Authorization: Bearer invalid-token"

# 测试无效访问令牌 (POST)
curl -X POST http://localhost:4001/api/auth/extension-verify \
  -H "Content-Type: application/json" \
  -d '{"accessToken": "invalid-token"}'

# 测试缺少令牌
curl -X GET http://localhost:4001/api/auth/extension-verify
```

## 📊 错误码参考

### 登录 API 错误码

- `MISSING_FIELDS`: 缺少必填字段
- `INVALID_EMAIL_FORMAT`: 邮箱格式不正确
- `USER_NOT_FOUND`: 用户不存在
- `ACCOUNT_DISABLED`: 账户已被禁用
- `NO_PASSWORD_SET`: 该账户需要通过第三方登录
- `INVALID_CREDENTIALS`: 用户名或密码错误
- `SERVER_CONFIG_ERROR`: 服务器配置错误
- `INTERNAL_ERROR`: 服务器内部错误

### 令牌刷新 API 错误码

- `MISSING_REFRESH_TOKEN`: 缺少刷新令牌
- `INVALID_REFRESH_TOKEN`: 无效的刷新令牌
- `INVALID_TOKEN_TYPE`: 令牌类型错误
- `USER_NOT_FOUND`: 用户不存在
- `ACCOUNT_DISABLED`: 账户已被禁用
- `SERVER_CONFIG_ERROR`: 服务器配置错误
- `REFRESH_FAILED`: 令牌刷新失败

### 用户验证 API 错误码

- `MISSING_ACCESS_TOKEN`: 缺少访问令牌
- `INVALID_ACCESS_TOKEN`: 无效的访问令牌
- `INVALID_TOKEN_TYPE`: 令牌类型错误
- `USER_NOT_FOUND`: 用户不存在
- `ACCOUNT_DISABLED`: 账户已被禁用
- `SERVER_CONFIG_ERROR`: 服务器配置错误
- `VERIFICATION_FAILED`: 令牌验证失败

## 🔧 配置要求

### 环境变量

确保在 `.env` 文件中配置了以下环境变量：

```env
# JWT 密钥（与 NextAuth.js 共用）
NEXTAUTH_SECRET=your-nextauth-secret-here

# 或者单独的 JWT 密钥
JWT_SECRET=your-jwt-secret-here

# 数据库连接
DATABASE_URL=your-database-url-here
```

### 安全注意事项

1. **JWT 密钥安全**: 确保 JWT 密钥足够复杂且保密
2. **HTTPS**: 生产环境必须使用 HTTPS
3. **令牌存储**: 在扩展中安全存储令牌，避免暴露给网页
4. **令牌过期**: 访问令牌1小时过期，刷新令牌30天过期
5. **错误信息**: 避免暴露过多敏感信息

## 🎯 优势对比

### 对比 NextAuth.js 原生端点

| 特性 | NextAuth.js | 扩展专用 API |
|------|-------------|--------------|
| 响应格式 | 重定向 | JSON |
| 浏览器扩展兼容 | ❌ | ✅ |
| 错误信息详细 | ❌ | ✅ |
| 自定义响应 | ❌ | ✅ |
| CORS 友好 | ❌ | ✅ |
| 令牌控制 | ❌ | ✅ |

## 📝 总结

这套 API 解决了 NextAuth.js 在浏览器扩展环境下的问题：

1. ✅ **解决重定向问题**: 直接返回 JSON 响应
2. ✅ **完整的错误处理**: 详细的中英文错误信息
3. ✅ **令牌管理**: 完整的 JWT 令牌生命周期管理
4. ✅ **扩展友好**: 专为浏览器扩展环境设计
5. ✅ **向后兼容**: 不影响现有的 NextAuth.js 网页认证

现在你可以在浏览器扩展中使用这些 API 端点来实现完整的用户认证功能！