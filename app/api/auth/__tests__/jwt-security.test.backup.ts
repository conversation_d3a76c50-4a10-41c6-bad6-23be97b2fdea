/**
 * @fileoverview JWT安全机制测试
 * 
 * 测试覆盖范围：
 * - JWT令牌黑名单验证
 * - 刷新令牌轮换机制
 * - JWT签名验证和完整性检查
 * - Token过期时间验证
 * - 令牌吊销场景测试
 * - JWT验证缓存机制
 * - 客户端安全存储要求验证
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';
import jwt from 'jsonwebtoken';

// Mock所有依赖 - 在导入路由之前
vi.mock('jsonwebtoken');
vi.mock('@/lib/1-services/auth/TokenBlacklistService');
vi.mock('@/lib/1-services/auth/JWTValidationCache');
vi.mock('@/lib/utils/env');
vi.mock('@/lib/utils/crypto');
vi.mock('@/lib/utils/redis');
vi.mock('@/lib/4-infrastructure/database/prisma');
vi.mock('@/lib/1-services/practice/PracticeService');
vi.mock('next-auth');
vi.mock('@/lib/utils/logger');
vi.mock('@/lib/1-services/auth/AuthMiddleware');

// Import API routes
import { GET as WordGet } from '@/api/word/route';
import { POST as RefreshPost } from '@/api/auth/refresh/route';

// Mock implementations
const mockJwtSign = jwt.sign as any;
const mockJwtVerify = jwt.verify as any;

// Mock services
const mockTokenBlacklistService = {
  isBlacklisted: vi.fn(),
  blacklist: vi.fn(),
  shouldForceExpireUserTokens: vi.fn(),
  isUserTokenForceExpired: vi.fn(),
};

const mockJwtValidationCache = {
  getCachedValidation: vi.fn(),
  cacheValidation: vi.fn(),
  invalidateUserTokens: vi.fn(),
};

const mockCrypto = {
  generateJTI: vi.fn(),
  generateSecureRandomString: vi.fn(),
};

const mockRedisRateLimiter = {
  checkRateLimit: vi.fn(),
};

const mockPrisma = {
  user: {
    findUnique: vi.fn(),
  },
  refreshToken: {
    findUnique: vi.fn(),
    delete: vi.fn(),
    create: vi.fn(),
  },
};

const mockPracticeService = {
  getUserWordList: vi.fn(),
};

// Mock implementations
vi.doMock('@/lib/1-services/auth/TokenBlacklistService', () => ({
  tokenBlacklistService: mockTokenBlacklistService,
}));

vi.doMock('@/lib/1-services/auth/JWTValidationCache', () => ({
  jwtValidationCache: mockJwtValidationCache,
}));

vi.doMock('@/lib/utils/env', () => ({
  getJWTSecret: () => 'test-jwt-secret-key-with-sufficient-length',
  getJWTRefreshSecret: () => 'test-refresh-secret-key-with-sufficient-length',
}));

vi.doMock('@/lib/utils/crypto', () => mockCrypto);

vi.doMock('@/lib/utils/redis', () => ({
  redisRateLimiter: mockRedisRateLimiter,
}));

vi.doMock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: mockPrisma,
}));

vi.doMock('@/lib/1-services/practice/PracticeService', () => ({
  createPracticeService: () => mockPracticeService,
}));

vi.doMock('next-auth', () => ({
  getServerSession: vi.fn().mockResolvedValue(null), // 默认没有session
}));

vi.doMock('@/lib/utils/logger', () => ({
  Logger: {
    rateLimit: vi.fn(),
  },
}));

// Mock AuthMiddleware - 这是关键的Mock
let shouldAuthenticationSucceed = true;
let authError: any = null;
let mockAuthContext: any = null;

const mockRequireAuth = vi.fn(() => 
  vi.fn((handler: any) => 
    vi.fn(async (request: NextRequest) => {
      if (!shouldAuthenticationSucceed) {
        // 返回认证失败的响应
        return new Response(JSON.stringify({
          success: false,
          error: authError?.message || 'No valid authentication found',
          code: authError?.code || 'UNAUTHORIZED_ERROR',
        }), {
          status: 401,
          headers: { 'Content-Type': 'application/json' },
        });
      }
      
      // 认证成功，调用处理器
      const context = mockAuthContext || {
        user: mockUser,
        authMethod: 'jwt' as const,
        clientType: 'mobile' as const,
        permissions: ['word:read', 'word:write'],
        scopes: ['word:read', 'word:write'],
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
      };
      
      return handler(request, context);
    })
  )
);

vi.doMock('@/lib/1-services/auth/AuthMiddleware', () => ({
  requireAuth: mockRequireAuth,
}));

// Test data
const mockUser = {
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>',
  isActive: true,
};

const mockJWTPayload = {
  sub: 'user-123',
  jti: 'jwt-123',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600, // 1小时后过期
  clientType: 'mobile',
  scopes: ['word:read', 'word:write'],
};

const mockRefreshTokenPayload = {
  sub: 'user-123',
  jti: 'refresh-123',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 604800, // 7天后过期
  type: 'refresh',
};

describe('JWT安全机制测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    
    // 重置认证状态
    shouldAuthenticationSucceed = true;
    authError = null;
    mockAuthContext = null;
    
    // 默认设置
    mockJwtVerify.mockReturnValue(mockJWTPayload);
    mockJwtSign.mockReturnValue('signed-jwt-token');
    mockTokenBlacklistService.isBlacklisted.mockResolvedValue(false);
    mockTokenBlacklistService.shouldForceExpireUserTokens.mockResolvedValue(false);
    mockJwtValidationCache.getCachedValidation.mockResolvedValue(null);
    mockPrisma.user.findUnique.mockResolvedValue(mockUser);
    mockRedisRateLimiter.checkRateLimit.mockResolvedValue({
      allowed: true,
      remaining: 99,
      resetTime: Date.now() / 1000 + 3600,
      total: 1,
    });
    mockCrypto.generateJTI.mockReturnValue('new-jti-123');
    mockPracticeService.getUserWordList.mockResolvedValue({
      words: [],
      pagination: { page: 1, limit: 20, total: 0, totalPages: 0, hasMore: false },
    });
  });

  describe('JWT令牌黑名单验证', () => {
    it('应该拒绝黑名单中的令牌', async () => {
      // Arrange
      shouldAuthenticationSucceed = false;
      authError = { message: 'Token has been revoked', code: 'UNAUTHORIZED_ERROR' };
      mockTokenBlacklistService.isBlacklisted.mockResolvedValue(true);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer blacklisted-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Token has been revoked',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该缓存黑名单令牌的验证失败结果', async () => {
      // Arrange
      mockTokenBlacklistService.isBlacklisted.mockResolvedValue(true);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer blacklisted-token',
        },
      });

      // Act
      await WordGet(request);

      // Assert
      expect(mockJwtValidationCache.cacheValidation).toHaveBeenCalledWith(
        'jwt-123',
        mockJWTPayload,
        false
      );
    });

    it('应该检查用户强制过期令牌', async () => {
      // Arrange
      mockTokenBlacklistService.shouldForceExpireUserTokens.mockResolvedValue(true);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer force-expired-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Token has been force expired due to security changes',
        code: 'UNAUTHORIZED_ERROR',
      });
      expect(mockTokenBlacklistService.shouldForceExpireUserTokens).toHaveBeenCalledWith(
        'user-123',
        mockJWTPayload.iat
      );
    });

    it('应该在用户密码修改后使所有令牌失效', async () => {
      // Arrange
      const oldTokenIssuedTime = Math.floor(Date.now() / 1000) - 3600; // 1小时前签发
      const passwordChangeTime = Math.floor(Date.now() / 1000) - 1800; // 30分钟前密码修改
      
      const oldJWTPayload = {
        ...mockJWTPayload,
        iat: oldTokenIssuedTime,
      };
      
      mockJwtVerify.mockReturnValue(oldJWTPayload);
      mockTokenBlacklistService.shouldForceExpireUserTokens.mockResolvedValue(true);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer old-token-before-password-change',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data.error).toBe('Token has been force expired due to security changes');
    });
  });

  describe('刷新令牌轮换机制', () => {
    it('应该成功轮换refresh token', async () => {
      // Arrange
      mockJwtVerify.mockReturnValue(mockRefreshTokenPayload);
      mockPrisma.refreshToken.findUnique.mockResolvedValue({
        id: 'refresh-token-id',
        userId: 'user-123',
        jti: 'refresh-123',
        isActive: true,
      });
      
      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({
          refreshToken: 'old-refresh-token',
        }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await RefreshPost(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveProperty('accessToken');
      expect(data.data).toHaveProperty('refreshToken');
      expect(data.data).toHaveProperty('expiresIn');
      
      // 验证旧refresh token被加入黑名单
      expect(mockTokenBlacklistService.blacklist).toHaveBeenCalledWith('refresh-123');
      
      // 验证新token被生成
      expect(mockJwtSign).toHaveBeenCalledTimes(2); // access + refresh
    });

    it('应该拒绝已被使用的refresh token', async () => {
      // Arrange
      mockJwtVerify.mockReturnValue(mockRefreshTokenPayload);
      mockTokenBlacklistService.isBlacklisted.mockResolvedValue(true);
      
      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({
          refreshToken: 'used-refresh-token',
        }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await RefreshPost(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Refresh token has been revoked',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该拒绝过期的refresh token', async () => {
      // Arrange
      const expiredRefreshTokenPayload = {
        ...mockRefreshTokenPayload,
        exp: Math.floor(Date.now() / 1000) - 3600, // 1小时前过期
      };
      mockJwtVerify.mockReturnValue(expiredRefreshTokenPayload);
      
      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({
          refreshToken: 'expired-refresh-token',
        }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await RefreshPost(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Refresh token has expired',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该在refresh token轮换时使用新的JTI', async () => {
      // Arrange
      mockJwtVerify.mockReturnValue(mockRefreshTokenPayload);
      mockPrisma.refreshToken.findUnique.mockResolvedValue({
        id: 'refresh-token-id',
        userId: 'user-123',
        jti: 'refresh-123',
        isActive: true,
      });
      mockCrypto.generateJTI
        .mockReturnValueOnce('new-access-jti')
        .mockReturnValueOnce('new-refresh-jti');
      
      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({
          refreshToken: 'valid-refresh-token',
        }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      await RefreshPost(request);

      // Assert
      expect(mockCrypto.generateJTI).toHaveBeenCalledTimes(2);
      expect(mockJwtSign).toHaveBeenCalledWith(
        expect.objectContaining({ jti: 'new-access-jti' }),
        expect.any(String),
        expect.any(Object)
      );
      expect(mockJwtSign).toHaveBeenCalledWith(
        expect.objectContaining({ jti: 'new-refresh-jti' }),
        expect.any(String),
        expect.any(Object)
      );
    });
  });

  describe('JWT签名验证和完整性检查', () => {
    it('应该拒绝无效签名的JWT', async () => {
      // Arrange
      mockJwtVerify.mockImplementation(() => {
        throw new Error('invalid signature');
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer invalid-signature-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'No valid authentication found',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该拒绝格式错误的JWT', async () => {
      // Arrange
      mockJwtVerify.mockImplementation(() => {
        throw new Error('jwt malformed');
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer malformed.jwt.token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data.error).toBe('No valid authentication found');
    });

    it('应该验证JWT的必需字段', async () => {
      // Arrange
      const incompletePayload = {
        sub: 'user-123',
        // 缺少jti字段
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      };
      mockJwtVerify.mockReturnValue(incompletePayload);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer incomplete-payload-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data.error).toBe('No valid authentication found');
    });
  });

  describe('Token过期时间验证', () => {
    it('应该拒绝过期的JWT', async () => {
      // Arrange
      const expiredPayload = {
        ...mockJWTPayload,
        exp: Math.floor(Date.now() / 1000) - 3600, // 1小时前过期
      };
      mockJwtVerify.mockReturnValue(expiredPayload);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer expired-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Token has expired',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该接受即将过期但仍有效的JWT', async () => {
      // Arrange
      const soonToExpirePayload = {
        ...mockJWTPayload,
        exp: Math.floor(Date.now() / 1000) + 60, // 1分钟后过期
      };
      mockJwtVerify.mockReturnValue(soonToExpirePayload);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer soon-to-expire-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('应该缓存过期令牌的验证失败结果', async () => {
      // Arrange
      const expiredPayload = {
        ...mockJWTPayload,
        exp: Math.floor(Date.now() / 1000) - 3600,
      };
      mockJwtVerify.mockReturnValue(expiredPayload);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer expired-token',
        },
      });

      // Act
      await WordGet(request);

      // Assert
      expect(mockJwtValidationCache.cacheValidation).toHaveBeenCalledWith(
        expiredPayload.jti,
        expiredPayload,
        false
      );
    });
  });

  describe('JWT验证缓存机制', () => {
    it('应该使用缓存的成功验证结果', async () => {
      // Arrange
      const cachedValidation = {
        isValid: true,
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read', 'word:write'],
      };
      mockJwtValidationCache.getCachedValidation.mockResolvedValue(cachedValidation);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer cached-valid-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      
      // 验证跳过了耗时的验证步骤
      expect(mockTokenBlacklistService.isBlacklisted).not.toHaveBeenCalled();
      expect(mockPrisma.user.findUnique).not.toHaveBeenCalled();
    });

    it('应该使用缓存的失败验证结果', async () => {
      // Arrange
      const cachedValidation = {
        isValid: false,
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read'],
      };
      mockJwtValidationCache.getCachedValidation.mockResolvedValue(cachedValidation);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer cached-invalid-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'Token validation failed (cached)',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该缓存成功的JWT验证结果', async () => {
      // Arrange
      // 没有缓存，需要完整验证
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer new-valid-token',
        },
      });

      // Act
      await WordGet(request);

      // Assert
      expect(mockJwtValidationCache.cacheValidation).toHaveBeenCalledWith(
        'jwt-123',
        mockJWTPayload,
        true
      );
    });
  });

  describe('速率限制集成测试', () => {
    it('应该对refresh端点应用速率限制', async () => {
      // Arrange
      mockRedisRateLimiter.checkRateLimit.mockResolvedValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() / 1000 + 300,
        total: 11,
      });
      
      const request = new NextRequest('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        body: JSON.stringify({
          refreshToken: 'valid-refresh-token',
        }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await RefreshPost(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(429);
      expect(data).toEqual({
        success: false,
        error: expect.stringContaining('Rate limit exceeded'),
        code: 'RATE_LIMIT_ERROR',
      });
    });

    it('应该对JWT认证的API请求应用速率限制', async () => {
      // Arrange
      mockRedisRateLimiter.checkRateLimit.mockResolvedValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() / 1000 + 300,
        total: 101,
      });
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer valid-jwt-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(429);
      expect(data.error).toContain('Rate limit exceeded');
    });
  });

  describe('用户令牌管理', () => {
    it('应该在用户登出时将所有令牌加入黑名单', async () => {
      // 这个测试模拟用户登出场景，所有该用户的令牌都应该被吊销
      // 在实际应用中，这会通过专门的logout API实现
      
      // Arrange
      const userTokens = ['jwt-1', 'jwt-2', 'jwt-3'];
      
      // Act - 模拟logout操作
      for (const jti of userTokens) {
        await mockTokenBlacklistService.blacklist(jti);
      }

      // Assert
      expect(mockTokenBlacklistService.blacklist).toHaveBeenCalledTimes(3);
      expect(mockTokenBlacklistService.blacklist).toHaveBeenCalledWith('jwt-1');
      expect(mockTokenBlacklistService.blacklist).toHaveBeenCalledWith('jwt-2');
      expect(mockTokenBlacklistService.blacklist).toHaveBeenCalledWith('jwt-3');
    });

    it('应该在密码修改后标记用户令牌为强制过期', async () => {
      // Arrange
      const passwordChangeTime = Math.floor(Date.now() / 1000);
      
      // Act - 模拟密码修改操作
      await mockTokenBlacklistService.shouldForceExpireUserTokens('user-123', passwordChangeTime);

      // Assert
      expect(mockTokenBlacklistService.shouldForceExpireUserTokens).toHaveBeenCalledWith(
        'user-123',
        passwordChangeTime
      );
    });
  });

  describe('安全边界测试', () => {
    it('应该处理非活跃用户的令牌', async () => {
      // Arrange
      const inactiveUser = { ...mockUser, isActive: false };
      mockPrisma.user.findUnique.mockResolvedValue(inactiveUser);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer inactive-user-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data).toEqual({
        success: false,
        error: 'User not found or inactive',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该处理不存在用户的令牌', async () => {
      // Arrange
      mockPrisma.user.findUnique.mockResolvedValue(null);
      
      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer nonexistent-user-token',
        },
      });

      // Act
      const response = await WordGet(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data.error).toBe('User not found or inactive');
    });

    it('应该正确处理令牌重放攻击', async () => {
      // Arrange
      const replayedJTI = 'replayed-jti-123';
      const replayedPayload = { ...mockJWTPayload, jti: replayedJTI };
      mockJwtVerify.mockReturnValue(replayedPayload);
      
      // 第一次请求成功
      mockTokenBlacklistService.isBlacklisted.mockResolvedValueOnce(false);
      // 第二次请求时令牌已在黑名单中
      mockTokenBlacklistService.isBlacklisted.mockResolvedValueOnce(true);
      
      const request1 = new NextRequest('http://localhost:3000/api/word', {
        headers: { authorization: 'Bearer replayed-token' },
      });
      const request2 = new NextRequest('http://localhost:3000/api/word', {
        headers: { authorization: 'Bearer replayed-token' },
      });

      // Act
      const response1 = await WordGet(request1);
      const response2 = await WordGet(request2);

      // Assert
      expect(response1.status).toBe(200); // 第一次成功
      expect(response2.status).toBe(401); // 第二次被拒绝
      
      const data2 = await response2.json();
      expect(data2.error).toBe('Token has been revoked');
    });
  });
});