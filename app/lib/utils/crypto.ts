/**
 * 加密和安全工具函数库
 * 提供统一的加密、哈希和随机数生成功能
 */

import crypto from 'crypto';

/**
 * 生成密码学安全的随机字符串
 */
export function generateSecureRandomString(length: number = 16): string {
  return crypto.randomBytes(length).toString('hex');
}

/**
 * 生成JWT ID (JTI) - 32字符的十六进制字符串
 */
export function generateJTI(): string {
  return generateSecureRandomString(16); // 16 bytes = 32 hex chars
}

/**
 * 生成API密钥或令牌ID
 */
export function generateTokenId(length: number = 32): string {
  return generateSecureRandomString(length);
}

/**
 * 计算字符串的SHA-256哈希值
 */
export function hashString(input: string): string {
  return crypto.createHash('sha256').update(input, 'utf8').digest('hex');
}

/**
 * 计算字符串的SHA-512哈希值（更强的安全性）
 */
export function hashStringSHA512(input: string): string {
  return crypto.createHash('sha512').update(input, 'utf8').digest('hex');
}

/**
 * 使用HMAC进行消息认证码计算
 */
export function createHMAC(message: string, secret: string, algorithm: 'sha256' | 'sha512' = 'sha256'): string {
  return crypto.createHmac(algorithm, secret).update(message, 'utf8').digest('hex');
}

/**
 * 安全比较两个字符串（防止时序攻击）
 */
export function secureCompare(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }
  
  // 使用crypto.timingSafeEqual进行常数时间比较
  const bufA = Buffer.from(a, 'utf8');
  const bufB = Buffer.from(b, 'utf8');
  
  try {
    return crypto.timingSafeEqual(bufA, bufB);
  } catch {
    return false;
  }
}

/**
 * 生成密码学安全的数字（用于验证码等）
 */
export function generateSecureNumeric(digits: number = 6): string {
  const max = Math.pow(10, digits);
  let result: number;
  
  do {
    const bytes = crypto.randomBytes(4);
    result = bytes.readUInt32BE() % max;
  } while (result < max / 10); // 确保有足够的位数
  
  return result.toString().padStart(digits, '0');
}

/**
 * 生成UUID v4（符合RFC 4122）
 */
export function generateUUID(): string {
  return crypto.randomUUID();
}

/**
 * 验证字符串是否为有效的十六进制
 */
export function isValidHex(hex: string): boolean {
  return /^[0-9a-fA-F]+$/.test(hex) && hex.length % 2 === 0;
}

/**
 * 验证字符串是否为有效的UUID
 */
export function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
}

/**
 * 将敏感数据编码为Base64URL（JWT标准）
 */
export function encodeBase64URL(data: string): string {
  return Buffer.from(data, 'utf8')
    .toString('base64')
    .replace(/\+/g, '-')
    .replace(/\//g, '_')
    .replace(/=/g, '');
}

/**
 * 解码Base64URL数据
 */
export function decodeBase64URL(encoded: string): string {
  // 补充填充字符
  const padded = encoded + '='.repeat((4 - (encoded.length % 4)) % 4);
  
  // 替换URL安全字符
  const base64 = padded.replace(/-/g, '+').replace(/_/g, '/');
  
  return Buffer.from(base64, 'base64').toString('utf8');
}

/**
 * 创建密码派生密钥（PBKDF2）
 */
export function deriveKey(
  password: string,
  salt: string,
  iterations: number = 100000,
  keyLength: number = 32,
  digest: string = 'sha512'
): Promise<Buffer> {
  return new Promise((resolve, reject) => {
    crypto.pbkdf2(password, salt, iterations, keyLength, digest, (err, key) => {
      if (err) reject(err);
      else resolve(key);
    });
  });
}

/**
 * 同步版本的密码派生密钥
 */
export function deriveKeySync(
  password: string,
  salt: string,
  iterations: number = 100000,
  keyLength: number = 32,
  digest: string = 'sha512'
): Buffer {
  return crypto.pbkdf2Sync(password, salt, iterations, keyLength, digest);
}