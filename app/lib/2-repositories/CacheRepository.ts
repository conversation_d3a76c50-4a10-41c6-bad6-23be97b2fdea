/**
 * 缓存仓储实现
 * 负责缓存相关的数据访问操作
 */

import { redis, getRedisConnection, releaseRedisConnection } from '../4-infrastructure/cache/redis';
import { ICacheRepository } from './interfaces/IRepository';

export class CacheRepository implements ICacheRepository {
  private readonly defaultTTL = 3600; // 1小时
  private readonly useConnectionPool = process.env.REDIS_USE_CONNECTION_POOL === 'true';

  /**
   * 执行Redis操作，可选择使用连接池或单连接
   */
  private async executeRedisOperation<T>(operation: (client: any) => Promise<T>): Promise<T> {
    if (this.useConnectionPool) {
      const connection = await getRedisConnection();
      try {
        return await operation(connection);
      } finally {
        await releaseRedisConnection(connection);
      }
    } else {
      return await operation(redis);
    }
  }

  /**
   * 获取缓存值
   */
  async get<T>(key: string): Promise<T | null> {
    const cacheId = Math.random().toString(36).substring(2, 8);
    const startTime = performance.now();

    try {
      // 🔍 DEBUG: 缓存获取开始
      const { Logger } = await import('../utils/logger');
      Logger.debug(`[CACHE-${cacheId}] 🎯 Redis GET开始`, {
        cacheId,
        key,
        useConnectionPool: this.useConnectionPool,
      });

      const value = await this.executeRedisOperation(async (client) => {
        return await client.get(key);
      });

      const getTime = performance.now() - startTime;

      if (!value) {
        Logger.debug(`[CACHE-${cacheId}] ❌ Redis GET未命中`, {
          cacheId,
          key,
          getTime: Math.round(getTime),
        });
        return null;
      }

      const parseStart = performance.now();
      const result = JSON.parse(value) as T;
      const parseTime = performance.now() - parseStart;
      const totalTime = performance.now() - startTime;

      Logger.debug(`[CACHE-${cacheId}] ✅ Redis GET命中`, {
        cacheId,
        key,
        getTime: Math.round(getTime),
        parseTime: Math.round(parseTime),
        totalTime: Math.round(totalTime),
        dataSize: value.length,
      });

      return result;
    } catch (error) {
      const errorTime = performance.now() - startTime;

      // 🔍 DEBUG: 缓存获取失败
      const { Logger } = await import('../utils/logger');
      Logger.debug(`[CACHE-${cacheId}] ❌ Redis GET失败`, {
        cacheId,
        key,
        errorTime: Math.round(errorTime),
        error: error instanceof Error ? error.message : String(error),
      });

      console.error('Cache get error:', error);
      return null;
    }
  }

  /**
   * 设置缓存值
   */
  async set<T>(key: string, value: T, ttlSeconds?: number): Promise<boolean> {
    const cacheId = Math.random().toString(36).substring(2, 8);
    const startTime = performance.now();

    try {
      // 🔍 DEBUG: 缓存设置开始
      const { Logger } = await import('../utils/logger');
      const ttl = ttlSeconds || this.defaultTTL;

      Logger.debug(`[CACHE-${cacheId}] 💾 Redis SET开始`, {
        cacheId,
        key,
        ttl,
        useConnectionPool: this.useConnectionPool,
      });

      const serializeStart = performance.now();
      const serialized = JSON.stringify(value);
      const serializeTime = performance.now() - serializeStart;

      const setStart = performance.now();
      await this.executeRedisOperation(async (client) => {
        return await client.setex(key, ttl, serialized);
      });
      const setTime = performance.now() - setStart;
      const totalTime = performance.now() - startTime;

      Logger.debug(`[CACHE-${cacheId}] ✅ Redis SET完成`, {
        cacheId,
        key,
        serializeTime: Math.round(serializeTime),
        setTime: Math.round(setTime),
        totalTime: Math.round(totalTime),
        dataSize: serialized.length,
        ttl,
      });

      return true;
    } catch (error) {
      const errorTime = performance.now() - startTime;

      // 🔍 DEBUG: 缓存设置失败
      const { Logger } = await import('../utils/logger');
      Logger.debug(`[CACHE-${cacheId}] ❌ Redis SET失败`, {
        cacheId,
        key,
        errorTime: Math.round(errorTime),
        error: error instanceof Error ? error.message : String(error),
      });

      console.error('Cache set error:', error);
      return false;
    }
  }

  /**
   * 删除缓存
   */
  async delete(key: string): Promise<boolean> {
    try {
      const result = await redis.del(key);
      return result > 0;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await redis.exists(key);
      return result > 0;
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  /**
   * 批量获取
   */
  async mget<T>(keys: string[]): Promise<(T | null)[]> {
    try {
      if (keys.length === 0) return [];

      const values = await redis.mget(...keys);
      return values.map((value) => {
        if (!value) return null;
        try {
          return JSON.parse(value) as T;
        } catch {
          return null;
        }
      });
    } catch (error) {
      console.error('Cache mget error:', error);
      return keys.map(() => null);
    }
  }

  /**
   * 批量设置
   */
  async mset<T>(keyValues: Array<{ key: string; value: T; ttl?: number }>): Promise<boolean> {
    try {
      if (keyValues.length === 0) return true;

      // Redis mset 不支持TTL，所以我们需要分别设置
      const promises = keyValues.map(({ key, value, ttl }) => this.set(key, value, ttl));

      const results = await Promise.all(promises);
      return results.every((result) => result);
    } catch (error) {
      console.error('Cache mset error:', error);
      return false;
    }
  }

  /**
   * 获取缓存统计
   */
  async getStats(): Promise<{
    hitRate: number;
    totalKeys: number;
    memoryUsage: number;
  }> {
    try {
      const info = await redis.info('stats');
      const memory = await redis.info('memory');

      // 解析Redis INFO命令的输出
      const statsLines = info.split('\r\n');
      const memoryLines = memory.split('\r\n');

      let hits = 0;
      let misses = 0;
      let totalKeys = 0;
      let memoryUsage = 0;

      // 解析统计信息
      for (const line of statsLines) {
        if (line.startsWith('keyspace_hits:')) {
          hits = parseInt(line.split(':')[1]);
        } else if (line.startsWith('keyspace_misses:')) {
          misses = parseInt(line.split(':')[1]);
        }
      }

      // 解析内存信息
      for (const line of memoryLines) {
        if (line.startsWith('used_memory:')) {
          memoryUsage = parseInt(line.split(':')[1]);
        }
      }

      // 获取总键数
      const dbInfo = await redis.info('keyspace');
      const dbLines = dbInfo.split('\r\n');
      for (const line of dbLines) {
        if (line.startsWith('db0:')) {
          const match = line.match(/keys=(\d+)/);
          if (match) {
            totalKeys = parseInt(match[1]);
          }
        }
      }

      const hitRate = hits + misses > 0 ? hits / (hits + misses) : 0;

      return {
        hitRate,
        totalKeys,
        memoryUsage,
      };
    } catch (error) {
      console.error('Cache stats error:', error);
      return {
        hitRate: 0,
        totalKeys: 0,
        memoryUsage: 0,
      };
    }
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<boolean> {
    try {
      await redis.flushdb();
      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  }

  /**
   * 设置带过期时间的缓存
   */
  async setWithExpiry<T>(key: string, value: T, expiryDate: Date): Promise<boolean> {
    try {
      const now = new Date();
      const ttlSeconds = Math.max(0, Math.floor((expiryDate.getTime() - now.getTime()) / 1000));

      if (ttlSeconds <= 0) {
        return false; // 已过期
      }

      return this.set(key, value, ttlSeconds);
    } catch (error) {
      console.error('Cache setWithExpiry error:', error);
      return false;
    }
  }

  /**
   * 获取缓存剩余TTL
   */
  async getTTL(key: string): Promise<number> {
    try {
      return await redis.ttl(key);
    } catch (error) {
      console.error('Cache getTTL error:', error);
      return -1;
    }
  }

  /**
   * 延长缓存过期时间
   */
  async extend(key: string, additionalSeconds: number): Promise<boolean> {
    try {
      const currentTTL = await this.getTTL(key);
      if (currentTTL <= 0) return false;

      const newTTL = currentTTL + additionalSeconds;
      await redis.expire(key, newTTL);
      return true;
    } catch (error) {
      console.error('Cache extend error:', error);
      return false;
    }
  }

  /**
   * 原子性递增
   */
  async increment(key: string, delta: number = 1): Promise<number> {
    try {
      return await redis.incrby(key, delta);
    } catch (error) {
      console.error('Cache increment error:', error);
      return 0;
    }
  }

  /**
   * 获取匹配模式的所有键
   */
  async getKeys(pattern: string): Promise<string[]> {
    try {
      return await redis.keys(pattern);
    } catch (error) {
      console.error('Cache getKeys error:', error);
      return [];
    }
  }

  /**
   * 批量删除匹配模式的键
   */
  async deletePattern(pattern: string): Promise<number> {
    try {
      const keys = await this.getKeys(pattern);
      if (keys.length === 0) return 0;

      return await redis.del(...keys);
    } catch (error) {
      console.error('Cache deletePattern error:', error);
      return 0;
    }
  }
}
