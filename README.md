This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

### Development with Docker

1. Start the PostgreSQL and Redis services:

```bash
docker compose up -d db redis
```

2. Run the development server:

```bash
pnpm dev
```

### Development without Docker

Run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## 🛠️ Environment Configuration

This project includes a powerful environment configuration management system for optimizing performance in different scenarios.

### 📋 Available Commands

```bash
# View current configuration
pnpm run env:show

# List available presets
pnpm run env:list

# Switch environments
pnpm run env:dev     # Development mode
pnpm run env:debug   # Debug mode (detailed logging)
pnpm run env:perf    # High-concurrency mode (performance testing)
pnpm run env:prod    # Production mode
pnpm run env:test    # Performance testing mode
```

### 🎯 Quick Memory Guide

- `env:show` - 看配置 (View configuration)
- `env:list` - 看选项 (View options)
- `env:dev` - 开发用 (Development)
- `env:debug` - 调试用 (Debugging)
- `env:perf` - 测性能 (Performance testing)
- `env:prod` - 上生产 (Production)
- `env:test` - 做基准 (Benchmarking)

### Configuration Modes

| Mode             | Command              | Use Case              | Log Level | Sampling Rate |
| ---------------- | -------------------- | --------------------- | --------- | ------------- |
| Development      | `pnpm run env:dev`   | Daily development     | INFO      | 10%           |
| Debug            | `pnpm run env:debug` | Troubleshooting       | DEBUG     | 100%          |
| High-Concurrency | `pnpm run env:perf`  | Performance testing   | WARN      | 1%            |
| Production       | `pnpm run env:prod`  | Production deployment | WARN      | 1%            |
| Performance Test | `pnpm run env:test`  | Benchmarking          | INFO      | 5%            |

### 📊 Usage Scenarios

**🔧 Daily Development:**

```bash
pnpm run env:dev && pnpm dev
```

**🐛 Problem Debugging:**

```bash
pnpm run env:debug && pnpm dev
```

**🚀 Performance Testing:**

```bash
pnpm run env:perf && pnpm dev
pnpm test -- --run doc/查词响应时间优化/response-time-optimized.test.ts
```

**🏭 Production Deployment:**

```bash
pnpm run env:prod && pnpm build && pnpm start
```

### Example Workflows

**Performance Testing:**

```bash
pnpm run env:perf    # Switch to high-concurrency mode
pnpm run dev         # Start server
pnpm test            # Run performance tests
```

**Debug Issues:**

```bash
pnpm run env:debug   # Enable detailed logging
pnpm run env:show    # Verify configuration
pnpm run dev         # Start with debug logs
```

For detailed documentation, see [Environment Configuration Guide](doc/查词响应时间优化/环境配置使用指南.md).

## Production Deployment

### Using Docker

To run the application in a production-like environment locally:

```bash
docker compose -f docker/prod.yml up -d
```

This will build the Next.js application and run it alongside PostgreSQL and Redis.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
