# 4. 数据模型与变更 (Data Models and Schema Changes)

为支持新功能，将新增以下数据表，现有核心表（如User）无需重大修改。

- **`Sentence` (句子表)**
  - **用途**: 存储所有句子的中央“公共句库”。
  - **关键字段**: `id`, `content`, `source`, `favoriteCount`, `createdAt`。
- **`UserSavedSentence` (用户收藏句子表)**
  - **用途**: 建立用户和句子之间的多对多“收藏”关系 (Join Table)。
  - **关键字段**: `userId`, `sentenceId`, `savedAt`。
- **`UserWordProficiency` (用户单词熟练度表)**
  - **用途**: 追踪每个用户对每个单词的掌握情况。
  - **关键字段**: `userId`, `wordId`, `practiceCount`, `errorCount`, `averageTime`, `isMarked`, `proficiencyScore`, `lastPracticed`。
- **`PracticeHistory` (练习历史表)**
  - **用途**: 记录每次练习的总结，用于生成WPM历史图表。
  - **关键字段**: `userId`, `wpm`, `accuracy`, `practiceMode`, `completedAt`。
