# 浏览器插件 JWT 认证对接文档

## 概述

本文档详细描述了浏览器插件如何接入 lucid-bd 项目的JWT认证系统。该系统基于 Next.js 15 构建，支持多种客户端类型（web、mobile、extension），并提供完整的JWT认证和刷新令牌机制。

## 认证架构

### 核心组件

1. **认证中间件 (AuthMiddleware)**: 统一的认证处理层
2. **JWT验证缓存 (JWTValidationCache)**: Redis缓存提升性能
3. **令牌黑名单服务 (TokenBlacklistService)**: 令牌撤销管理
4. **多重认证方式**: 支持 Session Cookie 和 Bearer Token

### 支持的客户端类型

- `web`: Web应用
- `mobile`: 移动应用
- `extension`: 浏览器插件 ✅

## API 端点

### 基础URL

生产环境配置： BACKEND_URL=https://lucid-bd.com
开发环境配置： BACKEND_DEV_URL=http://localhost:4000

```
生产环境: ${BACKEND_URL}/api/auth
开发环境: ${BACKEND_DEV_URL}/api/auth
```

### 1. 用户注册/登录

**端点**: `POST /api/auth/register`

**描述**: 智能注册端点，如果用户已存在且密码正确，将自动执行登录

**请求体**:

```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "name": "用户姓名"
}
```

**响应 - 注册成功** (201):

```json
{
  "message": "User created successfully",
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "用户姓名",
    "avatar": null,
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z"
  }
}
```

**响应 - 自动登录** (200):

```json
{
  "message": "Login successful",
  "type": "login",
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "用户姓名",
    "avatar": null,
    "createdAt": "2025-01-01T00:00:00.000Z",
    "updatedAt": "2025-01-01T00:00:00.000Z"
  }
}
```

### 2. NextAuth.js 登录

**端点**: `POST /api/auth/[...nextauth]`

**描述**: 使用NextAuth.js进行认证，支持Credentials和OAuth

**Credentials登录请求**:

```json
{
  "csrfToken": "csrf-token",
  "email": "<EMAIL>",
  "password": "securePassword123",
  "clientType": "extension",
  "redirect": false
}
```

**成功响应**:

```json
{
  "user": {
    "id": "user-uuid",
    "email": "<EMAIL>",
    "name": "用户姓名"
  },
  "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "expiresIn": 3600
}
```

### 3. 刷新访问令牌

**端点**: `POST /api/auth/refresh`

**描述**: 使用Refresh Token获取新的Access Token，实现令牌轮换机制

**请求头**:

```
Content-Type: application/json
```

**请求体**:

```json
{
  "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

**成功响应** (200):

```json
{
  "success": true,
  "data": {
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expiresIn": 3600,
    "tokenType": "Bearer"
  },
  "message": "Token refreshed successfully"
}
```

**错误响应** (401):

```json
{
  "success": false,
  "message": "Refresh token has been revoked",
  "code": "UNAUTHORIZED"
}
```

### 4. 密码重置

**端点**: `POST /api/auth/forgot-password`

**请求体**:

```json
{
  "email": "<EMAIL>"
}
```

**端点**: `POST /api/auth/reset-password`

**请求体**:

```json
{
  "token": "reset-token",
  "password": "newSecurePassword123"
}
```

### 5. 获取CSRF令牌

**端点**: `GET /api/auth/csrf-token`

**响应**:

```json
{
  "csrfToken": "csrf-token-value"
}
```

## JWT 令牌结构

### Access Token Payload

```typescript
interface JWTPayload {
  sub: string; // 用户ID
  email: string; // 用户邮箱
  iat: number; // 签发时间
  exp: number; // 过期时间
  jti: string; // JWT ID (用于撤销)
  clientType: 'extension'; // 客户端类型
  scopes: string[]; // 权限范围
}
```

### Refresh Token Payload

```typescript
interface RefreshTokenPayload extends JWTPayload {
  tokenId: string; // 数据库中的Token记录ID
  purpose: 'refresh'; // 令牌用途
}
```

### 默认权限范围 (Scopes)

- `word:read`: 读取词汇数据
- `word:write`: 写入词汇数据
- `practice:read`: 读取练习数据
- `practice:write`: 写入练习数据

## 浏览器插件集成指南

### 1. 认证流程

```javascript
class AuthManager {
  constructor() {
    this.baseURL = 'https://your-domain.com/api/auth';
    this.accessToken = localStorage.getItem('accessToken');
    this.refreshToken = localStorage.getItem('refreshToken');
  }

  // 登录
  async login(email, password) {
    try {
      // 1. 获取CSRF令牌
      const csrfResponse = await fetch(`${this.baseURL}/csrf-token`);
      const { csrfToken } = await csrfResponse.json();

      // 2. 执行登录
      const response = await fetch(`${this.baseURL}/signin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          csrfToken,
          email,
          password,
          clientType: 'extension',
          redirect: false,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.storeTokens(data.accessToken, data.refreshToken);
        return data;
      } else {
        throw new Error('Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // 存储令牌
  storeTokens(accessToken, refreshToken) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    localStorage.setItem('accessToken', accessToken);
    localStorage.setItem('refreshToken', refreshToken);
  }

  // 刷新令牌
  async refreshAccessToken() {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    try {
      const response = await fetch(`${this.baseURL}/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refreshToken: this.refreshToken,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        this.storeTokens(data.data.accessToken, data.data.refreshToken);
        return data.data;
      } else {
        // 刷新失败，清除令牌并要求重新登录
        this.clearTokens();
        throw new Error('Token refresh failed');
      }
    } catch (error) {
      console.error('Token refresh error:', error);
      this.clearTokens();
      throw error;
    }
  }

  // 清除令牌
  clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
  }

  // 获取Authorization头
  getAuthHeader() {
    return this.accessToken ? `Bearer ${this.accessToken}` : null;
  }
}
```

### 2. 自动令牌刷新拦截器

```javascript
class APIClient {
  constructor(authManager) {
    this.authManager = authManager;
    this.baseURL = 'https://your-domain.com/api';
  }

  async request(url, options = {}) {
    // 添加认证头
    const authHeader = this.authManager.getAuthHeader();
    if (authHeader) {
      options.headers = {
        ...options.headers,
        Authorization: authHeader,
      };
    }

    let response = await fetch(`${this.baseURL}${url}`, options);

    // 如果返回401，尝试刷新令牌
    if (response.status === 401 && this.authManager.refreshToken) {
      try {
        await this.authManager.refreshAccessToken();

        // 使用新令牌重试请求
        options.headers = {
          ...options.headers,
          Authorization: this.authManager.getAuthHeader(),
        };

        response = await fetch(`${this.baseURL}${url}`, options);
      } catch (error) {
        // 刷新失败，跳转到登录页面
        this.handleAuthenticationError();
        throw error;
      }
    }

    return response;
  }

  handleAuthenticationError() {
    // 插件特定的认证错误处理
    chrome.tabs.create({
      url: chrome.runtime.getURL('popup.html?auth=required'),
    });
  }
}
```

### 3. Chrome Extension Manifest V3 配置

```json
{
  "manifest_version": 3,
  "name": "Lucid BD Extension",
  "version": "1.0.0",
  "permissions": ["storage", "activeTab"],
  "host_permissions": ["https://your-domain.com/*"],
  "background": {
    "service_worker": "background.js"
  },
  "action": {
    "default_popup": "popup.html"
  },
  "content_scripts": [
    {
      "matches": ["<all_urls>"],
      "js": ["content.js"]
    }
  ]
}
```

### 4. Service Worker 中的认证管理

```javascript
// background.js
class BackgroundAuthManager {
  constructor() {
    this.setupMessageListener();
  }

  setupMessageListener() {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      if (request.action === 'authenticate') {
        this.handleAuthentication(request.credentials)
          .then(sendResponse)
          .catch((error) => sendResponse({ error: error.message }));
        return true; // 异步响应
      }
    });
  }

  async handleAuthentication(credentials) {
    const authManager = new AuthManager();
    try {
      const result = await authManager.login(credentials.email, credentials.password);

      // 存储到Chrome Storage
      await chrome.storage.local.set({
        accessToken: result.accessToken,
        refreshToken: result.refreshToken,
        user: result.user,
      });

      return { success: true, user: result.user };
    } catch (error) {
      throw error;
    }
  }
}

new BackgroundAuthManager();
```

## 安全机制

### 1. 令牌安全特性

- **JWT签名验证**: 使用HS256算法签名
- **令牌轮换**: 每次刷新生成新的RefreshToken
- **黑名单机制**: 立即撤销已使用的RefreshToken
- **强制过期**: 支持因安全变更强制所有令牌过期
- **缓存验证**: Redis缓存提升验证性能

### 2. 速率限制

- **刷新限制**: 每分钟最多10次令牌刷新
- **登录限制**: 防止暴力破解攻击
- **IP地址跟踪**: 记录客户端IP用于安全分析

### 3. 最佳安全实践

```javascript
// 安全的令牌存储（推荐使用Chrome Storage API）
class SecureStorage {
  static async storeTokens(accessToken, refreshToken) {
    await chrome.storage.local.set({
      accessToken: accessToken,
      refreshToken: refreshToken,
      tokenExpiresAt: Date.now() + 3600 * 1000, // 1小时后过期
    });
  }

  static async getTokens() {
    const result = await chrome.storage.local.get([
      'accessToken',
      'refreshToken',
      'tokenExpiresAt',
    ]);

    // 检查Access Token是否即将过期
    if (result.tokenExpiresAt && Date.now() > result.tokenExpiresAt - 5 * 60 * 1000) {
      // 提前5分钟刷新令牌
      return { ...result, shouldRefresh: true };
    }

    return result;
  }

  static async clearTokens() {
    await chrome.storage.local.remove(['accessToken', 'refreshToken', 'tokenExpiresAt']);
  }
}
```

## 错误处理

### 常见错误代码

| 错误码              | HTTP状态 | 描述           | 处理建议                     |
| ------------------- | -------- | -------------- | ---------------------------- |
| UNAUTHORIZED        | 401      | 令牌无效或过期 | 尝试刷新令牌，失败则重新登录 |
| FORBIDDEN           | 403      | 权限不足       | 检查客户端类型和权限范围     |
| VALIDATION_ERROR    | 400      | 请求参数错误   | 检查请求格式和必填字段       |
| RATE_LIMIT_EXCEEDED | 429      | 超出速率限制   | 延迟重试或提示用户           |
| TOKEN_REVOKED       | 401      | 令牌已被撤销   | 清除本地令牌，重新登录       |

### 错误处理示例

```javascript
class ErrorHandler {
  static handle(error, response) {
    switch (response.status) {
      case 401:
        if (error.code === 'TOKEN_REVOKED') {
          return this.handleTokenRevoked();
        }
        return this.handleUnauthorized();

      case 403:
        return this.handleForbidden();

      case 429:
        return this.handleRateLimit();

      default:
        return this.handleGenericError(error);
    }
  }

  static handleTokenRevoked() {
    // 清除所有本地令牌
    SecureStorage.clearTokens();
    // 显示重新登录提示
    this.showLoginRequired('您的登录已失效，请重新登录');
  }

  static handleUnauthorized() {
    // 尝试刷新令牌
    return authManager.refreshAccessToken().catch(() => {
      this.showLoginRequired('登录已过期，请重新登录');
    });
  }

  static handleRateLimit() {
    this.showError('请求过于频繁，请稍后再试');
  }
}
```

## 开发环境配置

### 1. 环境变量设置

```bash
# .env.local
DATABASE_URL="postgresql://username:password@localhost:5432/lucid_bd"
REDIS_URL="redis://localhost:6379"
JWT_SECRET="your-jwt-secret-key-at-least-32-characters"
JWT_REFRESH_SECRET="your-jwt-refresh-secret-key-at-least-32-characters"
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:4000"
```

### 2. 测试用户创建

```bash
# 使用API创建测试用户
curl -X POST http://localhost:3000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testPassword123",
    "name": "测试用户"
  }'
```

### 3. JWT令牌测试

```javascript
// 测试令牌验证
async function testTokenValidation() {
  const token = 'your-access-token';

  const response = await fetch('http://localhost:3000/api/protected-endpoint', {
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  console.log('Token validation result:', response.status);
}
```

## 生产环境部署

### 1. 安全配置检查清单

- [ ] JWT密钥使用强随机字符串（至少32字符）
- [ ] 启用HTTPS，禁用HTTP
- [ ] 配置CORS策略，仅允许授权域名
- [ ] 启用Rate Limiting
- [ ] 配置Redis连接池
- [ ] 设置合理的令牌过期时间
- [ ] 配置日志记录和监控

### 2. 性能优化

- **连接池**: 配置数据库和Redis连接池
- **缓存策略**: JWT验证结果缓存5分钟
- **压缩**: 启用响应压缩
- **CDN**: 静态资源使用CDN

### 3. 监控和日志

```javascript
// 认证事件日志
class AuthLogger {
  static logLoginAttempt(email, success, clientType, ip) {
    console.log(
      JSON.stringify({
        event: 'login_attempt',
        email,
        success,
        clientType,
        ip,
        timestamp: new Date().toISOString(),
      })
    );
  }

  static logTokenRefresh(userId, success, ip) {
    console.log(
      JSON.stringify({
        event: 'token_refresh',
        userId,
        success,
        ip,
        timestamp: new Date().toISOString(),
      })
    );
  }
}
```

## 常见问题 (FAQ)

### Q1: 浏览器插件如何处理跨域问题？

A: 在manifest.json中配置正确的host_permissions，确保包含API域名。

### Q2: 如何处理令牌刷新失败？

A: 当refresh token过期或被撤销时，应清除本地存储的所有令牌，并引导用户重新登录。

### Q3: 插件在后台如何保持认证状态？

A: 使用Chrome Storage API存储令牌，并在service worker中实现定期令牌验证和刷新逻辑。

### Q4: 如何确保插件的安全性？

A:

- 不在内容脚本中处理敏感信息
- 使用HTTPS API调用
- 定期清理过期令牌
- 实现CSP策略

### Q5: 支持哪些权限范围？

A: 当前支持的scopes：

- `word:read` - 读取词汇数据
- `word:write` - 写入词汇数据
- `practice:read` - 读取练习数据
- `practice:write` - 写入练习数据

更多权限范围可根据需要扩展。

## 版本历史

| 版本  | 日期       | 更新内容                  |
| ----- | ---------- | ------------------------- |
| 1.0.0 | 2025-01-01 | 初始版本，支持基础JWT认证 |

## 联系支持

如有问题或需要技术支持，请联系开发团队。

---

_本文档持续更新，请关注最新版本。_
