/**
 * 测试数据库连接池配置
 * 验证Prisma连接池是否正确设置
 */

import { prisma } from '../app/lib/4-infrastructure/database/prisma';

async function testConnectionPool() {
  console.log('🔧 测试数据库连接池配置...\n');

  try {
    // 1. 测试基本连接
    console.log('1. 测试基本数据库连接...');
    await prisma.$connect();
    console.log('✅ 数据库连接成功\n');

    // 2. 执行一个简单查询来触发连接池创建
    console.log('2. 执行查询以触发连接池创建...');
    const count = await prisma.vocabulary.count();
    console.log(`✅ 查询成功，词汇表中有 ${count} 条记录\n`);

    // 3. 获取连接池指标（如果可用）
    console.log('3. 尝试获取连接池指标...');
    try {
      // 注意：metrics功能需要在schema.prisma中启用previewFeatures = ["metrics"]
      const metrics = await (prisma as any).$metrics?.json();
      if (metrics) {
        console.log('📊 连接池指标:');

        // 查找连接池相关的指标
        const poolMetrics =
          metrics.gauges?.filter((gauge: any) => gauge.key.includes('pool_connections')) || [];

        poolMetrics.forEach((metric: any) => {
          console.log(`   ${metric.description}: ${metric.value}`);
        });

        const counters =
          metrics.counters?.filter((counter: any) => counter.key.includes('pool_connections')) ||
          [];

        counters.forEach((counter: any) => {
          console.log(`   ${counter.description}: ${counter.value}`);
        });
      } else {
        console.log('⚠️ 连接池指标不可用（需要启用metrics预览功能）');
      }
    } catch (error) {
      console.log('⚠️ 无法获取连接池指标:', error instanceof Error ? error.message : String(error));
    }

    console.log('\n4. 测试并发查询...');

    // 4. 测试并发查询以验证连接池
    const startTime = Date.now();
    const concurrentQueries = Array.from({ length: 10 }, (_, i) =>
      prisma.vocabulary.findFirst({
        where: { id: i + 1 },
        select: { id: true, word: true },
      })
    );

    const results = await Promise.all(concurrentQueries);
    const endTime = Date.now();

    const successCount = results.filter((r) => r !== null).length;
    console.log(`✅ 并发查询完成: ${successCount}/10 成功, 耗时: ${endTime - startTime}ms\n`);

    // 5. 检查环境变量配置
    console.log('5. 检查环境变量配置:');
    console.log(
      `   DATABASE_URL: ${process.env.DATABASE_URL?.replace(/\/\/[^:]+:[^@]+@/, '//***:***@')}`
    );
    console.log(`   DB_CONNECTION_LIMIT: ${process.env.DB_CONNECTION_LIMIT}`);
    console.log(`   DB_POOL_TIMEOUT: ${process.env.DB_POOL_TIMEOUT}`);
    console.log(`   NODE_ENV: ${process.env.NODE_ENV}\n`);

    // 6. 测试连接池压力
    console.log('6. 测试连接池压力（20个并发查询）...');
    const pressureStartTime = Date.now();

    const pressureQueries = Array.from({ length: 20 }, (_, i) =>
      prisma.vocabulary.findMany({
        take: 1,
        skip: i,
        select: { id: true, word: true },
      })
    );

    const pressureResults = await Promise.all(pressureQueries);
    const pressureEndTime = Date.now();

    console.log(
      `✅ 压力测试完成: ${pressureResults.length}/20 成功, 耗时: ${pressureEndTime - pressureStartTime}ms\n`
    );

    console.log('🎉 连接池测试完成！');
  } catch (error) {
    console.error('❌ 连接池测试失败:', error);
  } finally {
    await prisma.$disconnect();
    console.log('🔌 数据库连接已关闭');
  }
}

// 运行测试
testConnectionPool().catch(console.error);
