/**
 * Authentication Constants Unit Tests
 * 认证常量单元测试
 */

import { describe, it, expect } from 'vitest';
import {
  TOKEN_CONSTANTS,
  AUTH_ROUTES,
  API_ROUTES,
  VALIDATION_RULES,
  RATE_LIMITS,
  SECURITY_CONFIG,
} from '../index';

describe('Authentication Constants', () => {
  describe('TOKEN_CONSTANTS', () => {
    it('should have correct token expiration times', () => {
      expect(TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN).toBe(15 * 60); // 15 minutes
      expect(TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN).toBe(30 * 24 * 60 * 60); // 30 days
    });

    it('should have correct token type', () => {
      expect(TOKEN_CONSTANTS.TOKEN_TYPE).toBe('Bearer');
    });

    it('should have correct JWT algorithm', () => {
      expect(TOKEN_CONSTANTS.JWT_ALGORITHM).toBe('RS256');
    });

    it('should have correct issuer and audience', () => {
      expect(TOKEN_CONSTANTS.JWT_ISSUER).toBe('lucid-bd');
      expect(TOKEN_CONSTANTS.JWT_AUDIENCE).toBe('lucid-bd-users');
    });
  });

  describe('AUTH_ROUTES', () => {
    it('should have all authentication routes', () => {
      expect(AUTH_ROUTES.SIGN_IN).toBe('/auth/signin');
      expect(AUTH_ROUTES.SIGN_UP).toBe('/auth/signup');
      expect(AUTH_ROUTES.FORGOT_PASSWORD).toBe('/auth/forgot-password');
      expect(AUTH_ROUTES.RESET_PASSWORD).toBe('/auth/reset-password');
      expect(AUTH_ROUTES.VERIFY_EMAIL).toBe('/auth/verify-email');
      expect(AUTH_ROUTES.ERROR).toBe('/auth/error');
      expect(AUTH_ROUTES.DEFAULT_REDIRECT).toBe('/dashboard');
      expect(AUTH_ROUTES.LOGIN_REDIRECT).toBe('/auth/signin');
    });

    it('should have consistent route structure', () => {
      // All auth routes should start with /auth/
      const authRoutes = [
        AUTH_ROUTES.SIGN_IN,
        AUTH_ROUTES.SIGN_UP,
        AUTH_ROUTES.FORGOT_PASSWORD,
        AUTH_ROUTES.RESET_PASSWORD,
        AUTH_ROUTES.VERIFY_EMAIL,
        AUTH_ROUTES.ERROR,
      ];

      authRoutes.forEach((route) => {
        expect(route).toMatch(/^\/auth\//);
      });
    });
  });

  describe('API_ROUTES', () => {
    it('should have all API routes', () => {
      expect(API_ROUTES.NEXTAUTH).toBe('/api/auth');
      expect(API_ROUTES.REGISTER).toBe('/api/auth/register');
      expect(API_ROUTES.TOKEN_LOGIN).toBe('/api/auth/token/login');
      expect(API_ROUTES.TOKEN_REFRESH).toBe('/api/auth/token/refresh');
      expect(API_ROUTES.TOKEN_LOGOUT).toBe('/api/auth/token/logout');
      expect(API_ROUTES.GOOGLE_LOGIN).toBe('/api/auth/token/google');
      expect(API_ROUTES.GITHUB_LOGIN).toBe('/api/auth/token/github');
      expect(API_ROUTES.USER_PROFILE).toBe('/api/user/profile');
      expect(API_ROUTES.USER_EXPORT).toBe('/api/user/export');
      expect(API_ROUTES.USER_DELETE).toBe('/api/user/account');
      expect(API_ROUTES.AUDIT_LOGS).toBe('/api/user/audit-logs');
      expect(API_ROUTES.ADMIN_AUDIT_LOGS).toBe('/api/admin/audit-logs');
    });

    it('should have consistent API route structure', () => {
      // All API routes should start with /api/
      Object.values(API_ROUTES).forEach((route) => {
        expect(route).toMatch(/^\/api\//);
      });
    });
  });

  describe('VALIDATION_RULES', () => {
    it('should have email validation regex', () => {
      expect(VALIDATION_RULES.EMAIL_REGEX).toBeInstanceOf(RegExp);

      // Test valid emails
      expect(VALIDATION_RULES.EMAIL_REGEX.test('<EMAIL>')).toBe(true);
      expect(VALIDATION_RULES.EMAIL_REGEX.test('<EMAIL>')).toBe(true);
      expect(VALIDATION_RULES.EMAIL_REGEX.test('<EMAIL>')).toBe(true);

      // Test invalid emails
      expect(VALIDATION_RULES.EMAIL_REGEX.test('invalid.email')).toBe(false);
      expect(VALIDATION_RULES.EMAIL_REGEX.test('@example.com')).toBe(false);
      expect(VALIDATION_RULES.EMAIL_REGEX.test('user@')).toBe(false);
      expect(VALIDATION_RULES.EMAIL_REGEX.test('user@.com')).toBe(false);
    });

    it('should have password requirements', () => {
      expect(VALIDATION_RULES.PASSWORD_MIN_LENGTH).toBe(8);
      expect(VALIDATION_RULES.PASSWORD_MAX_LENGTH).toBe(128);
      expect(VALIDATION_RULES.PASSWORD_REQUIRE_NUMBERS).toBe(true);
      expect(VALIDATION_RULES.PASSWORD_REQUIRE_LETTERS).toBe(true);
      expect(VALIDATION_RULES.PASSWORD_REQUIRE_SPECIAL).toBe(true);
    });

    it('should have name requirements', () => {
      expect(VALIDATION_RULES.NAME_MIN_LENGTH).toBe(2);
      expect(VALIDATION_RULES.NAME_MAX_LENGTH).toBe(50);
    });
  });

  describe('RATE_LIMITS', () => {
    it('should have reasonable rate limits', () => {
      expect(RATE_LIMITS.LOGIN_PER_IP_PER_MINUTE).toBe(5);
      expect(RATE_LIMITS.LOGIN_PER_USER_PER_MINUTE).toBe(3);
      expect(RATE_LIMITS.REFRESH_PER_TOKEN_PER_MINUTE).toBe(10);
      expect(RATE_LIMITS.REGISTER_PER_IP_PER_HOUR).toBe(3);
      expect(RATE_LIMITS.PASSWORD_RESET_PER_EMAIL_PER_HOUR).toBe(2);
    });

    it('should have all rate limits as positive integers', () => {
      Object.values(RATE_LIMITS).forEach((limit) => {
        expect(typeof limit).toBe('number');
        expect(limit).toBeGreaterThan(0);
        expect(Number.isInteger(limit)).toBe(true);
      });
    });
  });

  describe('SECURITY_CONFIG', () => {
    it('should have secure bcrypt configuration', () => {
      expect(SECURITY_CONFIG.BCRYPT_ROUNDS).toBe(12);
      expect(SECURITY_CONFIG.BCRYPT_ROUNDS).toBeGreaterThanOrEqual(10); // Minimum security
    });

    it('should have login attempt limits', () => {
      expect(SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS).toBe(5);
      expect(SECURITY_CONFIG.ACCOUNT_LOCK_DURATION).toBe(24); // hours
    });

    it('should have session configuration', () => {
      expect(SECURITY_CONFIG.SESSION_TIMEOUT).toBe(24); // hours
      expect(SECURITY_CONFIG.REMEMBER_ME_DURATION).toBe(30); // days
    });

    it('should have CSRF protection enabled', () => {
      expect(SECURITY_CONFIG.CSRF_PROTECTION).toBe(true);
    });

    it('should have proper security headers', () => {
      expect(SECURITY_CONFIG.SECURITY_HEADERS).toEqual({
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
      });
    });

    it('should have all security values as expected types', () => {
      expect(typeof SECURITY_CONFIG.BCRYPT_ROUNDS).toBe('number');
      expect(typeof SECURITY_CONFIG.MAX_LOGIN_ATTEMPTS).toBe('number');
      expect(typeof SECURITY_CONFIG.ACCOUNT_LOCK_DURATION).toBe('number');
      expect(typeof SECURITY_CONFIG.SESSION_TIMEOUT).toBe('number');
      expect(typeof SECURITY_CONFIG.REMEMBER_ME_DURATION).toBe('number');
      expect(typeof SECURITY_CONFIG.CSRF_PROTECTION).toBe('boolean');
      expect(typeof SECURITY_CONFIG.SECURITY_HEADERS).toBe('object');
    });
  });

  describe('Cross-constant consistency', () => {
    it('should have consistent route references', () => {
      // Login redirect should point to sign in page
      expect(AUTH_ROUTES.LOGIN_REDIRECT).toBe(AUTH_ROUTES.SIGN_IN);
    });

    it('should have reasonable token expiration relationship', () => {
      // Refresh token should expire much later than access token
      expect(TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN).toBeGreaterThan(
        TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN * 100
      );
    });

    it('should have consistent API route structure', () => {
      // Token APIs should be under /api/auth/token/
      expect(API_ROUTES.TOKEN_LOGIN).toMatch(/^\/api\/auth\/token\//);
      expect(API_ROUTES.TOKEN_REFRESH).toMatch(/^\/api\/auth\/token\//);
      expect(API_ROUTES.TOKEN_LOGOUT).toMatch(/^\/api\/auth\/token\//);
    });
  });
});
