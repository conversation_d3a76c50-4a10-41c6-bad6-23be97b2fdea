/**
 * Next.js Authentication Middleware
 * Implements route protection and JWT verification
 *
 * Security Features:
 * - JWT token verification
 * - Route-based protection
 * - Rate limiting integration
 * - Security headers injection
 * - Request logging
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from 'next-auth/middleware';
import { getClientIP, logSecurityEvent } from '@/lib/auth/simple-security';
import { CSRFProtection, SecurityHeadersManager } from '@/lib/utils/cookies';

// Protected routes configuration
const PROTECTED_ROUTES = [
  '/api/user',
  '/api/admin',
  '/api/protected',
  '/dashboard',
  '/profile',
  '/settings',
];

// API routes that require authentication
const PROTECTED_API_ROUTES = [
  '/api/user/',
  '/api/admin/',
  '/api/protected/',
  '/api/dictionary/favorites',
  '/api/dictionary/history',
];

// Routes that require CSRF protection for write operations
const CSRF_PROTECTED_ROUTES = [
  '/api/auth/secure-token',
  '/api/auth/logout',
  '/api/user/',
  '/api/admin/',
];

// Routes that should be accessible without authentication
const PUBLIC_ROUTES = [
  '/',
  '/login',
  '/signup',
  '/forgot-password',
  '/api/auth',
  '/api/health',
  '/api/dictionary', // Public dictionary access
  '/home',
];

// Simplified rate limiting is now handled in simple-security-middleware.ts

function isProtectedRoute(pathname: string): boolean {
  return (
    PROTECTED_ROUTES.some((route) => pathname.startsWith(route)) ||
    PROTECTED_API_ROUTES.some((route) => pathname.startsWith(route))
  );
}

function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some((route) => {
    if (route === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(route);
  });
}

function needsCSRFProtection(request: NextRequest): boolean {
  const pathname = request.nextUrl.pathname;
  const method = request.method;

  // Only check CSRF for write operations
  const isWriteOperation = ['POST', 'PUT', 'DELETE', 'PATCH'].includes(method);

  return isWriteOperation && CSRF_PROTECTED_ROUTES.some((route) => pathname.startsWith(route));
}

function isAuthenticatedViaCookie(request: NextRequest): boolean {
  // Cookie authentication is now handled by NextAuth
  // This function kept for backward compatibility but always returns false
  return false;
}

// Rate limiting is now handled in simple-security-middleware.ts

// Security headers are now handled in simple-security-middleware.ts

// Create a combined middleware that supports both NextAuth and custom auth
const authMiddleware = withAuth(
  async function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    const clientIP = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';
    // Handle OPTIONS requests for CORS
    if (request.method === 'OPTIONS') {
      const response = new NextResponse(null, { status: 200 });
      SecurityHeadersManager.addSecurityHeaders(response, request);
      return response;
    }

    // CSRF Protection Check
    if (needsCSRFProtection(request)) {
      if (!(await CSRFProtection.validateCSRFToken(request))) {
        logSecurityEvent('CSRF_VALIDATION_FAILED', {
          pathname,
          ip: clientIP,
          userAgent,
          method: request.method,
        });

        return NextResponse.json({ error: 'CSRF token validation failed' }, { status: 403 });
      }
    }

    // Skip middleware for public routes
    if (isPublicRoute(pathname)) {
      const response = NextResponse.next();
      SecurityHeadersManager.addSecurityHeaders(response, request);
      return response;
    }

    // Check if route requires protection
    if (isProtectedRoute(pathname)) {
      try {
        // Cookie authentication is now handled by NextAuth middleware

        // Authentication handled by NextAuth middleware

        // NextAuth session authentication is handled by the withAuth wrapper
        // If we reach this point, authentication failed

        // No valid authentication found
        logSecurityEvent('UNAUTHORIZED_ACCESS', { pathname, ip: clientIP, userAgent });

        // Redirect to login for page requests
        if (!pathname.startsWith('/api/')) {
          const loginUrl = new URL('/login', request.url);
          loginUrl.searchParams.set('callbackUrl', pathname);
          return NextResponse.redirect(loginUrl);
        }

        // Return 401 for API requests
        const errorResponse = NextResponse.json(
          { error: 'Authentication required' },
          { status: 401 }
        );
        SecurityHeadersManager.addSecurityHeaders(errorResponse, request);
        return errorResponse;
      } catch (error) {
        console.error('Middleware error:', error);

        // Log middleware error as suspicious activity
        logSecurityEvent('MIDDLEWARE_ERROR', {
          pathname,
          ip: clientIP,
          userAgent,
          error: error instanceof Error ? error.message : 'Unknown error',
        });

        // Return 500 for unexpected errors
        const errorResponse = NextResponse.json(
          { error: 'Internal server error' },
          { status: 500 }
        );
        SecurityHeadersManager.addSecurityHeaders(errorResponse, request);
        return errorResponse;
      }
    }

    // Default response for non-protected routes
    const response = NextResponse.next();
    SecurityHeadersManager.addSecurityHeaders(response, request);
    return response;
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl;

        // Always allow access to public routes
        if (isPublicRoute(pathname)) {
          return true;
        }

        // For protected routes, check if user is authenticated
        if (isProtectedRoute(pathname)) {
          return !!token;
        }

        return true;
      },
    },
  }
);

export default authMiddleware;

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
};
