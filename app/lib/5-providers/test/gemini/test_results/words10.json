{"error": {"message": "Cannot read properties of undefined (reading 'catch')", "name": "TypeError", "stack": "TypeError: Cannot read properties of undefined (reading 'catch')\n    at ApiClient.apiCall (file:///Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/node_modules/.pnpm/@google+genai@1.0.1_@modelcontextprotocol+sdk@1.12.0/node_modules/@google/genai/src/_api_client.ts:610:35)\n    at ApiClient.unaryApiCall (file:///Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/node_modules/.pnpm/@google+genai@1.0.1_@modelcontextprotocol+sdk@1.12.0/node_modules/@google/genai/src/_api_client.ts:496:17)\n    at ApiClient.request (file:///Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/node_modules/.pnpm/@google+genai@1.0.1_@modelcontextprotocol+sdk@1.12.0/node_modules/@google/genai/src/_api_client.ts:411:17)\n    at Models.generateContent (file:///Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/node_modules/.pnpm/@google+genai@1.0.1_@modelcontextprotocol+sdk@1.12.0/node_modules/@google/genai/src/models.ts:78:14)\n    at Module.fetchDictionary (/Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/app/lib/5-providers/gemini.ts:75:25)\n    at /Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/app/lib/5-providers/test/gemini/gemini.test.ts:219:20\n    at file:///Users/<USER>/Workspace/tools/extension/lucid-group/lucid-bd/node_modules/.pnpm/@vitest+runner@3.1.4/node_modules/@vitest/runner/dist/index.js:596:20"}, "statusCode": 500}