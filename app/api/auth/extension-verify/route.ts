/**
 * 浏览器扩展专用用户验证 API
 * 验证 JWT 令牌并返回用户信息
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import jwt from 'jsonwebtoken';

// 响应体类型定义
interface ExtensionVerifyResponse {
  success: boolean;
  user?: {
    id: string;
    email: string;
    name: string | null;
    avatar: string | null;
  };
  error?: string;
  message?: string;
  code?: string;
}

// JWT 载荷类型定义
interface JWTPayload {
  sub: string;
  email: string;
  name: string | null;
  type: 'access' | 'refresh';
  iat: number;
  exp: number;
}

/**
 * 从请求头中提取 JWT 令牌
 */
function extractTokenFromHeader(authHeader: string | null): string | null {
  if (!authHeader) return null;
  
  // 支持 "Bearer TOKEN" 和 "TOKEN" 两种格式
  const parts = authHeader.split(' ');
  if (parts.length === 2 && parts[0] === 'Bearer') {
    return parts[1];
  } else if (parts.length === 1) {
    return parts[0];
  }
  
  return null;
}

/**
 * 浏览器扩展用户验证 API
 * GET /api/auth/extension-verify
 */
export async function GET(request: NextRequest): Promise<NextResponse<ExtensionVerifyResponse>> {
  try {
    // 从请求头中获取 Authorization 令牌
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少访问令牌",
          message: "Missing access token",
          code: "MISSING_ACCESS_TOKEN"
        },
        { status: 401 }
      );
    }

    // 获取 JWT 密钥
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not configured');
      return NextResponse.json(
        {
          success: false,
          error: "服务器配置错误",
          message: "Server configuration error",
          code: "SERVER_CONFIG_ERROR"
        },
        { status: 500 }
      );
    }

    // 验证 JWT 令牌
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    } catch (error) {
      console.error('Invalid access token:', error);
      return NextResponse.json(
        {
          success: false,
          error: "无效的访问令牌",
          message: "Invalid access token",
          code: "INVALID_ACCESS_TOKEN"
        },
        { status: 401 }
      );
    }

    // 检查令牌类型
    if (decoded.type !== 'access') {
      return NextResponse.json(
        {
          success: false,
          error: "令牌类型错误",
          message: "Invalid token type",
          code: "INVALID_TOKEN_TYPE"
        },
        { status: 401 }
      );
    }

    // 从数据库获取最新的用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.sub },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: "用户不存在",
          message: "User not found",
          code: "USER_NOT_FOUND"
        },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: "账户已被禁用",
          message: "Account is disabled",
          code: "ACCOUNT_DISABLED"
        },
        { status: 401 }
      );
    }

    // 返回成功响应
    return NextResponse.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
        },
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Token verification error:', error);
    
    // 返回通用错误响应
    return NextResponse.json(
      {
        success: false,
        error: "令牌验证失败",
        message: "Token verification failed",
        code: "VERIFICATION_FAILED"
      },
      { status: 500 }
    );
  }
}

/**
 * 支持 POST 请求（令牌可以在请求体中）
 */
export async function POST(request: NextRequest): Promise<NextResponse<ExtensionVerifyResponse>> {
  try {
    // 优先从请求头获取令牌
    const authHeader = request.headers.get('authorization');
    let token = extractTokenFromHeader(authHeader);

    // 如果请求头中没有令牌，尝试从请求体获取
    if (!token) {
      try {
        const body = await request.json();
        token = body.accessToken;
      } catch (error) {
        // 忽略 JSON 解析错误，继续处理
      }
    }

    if (!token) {
      return NextResponse.json(
        {
          success: false,
          error: "缺少访问令牌",
          message: "Missing access token",
          code: "MISSING_ACCESS_TOKEN"
        },
        { status: 401 }
      );
    }

    // 获取 JWT 密钥
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not configured');
      return NextResponse.json(
        {
          success: false,
          error: "服务器配置错误",
          message: "Server configuration error",
          code: "SERVER_CONFIG_ERROR"
        },
        { status: 500 }
      );
    }

    // 验证 JWT 令牌
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(token, jwtSecret) as JWTPayload;
    } catch (error) {
      console.error('Invalid access token:', error);
      return NextResponse.json(
        {
          success: false,
          error: "无效的访问令牌",
          message: "Invalid access token",
          code: "INVALID_ACCESS_TOKEN"
        },
        { status: 401 }
      );
    }

    // 检查令牌类型
    if (decoded.type !== 'access') {
      return NextResponse.json(
        {
          success: false,
          error: "令牌类型错误",
          message: "Invalid token type",
          code: "INVALID_TOKEN_TYPE"
        },
        { status: 401 }
      );
    }

    // 从数据库获取最新的用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.sub },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: "用户不存在",
          message: "User not found",
          code: "USER_NOT_FOUND"
        },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: "账户已被禁用",
          message: "Account is disabled",
          code: "ACCOUNT_DISABLED"
        },
        { status: 401 }
      );
    }

    // 返回成功响应
    return NextResponse.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
        },
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Token verification error:', error);
    
    // 返回通用错误响应
    return NextResponse.json(
      {
        success: false,
        error: "令牌验证失败",
        message: "Token verification failed",
        code: "VERIFICATION_FAILED"
      },
      { status: 500 }
    );
  }
}

/**
 * 不支持其他 HTTP 方法
 */
export async function PUT(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}