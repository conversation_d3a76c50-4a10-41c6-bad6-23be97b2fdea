import { GoogleGenAI } from '@google/genai';
import 'dotenv/config';
import { zodToJsonSchema } from 'zod-to-json-schema';
import {
  DictionaryResponse,
  DictionaryResponseWithWordCountType,
  HttpStatusCode,
  DictionaryQueryResult,
  processApiResponse,
} from '../utils/dict-types.js';
import { processTokens, logPerformance, DEFAULT_WORD_LIST } from '../utils/dict-utils.js';

/**
 * 默认示例词列表（逗号分隔）。
 * 可以根据需要替换成任意词列表以获取相应释义。
 */
const DEFAULT_PROMPT = 'world,apple,banana,cherry,date,elderberry,fig,grape,honeydew,wiki';

// 默认 Gemini 模型
const DEFAULT_MODEL = 'gemini-2.5-flash';
// const DEFAULT_MODEL = 'gemini-2.5-flash-lite-preview-06-17';

// 初始化默认 Gemini 客户端
const defaultClient = new GoogleGenAI({
  apiKey: process.env.GEMINI_KEY || '',
});

/**
 * 从 Gemini API 获取单词释义
 * @param {string} [customPrompt=DEFAULT_WORD_LIST] - 自定义单词列表（逗号分隔），不传则使用默认词表
 * @param {GoogleGenAI} [client=defaultClient] - 自定义 Gemini 客户端
 * @param {string} [model=DEFAULT_MODEL] - 自定义模型
 * @returns {Promise<DictionaryQueryResult>} 返回字典查询结果，包含数据、错误信息、状态码等
 */
export const fetchDictionary = async (
  customPrompt: string = DEFAULT_WORD_LIST,
  client: GoogleGenAI = defaultClient,
  model: string = DEFAULT_MODEL
): Promise<DictionaryQueryResult> => {
  const startTime = Date.now();
  const logApiTime = logPerformance(startTime, 'Gemini API调用');

  // 处理并验证输入词条
  const { validTokens, invalidTokens } = processTokens(customPrompt);

  // 如果全部非法，直接返回 400
  if (validTokens.length === 0) {
    return {
      error: { message: '全部词条非法，已取消查询' },
      info: { invalidTokens },
      statusCode: HttpStatusCode.BAD_REQUEST,
    };
  }

  try {
    // 转换 Zod schema 为 JSON schema，禁用$ref引用
    const jsonSchema = zodToJsonSchema(DictionaryResponse, {
      target: 'jsonSchema7',
      $refStrategy: 'none', // 禁用引用，直接内联所有类型定义
    });

    // 创建生成配置
    const generationConfig = {
      responseMimeType: 'application/json',
      responseSchema: jsonSchema,
      // 添加思考配置，设置为 0 关闭思考功能
      thinkingConfig: {
        thinkingBudget: 0, // 0 = 关闭思考，-1 = 动态思考，正数 = 指定思考 token 数量
      },
    };

    const apiCallStartTime = Date.now();

    // 调用 Gemini API
    const apiResponse = await client.models.generateContent({
      model: model,
      contents: [
        {
          role: 'user',
          parts: [
            {
              text: `You are a helpful dictionary. Respond ONLY with a JSON object that conforms to the provided JSON schema.
Do not include any other text or markdown formatting.

The input words may include:
1. Common English words
2. Technical abbreviations (e.g., 'k8s', 'i18n')
3. Brand names (e.g., '1password')
4. Proper nouns
5. Phrases or word combinations with spaces (e.g., 'good job', 'machine learning')

Treat all these as valid terms for definition.

For each word or phrase provided:
1. For EVERY word, you MUST include a "wordFormats" array that identifies its base form (原型):
   - If the word itself is a base form (like "date", "banana"), include { "form": "[word itself]", "name": "原型" }
   - If the word is a derived form (like "dates", "bananas"), include { "form": "[base form]", "name": "原型" }

2. Additionally, for derived forms:
   - If the word is a plural (like "bananas"), include both its base form AND indicate that the current word is the plural form of its base
   - If the word is a past tense (like "dated"), include both its base form AND indicate that the current word is the past tense form of its base
   - Use similar pattern for other derived forms (现在分词, 过去分词, 第三人称单数, etc.)

3. The "word" field in your response MUST be the exact form provided in the input (e.g., if "bananas" is input, "word" field must be "bananas", not "banana").

4. For words or phrases NOT found in your knowledge base:
   - Add them to an 'illegalWords' array in the response
   - For EACH illegal word, find up to 1 most similar valid words based on spelling or meaning
   - Create a separate 'suggests' array in your response that follows EXACTLY the same structure as the 'words' array
   - Each entry in 'suggests' should be a full dictionary entry (with complete word details, explanations, phonetics etc.) for the similar words you identified
   - For example, if "appple" was input, you would add "appple" to 'illegalWords' and add complete dictionary entries for words like "apple" to the 'suggests' array

Provide definitions for the following words: ${validTokens.join(',')}`,
            },
          ],
        },
      ],
      config: generationConfig,
    });

    const apiCallEndTime = Date.now();
    const apiCallDuration = (apiCallEndTime - apiCallStartTime) / 1000;
    console.log(`API call completed in ${apiCallDuration.toFixed(2)} seconds`);

    // 检查API响应
    if (!apiResponse.text) {
      return {
        error: { message: '未找到词条或解析失败' },
        statusCode: HttpStatusCode.NOT_FOUND,
      };
    }

    // 处理API响应
    const result = processApiResponse(apiResponse.text, invalidTokens);

    // {{CHENGQI:
    // Action: Modified (Commented out)
    // Timestamp: [2025-05-27 01:10:00 +08:00]
    // Reason: 根据用户反馈，移除（通过注释）详细的LLM响应日志，以避免日志文件过大和控制台输出过多信息。
    // Principle_Applied: User-centric (响应用户需求), KISS (简单地注释掉不需要的代码)
    // Optimization: N/A (移除日志，非性能优化)
    // Architectural_Note (AR): 调整日志级别/详细度，减少不必要的日志输出。
    // Documentation_Note (DW): 标记相关的日志代码已被注释。
    // }}
    // {{START MODIFICATIONS}}
    /*
    if (result.data) {
      console.log('Processed Gemini API data (result.data):');
      console.log(JSON.stringify(result.data, null, 2)); // 使用2个空格缩进，更紧凑
    } else if (apiResponse.text) {
      // 如果没有result.data (可能processApiResponse失败或返回空)，但有原始文本，则记录原始文本
      console.log('Gemini API raw response text (since result.data is null/undefined):');
      try {
        console.log(JSON.stringify(JSON.parse(apiResponse.text), null, 2));
      } catch (e) {
        console.log(apiResponse.text); // 如果解析失败，直接打印原始文本
      }
    }
    */
    // {{END MODIFICATIONS}}

    // 记录总耗时
    logApiTime();

    return result;
  } catch (error: unknown) {
    console.error('Error calling Gemini API:', error);

    // 类型守卫，检查 error 是否具有 status 属性
    const errorStatus =
      error && typeof error === 'object' && 'status' in error
        ? (error.status as number)
        : HttpStatusCode.INTERNAL_SERVER_ERROR;

    // 改进错误信息的序列化
    let errorInfo: Record<string, any> = {};

    if (error instanceof Error) {
      errorInfo = {
        message: error.message,
        name: error.name,
        stack: error.stack,
      };
      // 捕获额外属性
      Object.entries(error).forEach(([key, value]) => {
        errorInfo[key] = value;
      });
    } else if (error && typeof error === 'object') {
      try {
        // 尝试复制所有可枚举属性
        errorInfo = { ...error };
      } catch (e) {
        errorInfo = {
          description: String(error),
          serializationError: String(e),
        };
      }
    } else {
      errorInfo = { description: String(error) };
    }

    return {
      error: errorInfo,
      statusCode: errorStatus,
    };
  }
};
