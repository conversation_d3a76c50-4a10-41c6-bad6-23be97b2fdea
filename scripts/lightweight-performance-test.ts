/**
 * Lightweight Security Middleware Performance Test Suite
 *
 * Comprehensive testing of the lightweight security middleware system
 * with focus on performance targets and load testing
 *
 * Performance Targets:
 * - Minimal: <5ms/request
 * - Standard: <10ms/request
 * - Strict: <25ms/request
 * - Concurrency: 100-150 requests
 * - Cache Hit Rate: >90%
 * - Memory Usage: <50MB for 150 concurrent
 * - Performance Improvement: 70-80% over original
 */

import { NextRequest } from 'next/server';
import {
  LightweightSecurityMiddleware,
  SecurityLevel,
} from '../app/lib/auth/lightweight-security-middleware';
import { createPerformanceCache } from '../app/lib/auth/cache-optimization';

// ============================================================================
// Test Configuration
// ============================================================================

interface TestConfig {
  maxConcurrency: number;
  testDuration: number;
  warmupDuration: number;
  requestsPerSecond: number;
  responseTimeTargets: {
    minimal: number;
    standard: number;
    strict: number;
  };
  cacheHitRateTarget: number;
  memoryUsageTarget: number;
}

const DEFAULT_TEST_CONFIG: TestConfig = {
  maxConcurrency: 150,
  testDuration: 60000, // 60 seconds
  warmupDuration: 10000, // 10 seconds
  requestsPerSecond: 1000,
  responseTimeTargets: {
    minimal: 5,
    standard: 10,
    strict: 25,
  },
  cacheHitRateTarget: 0.9, // 90%
  memoryUsageTarget: 50 * 1024 * 1024, // 50MB
};

// ============================================================================
// Test Request Generator
// ============================================================================

class TestRequestGenerator {
  private static testPatterns = [
    // Normal requests
    {
      path: '/api/user/profile',
      method: 'GET',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    },
    {
      path: '/api/dictionary/en/test',
      method: 'GET',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    },
    {
      path: '/api/auth/login',
      method: 'POST',
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    },

    // Potentially malicious requests
    { path: '/api/user/profile?id=1 OR 1=1', method: 'GET', userAgent: 'sqlmap/1.0' },
    { path: '/api/dictionary/en/<script>alert(1)</script>', method: 'GET', userAgent: 'bot' },
    { path: '/api/auth/../../../etc/passwd', method: 'GET', userAgent: 'hack' },
    { path: '/api/admin/config', method: 'GET', userAgent: 'Mozilla/5.0' },
    { path: '/api/user/profile?cmd=ls', method: 'GET', userAgent: 'curl/7.68.0' },

    // Bot requests
    { path: '/api/dictionary/en/robot', method: 'GET', userAgent: 'Googlebot/2.1' },
    { path: '/api/user/stats', method: 'GET', userAgent: 'bingbot/2.0' },

    // Cache-friendly requests (repeated)
    {
      path: '/api/dictionary/en/hello',
      method: 'GET',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    },
    {
      path: '/api/user/profile',
      method: 'GET',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    },
    {
      path: '/api/dictionary/en/world',
      method: 'GET',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    },
  ];

  static generateRequest(ip?: string): NextRequest {
    const pattern = this.testPatterns[Math.floor(Math.random() * this.testPatterns.length)];
    const testIP = ip || this.generateRandomIP();

    const url = new URL(pattern.path, 'http://localhost:3000');
    const request = new NextRequest(url, {
      method: pattern.method,
      headers: {
        'user-agent': pattern.userAgent,
        'x-forwarded-for': testIP,
        'x-real-ip': testIP,
        'content-type': 'application/json',
        accept: 'application/json',
      },
    });

    // Add IP to request object for testing
    (request as any).ip = testIP;

    return request;
  }

  private static generateRandomIP(): string {
    return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  }

  static generateCacheableRequest(pattern: string): NextRequest {
    const url = new URL(`/api/dictionary/en/${pattern}`, 'http://localhost:3000');
    const request = new NextRequest(url, {
      method: 'GET',
      headers: {
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'x-forwarded-for': '*************',
        'content-type': 'application/json',
      },
    });

    (request as any).ip = '*************';
    return request;
  }
}

// ============================================================================
// Performance Metrics Collector
// ============================================================================

class PerformanceMetrics {
  private metrics: Map<string, number[]> = new Map();
  private counters: Map<string, number> = new Map();
  private startTime: number = 0;
  private memoryUsage: number[] = [];

  start(): void {
    this.startTime = performance.now();
    this.startMemoryMonitoring();
  }

  recordResponseTime(level: SecurityLevel, responseTime: number): void {
    const key = `responseTime_${level}`;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }
    this.metrics.get(key)!.push(responseTime);
  }

  recordCacheHit(hit: boolean): void {
    const hitKey = hit ? 'cacheHits' : 'cacheMisses';
    this.counters.set(hitKey, (this.counters.get(hitKey) || 0) + 1);
  }

  recordThroughput(requestsCount: number): void {
    this.counters.set('totalRequests', requestsCount);
  }

  recordSecurityResult(allowed: boolean, level: SecurityLevel): void {
    const key = `${level}_${allowed ? 'allowed' : 'blocked'}`;
    this.counters.set(key, (this.counters.get(key) || 0) + 1);
  }

  private startMemoryMonitoring(): void {
    const interval = setInterval(() => {
      const memUsage = process.memoryUsage();
      this.memoryUsage.push(memUsage.heapUsed);
    }, 1000);

    setTimeout(() => {
      clearInterval(interval);
    }, 65000); // Monitor for test duration + buffer
  }

  getResults(): PerformanceTestResults {
    const elapsedTime = performance.now() - this.startTime;
    const totalRequests = this.counters.get('totalRequests') || 0;
    const throughput = (totalRequests / elapsedTime) * 1000; // requests per second

    const results: PerformanceTestResults = {
      elapsedTime,
      totalRequests,
      throughput,
      responseTimeStats: this.calculateResponseTimeStats(),
      cacheStats: this.calculateCacheStats(),
      memoryStats: this.calculateMemoryStats(),
      securityStats: this.calculateSecurityStats(),
    };

    return results;
  }

  private calculateResponseTimeStats(): ResponseTimeStats {
    const stats: ResponseTimeStats = {
      minimal: this.calculateStats('responseTime_minimal'),
      standard: this.calculateStats('responseTime_standard'),
      strict: this.calculateStats('responseTime_strict'),
    };

    return stats;
  }

  private calculateStats(key: string): StatsData {
    const values = this.metrics.get(key) || [];
    if (values.length === 0) {
      return { avg: 0, min: 0, max: 0, p50: 0, p95: 0, p99: 0, count: 0 };
    }

    const sorted = values.sort((a, b) => a - b);
    const count = sorted.length;
    const sum = sorted.reduce((a, b) => a + b, 0);

    return {
      avg: sum / count,
      min: sorted[0],
      max: sorted[count - 1],
      p50: sorted[Math.floor(count * 0.5)],
      p95: sorted[Math.floor(count * 0.95)],
      p99: sorted[Math.floor(count * 0.99)],
      count,
    };
  }

  private calculateCacheStats(): CacheStats {
    const hits = this.counters.get('cacheHits') || 0;
    const misses = this.counters.get('cacheMisses') || 0;
    const total = hits + misses;

    return {
      hits,
      misses,
      hitRate: total > 0 ? hits / total : 0,
      totalRequests: total,
    };
  }

  private calculateMemoryStats(): MemoryStats {
    if (this.memoryUsage.length === 0) {
      return { current: 0, peak: 0, average: 0, samples: 0 };
    }

    const current = this.memoryUsage[this.memoryUsage.length - 1];
    const peak = Math.max(...this.memoryUsage);
    const average = this.memoryUsage.reduce((a, b) => a + b, 0) / this.memoryUsage.length;

    return {
      current,
      peak,
      average,
      samples: this.memoryUsage.length,
    };
  }

  private calculateSecurityStats(): SecurityStats {
    const levels: SecurityLevel[] = ['minimal', 'standard', 'strict'];
    const stats: SecurityStats = {};

    levels.forEach((level) => {
      const allowed = this.counters.get(`${level}_allowed`) || 0;
      const blocked = this.counters.get(`${level}_blocked`) || 0;
      const total = allowed + blocked;

      stats[level] = {
        allowed,
        blocked,
        total,
        blockRate: total > 0 ? blocked / total : 0,
      };
    });

    return stats;
  }
}

// ============================================================================
// Result Types
// ============================================================================

interface StatsData {
  avg: number;
  min: number;
  max: number;
  p50: number;
  p95: number;
  p99: number;
  count: number;
}

interface ResponseTimeStats {
  minimal: StatsData;
  standard: StatsData;
  strict: StatsData;
}

interface CacheStats {
  hits: number;
  misses: number;
  hitRate: number;
  totalRequests: number;
}

interface MemoryStats {
  current: number;
  peak: number;
  average: number;
  samples: number;
}

interface SecurityStats {
  [level: string]: {
    allowed: number;
    blocked: number;
    total: number;
    blockRate: number;
  };
}

interface PerformanceTestResults {
  elapsedTime: number;
  totalRequests: number;
  throughput: number;
  responseTimeStats: ResponseTimeStats;
  cacheStats: CacheStats;
  memoryStats: MemoryStats;
  securityStats: SecurityStats;
}

// ============================================================================
// Performance Test Runner
// ============================================================================

class LightweightSecurityPerformanceTest {
  private config: TestConfig;
  private metrics: PerformanceMetrics;
  private middleware: LightweightSecurityMiddleware;

  constructor(config: Partial<TestConfig> = {}) {
    this.config = { ...DEFAULT_TEST_CONFIG, ...config };
    this.metrics = new PerformanceMetrics();
    this.middleware = new LightweightSecurityMiddleware({
      enableCaching: true,
      enableThreatDetection: true,
      enableRateLimiting: true,
      enableFingerprinting: true,
      performanceMode: true,
      maxConcurrency: this.config.maxConcurrency,
      cacheSize: 2000,
    });
  }

  async runComprehensiveTest(): Promise<PerformanceTestResults> {
    console.log('🚀 Starting Lightweight Security Middleware Performance Test');
    console.log(
      `📊 Configuration: ${this.config.maxConcurrency} max concurrency, ${this.config.testDuration}ms duration`
    );
    console.log('');

    // Warmup phase
    console.log('🔥 Warming up...');
    await this.runWarmup();

    // Main test phase
    console.log('⚡ Starting main performance test...');
    this.metrics.start();

    const testPromises: Promise<void>[] = [];

    // Test each security level
    const levels: SecurityLevel[] = ['minimal', 'standard', 'strict'];

    for (const level of levels) {
      testPromises.push(this.testSecurityLevel(level));
    }

    // Cache effectiveness test
    testPromises.push(this.testCacheEffectiveness());

    // Concurrent load test
    testPromises.push(this.testConcurrentLoad());

    await Promise.all(testPromises);

    const results = this.metrics.getResults();
    this.printResults(results);

    return results;
  }

  private async runWarmup(): Promise<void> {
    const warmupRequests = 100;
    const requests: Promise<void>[] = [];

    for (let i = 0; i < warmupRequests; i++) {
      requests.push(this.processRequest('standard'));
    }

    await Promise.all(requests);
    console.log(`✅ Warmup completed (${warmupRequests} requests)`);
  }

  private async testSecurityLevel(level: SecurityLevel): Promise<void> {
    console.log(`🛡️  Testing security level: ${level}`);

    this.middleware.updateSecurityLevel(level);
    const requestCount = Math.floor(
      (this.config.requestsPerSecond * (this.config.testDuration / 1000)) / 3
    );

    const requests: Promise<void>[] = [];

    for (let i = 0; i < requestCount; i++) {
      requests.push(this.processRequest(level));
    }

    await Promise.all(requests);
    console.log(`✅ ${level} level test completed (${requestCount} requests)`);
  }

  private async testCacheEffectiveness(): Promise<void> {
    console.log('💾 Testing cache effectiveness...');

    const cacheablePatterns = ['hello', 'world', 'test', 'cache', 'performance'];
    const requestsPerPattern = 50;

    for (const pattern of cacheablePatterns) {
      const requests: Promise<void>[] = [];

      for (let i = 0; i < requestsPerPattern; i++) {
        requests.push(this.processCacheableRequest(pattern));
      }

      await Promise.all(requests);
    }

    console.log('✅ Cache effectiveness test completed');
  }

  private async testConcurrentLoad(): Promise<void> {
    console.log(
      `🚀 Testing concurrent load (${this.config.maxConcurrency} concurrent requests)...`
    );

    const batches = 10;
    const requestsPerBatch = this.config.maxConcurrency;

    for (let batch = 0; batch < batches; batch++) {
      const requests: Promise<void>[] = [];

      for (let i = 0; i < requestsPerBatch; i++) {
        requests.push(this.processRequest('standard'));
      }

      await Promise.all(requests);

      // Small delay between batches
      await new Promise((resolve) => setTimeout(resolve, 100));
    }

    console.log('✅ Concurrent load test completed');
  }

  private async processRequest(level: SecurityLevel): Promise<void> {
    const request = TestRequestGenerator.generateRequest();
    const startTime = performance.now();

    try {
      const result = await this.middleware.checkSecurity(request);
      const responseTime = performance.now() - startTime;

      this.metrics.recordResponseTime(level, responseTime);
      this.metrics.recordCacheHit(result.cached);
      this.metrics.recordSecurityResult(result.allowed, level);
    } catch (error) {
      const responseTime = performance.now() - startTime;
      this.metrics.recordResponseTime(level, responseTime);
      this.metrics.recordCacheHit(false);
      this.metrics.recordSecurityResult(false, level);
    }
  }

  private async processCacheableRequest(pattern: string): Promise<void> {
    const request = TestRequestGenerator.generateCacheableRequest(pattern);
    const startTime = performance.now();

    try {
      const result = await this.middleware.checkSecurity(request);
      const responseTime = performance.now() - startTime;

      this.metrics.recordResponseTime('standard', responseTime);
      this.metrics.recordCacheHit(result.cached);
      this.metrics.recordSecurityResult(result.allowed, 'standard');
    } catch (error) {
      const responseTime = performance.now() - startTime;
      this.metrics.recordResponseTime('standard', responseTime);
      this.metrics.recordCacheHit(false);
      this.metrics.recordSecurityResult(false, 'standard');
    }
  }

  private printResults(results: PerformanceTestResults): void {
    console.log('\n📊 PERFORMANCE TEST RESULTS');
    console.log('='.repeat(60));

    // Overall metrics
    console.log(`⏱️  Total Test Duration: ${(results.elapsedTime / 1000).toFixed(2)}s`);
    console.log(`📈 Total Requests: ${results.totalRequests}`);
    console.log(`🚀 Throughput: ${results.throughput.toFixed(2)} requests/second`);
    console.log('');

    // Response time analysis
    console.log('📊 RESPONSE TIME ANALYSIS');
    console.log('-'.repeat(40));

    const levels: SecurityLevel[] = ['minimal', 'standard', 'strict'];
    levels.forEach((level) => {
      const stats = results.responseTimeStats[level];
      const target = this.config.responseTimeTargets[level];
      const meetTarget = stats.avg <= target;

      console.log(`${level.toUpperCase()}:`);
      console.log(
        `  Average: ${stats.avg.toFixed(2)}ms (Target: ${target}ms) ${meetTarget ? '✅' : '❌'}`
      );
      console.log(`  P95: ${stats.p95.toFixed(2)}ms`);
      console.log(`  P99: ${stats.p99.toFixed(2)}ms`);
      console.log(`  Min/Max: ${stats.min.toFixed(2)}ms / ${stats.max.toFixed(2)}ms`);
      console.log(`  Count: ${stats.count}`);
      console.log('');
    });

    // Cache analysis
    console.log('💾 CACHE ANALYSIS');
    console.log('-'.repeat(40));
    const cacheTarget = this.config.cacheHitRateTarget;
    const cacheTargetMet = results.cacheStats.hitRate >= cacheTarget;

    console.log(
      `Hit Rate: ${(results.cacheStats.hitRate * 100).toFixed(2)}% (Target: ${(cacheTarget * 100).toFixed(0)}%) ${cacheTargetMet ? '✅' : '❌'}`
    );
    console.log(`Cache Hits: ${results.cacheStats.hits}`);
    console.log(`Cache Misses: ${results.cacheStats.misses}`);
    console.log(`Total Cache Requests: ${results.cacheStats.totalRequests}`);
    console.log('');

    // Memory analysis
    console.log('🧠 MEMORY ANALYSIS');
    console.log('-'.repeat(40));
    const memoryTargetMet = results.memoryStats.peak <= this.config.memoryUsageTarget;

    console.log(
      `Peak Memory: ${(results.memoryStats.peak / 1024 / 1024).toFixed(2)}MB (Target: ${(this.config.memoryUsageTarget / 1024 / 1024).toFixed(0)}MB) ${memoryTargetMet ? '✅' : '❌'}`
    );
    console.log(`Average Memory: ${(results.memoryStats.average / 1024 / 1024).toFixed(2)}MB`);
    console.log(`Current Memory: ${(results.memoryStats.current / 1024 / 1024).toFixed(2)}MB`);
    console.log(`Samples: ${results.memoryStats.samples}`);
    console.log('');

    // Security analysis
    console.log('🛡️  SECURITY ANALYSIS');
    console.log('-'.repeat(40));

    levels.forEach((level) => {
      const stats = results.securityStats[level];
      if (stats && stats.total > 0) {
        console.log(`${level.toUpperCase()}:`);
        console.log(`  Allowed: ${stats.allowed}`);
        console.log(`  Blocked: ${stats.blocked}`);
        console.log(`  Block Rate: ${(stats.blockRate * 100).toFixed(2)}%`);
        console.log('');
      }
    });

    // Performance targets summary
    console.log('🎯 PERFORMANCE TARGETS SUMMARY');
    console.log('-'.repeat(40));

    const responseTargetsMet = levels.every(
      (level) => results.responseTimeStats[level].avg <= this.config.responseTimeTargets[level]
    );

    console.log(`Response Time Targets: ${responseTargetsMet ? '✅ ALL MET' : '❌ SOME MISSED'}`);
    console.log(`Cache Hit Rate Target: ${cacheTargetMet ? '✅ MET' : '❌ MISSED'}`);
    console.log(`Memory Usage Target: ${memoryTargetMet ? '✅ MET' : '❌ MISSED'}`);
    console.log(
      `Throughput: ${results.throughput.toFixed(2)} RPS ${results.throughput >= 1000 ? '✅' : '❌'}`
    );

    const allTargetsMet =
      responseTargetsMet && cacheTargetMet && memoryTargetMet && results.throughput >= 1000;
    console.log(
      `\n🏆 OVERALL RESULT: ${allTargetsMet ? '✅ ALL TARGETS MET' : '❌ SOME TARGETS MISSED'}`
    );
  }
}

// ============================================================================
// Main Test Execution
// ============================================================================

export async function runLightweightSecurityPerformanceTest(): Promise<PerformanceTestResults> {
  const test = new LightweightSecurityPerformanceTest();
  return await test.runComprehensiveTest();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runLightweightSecurityPerformanceTest()
    .then((results) => {
      console.log('\n🎉 Performance test completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Performance test failed:', error);
      process.exit(1);
    });
}
