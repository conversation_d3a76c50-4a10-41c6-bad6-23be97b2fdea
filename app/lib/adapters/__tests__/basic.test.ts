/**
 * @fileoverview 基础功能测试套件
 *
 * 测试最基本的适配器功能，不依赖复杂的mock
 */

import { describe, it, expect } from 'vitest';
import { MemoryStorageAdapter } from '../storage';

describe('基础功能测试', () => {
  describe('MemoryStorageAdapter', () => {
    it('应该能够创建实例', () => {
      const adapter = new MemoryStorageAdapter();
      expect(adapter).toBeDefined();
      expect(typeof adapter.get).toBe('function');
      expect(typeof adapter.set).toBe('function');
      expect(typeof adapter.remove).toBe('function');
      expect(typeof adapter.clear).toBe('function');
    });

    it('应该能够存储和获取数据', async () => {
      const adapter = new MemoryStorageAdapter();

      await adapter.set('testKey', 'testValue');
      const result = await adapter.get('testKey');

      expect(result).toBe('testValue');
    });

    it('应该能够删除数据', async () => {
      const adapter = new MemoryStorageAdapter();

      await adapter.set('testKey', 'testValue');
      await adapter.remove('testKey');
      const result = await adapter.get('testKey');

      expect(result).toBeNull();
    });

    it('应该能够清空所有数据', async () => {
      const adapter = new MemoryStorageAdapter();

      await adapter.set('key1', 'value1');
      await adapter.set('key2', 'value2');
      await adapter.clear();

      const result1 = await adapter.get('key1');
      const result2 = await adapter.get('key2');

      expect(result1).toBeNull();
      expect(result2).toBeNull();
    });

    it('应该为不存在的键返回null', async () => {
      const adapter = new MemoryStorageAdapter();

      const result = await adapter.get('nonexistentKey');

      expect(result).toBeNull();
    });
  });
});
