/**
 * @fileoverview 生词本管理API集成测试
 * 
 * 测试覆盖范围：
 * - 完整的请求-响应流程测试
 * - 认证中间件集成测试
 * - 参数验证和错误处理
 * - 业务逻辑与数据层集成
 * - 响应格式验证
 * - 双认证策略测试（Session + JWT）
 * - 客户端类型和权限作用域验证
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { GET, POST, DELETE } from '@/api/word/route';
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import jwt from 'jsonwebtoken';

// Mock所有依赖
vi.mock('next-auth');
vi.mock('jsonwebtoken');
vi.mock('@/lib/1-services/practice/PracticeService');
vi.mock('@/lib/1-services/auth/TokenBlacklistService');
vi.mock('@/lib/1-services/auth/JWTValidationCache');
vi.mock('@/lib/utils/env');
vi.mock('@/lib/utils/redis');
vi.mock('@/lib/4-infrastructure/database/prisma');

// Mock AuthMiddleware - 简化版本
vi.mock('@/lib/1-services/auth/AuthMiddleware', () => ({
  requireAuth: vi.fn((options = {}) => {
    return (handler: any) => {
      return async (request: NextRequest) => {
        try {
          // 基础认证检查
          const authHeader = request.headers.get('authorization');
          const sessionValue = await mockGetServerSession();
          const hasAuth = authHeader?.startsWith('Bearer ') || sessionValue?.user?.id;

          if (!hasAuth) {
            return NextResponse.json(
              {
                success: false,
                error: 'No valid authentication found',
                code: 'UNAUTHORIZED_ERROR',
              },
              { status: 401 }
            );
          }

          // 构建认证上下文
          let context = {
            user: { 
              id: 'user-123',
              name: 'Test User',
              email: '<EMAIL>',
            },
            authMethod: 'session' as const,
            clientType: 'web' as const,
            permissions: ['word:read', 'word:write'],
            scopes: ['word:read', 'word:write'],
            ipAddress: '127.0.0.1',
            userAgent: 'test-agent',
          };

          // JWT认证情况 - 简化处理
          if (authHeader?.startsWith('Bearer ')) {
            const token = authHeader.substring(7);
            
            try {
              const decoded = mockJwtVerify(token, mockGetJWTSecret());
              
              // 检查黑名单 - 简化
              if (options.checkBlacklist !== false) {
                const isBlacklisted = await mockTokenBlacklistService.isBlacklisted(decoded.jti);
                if (isBlacklisted) {
                  return NextResponse.json(
                    {
                      success: false,
                      error: 'Token has been revoked',
                      code: 'UNAUTHORIZED_ERROR',
                    },
                    { status: 401 }
                  );
                }
              }
              
              context = {
                user: { 
                  id: decoded.sub, 
                  name: 'Test User',
                  email: '<EMAIL>',
                },
                authMethod: 'jwt' as const,
                clientType: decoded.clientType || 'web',
                permissions: decoded.scopes || ['word:read', 'word:write'],
                scopes: decoded.scopes || ['word:read', 'word:write'],
                ipAddress: '127.0.0.1',
                userAgent: 'test-agent',
              };
            } catch (jwtError) {
              // JWT失败，如果有session则使用session
              if (!sessionValue?.user?.id) {
                return NextResponse.json(
                  {
                    success: false,
                    error: 'No valid authentication found',
                    code: 'UNAUTHORIZED_ERROR',
                  },
                  { status: 401 }
                );
              }
            }
          }

          // 检查客户端类型权限 - 简化
          if (options.clientTypes && !options.clientTypes.includes(context.clientType)) {
            return NextResponse.json(
              {
                success: false,
                error: `Client type '${context.clientType}' not allowed`,
                code: 'FORBIDDEN_ERROR',
              },
              { status: 403 }
            );
          }

          // 检查权限作用域 - 简化
          if (options.scopes && options.scopes.length > 0) {
            const hasRequiredScope = options.scopes.some((scope) =>
              context.scopes.includes(scope)
            );
            if (!hasRequiredScope) {
              return NextResponse.json(
                {
                  success: false,
                  error: `Required scopes: ${options.scopes.join(', ')}`,
                  code: 'FORBIDDEN_ERROR',
                },
                { status: 403 }
              );
            }
          }

          // 检查速率限制 - 简化
          if (options.rateLimit) {
            const rateLimitResult = await mockRedisRateLimiter.checkRateLimit();
            if (!rateLimitResult.allowed) {
              return NextResponse.json(
                {
                  success: false,
                  error: `Rate limit exceeded. Try again in ${Math.ceil((rateLimitResult.resetTime - Date.now() / 1000) / 60)} minutes.`,
                  code: 'RATE_LIMIT_ERROR',
                },
                { status: 429 }
              );
            }
          }

          // 调用处理器
          return await handler(request, context);
        } catch (error) {
          console.error('Authentication middleware error:', error);
          return NextResponse.json(
            {
              success: false,
              error: 'Internal server error',
              code: 'INTERNAL_ERROR',
            },
            { status: 500 }
          );
        }
      };
    };
  }),
}));

vi.mock('@/lib/1-services/practice/PracticeService', () => ({
  createPracticeService: vi.fn(() => ({
    getUserWordList: vi.fn(),
    addWordsToUserList: vi.fn(), 
    removeWordsFromUserList: vi.fn(),
  })),
}));

// Get mock instance  
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
const mockPracticeService_obj = (createPracticeService as any)();

// Mock implementations
const mockGetServerSession = getServerSession as any;
const mockJwtVerify = jwt.verify as any;

// Mock auth services
const mockTokenBlacklistService = {
  isBlacklisted: vi.fn(),
  shouldForceExpireUserTokens: vi.fn(),
};

const mockJwtValidationCache = {
  getCachedValidation: vi.fn(),
  cacheValidation: vi.fn(),
};

vi.mock('@/lib/1-services/auth/TokenBlacklistService', () => ({
  tokenBlacklistService: mockTokenBlacklistService,
}));

vi.mock('@/lib/1-services/auth/JWTValidationCache', () => ({
  jwtValidationCache: mockJwtValidationCache,
}));

// Mock env utilities
const mockGetJWTSecret = vi.fn(() => 'test-jwt-secret-key');
vi.mock('@/lib/utils/env', () => ({
  getJWTSecret: mockGetJWTSecret,
}));

// Mock Redis rate limiter
const mockRedisRateLimiter = {
  checkRateLimit: vi.fn(),
};

vi.mock('@/lib/utils/redis', () => ({
  redisRateLimiter: mockRedisRateLimiter,
}));

// Mock Prisma
const mockPrisma = {
  user: {
    findUnique: vi.fn(),
  },
};

vi.mock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: mockPrisma,
}));

// Test data
const mockUser = {
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>',
  isActive: true,
};

const mockWordList = {
  words: [
    {
      id: 1,
      word: 'example',
      phonetics: ['ɪɡˈzæmpl'],
      freqRank: 100,
      proficiency: {
        practiceCount: 5,
        errorCount: 1,
        averageTime: 2500,
        proficiencyScore: 0.8,
        isMarked: false,
        lastPracticed: new Date('2025-07-30'),
      },
    },
  ],
  pagination: {
    page: 1,
    limit: 20,
    total: 1,
    totalPages: 1,
    hasMore: false,
  },
};

const mockJWTPayload = {
  sub: 'user-123',
  jti: 'jwt-123',
  iat: Math.floor(Date.now() / 1000),
  exp: Math.floor(Date.now() / 1000) + 3600,
  clientType: 'mobile',
  scopes: ['word:read', 'word:write'],
};

describe('生词本管理API集成测试', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // 默认设置所有认证为成功
    mockGetServerSession.mockResolvedValue({
      user: { id: mockUser.id },
    });

    mockJwtVerify.mockReturnValue(mockJWTPayload);
    mockGetJWTSecret.mockReturnValue('test-jwt-secret-key');
    mockTokenBlacklistService.isBlacklisted.mockResolvedValue(false);
    mockTokenBlacklistService.shouldForceExpireUserTokens.mockResolvedValue(false);
    mockJwtValidationCache.getCachedValidation.mockResolvedValue(null);
    mockPrisma.user.findUnique.mockResolvedValue(mockUser);

    // 默认速率限制通过
    mockRedisRateLimiter.checkRateLimit.mockResolvedValue({
      allowed: true,
      remaining: 99,
      resetTime: Date.now() / 1000 + 3600,
      total: 1,
    });
  });

  describe('GET /api/word - 生词列表集成测试', () => {
    it('应该通过Session认证成功获取生词列表', async () => {
      // Arrange
      mockPracticeService_obj.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest(
        'http://localhost:3000/api/word?page=1&limit=20&sortBy=time&order=desc'
      );

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Debug: Log the actual response for debugging
      if (response.status === 500) {
        console.error('500 Error Response:', responseData);
      }

      // Assert
      expect(response.status).toBe(200);
      expect(responseData).toEqual({
        success: true,
        data: mockWordList.words,
        pagination: mockWordList.pagination,
        message: 'Successfully retrieved 1 words',
      });

      expect(mockPracticeService_obj.getUserWordList).toHaveBeenCalledWith('user-123', {
        page: 1,
        limit: 20,
        sortBy: 'time',
        order: 'desc',
      });
    });

    it('应该通过JWT认证成功获取生词列表', async () => {
      // Arrange
      mockGetServerSession.mockResolvedValue(null); // 无Session
      mockPracticeService_obj.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer valid-jwt-token',
        },
      });

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(mockJwtVerify).toHaveBeenCalledWith('valid-jwt-token', 'test-jwt-secret-key');
      expect(mockTokenBlacklistService.isBlacklisted).toHaveBeenCalledWith('jwt-123');
    });

    it('应该验证查询参数并返回400错误', async () => {
      // Arrange
      const request = new NextRequest(
        'http://localhost:3000/api/word?page=0&limit=200&sortBy=invalid'
      );

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(responseData).toEqual({
        success: false,
        error: 'Invalid query parameters',
        code: 'VALIDATION_ERROR',
        details: expect.any(Array),
      });
    });

    it('应该拒绝已被吊销的JWT令牌', async () => {
      // Arrange
      mockGetServerSession.mockResolvedValue(null);
      mockTokenBlacklistService.isBlacklisted.mockResolvedValue(true);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer revoked-jwt-token',
        },
      });

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(responseData).toEqual({
        success: false,
        error: 'Token has been revoked',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该处理速率限制', async () => {
      // Arrange
      mockRedisRateLimiter.checkRateLimit.mockResolvedValue({
        allowed: false,
        remaining: 0,
        resetTime: Date.now() / 1000 + 300,
        total: 101,
      });

      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(429);
      expect(responseData).toEqual({
        success: false,
        error: expect.stringContaining('Rate limit exceeded'),
        code: 'RATE_LIMIT_ERROR',
      });
    });

    it('应该处理服务层错误', async () => {
      // Arrange
      mockPracticeService_obj.getUserWordList.mockRejectedValue(
        new Error('Database connection failed')
      );

      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(responseData).toEqual({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR',
      });
    });
  });

  describe('POST /api/word - 添加生词集成测试', () => {
    it('应该成功添加生词到用户生词本', async () => {
      // Arrange
      const addResult = {
        added: 2,
        skipped: 0,
        errors: [],
      };
      mockPracticeService_obj.addWordsToUserList.mockResolvedValue(addResult);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['example', 'test'] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(responseData).toEqual({
        success: true,
        data: addResult,
        message: 'Successfully added 2 words to your vocabulary list',
      });

      expect(mockPracticeService_obj.addWordsToUserList).toHaveBeenCalledWith(
        'user-123',
        ['example', 'test'],
        undefined
      );
    });

    it('应该通过wordIds添加生词', async () => {
      // Arrange
      const addResult = {
        added: 1,
        skipped: 1,
        errors: ['Words not found: nonexistent'],
      };
      mockPracticeService_obj.addWordsToUserList.mockResolvedValue(addResult);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ wordIds: [1, 2] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(responseData).toEqual({
        success: true,
        data: addResult,
        message: 'Successfully added 1 words to your vocabulary list, 1 words were already in your list. Errors: Words not found: nonexistent',
      });
    });

    it('应该验证请求体格式', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({}), // 空请求体
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(responseData).toEqual({
        success: false,
        error: 'Invalid request body',
        code: 'VALIDATION_ERROR',
        details: expect.any(Array),
      });
    });

    it('应该要求正确的权限作用域', async () => {
      // Arrange
      const jwtPayloadWithoutWriteScope = {
        ...mockJWTPayload,
        scopes: ['word:read'], // 缺少write权限
      };
      mockJwtVerify.mockReturnValue(jwtPayloadWithoutWriteScope);
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['test'] }),
        headers: {
          authorization: 'Bearer jwt-without-write-scope',
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await POST(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(responseData).toEqual({
        success: false,
        error: expect.stringContaining('Required scopes'),
        code: 'FORBIDDEN_ERROR',
      });
    });
  });

  describe('DELETE /api/word - 删除生词集成测试', () => {
    it('应该成功从用户生词本删除单词', async () => {
      // Arrange
      const deleteResult = {
        deleted: 2,
        notFound: 0,
      };
      mockPracticeService_obj.removeWordsFromUserList.mockResolvedValue(deleteResult);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [1, 2] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await DELETE(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(responseData).toEqual({
        success: true,
        data: deleteResult,
        message: 'Successfully removed 2 words from your vocabulary list',
      });

      expect(mockPracticeService_obj.removeWordsFromUserList).toHaveBeenCalledWith(
        'user-123',
        [1, 2]
      );
    });

    it('应该处理部分删除的情况', async () => {
      // Arrange
      const deleteResult = {
        deleted: 1,
        notFound: 1,
      };
      mockPracticeService_obj.removeWordsFromUserList.mockResolvedValue(deleteResult);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [1, 999] }),
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await DELETE(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(responseData).toEqual({
        success: true,
        data: deleteResult,
        message: 'Successfully removed 1 words from your vocabulary list, 1 words were not found in your list',
      });
    });

    it('应该验证wordIds参数', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [] }), // 空数组
        headers: {
          'content-type': 'application/json',
        },
      });

      // Act
      const response = await DELETE(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(responseData).toEqual({
        success: false,
        error: 'Invalid request body',
        code: 'VALIDATION_ERROR',
        details: expect.any(Array),
      });
    });
  });

  describe('认证流程集成测试', () => {
    it('应该优先使用Authorization头中的JWT', async () => {
      // Arrange
      mockPracticeService_obj.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer jwt-token',
          cookie: 'next-auth.session-token=session-token',
        },
      });

      // Act
      await GET(request);

      // Assert
      expect(mockJwtVerify).toHaveBeenCalledWith('jwt-token', 'test-jwt-secret-key');
      // Session认证不应该被调用
      expect(mockGetServerSession).not.toHaveBeenCalled();
    });

    it('应该在JWT失败时回退到Session认证', async () => {
      // Arrange
      mockJwtVerify.mockImplementation(() => {
        throw new Error('Invalid JWT');
      });
      mockPracticeService_obj.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer invalid-jwt',
        },
      });

      // Act
      await GET(request);

      // Assert
      expect(mockJwtVerify).toHaveBeenCalled();
      expect(mockGetServerSession).toHaveBeenCalled();
    });

    it('应该在两种认证都失败时返回401', async () => {
      // Arrange
      mockJwtVerify.mockImplementation(() => {
        throw new Error('Invalid JWT');
      });
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer invalid-jwt',
        },
      });

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(responseData).toEqual({
        success: false,
        error: 'No valid authentication found',
        code: 'UNAUTHORIZED_ERROR',
      });
    });

    it('应该验证客户端类型权限', async () => {
      // Arrange
      const jwtPayloadWithInvalidClient = {
        ...mockJWTPayload,
        clientType: 'desktop', // 不支持的客户端类型
      };
      mockJwtVerify.mockReturnValue(jwtPayloadWithInvalidClient);
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer jwt-with-invalid-client',
        },
      });

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(responseData).toEqual({
        success: false,
        error: "Client type 'desktop' not allowed",
        code: 'FORBIDDEN_ERROR',
      });
    });
  });

  describe('JWT缓存集成测试', () => {
    it('应该使用缓存的JWT验证结果', async () => {
      // Arrange
      const cachedValidation = {
        isValid: true,
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read', 'word:write'],
      };
      mockJwtValidationCache.getCachedValidation.mockResolvedValue(cachedValidation);
      mockGetServerSession.mockResolvedValue(null);
      mockPracticeService_obj.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer cached-jwt-token',
        },
      });

      // Act
      const response = await GET(request);

      // Assert
      expect(response.status).toBe(200);
      expect(mockJwtValidationCache.getCachedValidation).toHaveBeenCalledWith('jwt-123');
      // 不应该检查黑名单或数据库，因为使用了缓存
      expect(mockTokenBlacklistService.isBlacklisted).not.toHaveBeenCalled();
      expect(mockPrisma.user.findUnique).not.toHaveBeenCalled();
    });

    it('应该处理缓存的失败验证结果', async () => {
      // Arrange
      const cachedValidation = {
        isValid: false,
        userId: 'user-123',
        email: '<EMAIL>',
        clientType: 'mobile',
        scopes: ['word:read'],
      };
      mockJwtValidationCache.getCachedValidation.mockResolvedValue(cachedValidation);
      mockGetServerSession.mockResolvedValue(null);

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer invalid-cached-jwt',
        },
      });

      // Act
      const response = await GET(request);
      const responseData = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(responseData).toEqual({
        success: false,
        error: 'Token validation failed (cached)',
        code: 'UNAUTHORIZED_ERROR',
      });
    });
  });

  describe('用户数据隔离测试', () => {
    it('应该确保用户只能访问自己的生词本', async () => {
      // Arrange
      mockPracticeService_obj.getUserWordList.mockResolvedValue(mockWordList);

      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      await GET(request);

      // Assert
      expect(mockPracticeService_obj.getUserWordList).toHaveBeenCalledWith(
        'user-123', // 确保使用认证用户的ID
        expect.any(Object)
      );
    });

    it('应该防止用户访问其他用户的数据', async () => {
      // Arrange
      const anotherUserJWT = {
        ...mockJWTPayload,
        sub: 'other-user-456',
      };
      mockJwtVerify.mockReturnValue(anotherUserJWT);
      mockGetServerSession.mockResolvedValue(null);
      mockPrisma.user.findUnique.mockResolvedValue({
        id: 'other-user-456',
        name: 'Other User',
        email: '<EMAIL>',
        isActive: true,
      });
      mockPracticeService_obj.getUserWordList.mockResolvedValue({
        words: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0, hasMore: false },
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        headers: {
          authorization: 'Bearer other-user-jwt',
        },
      });

      // Act
      await GET(request);

      // Assert
      expect(mockPracticeService_obj.getUserWordList).toHaveBeenCalledWith(
        'other-user-456', // 应该使用JWT中的用户ID
        expect.any(Object)
      );
    });
  });
});