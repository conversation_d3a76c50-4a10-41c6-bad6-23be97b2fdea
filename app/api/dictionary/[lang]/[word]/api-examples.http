@hostname = localhost:3000

# Dictionary API Examples

### 1. 基本查询 - 查询单词 "do"
GET http://{{hostname}}/api/dictionary/en/did
Content-Type: application/json

### 2. 强制刷新缓存查询
GET http://{{hostname}}/api/dictionary/en/did?fresh=true
Content-Type: application/json

### 3. 查询包含连字符的单词
GET http://{{hostname}}/api/dictionary/en/do
Content-Type: application/json

### 4. 查询包含撇号的单词
GET http://{{hostname}}/api/dictionary/en/don't
Content-Type: application/json

### 5. 错误测试 - 不支持的语言
GET http://{{hostname}}/api/dictionary/fr/do
Content-Type: application/json

### 6. 错误测试 - 无效的单词格式（包含数字）
GET http://{{hostname}}/api/dictionary/en/do123
Content-Type: application/json

### 7. 错误测试 - 不支持的HTTP方法
POST http://{{hostname}}/api/dictionary/en/do
Content-Type: application/json

### 8. 错误测试 - 单词太长
GET http://{{hostname}}/api/dictionary/en/verylongwordthatexceedsthelimitofcharactersallowed
Content-Type: application/json

### 9. 错误测试 - 空单词
GET http://{{hostname}}/api/dictionary/en/
Content-Type: application/json

### 10. 查询常见单词（如果数据库中有数据）
GET http://{{hostname}}/api/dictionary/en/hello
Content-Type: application/json

### 11. 查询另一个常见单词
GET http://{{hostname}}/api/dictionary/en/world
Content-Type: application/json

### 12. 查询复数形式
GET http://{{hostname}}/api/dictionary/en/cats
Content-Type: application/json

### 响应格式说明：
### 成功响应 (200):
### {
###   "success": true,
###   "data": {
###     "words": [                // 单词数据数组
###       {
###         "word": "do",
###         "explain": [...],       // 词性和释义
###         "wordFormats": [        // 词形变化（完整数据）
###           {"name": "原型", "form": "do"},
###           {"name": "过去式", "form": "did"},
###           {"name": "过去分词", "form": "done"},
###           {"name": "现在分词", "form": "doing"}
###         ],
###         "phonetic": {...}       // 音标
###       }
###     ],
###     "wordCount": 1,             // 单词数量
###     "wordList": ["do"],         // 单词列表
###     "queryMetadata": {          // 查询元数据
###       "searchTerm": "do",
###       "matchedForm": "do",
###       "searchStrategy": "exact_vocabulary",
###       "isBaseFormQuery": true,
###       "processingTimeMs": 50
###     }
###   },
###   "timestamp": "2025-05-29T17:45:00.000Z"
### }
###
### 未找到响应 (200):
### {
###   "success": true,
###   "message": "未找到单词 \"word\" 的释义，返回空结果",
###   "data": {
###     "words": [],
###     "wordCount": 0,
###     "wordList": [],
###     "queryMetadata": { ... }
###   },
###   "timestamp": "2025-05-26T07:18:27.903Z"
### }
###
### 错误响应 (400/405/500):
### {
###   "error": {
###     "message": "错误描述",
###     "statusCode": 400,
###     "details": { ... },       // 可选的详细信息
###     "timestamp": "2025-05-26T07:18:27.903Z"
###   }
### }
