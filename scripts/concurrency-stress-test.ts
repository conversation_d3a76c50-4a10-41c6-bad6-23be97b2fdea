/**
 * Concurrency Stress Test
 *
 * Comprehensive stress testing of the lightweight security middleware
 * under high concurrency conditions
 *
 * Test Goals:
 * - Validate 100-150 concurrent request handling
 * - Test response time targets under load
 * - Validate memory usage under stress (<50MB)
 * - Test system stability under extreme load
 * - Validate graceful degradation
 */

import { NextRequest } from 'next/server';
import {
  LightweightSecurityMiddleware,
  SecurityLevel,
} from '../app/lib/auth/lightweight-security-middleware';

// ============================================================================
// Stress Test Configuration
// ============================================================================

interface StressTestConfig {
  maxConcurrency: number;
  testDuration: number;
  rampUpDuration: number;
  sustainedLoadDuration: number;
  rampDownDuration: number;
  targetResponseTimes: {
    minimal: number;
    standard: number;
    strict: number;
  };
  memoryTarget: number;
  errorRateThreshold: number;
  throughputTarget: number;
  stabilityTestDuration: number;
}

const DEFAULT_STRESS_TEST_CONFIG: StressTestConfig = {
  maxConcurrency: 150,
  testDuration: 180000, // 3 minutes
  rampUpDuration: 30000, // 30 seconds
  sustainedLoadDuration: 120000, // 2 minutes
  rampDownDuration: 30000, // 30 seconds
  targetResponseTimes: {
    minimal: 5,
    standard: 10,
    strict: 25,
  },
  memoryTarget: 50 * 1024 * 1024, // 50MB
  errorRateThreshold: 0.01, // 1%
  throughputTarget: 1000, // RPS
  stabilityTestDuration: 300000, // 5 minutes
};

// ============================================================================
// Stress Test Request Generator
// ============================================================================

class StressTestRequestGenerator {
  private static requestCounter = 0;
  private static patterns = [
    // High-frequency patterns (50%)
    { weight: 0.5, patterns: ['hello', 'world', 'test', 'api', 'user'] },
    // Medium-frequency patterns (30%)
    { weight: 0.3, patterns: ['dictionary', 'auth', 'login', 'profile', 'stats'] },
    // Low-frequency patterns (15%)
    { weight: 0.15, patterns: ['admin', 'config', 'settings', 'cache', 'debug'] },
    // Attack patterns (5%)
    { weight: 0.05, patterns: ['<script>', 'OR 1=1', '../../../', 'DROP TABLE', 'javascript:'] },
  ];

  static generateStressRequest(useRandomIP: boolean = true): NextRequest {
    this.requestCounter++;

    const pattern = this.selectPattern();
    const method = Math.random() < 0.8 ? 'GET' : 'POST';
    const ip = useRandomIP ? this.generateRandomIP() : '*************';

    const url = new URL(`/api/dictionary/en/${pattern}`, 'http://localhost:3000');

    // Add random query parameters for some requests
    if (Math.random() < 0.3) {
      url.searchParams.set('id', this.requestCounter.toString());
      url.searchParams.set('timestamp', Date.now().toString());
    }

    const headers: Record<string, string> = {
      'x-forwarded-for': ip,
      accept: 'application/json',
      'content-type': 'application/json',
    };

    // Vary user agents
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
      'curl/7.68.0',
      'Googlebot/2.1',
      'sqlmap/1.0',
    ];

    headers['user-agent'] = userAgents[Math.floor(Math.random() * userAgents.length)];

    const request = new NextRequest(url, {
      method,
      headers,
      body: method === 'POST' ? JSON.stringify({ data: `test_${this.requestCounter}` }) : undefined,
    });

    (request as any).ip = ip;
    return request;
  }

  private static selectPattern(): string {
    const random = Math.random();
    let cumulativeWeight = 0;

    for (const category of this.patterns) {
      cumulativeWeight += category.weight;
      if (random <= cumulativeWeight) {
        return category.patterns[Math.floor(Math.random() * category.patterns.length)];
      }
    }

    return 'default';
  }

  private static generateRandomIP(): string {
    return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
  }
}

// ============================================================================
// Stress Test Metrics
// ============================================================================

interface StressTestMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  timeouts: number;
  errors: Map<string, number>;
  responseTimes: {
    all: number[];
    bySecurityLevel: Map<SecurityLevel, number[]>;
    byPhase: Map<string, number[]>;
  };
  throughput: {
    overall: number;
    byPhase: Map<string, number>;
    peak: number;
  };
  memoryUsage: {
    initial: number;
    peak: number;
    final: number;
    samples: number[];
  };
  concurrency: {
    requested: number[];
    actual: number[];
    queued: number[];
  };
  stability: {
    uptime: number;
    crashes: number;
    recoveries: number;
  };
}

interface StressTestResults {
  overall: StressTestMetrics;
  bySecurityLevel: Map<SecurityLevel, StressTestMetrics>;
  phases: {
    rampUp: StressTestMetrics;
    sustained: StressTestMetrics;
    rampDown: StressTestMetrics;
  };
  targetsMet: {
    concurrency: boolean;
    responseTime: boolean;
    memoryUsage: boolean;
    errorRate: boolean;
    throughput: boolean;
    stability: boolean;
  };
  recommendations: string[];
}

// ============================================================================
// System Monitor
// ============================================================================

class SystemMonitor {
  private memoryUsage: number[] = [];
  private cpuUsage: number[] = [];
  private interval: NodeJS.Timeout | null = null;
  private initialMemory: number = 0;

  start(): void {
    this.initialMemory = process.memoryUsage().heapUsed;
    this.memoryUsage = [];
    this.cpuUsage = [];

    this.interval = setInterval(() => {
      const memory = process.memoryUsage();
      this.memoryUsage.push(memory.heapUsed);

      // Simple CPU usage approximation
      const usage = process.cpuUsage();
      this.cpuUsage.push(usage.user + usage.system);
    }, 250); // Every 250ms for fine-grained monitoring
  }

  stop(): void {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }

  getMemoryStats() {
    const final = process.memoryUsage().heapUsed;
    const peak = Math.max(...this.memoryUsage);

    return {
      initial: this.initialMemory,
      peak,
      final,
      samples: [...this.memoryUsage],
    };
  }

  getCurrentMemory(): number {
    return process.memoryUsage().heapUsed;
  }
}

// ============================================================================
// Concurrency Controller
// ============================================================================

class ConcurrencyController {
  private activeRequests = 0;
  private maxConcurrency = 0;
  private requestQueue: (() => Promise<void>)[] = [];
  private processing = false;

  async executeWithConcurrency<T>(task: () => Promise<T>, maxConcurrency: number): Promise<T> {
    this.maxConcurrency = maxConcurrency;

    return new Promise((resolve, reject) => {
      const execute = async () => {
        if (this.activeRequests >= maxConcurrency) {
          this.requestQueue.push(execute);
          return;
        }

        this.activeRequests++;

        try {
          const result = await task();
          resolve(result);
        } catch (error) {
          reject(error);
        } finally {
          this.activeRequests--;
          this.processQueue();
        }
      };

      execute();
    });
  }

  private processQueue(): void {
    if (this.processing || this.requestQueue.length === 0) return;

    this.processing = true;

    while (this.requestQueue.length > 0 && this.activeRequests < this.maxConcurrency) {
      const nextTask = this.requestQueue.shift();
      if (nextTask) {
        nextTask();
      }
    }

    this.processing = false;
  }

  getStats() {
    return {
      active: this.activeRequests,
      queued: this.requestQueue.length,
      maxConcurrency: this.maxConcurrency,
    };
  }
}

// ============================================================================
// Stress Test Runner
// ============================================================================

class ConcurrencyStressTest {
  private config: StressTestConfig;
  private middleware: LightweightSecurityMiddleware;
  private systemMonitor: SystemMonitor;
  private concurrencyController: ConcurrencyController;
  private metrics: StressTestMetrics;

  constructor(config: Partial<StressTestConfig> = {}) {
    this.config = { ...DEFAULT_STRESS_TEST_CONFIG, ...config };
    this.initializeComponents();
  }

  private initializeComponents(): void {
    this.middleware = new LightweightSecurityMiddleware({
      enableCaching: true,
      enableThreatDetection: true,
      enableRateLimiting: true,
      enableFingerprinting: true,
      performanceMode: true,
      maxConcurrency: this.config.maxConcurrency,
      cacheSize: 2000,
    });

    this.systemMonitor = new SystemMonitor();
    this.concurrencyController = new ConcurrencyController();
    this.metrics = this.createEmptyMetrics();
  }

  async runStressTest(): Promise<StressTestResults> {
    console.log('🚀 Starting Concurrency Stress Test');
    console.log('='.repeat(60));
    console.log(`📊 Max Concurrency: ${this.config.maxConcurrency}`);
    console.log(`⏱️  Test Duration: ${this.config.testDuration / 1000}s`);
    console.log(`🎯 Memory Target: ${(this.config.memoryTarget / 1024 / 1024).toFixed(0)}MB`);
    console.log('');

    // Start system monitoring
    this.systemMonitor.start();

    const results: StressTestResults = {
      overall: this.createEmptyMetrics(),
      bySecurityLevel: new Map(),
      phases: {
        rampUp: await this.runRampUpPhase(),
        sustained: await this.runSustainedPhase(),
        rampDown: await this.runRampDownPhase(),
      },
      targetsMet: {
        concurrency: false,
        responseTime: false,
        memoryUsage: false,
        errorRate: false,
        throughput: false,
        stability: false,
      },
      recommendations: [],
    };

    // Security level tests
    results.bySecurityLevel = await this.runSecurityLevelTests();

    // Stop monitoring
    this.systemMonitor.stop();

    // Calculate overall metrics
    results.overall = this.calculateOverallMetrics(results);

    // Evaluate targets
    results.targetsMet = this.evaluateTargets(results);
    results.recommendations = this.generateRecommendations(results);

    this.printStressTestResults(results);
    return results;
  }

  private async runRampUpPhase(): Promise<StressTestMetrics> {
    console.log('📈 Starting ramp-up phase...');

    const metrics = this.createEmptyMetrics();
    const startTime = Date.now();
    const endTime = startTime + this.config.rampUpDuration;

    let currentConcurrency = 1;
    const maxConcurrency = this.config.maxConcurrency;
    const rampUpSteps = 10;
    const stepDuration = this.config.rampUpDuration / rampUpSteps;
    const concurrencyStep = Math.floor(maxConcurrency / rampUpSteps);

    for (let step = 0; step < rampUpSteps; step++) {
      const stepStart = Date.now();
      const stepEnd = stepStart + stepDuration;

      console.log(
        `  📊 Step ${step + 1}/${rampUpSteps}: ${currentConcurrency} concurrent requests`
      );

      const promises: Promise<void>[] = [];

      for (let i = 0; i < currentConcurrency; i++) {
        promises.push(this.runConcurrentRequests(metrics, stepEnd));
      }

      await Promise.all(promises);

      currentConcurrency = Math.min(currentConcurrency + concurrencyStep, maxConcurrency);

      // Wait for step completion
      const remaining = stepEnd - Date.now();
      if (remaining > 0) {
        await new Promise((resolve) => setTimeout(resolve, remaining));
      }
    }

    console.log(`  ✅ Ramp-up completed: ${metrics.totalRequests} requests processed`);
    return metrics;
  }

  private async runSustainedPhase(): Promise<StressTestMetrics> {
    console.log('🔥 Starting sustained load phase...');

    const metrics = this.createEmptyMetrics();
    const startTime = Date.now();
    const endTime = startTime + this.config.sustainedLoadDuration;

    const concurrency = this.config.maxConcurrency;
    const batchSize = 10;
    const batchDuration = 1000; // 1 second batches

    while (Date.now() < endTime) {
      const batchStart = Date.now();
      const batchEnd = batchStart + batchDuration;

      const promises: Promise<void>[] = [];

      for (let i = 0; i < concurrency; i++) {
        promises.push(this.runConcurrentRequests(metrics, batchEnd, batchSize));
      }

      await Promise.all(promises);

      // Log progress
      const elapsed = Date.now() - startTime;
      const progress = (elapsed / this.config.sustainedLoadDuration) * 100;

      if (Math.floor(progress) % 10 === 0) {
        console.log(
          `  📊 Progress: ${progress.toFixed(0)}% | Requests: ${metrics.totalRequests} | Memory: ${(this.systemMonitor.getCurrentMemory() / 1024 / 1024).toFixed(2)}MB`
        );
      }

      // Wait for batch completion
      const remaining = batchEnd - Date.now();
      if (remaining > 0) {
        await new Promise((resolve) => setTimeout(resolve, remaining));
      }
    }

    console.log(`  ✅ Sustained load completed: ${metrics.totalRequests} requests processed`);
    return metrics;
  }

  private async runRampDownPhase(): Promise<StressTestMetrics> {
    console.log('📉 Starting ramp-down phase...');

    const metrics = this.createEmptyMetrics();
    const startTime = Date.now();

    let currentConcurrency = this.config.maxConcurrency;
    const rampDownSteps = 10;
    const stepDuration = this.config.rampDownDuration / rampDownSteps;
    const concurrencyStep = Math.floor(this.config.maxConcurrency / rampDownSteps);

    for (let step = 0; step < rampDownSteps; step++) {
      const stepStart = Date.now();
      const stepEnd = stepStart + stepDuration;

      console.log(
        `  📊 Step ${step + 1}/${rampDownSteps}: ${currentConcurrency} concurrent requests`
      );

      const promises: Promise<void>[] = [];

      for (let i = 0; i < currentConcurrency; i++) {
        promises.push(this.runConcurrentRequests(metrics, stepEnd));
      }

      await Promise.all(promises);

      currentConcurrency = Math.max(currentConcurrency - concurrencyStep, 1);

      // Wait for step completion
      const remaining = stepEnd - Date.now();
      if (remaining > 0) {
        await new Promise((resolve) => setTimeout(resolve, remaining));
      }
    }

    console.log(`  ✅ Ramp-down completed: ${metrics.totalRequests} requests processed`);
    return metrics;
  }

  private async runSecurityLevelTests(): Promise<Map<SecurityLevel, StressTestMetrics>> {
    console.log('🛡️  Running security level stress tests...');

    const results = new Map<SecurityLevel, StressTestMetrics>();
    const levels: SecurityLevel[] = ['minimal', 'standard', 'strict'];

    for (const level of levels) {
      console.log(`  🔒 Testing ${level} security level under stress...`);

      this.middleware.updateSecurityLevel(level);
      const metrics = this.createEmptyMetrics();

      // Run concentrated load test
      const concurrency = Math.floor(this.config.maxConcurrency * 0.8);
      const duration = 30000; // 30 seconds
      const endTime = Date.now() + duration;

      const promises: Promise<void>[] = [];

      for (let i = 0; i < concurrency; i++) {
        promises.push(this.runConcurrentRequests(metrics, endTime));
      }

      await Promise.all(promises);

      results.set(level, metrics);

      console.log(
        `    ✅ ${level}: ${metrics.totalRequests} requests, ${metrics.successfulRequests} successful`
      );
    }

    return results;
  }

  private async runConcurrentRequests(
    metrics: StressTestMetrics,
    endTime: number,
    batchSize: number = 5
  ): Promise<void> {
    while (Date.now() < endTime) {
      const batch: Promise<void>[] = [];

      for (let i = 0; i < batchSize; i++) {
        batch.push(this.processStressRequest(metrics));
      }

      await Promise.all(batch);

      // Small delay to prevent overwhelming
      await new Promise((resolve) => setTimeout(resolve, 10));
    }
  }

  private async processStressRequest(metrics: StressTestMetrics): Promise<void> {
    const request = StressTestRequestGenerator.generateStressRequest();
    const startTime = performance.now();

    try {
      const result = await this.concurrencyController.executeWithConcurrency(
        () => this.middleware.checkSecurity(request),
        this.config.maxConcurrency
      );

      const responseTime = performance.now() - startTime;

      metrics.totalRequests++;
      metrics.successfulRequests++;
      metrics.responseTimes.all.push(responseTime);

      if (result.level) {
        if (!metrics.responseTimes.bySecurityLevel.has(result.level)) {
          metrics.responseTimes.bySecurityLevel.set(result.level, []);
        }
        metrics.responseTimes.bySecurityLevel.get(result.level)!.push(responseTime);
      }
    } catch (error) {
      const responseTime = performance.now() - startTime;

      metrics.totalRequests++;
      metrics.failedRequests++;
      metrics.responseTimes.all.push(responseTime);

      const errorType = error instanceof Error ? error.name : 'Unknown';
      metrics.errors.set(errorType, (metrics.errors.get(errorType) || 0) + 1);

      if (responseTime > 30000) {
        // 30 second timeout
        metrics.timeouts++;
      }
    }
  }

  private calculateOverallMetrics(results: StressTestResults): StressTestMetrics {
    const overall = this.createEmptyMetrics();

    // Combine phase metrics
    const phases = [results.phases.rampUp, results.phases.sustained, results.phases.rampDown];

    for (const phase of phases) {
      overall.totalRequests += phase.totalRequests;
      overall.successfulRequests += phase.successfulRequests;
      overall.failedRequests += phase.failedRequests;
      overall.timeouts += phase.timeouts;
      overall.responseTimes.all.push(...phase.responseTimes.all);

      // Combine errors
      for (const [error, count] of phase.errors.entries()) {
        overall.errors.set(error, (overall.errors.get(error) || 0) + count);
      }
    }

    // Calculate throughput
    const totalDuration = this.config.testDuration / 1000;
    overall.throughput.overall = overall.totalRequests / totalDuration;

    // Memory usage
    overall.memoryUsage = this.systemMonitor.getMemoryStats();

    return overall;
  }

  private evaluateTargets(results: StressTestResults): StressTestResults['targetsMet'] {
    const overall = results.overall;

    // Response time targets
    const responseTimeTargets = Object.values(this.config.targetResponseTimes);
    const avgResponseTime =
      overall.responseTimes.all.reduce((a, b) => a + b, 0) / overall.responseTimes.all.length;
    const responseTimeTarget = avgResponseTime <= Math.max(...responseTimeTargets);

    // Memory usage target
    const memoryTarget = overall.memoryUsage.peak <= this.config.memoryTarget;

    // Error rate target
    const errorRate = overall.failedRequests / overall.totalRequests;
    const errorRateTarget = errorRate <= this.config.errorRateThreshold;

    // Throughput target
    const throughputTarget = overall.throughput.overall >= this.config.throughputTarget;

    // Concurrency target
    const concurrencyTarget = overall.successfulRequests > 0;

    // Stability target
    const stabilityTarget = overall.timeouts < overall.totalRequests * 0.01;

    return {
      concurrency: concurrencyTarget,
      responseTime: responseTimeTarget,
      memoryUsage: memoryTarget,
      errorRate: errorRateTarget,
      throughput: throughputTarget,
      stability: stabilityTarget,
    };
  }

  private generateRecommendations(results: StressTestResults): string[] {
    const recommendations: string[] = [];
    const overall = results.overall;
    const targets = results.targetsMet;

    if (!targets.responseTime) {
      recommendations.push('Optimize response time by improving cache hit rates');
      recommendations.push('Consider reducing security checks in minimal mode');
    }

    if (!targets.memoryUsage) {
      recommendations.push('Reduce memory usage by optimizing cache size');
      recommendations.push('Implement more aggressive garbage collection');
    }

    if (!targets.errorRate) {
      recommendations.push('Investigate error patterns and improve error handling');
      recommendations.push('Consider implementing circuit breaker pattern');
    }

    if (!targets.throughput) {
      recommendations.push('Optimize processing pipeline for better throughput');
      recommendations.push('Consider async processing where possible');
    }

    if (!targets.stability) {
      recommendations.push('Implement better timeout handling');
      recommendations.push('Add request queuing and backpressure mechanisms');
    }

    return recommendations;
  }

  private createEmptyMetrics(): StressTestMetrics {
    return {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      timeouts: 0,
      errors: new Map(),
      responseTimes: {
        all: [],
        bySecurityLevel: new Map(),
        byPhase: new Map(),
      },
      throughput: {
        overall: 0,
        byPhase: new Map(),
        peak: 0,
      },
      memoryUsage: {
        initial: 0,
        peak: 0,
        final: 0,
        samples: [],
      },
      concurrency: {
        requested: [],
        actual: [],
        queued: [],
      },
      stability: {
        uptime: 0,
        crashes: 0,
        recoveries: 0,
      },
    };
  }

  private printStressTestResults(results: StressTestResults): void {
    console.log('\n📊 CONCURRENCY STRESS TEST RESULTS');
    console.log('='.repeat(60));
    console.log('');

    // Overall metrics
    console.log('📈 OVERALL METRICS');
    console.log('-'.repeat(40));
    console.log(`Total Requests: ${results.overall.totalRequests}`);
    console.log(`Successful Requests: ${results.overall.successfulRequests}`);
    console.log(`Failed Requests: ${results.overall.failedRequests}`);
    console.log(
      `Success Rate: ${((results.overall.successfulRequests / results.overall.totalRequests) * 100).toFixed(2)}%`
    );
    console.log(`Timeouts: ${results.overall.timeouts}`);
    console.log(`Throughput: ${results.overall.throughput.overall.toFixed(2)} RPS`);
    console.log('');

    // Response time analysis
    console.log('⏱️  RESPONSE TIME ANALYSIS');
    console.log('-'.repeat(40));
    const responseTimes = results.overall.responseTimes.all;
    const sortedTimes = responseTimes.sort((a, b) => a - b);

    console.log(
      `Average: ${(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length).toFixed(2)}ms`
    );
    console.log(`Median: ${sortedTimes[Math.floor(sortedTimes.length / 2)].toFixed(2)}ms`);
    console.log(`P95: ${sortedTimes[Math.floor(sortedTimes.length * 0.95)].toFixed(2)}ms`);
    console.log(`P99: ${sortedTimes[Math.floor(sortedTimes.length * 0.99)].toFixed(2)}ms`);
    console.log(`Min: ${sortedTimes[0].toFixed(2)}ms`);
    console.log(`Max: ${sortedTimes[sortedTimes.length - 1].toFixed(2)}ms`);
    console.log('');

    // Memory analysis
    console.log('🧠 MEMORY ANALYSIS');
    console.log('-'.repeat(40));
    const memoryStats = results.overall.memoryUsage;
    console.log(`Initial Memory: ${(memoryStats.initial / 1024 / 1024).toFixed(2)}MB`);
    console.log(
      `Peak Memory: ${(memoryStats.peak / 1024 / 1024).toFixed(2)}MB (Target: ${(this.config.memoryTarget / 1024 / 1024).toFixed(0)}MB) ${results.targetsMet.memoryUsage ? '✅' : '❌'}`
    );
    console.log(`Final Memory: ${(memoryStats.final / 1024 / 1024).toFixed(2)}MB`);
    console.log(
      `Memory Growth: ${((memoryStats.peak - memoryStats.initial) / 1024 / 1024).toFixed(2)}MB`
    );
    console.log('');

    // Phase analysis
    console.log('📊 PHASE ANALYSIS');
    console.log('-'.repeat(40));
    console.log('Phase     | Requests | Success Rate | Avg Response Time');
    console.log('-'.repeat(40));

    const phases = [
      ['Ramp-Up', results.phases.rampUp],
      ['Sustained', results.phases.sustained],
      ['Ramp-Down', results.phases.rampDown],
    ] as const;

    for (const [name, metrics] of phases) {
      const successRate = (metrics.successfulRequests / metrics.totalRequests) * 100;
      const avgResponseTime =
        metrics.responseTimes.all.reduce((a, b) => a + b, 0) / metrics.responseTimes.all.length;

      console.log(
        `${name.padEnd(9)} | ${metrics.totalRequests.toString().padStart(8)} | ${successRate.toFixed(2).padStart(11)}% | ${avgResponseTime.toFixed(2).padStart(17)}ms`
      );
    }
    console.log('');

    // Security level analysis
    console.log('🛡️  SECURITY LEVEL ANALYSIS');
    console.log('-'.repeat(40));
    console.log('Level    | Requests | Success Rate | Avg Response Time');
    console.log('-'.repeat(40));

    for (const [level, metrics] of results.bySecurityLevel.entries()) {
      const successRate = (metrics.successfulRequests / metrics.totalRequests) * 100;
      const avgResponseTime =
        metrics.responseTimes.all.reduce((a, b) => a + b, 0) / metrics.responseTimes.all.length;

      console.log(
        `${level.padEnd(8)} | ${metrics.totalRequests.toString().padStart(8)} | ${successRate.toFixed(2).padStart(11)}% | ${avgResponseTime.toFixed(2).padStart(17)}ms`
      );
    }
    console.log('');

    // Error analysis
    if (results.overall.errors.size > 0) {
      console.log('❌ ERROR ANALYSIS');
      console.log('-'.repeat(40));
      console.log('Error Type            | Count | Percentage');
      console.log('-'.repeat(40));

      for (const [error, count] of results.overall.errors.entries()) {
        const percentage = (count / results.overall.totalRequests) * 100;
        console.log(
          `${error.padEnd(20)} | ${count.toString().padStart(5)} | ${percentage.toFixed(2).padStart(10)}%`
        );
      }
      console.log('');
    }

    // Target evaluation
    console.log('🎯 TARGET EVALUATION');
    console.log('-'.repeat(40));
    console.log(`Concurrency Handling: ${results.targetsMet.concurrency ? '✅ MET' : '❌ MISSED'}`);
    console.log(`Response Time: ${results.targetsMet.responseTime ? '✅ MET' : '❌ MISSED'}`);
    console.log(`Memory Usage: ${results.targetsMet.memoryUsage ? '✅ MET' : '❌ MISSED'}`);
    console.log(`Error Rate: ${results.targetsMet.errorRate ? '✅ MET' : '❌ MISSED'}`);
    console.log(`Throughput: ${results.targetsMet.throughput ? '✅ MET' : '❌ MISSED'}`);
    console.log(`Stability: ${results.targetsMet.stability ? '✅ MET' : '❌ MISSED'}`);
    console.log('');

    // Recommendations
    if (results.recommendations.length > 0) {
      console.log('💡 RECOMMENDATIONS');
      console.log('-'.repeat(40));
      results.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
      console.log('');
    }

    // Overall assessment
    const allTargetsMet = Object.values(results.targetsMet).every((met) => met);
    console.log('🏆 OVERALL ASSESSMENT');
    console.log('-'.repeat(40));
    console.log(
      `Stress Test Result: ${allTargetsMet ? '✅ ALL TARGETS MET' : '❌ SOME TARGETS MISSED'}`
    );
    console.log(
      `System Stability: ${results.overall.timeouts === 0 ? '✅ STABLE' : '⚠️  UNSTABLE'}`
    );
    console.log(`Peak Concurrency: ${this.config.maxConcurrency} concurrent requests`);
    console.log(
      `Memory Efficiency: ${results.targetsMet.memoryUsage ? '✅ EFFICIENT' : '❌ NEEDS OPTIMIZATION'}`
    );
  }
}

// ============================================================================
// Main Execution
// ============================================================================

export async function runConcurrencyStressTest(): Promise<StressTestResults> {
  const test = new ConcurrencyStressTest();
  return await test.runStressTest();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runConcurrencyStressTest()
    .then((results) => {
      console.log('\n🎉 Concurrency stress test completed successfully!');

      // Output summary for automation
      console.log('\n📊 SUMMARY FOR AUTOMATION:');
      console.log(`MAX_CONCURRENCY: ${DEFAULT_STRESS_TEST_CONFIG.maxConcurrency}`);
      console.log(
        `SUCCESS_RATE: ${((results.overall.successfulRequests / results.overall.totalRequests) * 100).toFixed(2)}%`
      );
      console.log(`MEMORY_PEAK: ${(results.overall.memoryUsage.peak / 1024 / 1024).toFixed(2)}MB`);
      console.log(`TARGETS_MET: ${Object.values(results.targetsMet).every((met) => met)}`);

      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Concurrency stress test failed:', error);
      process.exit(1);
    });
}
