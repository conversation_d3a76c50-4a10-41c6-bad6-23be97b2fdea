import fs from 'fs';
import path from 'path';
import { prisma } from '../4-infrastructure/database/prisma';
import { WordListRepository } from '../2-repositories/WordListRepository';
import { WordListEntryRepository } from '../2-repositories/WordListEntryRepository';

/**
 * 词库数据导入脚本
 * 从 docs/res/english-vocabulary/ 目录读取7个TXT文件并导入到数据库
 */

interface WordListConfig {
  filename: string;
  name: string;
  description: string;
  difficulty: number;
}

const WORD_LIST_CONFIGS: WordListConfig[] = [
  {
    filename: '1 初中-乱序.txt',
    name: '初中词汇',
    description: '初中英语词汇，适合英语基础学习',
    difficulty: 2
  },
  {
    filename: '2 高中-乱序.txt',
    name: '高中词汇', 
    description: '高中英语词汇，进阶英语学习',
    difficulty: 4
  },
  {
    filename: '3 四级-乱序.txt',
    name: 'CET-4',
    description: '大学英语四级词汇',
    difficulty: 5
  },
  {
    filename: '4 六级-乱序.txt',
    name: 'CET-6',
    description: '大学英语六级词汇',
    difficulty: 6
  },
  {
    filename: '5 考研-乱序.txt',
    name: '考研词汇',
    description: '考研英语词汇',
    difficulty: 7
  },
  {
    filename: '6 托福-乱序.txt',
    name: 'TOEFL',
    description: '托福考试词汇',
    difficulty: 8
  },
  {
    filename: '7 SAT-乱序.txt',
    name: 'SAT',
    description: 'SAT考试词汇',
    difficulty: 9
  }
];

export class WordListImporter {
  private wordListRepo: WordListRepository;
  private wordListEntryRepo: WordListEntryRepository;
  private vocabDirectory: string;

  constructor() {
    this.wordListRepo = new WordListRepository();
    this.wordListEntryRepo = new WordListEntryRepository();
    this.vocabDirectory = path.join(process.cwd(), 'docs', 'res', 'english-vocabulary');
  }

  /**
   * 执行完整的导入流程
   */
  async importAll(): Promise<void> {
    console.log('🚀 开始导入词库数据...');
    
    try {
      // 1. 清理现有数据（可选）
      await this.cleanExistingData();

      // 2. 创建词库
      const wordLists = await this.createWordLists();
      console.log(`✅ 创建了 ${wordLists.length} 个词库`);

      // 3. 导入每个词库的单词
      for (const config of WORD_LIST_CONFIGS) {
        await this.importWordListFromFile(config);
      }

      // 4. 更新词库统计信息
      await this.updateWordListStats();

      console.log('🎉 词库数据导入完成！');

    } catch (error) {
      console.error('❌ 导入失败:', error);
      throw error;
    }
  }

  /**
   * 清理现有的词库数据
   */
  private async cleanExistingData(): Promise<void> {
    console.log('🧹 清理现有词库数据...');
    
    await prisma.wordListEntry.deleteMany();
    await prisma.wordList.deleteMany();
    
    console.log('✅ 清理完成');
  }

  /**
   * 创建所有词库
   */
  private async createWordLists(): Promise<any[]> {
    console.log('📚 创建词库...');
    
    const wordListsData = WORD_LIST_CONFIGS.map(config => ({
      name: config.name,
      description: config.description,
      difficulty: config.difficulty,
      totalWords: 0, // 将在导入单词后更新
      isActive: true
    }));

    return await this.wordListRepo.createMany(wordListsData);
  }

  /**
   * 从TXT文件导入单个词库
   */
  private async importWordListFromFile(config: WordListConfig): Promise<void> {
    const filePath = path.join(this.vocabDirectory, config.filename);
    
    console.log(`📖 开始导入 ${config.name} (${config.filename})`);

    // 检查文件是否存在
    if (!fs.existsSync(filePath)) {
      console.warn(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    // 获取词库信息
    const wordList = await this.wordListRepo.findByName(config.name);
    if (!wordList) {
      console.error(`❌ 词库不存在: ${config.name}`);
      return;
    }

    // 读取和解析文件
    const words = await this.parseWordListFile(filePath);
    console.log(`  📝 解析到 ${words.length} 个单词`);

    // 批量导入单词
    await this.importWordsInBatches(wordList.id, words);
    
    console.log(`✅ ${config.name} 导入完成`);
  }

  /**
   * 解析TXT文件，提取单词
   */
  private async parseWordListFile(filePath: string): Promise<string[]> {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.split('\n').filter(line => line.trim());
    
    const words: string[] = [];
    
    for (const line of lines) {
      // 解析格式: word\tdefinition
      const parts = line.split('\t');
      if (parts.length >= 2) {
        const word = parts[0].trim().toLowerCase();
        
        // 基本单词验证
        if (this.isValidEnglishWord(word)) {
          words.push(word);
        }
      }
    }

    // 去重
    return Array.from(new Set(words));
  }

  /**
   * 验证是否为有效的英文单词
   */
  private isValidEnglishWord(word: string): boolean {
    // 基本验证规则
    if (!word || word.length < 2 || word.length > 30) {
      return false;
    }

    // 只包含字母、连字符和撇号
    const validPattern = /^[a-zA-Z][a-zA-Z'-]*[a-zA-Z]$|^[a-zA-Z]$/;
    return validPattern.test(word);
  }

  /**
   * 批量导入单词，处理去重和多词库关联
   */
  private async importWordsInBatches(wordListId: number, words: string[]): Promise<void> {
    const BATCH_SIZE = 500;
    let processed = 0;

    for (let i = 0; i < words.length; i += BATCH_SIZE) {
      const batch = words.slice(i, i + BATCH_SIZE);
      
      for (const word of batch) {
        try {
          // 检查单词是否已存在
          const existingEntry = await this.wordListEntryRepo.findByWord(word);
          
          if (existingEntry) {
            // 如果存在，更新词库关联
            await this.wordListEntryRepo.updateWordListAssociation(word, [wordListId]);
          } else {
            // 如果不存在，创建新条目
            await this.wordListEntryRepo.create({
              word,
              wordListIds: [wordListId],
              wordListId
            });
          }
          
          processed++;
        } catch (error) {
          console.warn(`⚠️  导入单词失败: ${word}`, error);
        }
      }
      
      // 显示进度
      const progress = Math.round((processed / words.length) * 100);
      console.log(`  📊 进度: ${processed}/${words.length} (${progress}%)`);
    }
  }

  /**
   * 更新所有词库的统计信息
   */
  private async updateWordListStats(): Promise<void> {
    console.log('📊 更新词库统计信息...');
    
    const wordLists = await this.wordListRepo.findAll();
    
    for (const wordList of wordLists) {
      const count = await prisma.wordListEntry.count({
        where: {
          wordListIds: {
            has: wordList.id
          }
        }
      });
      
      await this.wordListRepo.update(wordList.id, {
        totalWords: count
      });
      
      console.log(`  ✅ ${wordList.name}: ${count} 个单词`);
    }
  }

  /**
   * 获取导入进度报告
   */
  async getImportReport(): Promise<void> {
    console.log('\n📋 导入报告:');
    console.log('='.repeat(50));
    
    const wordLists = await this.wordListRepo.findAll();
    let totalWords = 0;
    
    for (const wordList of wordLists) {
      console.log(`📚 ${wordList.name.padEnd(12)} | 难度: ${wordList.difficulty} | 单词数: ${wordList.totalWords}`);
      totalWords += wordList.totalWords;
    }
    
    console.log('='.repeat(50));
    console.log(`📈 总计: ${wordLists.length} 个词库, ${totalWords} 个单词`);
    
    // 检查重复单词统计
    const uniqueWords = await prisma.wordListEntry.count();
    console.log(`🔄 去重后: ${uniqueWords} 个唯一单词`);
    console.log(`📊 平均每个单词属于 ${(totalWords / uniqueWords).toFixed(1)} 个词库`);
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const importer = new WordListImporter();
  
  importer.importAll()
    .then(() => importer.getImportReport())
    .then(() => {
      console.log('🎯 导入脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 导入脚本执行失败:', error);
      process.exit(1);
    });
}

export default WordListImporter;