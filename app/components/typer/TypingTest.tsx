'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { useTypingEngine, State } from '@/hooks/use-typing-engine';
import { Character, Caret } from './shared';
import { TYPING_CONFIG } from '@/config/typing';
import { generateRandomWords } from '@/data/words';

/**
 * 正常打字训练组件
 * 提供标准的英语单词打字练习，包含时间限制和实时统计
 */
export const TypingTest: React.FC = () => {
  // Fix hydration mismatch by initializing words on client side only
  const [words, setWords] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize words on client side to avoid SSR/CSR mismatch
  useEffect(() => {
    setWords(generateRandomWords(TYPING_CONFIG.WORDS_COUNT));
    setIsInitialized(true);
  }, []);

  const { state, typed, timeLeft, wpm, accuracy, errors, restart, fullText } = useTypingEngine(
    words,
    TYPING_CONFIG.TEST_TIME_LIMIT
  );

  const handleRestart = useCallback(() => {
    setWords(generateRandomWords(TYPING_CONFIG.WORDS_COUNT));
  }, []);

  useEffect(() => {
    if (state === State.Start) {
      restart();
    }
  }, [words, restart, state]);

  const characters = useMemo(() => {
    if (!isInitialized || !fullText) return [];

    return fullText
      .split('')
      .map((char: string, index: number) => (
        <Character
          key={`${char}-${index}`}
          expected={char}
          actual={typed[index]}
          isTyped={index < typed.length}
        />
      ));
  }, [fullText, typed, isInitialized]);

  // Show loading state during initialization to prevent hydration mismatch
  if (!isInitialized) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-muted-foreground animate-pulse">Loading...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-around items-center p-4 bg-muted rounded-lg">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Time Left</p>
          <p className="text-3xl font-bold text-primary">{timeLeft}</p>
        </div>
        {state === State.Finish ? (
          <>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">WPM</p>
              <p className="text-3xl font-bold">{wpm}</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Accuracy</p>
              <p className="text-3xl font-bold">{accuracy}%</p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Errors</p>
              <p className="text-3xl font-bold text-destructive">{errors}</p>
            </div>
          </>
        ) : (
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Errors</p>
            <p className="text-3xl font-bold text-destructive">{errors}</p>
          </div>
        )}
      </div>

      <div className="text-2xl leading-relaxed break-words p-6 border rounded-md relative select-none font-mono">
        {typed.length === 0 && state === State.Start && <Caret />}
        {characters.map((charComponent, index) => (
          <React.Fragment key={index}>
            {charComponent}
            {index === typed.length - 1 && state === State.Run && <Caret />}
          </React.Fragment>
        ))}
        {state === State.Start && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80">
            <p className="text-muted-foreground animate-pulse">Start typing to begin...</p>
          </div>
        )}
      </div>

      <div className="flex justify-center">
        <Button onClick={handleRestart}>Restart</Button>
      </div>
    </div>
  );
};
