/**
 * OAuth Integration Tests
 * Testing NextAuth integration with enhanced token system
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import {
  enhancedGoogleProvider,
  enhancedGitHubProvider,
  getAvailableOAuthProviders,
  oauthEventHandlers,
  oauthUtils,
} from '../oauth-providers';
import { createTokenResponse, blacklistToken } from '../token';

// Mock NextAuth
vi.mock('next-auth', () => ({
  getServerSession: vi.fn(),
  default: vi.fn(),
}));

// Mock NextAuth providers
vi.mock('next-auth/providers/google', () => ({
  default: vi.fn(() => ({ id: 'google', name: 'Google' })),
}));

vi.mock('next-auth/providers/github', () => ({
  default: vi.fn(() => ({ id: 'github', name: 'GitHub' })),
}));

// Mock Prisma
vi.mock('@prisma/client', () => ({
  PrismaClient: vi.fn(() => ({
    user: {
      findUnique: vi.fn(),
      update: vi.fn(),
      create: vi.fn(),
    },
    account: {
      findUnique: vi.fn(),
      create: vi.fn(),
    },
    session: {
      findUnique: vi.fn(),
      create: vi.fn(),
      update: vi.fn(),
    },
  })),
}));

// Mock environment variables
const mockEnv = {
  GOOGLE_CLIENT_ID: 'test-google-client-id',
  GOOGLE_CLIENT_SECRET: 'test-google-client-secret',
  GITHUB_ID: 'test-github-id',
  GITHUB_SECRET: 'test-github-secret',
  NEXTAUTH_SECRET: 'test-nextauth-secret',
  NEXTAUTH_URL: 'http://localhost:3000',
};

describe('OAuth Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    Object.assign(process.env, mockEnv);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('OAuth Provider Configuration', () => {
    it('should configure Google provider with enhanced security', () => {
      const provider = enhancedGoogleProvider;

      expect(provider.id).toBe('google');
      expect(provider.name).toBe('Google');
      expect(provider.type).toBe('oauth');
      expect(provider.authorization?.params).toMatchObject({
        prompt: 'consent',
        access_type: 'offline',
        response_type: 'code',
        scope: 'openid email profile',
        include_granted_scopes: 'true',
        code_challenge_method: 'S256',
      });
    });

    it('should configure GitHub provider with enhanced security', () => {
      const provider = enhancedGitHubProvider;

      expect(provider.id).toBe('github');
      expect(provider.name).toBe('GitHub');
      expect(provider.type).toBe('oauth');
      expect(provider.authorization?.params).toMatchObject({
        scope: 'read:user user:email',
        allow_signup: 'true',
      });
    });

    it('should return available providers based on environment', () => {
      const providers = getAvailableOAuthProviders();

      expect(providers).toHaveLength(2);
      expect(providers.some((p) => p.id === 'google')).toBe(true);
      expect(providers.some((p) => p.id === 'github')).toBe(true);
    });

    it('should handle missing environment variables', () => {
      delete process.env.GOOGLE_CLIENT_ID;
      delete process.env.GITHUB_ID;

      const providers = getAvailableOAuthProviders();

      expect(providers).toHaveLength(0);
    });
  });

  describe('OAuth Event Handlers', () => {
    it('should handle sign-in events with audit logging', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockAccount = {
        provider: 'google',
        providerAccountId: 'google123',
      };

      const mockProfile = {
        id: 'google123',
        email: '<EMAIL>',
        email_verified: true,
        name: 'Test User',
      };

      const result = await oauthEventHandlers.signIn({
        user: mockUser,
        account: mockAccount,
        profile: mockProfile,
        isNewUser: false,
      });

      expect(result).toBe(true);
    });

    it('should reject Google sign-in with unverified email', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockAccount = {
        provider: 'google',
        providerAccountId: 'google123',
      };

      const mockProfile = {
        id: 'google123',
        email: '<EMAIL>',
        email_verified: false, // Unverified email
        name: 'Test User',
      };

      const result = await oauthEventHandlers.signIn({
        user: mockUser,
        account: mockAccount,
        profile: mockProfile,
        isNewUser: false,
      });

      expect(result).toBe(false);
    });

    it('should handle sign-out events with audit logging', async () => {
      const mockToken = {
        sub: 'user123',
        provider: 'google',
        iat: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago
      };

      await expect(
        oauthEventHandlers.signOut({
          token: mockToken,
        })
      ).resolves.not.toThrow();
    });

    it('should handle user creation events', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        image: 'https://example.com/avatar.jpg',
        provider: 'google',
      };

      await expect(
        oauthEventHandlers.createUser({
          user: mockUser,
        })
      ).resolves.not.toThrow();
    });

    it('should handle account linking events', async () => {
      const mockUser = {
        id: 'user123',
        accounts: [{ provider: 'google' }],
      };

      const mockAccount = {
        provider: 'github',
        providerAccountId: 'github123',
      };

      const mockProfile = {
        id: 'github123',
        login: 'testuser',
      };

      await expect(
        oauthEventHandlers.linkAccount({
          user: mockUser,
          account: mockAccount,
          profile: mockProfile,
        })
      ).resolves.not.toThrow();
    });
  });

  describe('OAuth Utilities', () => {
    it('should return correct provider icons', () => {
      expect(oauthUtils.getProviderIcon('google')).toBe('fab fa-google');
      expect(oauthUtils.getProviderIcon('github')).toBe('fab fa-github');
      expect(oauthUtils.getProviderIcon('linkedin')).toBe('fab fa-linkedin');
      expect(oauthUtils.getProviderIcon('unknown')).toBe('fas fa-sign-in-alt');
    });

    it('should return correct provider display names', () => {
      expect(oauthUtils.getProviderDisplayName('google')).toBe('Google');
      expect(oauthUtils.getProviderDisplayName('github')).toBe('GitHub');
      expect(oauthUtils.getProviderDisplayName('linkedin')).toBe('LinkedIn');
      expect(oauthUtils.getProviderDisplayName('unknown')).toBe('unknown');
    });

    it('should return correct provider colors', () => {
      expect(oauthUtils.getProviderColor('google')).toBe('#4285f4');
      expect(oauthUtils.getProviderColor('github')).toBe('#333333');
      expect(oauthUtils.getProviderColor('linkedin')).toBe('#0077b5');
      expect(oauthUtils.getProviderColor('unknown')).toBe('#6b7280');
    });
  });

  describe('NextAuth Integration', () => {
    it('should configure NextAuth with enhanced security', () => {
      expect(authOptions).toBeDefined();
      expect(authOptions.adapter).toBeDefined();
      expect(authOptions.providers).toBeDefined();
      expect(authOptions.session?.strategy).toBe('jwt');
      expect(authOptions.callbacks).toBeDefined();
    });

    it('should handle JWT callbacks correctly', async () => {
      const mockToken = {
        sub: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      };

      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockAccount = {
        provider: 'google',
        access_token: 'mock-access-token',
        refresh_token: 'mock-refresh-token',
      };

      const result = await authOptions.callbacks?.jwt?.({
        token: mockToken,
        user: mockUser,
        account: mockAccount,
      });

      expect(result).toBeDefined();
      expect(result.sub).toBe('user123');
      expect(result.email).toBe('<EMAIL>');
    });

    it('should handle session callbacks correctly', async () => {
      const mockSession = {
        user: {
          id: 'user123',
          email: '<EMAIL>',
          name: 'Test User',
        },
        expires: new Date(Date.now() + 3600000).toISOString(),
      };

      const mockToken = {
        sub: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        accessToken: 'mock-access-token',
      };

      const result = await authOptions.callbacks?.session?.({
        session: mockSession,
        token: mockToken,
      });

      expect(result).toBeDefined();
      expect(result.user?.id).toBe('user123');
      expect(result.user?.email).toBe('<EMAIL>');
    });

    it('should handle sign-in callbacks with security checks', async () => {
      const mockUser = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
      };

      const mockAccount = {
        provider: 'google',
        type: 'oauth',
      };

      const result = await authOptions.callbacks?.signIn?.({
        user: mockUser,
        account: mockAccount,
      });

      expect(result).toBe(true);
    });

    it('should handle redirect callbacks securely', async () => {
      const baseUrl = 'http://localhost:3000';

      // Test relative URL
      const relativeResult = await authOptions.callbacks?.redirect?.({
        url: '/dashboard',
        baseUrl,
      });
      expect(relativeResult).toBe('http://localhost:3000/dashboard');

      // Test same origin URL
      const sameOriginResult = await authOptions.callbacks?.redirect?.({
        url: 'http://localhost:3000/profile',
        baseUrl,
      });
      expect(sameOriginResult).toBe('http://localhost:3000/profile');

      // Test external URL (should redirect to baseUrl)
      const externalResult = await authOptions.callbacks?.redirect?.({
        url: 'https://malicious-site.com',
        baseUrl,
      });
      expect(externalResult).toBe(baseUrl);
    });
  });

  describe('Security Features', () => {
    it('should implement rate limiting for OAuth requests', () => {
      const { oauthSecurityConfig } = require('../oauth-providers');

      expect(oauthSecurityConfig.rateLimits).toBeDefined();
      expect(oauthSecurityConfig.rateLimits.signIn).toMatchObject({
        max: 5,
        windowMs: 60 * 1000,
      });
      expect(oauthSecurityConfig.rateLimits.signUp).toMatchObject({
        max: 3,
        windowMs: 60 * 60 * 1000,
      });
    });

    it('should implement security headers', () => {
      const { oauthSecurityConfig } = require('../oauth-providers');

      expect(oauthSecurityConfig.securityHeaders).toBeDefined();
      expect(oauthSecurityConfig.securityHeaders['X-Frame-Options']).toBe('DENY');
      expect(oauthSecurityConfig.securityHeaders['X-Content-Type-Options']).toBe('nosniff');
      expect(oauthSecurityConfig.securityHeaders['X-XSS-Protection']).toBe('1; mode=block');
    });

    it('should implement PKCE configuration', () => {
      const { oauthSecurityConfig } = require('../oauth-providers');

      expect(oauthSecurityConfig.pkceConfig).toBeDefined();
      expect(oauthSecurityConfig.pkceConfig.codeChallenge).toBe(true);
      expect(oauthSecurityConfig.pkceConfig.codeChallengeMethod).toBe('S256');
    });
  });

  describe('Error Handling', () => {
    it('should handle OAuth provider errors gracefully', async () => {
      const mockError = new Error('OAuth provider error');

      // Mock a failing OAuth request
      vi.mocked(enhancedGoogleProvider.authorization as any).mockRejectedValue(mockError);

      // The error should be handled gracefully without crashing
      expect(() => {
        getAvailableOAuthProviders();
      }).not.toThrow();
    });

    it('should handle token refresh errors', async () => {
      const mockToken = {
        sub: 'user123',
        email: '<EMAIL>',
        refreshToken: 'invalid-refresh-token',
        accessTokenExpires: Date.now() - 1000, // Expired
      };

      const result = await authOptions.callbacks?.jwt?.({
        token: mockToken,
      });

      expect(result).toBeDefined();
      expect(result.error).toBe('RefreshAccessTokenError');
    });
  });

  describe('Performance', () => {
    it('should handle multiple concurrent OAuth requests', async () => {
      const requests = Array(10)
        .fill(null)
        .map((_, i) =>
          oauthEventHandlers.signIn({
            user: { id: `user${i}`, email: `user${i}@example.com` },
            account: { provider: 'google' },
            profile: { email_verified: true },
            isNewUser: false,
          })
        );

      const results = await Promise.all(requests);

      expect(results).toHaveLength(10);
      expect(results.every((r) => r === true)).toBe(true);
    });

    it('should optimize provider configuration loading', () => {
      const startTime = Date.now();

      // Load providers multiple times
      for (let i = 0; i < 100; i++) {
        getAvailableOAuthProviders();
      }

      const endTime = Date.now();

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(100);
    });
  });
});
