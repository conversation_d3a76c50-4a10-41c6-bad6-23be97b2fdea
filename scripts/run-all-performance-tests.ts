#!/usr/bin/env tsx

/**
 * Master Performance Test Runner
 *
 * Orchestrates all performance tests and generates comprehensive reports
 *
 * Usage:
 *   tsx scripts/run-all-performance-tests.ts
 *   tsx scripts/run-all-performance-tests.ts --generate-report
 *   tsx scripts/run-all-performance-tests.ts --regression-only
 */

import { generateComprehensiveReport } from './comprehensive-performance-report';
import { runLightweightSecurityPerformanceTest } from './lightweight-performance-test';
import { runBenchmarkComparison } from './benchmark-comparison';
import { runCachePerformanceTest } from './cache-performance-test';
import { runConcurrencyStressTest } from './concurrency-stress-test';
import { runPerformanceRegressionTest } from './performance-regression-test';

// ============================================================================
// Test Runner Configuration
// ============================================================================

interface TestRunnerConfig {
  runLightweightTest: boolean;
  runBenchmarkTest: boolean;
  runCacheTest: boolean;
  runConcurrencyTest: boolean;
  runRegressionTest: boolean;
  generateReport: boolean;
  verbose: boolean;
  exitOnFailure: boolean;
}

// ============================================================================
// Command Line Parser
// ============================================================================

function parseCommandLine(): TestRunnerConfig {
  const args = process.argv.slice(2);

  const config: TestRunnerConfig = {
    runLightweightTest: true,
    runBenchmarkTest: true,
    runCacheTest: true,
    runConcurrencyTest: true,
    runRegressionTest: true,
    generateReport: false,
    verbose: false,
    exitOnFailure: true,
  };

  for (const arg of args) {
    switch (arg) {
      case '--generate-report':
        config.generateReport = true;
        break;
      case '--regression-only':
        config.runLightweightTest = false;
        config.runBenchmarkTest = false;
        config.runCacheTest = false;
        config.runConcurrencyTest = false;
        config.runRegressionTest = true;
        break;
      case '--lightweight-only':
        config.runLightweightTest = true;
        config.runBenchmarkTest = false;
        config.runCacheTest = false;
        config.runConcurrencyTest = false;
        config.runRegressionTest = false;
        break;
      case '--benchmark-only':
        config.runLightweightTest = false;
        config.runBenchmarkTest = true;
        config.runCacheTest = false;
        config.runConcurrencyTest = false;
        config.runRegressionTest = false;
        break;
      case '--cache-only':
        config.runLightweightTest = false;
        config.runBenchmarkTest = false;
        config.runCacheTest = true;
        config.runConcurrencyTest = false;
        config.runRegressionTest = false;
        break;
      case '--concurrency-only':
        config.runLightweightTest = false;
        config.runBenchmarkTest = false;
        config.runCacheTest = false;
        config.runConcurrencyTest = true;
        config.runRegressionTest = false;
        break;
      case '--verbose':
        config.verbose = true;
        break;
      case '--no-exit-on-failure':
        config.exitOnFailure = false;
        break;
      case '--help':
        printHelp();
        process.exit(0);
        break;
    }
  }

  return config;
}

function printHelp(): void {
  console.log(`
Performance Test Runner

Usage: tsx scripts/run-all-performance-tests.ts [options]

Options:
  --generate-report      Generate comprehensive performance report
  --regression-only      Run only regression tests
  --lightweight-only     Run only lightweight middleware tests
  --benchmark-only       Run only benchmark comparison tests
  --cache-only          Run only cache performance tests
  --concurrency-only    Run only concurrency stress tests
  --verbose             Enable verbose output
  --no-exit-on-failure  Don't exit on test failures
  --help                Show this help message

Examples:
  tsx scripts/run-all-performance-tests.ts
  tsx scripts/run-all-performance-tests.ts --generate-report
  tsx scripts/run-all-performance-tests.ts --regression-only
  tsx scripts/run-all-performance-tests.ts --lightweight-only --verbose
`);
}

// ============================================================================
// Test Results Aggregator
// ============================================================================

interface TestResult {
  name: string;
  success: boolean;
  duration: number;
  error?: string;
  results?: any;
}

interface TestSummary {
  totalTests: number;
  successfulTests: number;
  failedTests: number;
  totalDuration: number;
  results: TestResult[];
  overallSuccess: boolean;
}

class TestResultsAggregator {
  private results: TestResult[] = [];
  private startTime: number = 0;

  start(): void {
    this.startTime = Date.now();
    this.results = [];
  }

  addResult(result: TestResult): void {
    this.results.push(result);
  }

  getSummary(): TestSummary {
    const successfulTests = this.results.filter((r) => r.success).length;
    const failedTests = this.results.filter((r) => !r.success).length;
    const totalDuration = Date.now() - this.startTime;

    return {
      totalTests: this.results.length,
      successfulTests,
      failedTests,
      totalDuration,
      results: this.results,
      overallSuccess: failedTests === 0,
    };
  }
}

// ============================================================================
// Main Test Runner
// ============================================================================

class PerformanceTestRunner {
  private config: TestRunnerConfig;
  private aggregator: TestResultsAggregator;

  constructor(config: TestRunnerConfig) {
    this.config = config;
    this.aggregator = new TestResultsAggregator();
  }

  async runAllTests(): Promise<TestSummary> {
    console.log('🚀 Starting Performance Test Suite');
    console.log('='.repeat(60));
    this.printConfiguration();
    console.log('');

    this.aggregator.start();

    // Run individual tests
    if (this.config.runLightweightTest) {
      await this.runTest('Lightweight Security Performance', runLightweightSecurityPerformanceTest);
    }

    if (this.config.runBenchmarkTest) {
      await this.runTest('Benchmark Comparison', runBenchmarkComparison);
    }

    if (this.config.runCacheTest) {
      await this.runTest('Cache Performance', runCachePerformanceTest);
    }

    if (this.config.runConcurrencyTest) {
      await this.runTest('Concurrency Stress', runConcurrencyStressTest);
    }

    if (this.config.runRegressionTest) {
      await this.runTest('Performance Regression', runPerformanceRegressionTest);
    }

    // Generate comprehensive report if requested
    if (this.config.generateReport) {
      await this.runTest('Comprehensive Report Generation', generateComprehensiveReport);
    }

    const summary = this.aggregator.getSummary();
    this.printSummary(summary);

    return summary;
  }

  private printConfiguration(): void {
    console.log('📋 Test Configuration:');
    console.log(`  - Lightweight Security Test: ${this.config.runLightweightTest ? '✅' : '❌'}`);
    console.log(`  - Benchmark Comparison Test: ${this.config.runBenchmarkTest ? '✅' : '❌'}`);
    console.log(`  - Cache Performance Test: ${this.config.runCacheTest ? '✅' : '❌'}`);
    console.log(`  - Concurrency Stress Test: ${this.config.runConcurrencyTest ? '✅' : '❌'}`);
    console.log(`  - Regression Test: ${this.config.runRegressionTest ? '✅' : '❌'}`);
    console.log(`  - Generate Report: ${this.config.generateReport ? '✅' : '❌'}`);
    console.log(`  - Verbose Mode: ${this.config.verbose ? '✅' : '❌'}`);
    console.log(`  - Exit on Failure: ${this.config.exitOnFailure ? '✅' : '❌'}`);
  }

  private async runTest(name: string, testFunction: () => Promise<any>): Promise<void> {
    console.log(`\n🔧 Running ${name}...`);
    const startTime = Date.now();

    try {
      const results = await testFunction();
      const duration = Date.now() - startTime;

      const result: TestResult = {
        name,
        success: true,
        duration,
        results,
      };

      this.aggregator.addResult(result);
      console.log(`✅ ${name} completed in ${(duration / 1000).toFixed(2)}s`);

      if (this.config.verbose) {
        console.log(`   Results: ${JSON.stringify(results, null, 2)}`);
      }
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      const result: TestResult = {
        name,
        success: false,
        duration,
        error: errorMessage,
      };

      this.aggregator.addResult(result);
      console.error(`❌ ${name} failed after ${(duration / 1000).toFixed(2)}s`);
      console.error(`   Error: ${errorMessage}`);

      if (this.config.verbose && error instanceof Error) {
        console.error(`   Stack: ${error.stack}`);
      }

      if (this.config.exitOnFailure) {
        console.error('\n🚨 Test suite terminated due to failure');
        process.exit(1);
      }
    }
  }

  private printSummary(summary: TestSummary): void {
    console.log('\n📊 TEST SUITE SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${summary.totalTests}`);
    console.log(`Successful: ${summary.successfulTests} ✅`);
    console.log(`Failed: ${summary.failedTests} ${summary.failedTests > 0 ? '❌' : '✅'}`);
    console.log(`Total Duration: ${(summary.totalDuration / 1000).toFixed(2)}s`);
    console.log(`Overall Result: ${summary.overallSuccess ? '✅ SUCCESS' : '❌ FAILURE'}`);
    console.log('');

    // Detailed results
    console.log('📋 DETAILED RESULTS:');
    console.log('-'.repeat(60));
    console.log('Test                           | Status | Duration');
    console.log('-'.repeat(60));

    for (const result of summary.results) {
      const statusIcon = result.success ? '✅' : '❌';
      const durationStr = `${(result.duration / 1000).toFixed(2)}s`;
      console.log(
        `${result.name.padEnd(30)} | ${statusIcon} ${(result.success ? 'PASS' : 'FAIL').padEnd(4)} | ${durationStr.padStart(8)}`
      );
    }
    console.log('');

    // Performance insights
    this.printPerformanceInsights(summary);
  }

  private printPerformanceInsights(summary: TestSummary): void {
    console.log('💡 PERFORMANCE INSIGHTS:');
    console.log('-'.repeat(40));

    const successfulResults = summary.results.filter((r) => r.success);

    if (successfulResults.length === 0) {
      console.log('❌ No successful tests to analyze');
      return;
    }

    // Find the longest running test
    const longestTest = successfulResults.reduce((prev, current) =>
      current.duration > prev.duration ? current : prev
    );

    console.log(
      `⏱️  Longest test: ${longestTest.name} (${(longestTest.duration / 1000).toFixed(2)}s)`
    );

    // Find the fastest test
    const fastestTest = successfulResults.reduce((prev, current) =>
      current.duration < prev.duration ? current : prev
    );

    console.log(
      `⚡ Fastest test: ${fastestTest.name} (${(fastestTest.duration / 1000).toFixed(2)}s)`
    );

    // Calculate average test duration
    const avgDuration =
      successfulResults.reduce((sum, r) => sum + r.duration, 0) / successfulResults.length;
    console.log(`📊 Average test duration: ${(avgDuration / 1000).toFixed(2)}s`);

    // Performance recommendations
    if (summary.overallSuccess) {
      console.log('✅ All performance tests passed successfully');
      console.log('💡 Consider running these tests regularly to maintain performance');
    } else {
      console.log('⚠️  Some tests failed - investigate and fix issues');
      console.log('💡 Run individual tests to debug specific failures');
    }
  }
}

// ============================================================================
// Main Execution
// ============================================================================

async function main(): Promise<void> {
  try {
    const config = parseCommandLine();
    const runner = new PerformanceTestRunner(config);
    const summary = await runner.runAllTests();

    // Exit with appropriate code
    if (summary.overallSuccess) {
      console.log('\n🎉 All performance tests completed successfully!');

      // Output machine-readable summary
      console.log('\n📊 MACHINE-READABLE SUMMARY:');
      console.log(`TESTS_TOTAL: ${summary.totalTests}`);
      console.log(`TESTS_PASSED: ${summary.successfulTests}`);
      console.log(`TESTS_FAILED: ${summary.failedTests}`);
      console.log(`DURATION_TOTAL: ${summary.totalDuration}`);
      console.log(`OVERALL_SUCCESS: ${summary.overallSuccess}`);

      process.exit(0);
    } else {
      console.log('\n❌ Some performance tests failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('💥 Fatal error in test runner:', error);
    process.exit(1);
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  main();
}

export { PerformanceTestRunner, TestResult, TestSummary };
