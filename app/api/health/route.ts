import { NextResponse } from 'next/server';
import { checkRedisHealth, getRedisStats } from '../../lib/4-infrastructure/cache/redis';
import { checkDatabaseHealth } from '../../lib/4-infrastructure/database/prisma';

/**
 * 健康检查API
 * GET /api/health
 *
 * 检查系统各组件的健康状态：
 * - Redis 缓存连接
 * - PostgreSQL 数据库连接
 * - 基本系统信息
 */
export async function GET(request: Request) {
  const startTime = Date.now();

  try {
    // 并行检查各个服务的健康状态
    const [redisHealthy, dbHealthy, redisStats] = await Promise.allSettled([
      checkRedisHealth(),
      checkDatabaseHealth(),
      getRedisStats(),
    ]);

    // 处理Redis健康检查结果
    const redisStatus = {
      healthy: redisHealthy.status === 'fulfilled' ? redisHealthy.value : false,
      error: redisHealthy.status === 'rejected' ? redisHealthy.reason?.message : null,
      stats: redisStats.status === 'fulfilled' ? redisStats.value : null,
    };

    // 处理数据库健康检查结果
    const dbStatus = {
      healthy: dbHealthy.status === 'fulfilled' ? dbHealthy.value : false,
      error: dbHealthy.status === 'rejected' ? dbHealthy.reason?.message : null,
    };

    // 计算总体健康状态
    const overallHealthy = redisStatus.healthy && dbStatus.healthy;
    const processingTime = Date.now() - startTime;

    const healthData = {
      ok: overallHealthy,
      message: overallHealthy ? 'All services are healthy' : 'Some services are unhealthy',
      timestamp: new Date().toISOString(),
      processingTime: `${processingTime}ms`,
      services: {
        redis: {
          status: redisStatus.healthy ? 'healthy' : 'unhealthy',
          connected: redisStatus.stats?.connected || false,
          memoryUsage: redisStatus.stats?.memoryUsage || 0,
          keyCount: redisStatus.stats?.keyCount || 0,
          hitRate: redisStatus.stats?.hitRate || 0,
          error: redisStatus.error,
        },
        database: {
          status: dbStatus.healthy ? 'healthy' : 'unhealthy',
          error: dbStatus.error,
        },
        application: {
          status: 'healthy',
          uptime: process.uptime(),
          nodeVersion: process.version,
          environment: process.env.NODE_ENV || 'development',
        },
      },
    };

    // 根据健康状态返回适当的HTTP状态码
    const statusCode = overallHealthy ? 200 : 503;

    return NextResponse.json(healthData, { status: statusCode });
  } catch (error) {
    const processingTime = Date.now() - startTime;

    console.error('Health check failed:', error);

    return NextResponse.json(
      {
        ok: false,
        message: 'Health check failed',
        timestamp: new Date().toISOString(),
        processingTime: `${processingTime}ms`,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
