/**
 * 生词本管理API路由
 * 支持GET/POST/DELETE操作，使用双认证策略（Session + JWT）
 */

import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/1-services/auth/AuthMiddleware';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import {
  GetWordsQuerySchema,
  AddWordsBodySchema,
  DeleteWordsBodySchema,
  ApiResponse,
  ValidationError,
  NotFoundError,
  ConflictError,
} from '@/lib/types/api-types';

// 创建PracticeService实例
const practiceService = createPracticeService();

/**
 * GET /api/word - 获取用户生词列表
 * 支持分页和排序功能
 */
export const GET = requireAuth({
  methods: ['session', 'jwt'],
  clientTypes: ['web', 'mobile', 'extension'],
  scopes: ['word:read'],
  rateLimit: { limit: 100, window: 60000 }, // 每分钟100次请求
})(async (request: NextRequest, context) => {
  try {
    const { user } = context;
    const { searchParams } = new URL(request.url);

    // 解析和验证查询参数
    const queryResult = GetWordsQuerySchema.safeParse({
      page: searchParams.get('page') || undefined,
      limit: searchParams.get('limit') || undefined,
      sortBy: searchParams.get('sortBy') || undefined,
      order: searchParams.get('order') || undefined,
    });

    if (!queryResult.success) {
      throw new ValidationError('Invalid query parameters', queryResult.error.issues);
    }

    const query = queryResult.data;

    // 获取用户生词列表
    const userWordList = await practiceService.getUserWordList(user.id, query);

    const response: ApiResponse = {
      success: true,
      data: userWordList.words,
      pagination: userWordList.pagination,
      message: `Successfully retrieved ${userWordList.words.length} words`,
    };

    return NextResponse.json(response);
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * POST /api/word - 批量向生词本添加单词
 * 支持通过单词文本或ID添加
 */
export const POST = requireAuth({
  methods: ['session', 'jwt'],
  clientTypes: ['web', 'mobile', 'extension'],
  scopes: ['word:write'],
  rateLimit: { limit: 50, window: 60000 }, // 每分钟50次请求
})(async (request: NextRequest, context) => {
  try {
    const { user } = context;
    const body = await request.json();

    // 验证请求体
    const bodyResult = AddWordsBodySchema.safeParse(body);
    if (!bodyResult.success) {
      throw new ValidationError('Invalid request body', bodyResult.error.issues);
    }

    const { words, wordIds } = bodyResult.data;

    // 添加单词到用户生词本
    const result = await practiceService.addWordsToUserList(user.id, words, wordIds);

    // 构建响应消息
    let message = `Successfully added ${result.added} words to your vocabulary list`;
    if (result.skipped > 0) {
      message += `, ${result.skipped} words were already in your list`;
    }
    if (result.errors.length > 0) {
      message += `. Errors: ${result.errors.join(', ')}`;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        added: result.added,
        skipped: result.skipped,
        errors: result.errors,
      },
      message,
    };

    return NextResponse.json(response, { status: 201 });
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * DELETE /api/word - 批量从生词本删除单词
 * 通过单词ID删除
 */
export const DELETE = requireAuth({
  methods: ['session', 'jwt'],
  clientTypes: ['web', 'mobile', 'extension'],
  scopes: ['word:write'],
  rateLimit: { limit: 30, window: 60000 }, // 每分钟30次请求
})(async (request: NextRequest, context) => {
  try {
    const { user } = context;
    const body = await request.json();

    // 验证请求体
    const bodyResult = DeleteWordsBodySchema.safeParse(body);
    if (!bodyResult.success) {
      throw new ValidationError('Invalid request body', bodyResult.error.issues);
    }

    const { wordIds } = bodyResult.data;

    // 从用户生词本删除单词
    const result = await practiceService.removeWordsFromUserList(user.id, wordIds);

    // 构建响应消息
    let message = `Successfully removed ${result.deleted} words from your vocabulary list`;
    if (result.notFound > 0) {
      message += `, ${result.notFound} words were not found in your list`;
    }

    const response: ApiResponse = {
      success: true,
      data: {
        deleted: result.deleted,
        notFound: result.notFound,
      },
      message,
    };

    return NextResponse.json(response);
  } catch (error) {
    return handleApiError(error);
  }
});

/**
 * 统一的API错误处理函数
 */
function handleApiError(error: unknown): NextResponse {
  console.error('API Error:', error);

  if (error instanceof ValidationError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
        details: error.details,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof NotFoundError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
      },
      { status: error.statusCode }
    );
  }

  if (error instanceof ConflictError) {
    return NextResponse.json(
      {
        success: false,
        error: error.message,
        code: error.code,
      },
      { status: error.statusCode }
    );
  }

  // 未知错误
  return NextResponse.json(
    {
      success: false,
      error: 'Internal server error',
      code: 'INTERNAL_ERROR',
    },
    { status: 500 }
  );
}