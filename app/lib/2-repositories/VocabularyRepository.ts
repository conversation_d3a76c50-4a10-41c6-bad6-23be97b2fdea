/**
 * 词汇仓储实现
 * 负责词汇相关的数据访问操作
 */

import { prisma } from '../4-infrastructure/database/prisma';
import { Vocabulary, VocabularyData } from '../3-domain/entities/Vocabulary';
import { IVocabularyRepository, QueryOptions } from './interfaces/IRepository';

export class VocabularyRepository implements IVocabularyRepository {
  /**
   * 根据ID查找词汇
   */
  async findById(id: number): Promise<Vocabulary | null> {
    const data = await prisma.vocabulary.findUnique({
      where: { id },
      include: this.getDefaultIncludes(),
    });

    return data ? this.mapToEntity(data) : null;
  }

  /**
   * 根据单词查找词汇
   */
  async findByWord(word: string): Promise<Vocabulary | null> {
    const data = await prisma.vocabulary.findFirst({
      where: { word: word.toLowerCase() },
      include: this.getDefaultIncludes(),
    });

    return data ? this.mapToEntity(data) : null;
  }

  /**
   * 根据单词查找词汇（高性能优化版本）
   * 使用分离查询策略，避免复杂JOIN，专为高并发场景优化
   */
  async findByWordOptimized(word: string): Promise<Vocabulary | null> {
    const queryId = Math.random().toString(36).substring(2, 10);
    const startTime = performance.now();

    // 🔍 DEBUG: 数据库查询开始
    const { Logger } = await import('../utils/logger');
    Logger.debug(`[DB-${queryId}] 🗄️ 开始高性能数据库查询`, {
      queryId,
      operation: 'findByWordOptimized_v2',
      word: word.toLowerCase(),
      strategy: 'separated_queries',
      timestamp: new Date().toISOString(),
    });

    try {
      // 第一步：查询基础词汇信息和释义（优化的JOIN）
      const baseQueryStart = performance.now();
      const vocabularyData = await prisma.vocabulary.findFirst({
        where: { word: word.toLowerCase() },
        include: {
          explains: {
            include: {
              definitions: true,
            },
            orderBy: { id: 'asc' },
          },
        },
      });
      const baseQueryTime = performance.now() - baseQueryStart;

      if (!vocabularyData) {
        Logger.debug(`[DB-${queryId}] ❌ 词汇未找到`, {
          queryId,
          baseQueryTime: Math.round(baseQueryTime),
          word: word.toLowerCase(),
        });
        return null;
      }

      // 第二步：如果需要，单独查询词形变化（可选，高性能）
      let formats = null;
      let formatsQueryTime = 0;

      // 注释掉formats查询以获得最佳性能，如果需要可以启用
      // const formatsQueryStart = performance.now();
      // formats = await prisma.wordFormat.findMany({
      //   where: { vocabularyId: vocabularyData.id },
      //   select: {
      //     id: true,
      //     name: true,
      //     form: true,
      //   },
      // });
      // formatsQueryTime = performance.now() - formatsQueryStart;

      const totalQueryTime = baseQueryTime + formatsQueryTime;

      // 🔍 DEBUG: 数据库查询完成
      Logger.debug(`[DB-${queryId}] ✅ 高性能查询完成`, {
        queryId,
        baseQueryTime: Math.round(baseQueryTime),
        formatsQueryTime: Math.round(formatsQueryTime),
        totalQueryTime: Math.round(totalQueryTime),
        found: true,
        word: word.toLowerCase(),
        recordId: vocabularyData.id,
        hasFormats: !!formats,
      });

      // 第三步：实体映射
      const mappingStart = performance.now();
      const result = this.mapToEntity({
        ...vocabularyData,
        formats: formats || [],
      });
      const mappingTime = performance.now() - mappingStart;
      const totalTime = performance.now() - startTime;

      // 🔍 DEBUG: 处理完成
      Logger.debug(`[DB-${queryId}] 🎉 高性能处理完成`, {
        queryId,
        mappingTime: Math.round(mappingTime),
        totalProcessingTime: Math.round(totalTime),
        performanceGain: 'optimized_separated_queries',
      });

      return result;
    } catch (error) {
      const errorTime = performance.now() - startTime;

      // 🔍 DEBUG: 数据库查询失败
      Logger.debug(`[DB-${queryId}] ❌ 高性能查询失败`, {
        queryId,
        errorTime: Math.round(errorTime),
        error: error instanceof Error ? error.message : String(error),
        word: word.toLowerCase(),
      });

      throw error;
    }
  }

  /**
   * 根据单词列表批量查找
   */
  async findByWords(words: string[]): Promise<Vocabulary[]> {
    const data = await prisma.vocabulary.findMany({
      where: {
        word: {
          in: words.map((w) => w.toLowerCase()),
        },
      },
      include: this.getDefaultIncludes(),
    });

    return data.map((item) => this.mapToEntity(item));
  }

  /**
   * 根据词频排名查找
   */
  async findByFreqRank(minRank: number, maxRank: number): Promise<Vocabulary[]> {
    const data = await prisma.vocabulary.findMany({
      where: {
        freqRank: {
          gte: minRank,
          lte: maxRank,
        },
      },
      include: this.getDefaultIncludes(),
      orderBy: { freqRank: 'asc' },
    });

    return data.map((item) => this.mapToEntity(item));
  }

  /**
   * 搜索相似词汇
   */
  async findSimilar(word: string, limit: number = 10): Promise<Vocabulary[]> {
    const searchTerm = word.toLowerCase();
    const prefix = searchTerm.substring(0, Math.min(3, searchTerm.length));

    const data = await prisma.vocabulary.findMany({
      where: {
        word: {
          contains: prefix,
          mode: 'insensitive',
        },
        NOT: {
          word: searchTerm,
        },
      },
      include: this.getDefaultIncludes(),
      orderBy: [{ freqRank: 'asc' }, { word: 'asc' }],
      take: limit,
    });

    return data.map((item) => this.mapToEntity(item));
  }

  /**
   * 获取高频词汇
   */
  async findHighFrequency(limit: number = 100): Promise<Vocabulary[]> {
    const data = await prisma.vocabulary.findMany({
      where: {
        freqRank: {
          not: null,
          lte: 1000,
        },
      },
      include: this.getDefaultIncludes(),
      orderBy: { freqRank: 'asc' },
      take: limit,
    });

    return data.map((item) => this.mapToEntity(item));
  }

  /**
   * 根据词性查找词汇
   */
  async findByPOS(pos: string, limit: number = 50): Promise<Vocabulary[]> {
    const data = await prisma.vocabulary.findMany({
      where: {
        explains: {
          some: {
            pos: pos,
          },
        },
      },
      include: this.getDefaultIncludes(),
      orderBy: { freqRank: 'asc' },
      take: limit,
    });

    return data.map((item) => this.mapToEntity(item));
  }

  /**
   * 全文搜索
   */
  async search(query: string, limit: number = 20): Promise<Vocabulary[]> {
    const searchTerm = query.toLowerCase();

    // 使用多种搜索策略
    const [exactMatch, prefixMatch, containsMatch] = await Promise.all([
      // 精确匹配
      prisma.vocabulary.findMany({
        where: { word: searchTerm },
        include: this.getDefaultIncludes(),
        take: 1,
      }),
      // 前缀匹配
      prisma.vocabulary.findMany({
        where: {
          word: {
            startsWith: searchTerm,
          },
        },
        include: this.getDefaultIncludes(),
        orderBy: { freqRank: 'asc' },
        take: Math.floor(limit / 2),
      }),
      // 包含匹配
      prisma.vocabulary.findMany({
        where: {
          word: {
            contains: searchTerm,
            mode: 'insensitive',
          },
          NOT: {
            word: {
              startsWith: searchTerm,
            },
          },
        },
        include: this.getDefaultIncludes(),
        orderBy: { freqRank: 'asc' },
        take: Math.floor(limit / 2),
      }),
    ]);

    // 合并结果并去重
    const allResults = [...exactMatch, ...prefixMatch, ...containsMatch];
    const uniqueResults = allResults.filter(
      (item, index, self) => self.findIndex((t) => t.id === item.id) === index
    );

    return uniqueResults.slice(0, limit).map((item) => this.mapToEntity(item));
  }

  /**
   * 获取词汇统计信息
   */
  async getStats(): Promise<{
    totalCount: number;
    avgComplexity: number;
    topPOS: Array<{ pos: string; count: number }>;
  }> {
    const [totalCount, posStats] = await Promise.all([
      prisma.vocabulary.count(),
      prisma.explain.groupBy({
        by: ['pos'],
        _count: {
          pos: true,
        },
        orderBy: {
          _count: {
            pos: 'desc',
          },
        },
        take: 10,
      }),
    ]);

    // 简化的复杂度计算（实际项目中可能需要更复杂的逻辑）
    const avgComplexity = 2.5; // 占位值

    return {
      totalCount,
      avgComplexity,
      topPOS: posStats.map((stat) => ({
        pos: stat.pos,
        count: stat._count.pos,
      })),
    };
  }

  /**
   * 保存词汇
   */
  async save(vocabulary: Vocabulary): Promise<Vocabulary> {
    const data = vocabulary.toPersistence();

    const savedData = await prisma.vocabulary.upsert({
      where: { id: data.id },
      update: {
        word: data.word,
        phonetics: data.phonetics,
        freqRank: data.freqRank,
      },
      create: {
        word: data.word,
        phonetics: data.phonetics,
        freqRank: data.freqRank,
      },
      include: this.getDefaultIncludes(),
    });

    return this.mapToEntity(savedData);
  }

  /**
   * 删除词汇
   */
  async delete(id: number): Promise<boolean> {
    try {
      await prisma.vocabulary.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 根据ID批量查找词汇 (为PracticeService提供的兼容方法)
   */
  async findManyByIds(ids: number[]): Promise<Vocabulary[]> {
    const data = await prisma.vocabulary.findMany({
      where: {
        id: { in: ids },
      },
      include: this.getDefaultIncludes(),
      orderBy: { freqRank: 'asc' },
    });

    return data.map((item) => this.mapToEntity(item));
  }

  /**
   * 根据单词批量查找词汇 (为PracticeService提供的兼容方法)
   */
  async findManyByWords(words: string[]): Promise<Vocabulary[]> {
    return this.findByWords(words);
  }

  /**
   * 检查词汇是否存在
   */
  async exists(id: number): Promise<boolean> {
    const count = await prisma.vocabulary.count({
      where: { id },
    });
    return count > 0;
  }

  /**
   * 获取默认的关联查询
   */
  private getDefaultIncludes() {
    return {
      explains: {
        include: {
          definitions: true,
        },
        orderBy: { id: 'asc' as const },
      },
      formats: {
        orderBy: [{ name: 'asc' as const }, { id: 'asc' as const }],
      },
    };
  }

  /**
   * 将数据库数据映射为领域实体
   */
  private mapToEntity(data: any): Vocabulary {
    const vocabularyData: VocabularyData = {
      id: data.id,
      word: data.word,
      phonetics: data.phonetics || [],
      freqRank: data.freqRank,
      explains:
        data.explains?.map((explain: any) => ({
          id: explain.id,
          pos: explain.pos,
          definitions:
            explain.definitions?.map((def: any) => ({
              id: def.id,
              definition: def.definition,
              chinese: def.chinese,
              chineseS: def.chineseS,
            })) || [],
        })) || [],
      formats:
        data.formats?.map((format: any) => ({
          id: format.id,
          name: format.name,
          form: format.form,
          vocabularyId: format.vocabularyId,
          baseFormId: format.baseFormId,
        })) || [],
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
    };

    return Vocabulary.create(vocabularyData);
  }
}
