#!/usr/bin/env tsx

/**
 * 数据库连接池验证脚本
 * 用于验证 Prisma 连接池配置是否正确生效
 */

import { prisma } from '../app/lib/4-infrastructure/database/prisma';

interface PrismaMetric {
  key: string;
  value: number;
  labels: Record<string, string>;
}

/**
 * 获取 Prisma 连接池统计信息
 */
async function getPrismaPoolStats() {
  try {
    // 使用类型断言，因为$metrics是预览功能
    const prismaWithMetrics = prisma as any;

    if (!prismaWithMetrics.$metrics) {
      console.log(
        '⚠️ Prisma metrics 未启用。请在 schema.prisma 中添加 `metrics` 到 `previewFeatures`'
      );
      return null;
    }

    const metrics = await prismaWithMetrics.$metrics.json();
    const gauges: PrismaMetric[] = metrics.gauges;
    const counters: PrismaMetric[] = metrics.counters;

    const getGauge = (name: string) => gauges.find((g) => g.key === name)?.value ?? 0;
    const getCounter = (name: string) => counters.find((c) => c.key === name)?.value ?? 0;

    return {
      connections: {
        open: getGauge('prisma_pool_connections_open'),
        idle: getGauge('prisma_pool_connections_idle'),
        busy: getGauge('prisma_pool_connections_busy'),
      },
      requests: {
        waitCount: getCounter('prisma_pool_wait_count'),
        timeoutCount: getCounter('prisma_pool_timeout_count'),
      },
    };
  } catch (error) {
    console.error('❌ 获取 Prisma 连接池统计失败:', error);
    return null;
  }
}

/**
 * 验证数据库连接配置
 */
async function verifyDatabaseConfig() {
  console.log('🔍 验证数据库连接配置...\n');

  // 1. 检查环境变量
  console.log('📋 环境变量配置:');
  console.log(`   DATABASE_URL: ${process.env.DATABASE_URL?.replace(/:[^:@]*@/, ':***@')}`);
  console.log(`   DB_CONNECTION_LIMIT: ${process.env.DB_CONNECTION_LIMIT || '未设置'}`);
  console.log(`   DB_POOL_TIMEOUT: ${process.env.DB_POOL_TIMEOUT || '未设置'}`);
  console.log(`   DB_CONNECT_TIMEOUT: ${process.env.DB_CONNECT_TIMEOUT || '未设置'}`);
  console.log(`   DB_STATEMENT_TIMEOUT: ${process.env.DB_STATEMENT_TIMEOUT || '未设置'}\n`);

  // 2. 解析 DATABASE_URL 中的连接池参数
  const databaseUrl = process.env.DATABASE_URL;
  if (databaseUrl) {
    const url = new URL(databaseUrl);
    const connectionLimit = url.searchParams.get('connection_limit');
    const poolTimeout = url.searchParams.get('pool_timeout');

    console.log('🔗 DATABASE_URL 中的连接池参数:');
    console.log(`   connection_limit: ${connectionLimit || '未设置'}`);
    console.log(`   pool_timeout: ${poolTimeout || '未设置'}\n`);
  }

  // 3. 测试数据库连接
  console.log('🔌 测试数据库连接...');
  try {
    await prisma.$queryRaw`SELECT 1 as test`;
    console.log('✅ 数据库连接成功\n');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return;
  }

  // 4. 获取连接池统计
  const stats = await getPrismaPoolStats();
  if (stats) {
    console.log('📊 Prisma 连接池状态:');
    console.log(`   总连接数: ${stats.connections.open}`);
    console.log(`   空闲连接: ${stats.connections.idle}`);
    console.log(`   繁忙连接: ${stats.connections.busy}`);
    console.log(`   等待请求数(累计): ${stats.requests.waitCount}`);
    console.log(`   超时请求数(累计): ${stats.requests.timeoutCount}\n`);

    // 5. 分析连接池状态
    console.log('🔍 连接池分析:');
    if (stats.connections.open === 0) {
      console.log('   ⚠️  没有活跃连接，这可能是正常的（懒加载）');
    } else if (stats.connections.open === 1 && stats.connections.busy === 1) {
      console.log('   ✅ 当前有1个活跃连接，这是正常的单请求状态');
    } else if (stats.connections.open > 1) {
      console.log(`   ✅ 连接池正常工作，有 ${stats.connections.open} 个连接`);
    }

    if (stats.requests.waitCount > 0) {
      console.log(`   🔴 警告: 有 ${stats.requests.waitCount} 个请求曾经等待连接`);
      console.log('   💡 建议增加 connection_limit 值');
    } else {
      console.log('   ✅ 没有请求等待，连接池配置合理');
    }
  }
}

/**
 * 并发测试连接池
 */
async function testConcurrentConnections(concurrency: number = 5) {
  console.log(`\n🚀 并发测试 (${concurrency} 个并发请求)...`);

  const startTime = Date.now();
  const promises = Array.from({ length: concurrency }, async (_, i) => {
    const queryStart = Date.now();
    await prisma.$queryRaw`SELECT ${i} as query_id, pg_sleep(0.1)::text as sleep_result`;
    const queryTime = Date.now() - queryStart;
    return { queryId: i, duration: queryTime };
  });

  try {
    const results = await Promise.all(promises);
    const totalTime = Date.now() - startTime;

    console.log('📈 并发测试结果:');
    console.log(`   总耗时: ${totalTime}ms`);
    console.log(
      `   平均查询时间: ${Math.round(results.reduce((sum, r) => sum + r.duration, 0) / results.length)}ms`
    );
    console.log(`   最快查询: ${Math.min(...results.map((r) => r.duration))}ms`);
    console.log(`   最慢查询: ${Math.max(...results.map((r) => r.duration))}ms`);

    // 再次检查连接池状态
    const statsAfter = await getPrismaPoolStats();
    if (statsAfter) {
      console.log('\n📊 并发测试后的连接池状态:');
      console.log(`   总连接数: ${statsAfter.connections.open}`);
      console.log(`   空闲连接: ${statsAfter.connections.idle}`);
      console.log(`   繁忙连接: ${statsAfter.connections.busy}`);
      console.log(`   等待请求数(累计): ${statsAfter.requests.waitCount}`);
    }
  } catch (error) {
    console.error('❌ 并发测试失败:', error);
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🔧 数据库连接池验证工具\n');
  console.log('='.repeat(50));

  try {
    await verifyDatabaseConfig();
    await testConcurrentConnections(5);

    console.log('\n' + '='.repeat(50));
    console.log('✅ 验证完成');
  } catch (error) {
    console.error('❌ 验证过程中出现错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行脚本
main().catch(console.error);
