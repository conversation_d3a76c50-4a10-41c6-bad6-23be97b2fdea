#!/usr/bin/env tsx

/**
 * 统一的数据完整性管理工具
 * 整合了所有数据完整性相关功能
 */

import { PrismaClient } from '@prisma/client';

const prismaClient = new PrismaClient();

interface IntegrityReport {
  timestamp: string;
  status: 'HEALTHY' | 'WARNING' | 'ERROR';
  issues: string[];
  statistics: {
    vocabularies: number;
    totalWordFormats: number;
    mainWordFormats: number;
    derivedWordFormats: number;
    orphanedWordFormats: number;
    invalidBaseFormRefs: number;
    invalidVocabRefs: number;
  };
}

// 检查数据完整性
async function checkIntegrity(): Promise<IntegrityReport> {
  const timestamp = new Date().toISOString();
  const issues: string[] = [];

  console.log('🔍 数据完整性检查...');
  console.log(`⏰ 检查时间: ${timestamp}\n`);

  try {
    // 统计基础数据
    const totalVocabularies = await prismaClient.vocabulary.count();
    const totalWordFormats = await prismaClient.wordFormat.count();
    const mainWordFormats = await prismaClient.wordFormat.count({
      where: { vocabularyId: { not: null } },
    });
    const derivedWordFormats = await prismaClient.wordFormat.count({
      where: { vocabularyId: null, baseFormId: { not: null } },
    });

    // 检查孤立记录
    const orphanedWordFormats = await prismaClient.wordFormat.count({
      where: { AND: [{ vocabularyId: null }, { baseFormId: null }] },
    });

    if (orphanedWordFormats > 0) {
      issues.push(`发现 ${orphanedWordFormats} 个孤立的WordFormat记录`);
    }

    // 检查无效引用
    const invalidBaseFormRefs = await prismaClient.wordFormat.count({
      where: { baseFormId: { not: null }, baseForm: null },
    });

    if (invalidBaseFormRefs > 0) {
      issues.push(`发现 ${invalidBaseFormRefs} 个无效的baseFormId引用`);
    }

    const invalidVocabRefs = await prismaClient.wordFormat.count({
      where: { vocabularyId: { not: null }, vocabulary: null },
    });

    if (invalidVocabRefs > 0) {
      issues.push(`发现 ${invalidVocabRefs} 个无效的vocabularyId引用`);
    }

    // 检查缺失的主WordFormat
    const vocabulariesWithoutMainFormat = await prismaClient.vocabulary.count({
      where: { formats: { none: { vocabularyId: { not: null } } } },
    });

    if (vocabulariesWithoutMainFormat > 0) {
      issues.push(`发现 ${vocabulariesWithoutMainFormat} 个Vocabulary记录没有对应的主WordFormat`);
    }

    const statistics = {
      vocabularies: totalVocabularies,
      totalWordFormats: totalWordFormats,
      mainWordFormats: mainWordFormats,
      derivedWordFormats: derivedWordFormats,
      orphanedWordFormats: orphanedWordFormats,
      invalidBaseFormRefs: invalidBaseFormRefs,
      invalidVocabRefs: invalidVocabRefs,
    };

    const status: 'HEALTHY' | 'WARNING' | 'ERROR' =
      issues.length === 0 ? 'HEALTHY' : issues.length <= 2 ? 'WARNING' : 'ERROR';

    // 输出结果
    console.log('📊 数据统计:');
    console.log(`  - Vocabulary记录: ${statistics.vocabularies} 个`);
    console.log(`  - WordFormat记录总数: ${statistics.totalWordFormats} 个`);
    console.log(`  - 主WordFormat记录: ${statistics.mainWordFormats} 个`);
    console.log(`  - 派生WordFormat记录: ${statistics.derivedWordFormats} 个`);
    console.log(`  - 孤立WordFormat记录: ${statistics.orphanedWordFormats} 个`);

    console.log(`\n🎯 整体状态: ${status}`);

    if (issues.length === 0) {
      console.log('✅ 数据完整性检查通过，没有发现问题');
    } else {
      console.log(`⚠️  发现 ${issues.length} 个问题:`);
      issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }

    return { timestamp, status, issues, statistics };
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
    return {
      timestamp,
      status: 'ERROR',
      issues: [`检查失败: ${(error as Error).message}`],
      statistics: {
        vocabularies: 0,
        totalWordFormats: 0,
        mainWordFormats: 0,
        derivedWordFormats: 0,
        orphanedWordFormats: 0,
        invalidBaseFormRefs: 0,
        invalidVocabRefs: 0,
      },
    };
  }
}

// 清理孤立记录
async function cleanupOrphaned(): Promise<boolean> {
  console.log('🧹 开始清理孤立的WordFormat记录...\n');

  try {
    const orphanedFormats = await prismaClient.wordFormat.findMany({
      where: { AND: [{ vocabularyId: null }, { baseFormId: null }] },
      orderBy: { id: 'asc' },
    });

    console.log(`🔍 发现 ${orphanedFormats.length} 个孤立的WordFormat记录`);

    if (orphanedFormats.length === 0) {
      console.log('✅ 没有发现孤立记录，无需清理');
      return true;
    }

    // 执行删除
    const deleteResult = await prismaClient.wordFormat.deleteMany({
      where: { AND: [{ vocabularyId: null }, { baseFormId: null }] },
    });

    console.log(`✅ 成功删除 ${deleteResult.count} 个孤立的WordFormat记录`);

    // 验证清理结果
    const remainingOrphaned = await prismaClient.wordFormat.count({
      where: { AND: [{ vocabularyId: null }, { baseFormId: null }] },
    });

    if (remainingOrphaned === 0) {
      console.log('🎉 清理完成！所有孤立记录已删除');
      return true;
    } else {
      console.log('⚠️  仍有孤立记录存在，可能需要进一步检查');
      return false;
    }
  } catch (error) {
    console.error('❌ 清理失败:', error);
    return false;
  }
}

// 修复缺失的主WordFormat
async function fixMissingMainFormats(): Promise<boolean> {
  console.log('🔧 开始修复缺少主WordFormat的Vocabulary记录...\n');

  try {
    const vocabulariesWithoutMainFormat = await prismaClient.vocabulary.findMany({
      where: { formats: { none: { vocabularyId: { not: null } } } },
      include: { explains: true },
      orderBy: { word: 'asc' },
    });

    console.log(`🔍 发现 ${vocabulariesWithoutMainFormat.length} 个需要修复的Vocabulary记录`);

    if (vocabulariesWithoutMainFormat.length === 0) {
      console.log('✅ 没有需要修复的记录');
      return true;
    }

    let fixedCount = 0;

    for (const vocab of vocabulariesWithoutMainFormat) {
      if (vocab.explains.length === 0) {
        console.log(`⏭️  跳过 ${vocab.word} - 没有释义`);
        continue;
      }

      try {
        await prismaClient.wordFormat.create({
          data: {
            name: '原型',
            form: vocab.word,
            vocabularyId: vocab.id,
          },
        });
        console.log(`✅ 修复: ${vocab.word}`);
        fixedCount++;
      } catch (error) {
        console.log(`❌ 修复失败: ${vocab.word} - ${(error as Error).message}`);
      }
    }

    console.log(`\n📊 修复结果: 成功修复 ${fixedCount} 个记录`);
    return fixedCount > 0;
  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error);
    return false;
  }
}

// 测试删除逻辑
async function testDeleteLogic(): Promise<boolean> {
  console.log('🧪 开始测试删除逻辑...\n');

  try {
    // 创建测试数据
    const testVocab = await prismaClient.vocabulary.create({
      data: { word: 'test_delete_logic', phonetics: ['/test/'] },
    });

    const mainFormat = await prismaClient.wordFormat.create({
      data: { name: '原型', form: 'test_delete_logic', vocabularyId: testVocab.id },
    });

    await prismaClient.wordFormat.create({
      data: { name: '复数', form: 'test_delete_logics', baseFormId: mainFormat.id },
    });

    console.log('✅ 创建测试数据完成');

    // 测试删除逻辑
    const mainFormats = await prismaClient.wordFormat.findMany({
      where: { vocabularyId: testVocab.id },
    });

    for (const format of mainFormats) {
      await prismaClient.wordFormat.deleteMany({
        where: { baseFormId: format.id },
      });
    }

    await prismaClient.wordFormat.deleteMany({
      where: { vocabularyId: testVocab.id },
    });

    await prismaClient.vocabulary.delete({
      where: { id: testVocab.id },
    });

    // 验证删除结果
    const orphanedAfterDelete = await prismaClient.wordFormat.count({
      where: { AND: [{ vocabularyId: null }, { baseFormId: null }] },
    });

    if (orphanedAfterDelete === 0) {
      console.log('🎉 删除逻辑测试通过！');
      return true;
    } else {
      console.log('❌ 删除逻辑测试失败！产生了孤立记录');
      return false;
    }
  } catch (error) {
    console.error('❌ 测试失败:', error);
    // 清理测试数据
    try {
      await prismaClient.wordFormat.deleteMany({
        where: { form: { in: ['test_delete_logic', 'test_delete_logics'] } },
      });
      await prismaClient.vocabulary.deleteMany({
        where: { word: 'test_delete_logic' },
      });
    } catch {}
    return false;
  }
}

// 主函数 - CLI接口
async function main() {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'check':
      case 'monitor':
        const report = await checkIntegrity();
        if (report.status === 'HEALTHY') {
          process.exit(0);
        } else if (report.status === 'WARNING') {
          process.exit(1);
        } else {
          process.exit(2);
        }
        break;

      case 'cleanup':
        const cleanupSuccess = await cleanupOrphaned();
        process.exit(cleanupSuccess ? 0 : 1);
        break;

      case 'fix':
        const fixSuccess = await fixMissingMainFormats();
        process.exit(fixSuccess ? 0 : 1);
        break;

      case 'test':
        const testSuccess = await testDeleteLogic();
        process.exit(testSuccess ? 0 : 1);
        break;

      case 'auto':
        console.log('🚀 自动修复模式...\n');

        // 1. 检查问题
        const initialReport = await checkIntegrity();

        if (initialReport.status === 'HEALTHY') {
          console.log('\n🎉 数据库状态良好，无需修复');
          process.exit(0);
        }

        // 2. 清理孤立记录
        if (initialReport.statistics.orphanedWordFormats > 0) {
          console.log('\n🧹 清理孤立记录...');
          await cleanupOrphaned();
        }

        // 3. 修复缺失的主WordFormat
        if (initialReport.issues.some((issue) => issue.includes('没有对应的主WordFormat'))) {
          console.log('\n🔧 修复缺失的主WordFormat...');
          await fixMissingMainFormats();
        }

        // 4. 最终检查
        console.log('\n🔍 最终检查...');
        const finalReport = await checkIntegrity();

        if (finalReport.status === 'HEALTHY') {
          console.log('\n🎉 自动修复完成！数据库状态良好');
          process.exit(0);
        } else {
          console.log('\n⚠️  自动修复后仍有问题，请手动检查');
          process.exit(1);
        }
        break;

      default:
        console.log(`
🛠️  数据完整性管理工具

用法: npx tsx data-integrity.ts <命令>

命令:
  check     检查数据完整性
  cleanup   清理孤立记录
  fix       修复缺失的主WordFormat
  test      测试删除逻辑
  auto      自动检查并修复所有问题

示例:
  npx tsx data-integrity.ts check
  npx tsx data-integrity.ts auto
        `);
        process.exit(0);
    }
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(3);
  } finally {
    await prismaClient.$disconnect();
  }
}

// 运行CLI
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  checkIntegrity,
  cleanupOrphaned,
  fixMissingMainFormats,
  testDeleteLogic,
  type IntegrityReport,
};
