import { PrismaClient, UserWordProficiency, Prisma } from '@prisma/client';

// Type definitions for better type safety
export interface CreateUserWordProficiencyInput {
  userId: string;
  wordId: number;
  practiceCount?: number;
  errorCount?: number;
  averageTime?: number;
  isMarked?: boolean;
  proficiencyScore?: number;
  lastPracticed?: Date;
}

export interface UpdateUserWordProficiencyInput {
  practiceCount?: number;
  errorCount?: number;
  averageTime?: number;
  isMarked?: boolean;
  proficiencyScore?: number;
  lastPracticed?: Date;
}

export interface ProficiencyQueryOptions {
  sortBy?: 'proficiencyScore' | 'lastPracticed' | 'practiceCount';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
  markedOnly?: boolean;
}

// Type for UserWordProficiency with vocabulary relation
export type UserWordProficiencyWithVocabulary = UserWordProficiency & {
  vocabulary: {
    id: number;
    word: string;
    phonetics: string[];
    freqRank: number | null;
  };
};

/**
 * Repository for UserWordProficiency operations
 * Provides CRUD operations and specialized queries for user word proficiency tracking
 */
export class UserWordProficiencyRepository {
  // 常量定义
  private static readonly DEFAULT_REVIEW_LIMIT = 20;
  private static readonly DEFAULT_PRACTICE_LIMIT = 20;

  constructor(private prisma: PrismaClient) {}

  /**
   * Create a new user word proficiency record
   */
  async create(data: CreateUserWordProficiencyInput): Promise<UserWordProficiency> {
    return this.prisma.userWordProficiency.create({
      data: {
        userId: data.userId,
        wordId: data.wordId,
        practiceCount: data.practiceCount ?? 0,
        errorCount: data.errorCount ?? 0,
        averageTime: data.averageTime ?? 0,
        isMarked: data.isMarked ?? false,
        proficiencyScore: data.proficiencyScore ?? 0.0,
        lastPracticed: data.lastPracticed,
      },
    });
  }

  /**
   * Find user word proficiency by user ID and word ID
   */
  async findByUserAndWord(userId: string, wordId: number): Promise<UserWordProficiency | null> {
    return this.prisma.userWordProficiency.findUnique({
      where: {
        userId_wordId: {
          userId,
          wordId,
        },
      },
    });
  }

  /**
   * Update user word proficiency record
   */
  async update(
    userId: string,
    wordId: number,
    data: UpdateUserWordProficiencyInput
  ): Promise<UserWordProficiency> {
    return this.prisma.userWordProficiency.update({
      where: {
        userId_wordId: {
          userId,
          wordId,
        },
      },
      data: {
        ...data,
        updatedAt: new Date(),
      },
    });
  }

  /**
   * Delete user word proficiency record
   */
  async delete(userId: string, wordId: number): Promise<void> {
    await this.prisma.userWordProficiency.delete({
      where: {
        userId_wordId: {
          userId,
          wordId,
        },
      },
    });
  }

  /**
   * Get all word proficiencies for a user with optional filtering and sorting
   */
  async findManyByUser(
    userId: string,
    options: ProficiencyQueryOptions = {}
  ): Promise<UserWordProficiencyWithVocabulary[]> {
    const {
      sortBy = 'proficiencyScore',
      sortOrder = 'asc',
      limit,
      offset,
      markedOnly,
    } = options;

    const where: Prisma.UserWordProficiencyWhereInput = {
      userId,
      ...(markedOnly && { isMarked: true }),
    };

    const orderBy: Prisma.UserWordProficiencyOrderByWithRelationInput = {};
    orderBy[sortBy] = sortOrder;

    return this.prisma.userWordProficiency.findMany({
      where,
      orderBy,
      take: limit,
      skip: offset,
      include: {
        vocabulary: {
          select: {
            id: true,
            word: true,
            phonetics: true,
            freqRank: true,
          },
        },
      },
    });
  }

  /**
   * Get proficiency records sorted by score (lowest first for practice priority)
   */
  async findLowestProficiencyWords(
    userId: string,
    limit: number = UserWordProficiencyRepository.DEFAULT_PRACTICE_LIMIT
  ): Promise<UserWordProficiency[]> {
    return this.findManyByUser(userId, {
      sortBy: 'proficiencyScore',
      sortOrder: 'asc',
      limit,
    });
  }

  /**
   * Get marked (difficult) words for a user
   */
  async findMarkedWords(userId: string): Promise<UserWordProficiency[]> {
    return this.findManyByUser(userId, {
      markedOnly: true,
      sortBy: 'lastPracticed',
      sortOrder: 'desc',
    });
  }

  /**
   * Bulk create or update user word proficiency records
   */
  async upsertMany(records: CreateUserWordProficiencyInput[]): Promise<void> {
    const operations = records.map((record) =>
      this.prisma.userWordProficiency.upsert({
        where: {
          userId_wordId: {
            userId: record.userId,
            wordId: record.wordId,
          },
        },
        update: {
          practiceCount: record.practiceCount ?? 0,
          errorCount: record.errorCount ?? 0,
          averageTime: record.averageTime ?? 0,
          isMarked: record.isMarked ?? false,
          proficiencyScore: record.proficiencyScore ?? 0.0,
          lastPracticed: record.lastPracticed,
          updatedAt: new Date(),
        },
        create: {
          userId: record.userId,
          wordId: record.wordId,
          practiceCount: record.practiceCount ?? 0,
          errorCount: record.errorCount ?? 0,
          averageTime: record.averageTime ?? 0,
          isMarked: record.isMarked ?? false,
          proficiencyScore: record.proficiencyScore ?? 0.0,
          lastPracticed: record.lastPracticed,
        },
      })
    );

    await this.prisma.$transaction(operations);
  }

  /**
   * Mark or unmark a word as difficult
   */
  async toggleMarked(userId: string, wordId: number, isMarked: boolean): Promise<UserWordProficiency> {
    return this.update(userId, wordId, { isMarked });
  }

  /**
   * Get user's practice statistics
   */
  async getUserStats(userId: string): Promise<{
    totalWords: number;
    markedWords: number;
    averageProficiency: number;
    totalPracticeCount: number;
  }> {
    const stats = await this.prisma.userWordProficiency.aggregate({
      where: { userId },
      _count: { _all: true },
      _avg: { proficiencyScore: true },
      _sum: { practiceCount: true },
    });

    const markedCount = await this.prisma.userWordProficiency.count({
      where: { userId, isMarked: true },
    });

    return {
      totalWords: stats._count._all,
      markedWords: markedCount,
      averageProficiency: stats._avg.proficiencyScore ?? 0,
      totalPracticeCount: stats._sum.practiceCount ?? 0,
    };
  }

  /**
   * Get words that haven't been practiced recently (for review)
   */
  async findWordsForReview(
    userId: string, 
    daysSinceLastPractice: number = 7
  ): Promise<UserWordProficiency[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysSinceLastPractice);

    return this.prisma.userWordProficiency.findMany({
      where: {
        userId,
        OR: [
          { lastPracticed: null },
          { lastPracticed: { lt: cutoffDate } },
        ],
      },
      orderBy: [
        { proficiencyScore: 'asc' },
        { lastPracticed: 'asc' },
      ],
      take: UserWordProficiencyRepository.DEFAULT_REVIEW_LIMIT,
      include: {
        vocabulary: {
          select: {
            id: true,
            word: true,
            phonetics: true,
          },
        },
      },
    });
  }
}