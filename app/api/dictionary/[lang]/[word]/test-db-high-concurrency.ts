/**
 * 数据库高并发性能测试
 * 使用 ?fresh=true 绕过缓存，专注测试数据库性能
 */

interface DbConcurrencyTestResult {
  concurrency: number;
  totalRequests: number;
  successfulRequests: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  totalTime: number;
  requestsPerSecond: number;
}

async function testDatabaseConcurrency(
  concurrency: number,
  testWords: string[]
): Promise<DbConcurrencyTestResult> {
  console.log(`🚀 测试 ${concurrency} 个并发数据库请求...`);

  const startTime = performance.now();
  const promises: Promise<{ success: boolean; responseTime: number }>[] = [];

  // 创建并发请求，使用 ?fresh=true 绕过缓存
  for (let i = 0; i < concurrency; i++) {
    const word = testWords[i % testWords.length];
    const requestStart = performance.now();

    const promise = fetch(`http://localhost:3000/api/dictionary/en/${word}?fresh=true`)
      .then(async (response) => {
        const responseTime = performance.now() - requestStart;
        const success = response.ok;

        if (!success) {
          console.log(`❌ 请求失败: ${word} - ${response.status}`);
        }

        return { success, responseTime };
      })
      .catch((error) => {
        const responseTime = performance.now() - requestStart;
        console.log(`❌ 请求错误: ${word} - ${error.message}`);
        return { success: false, responseTime };
      });

    promises.push(promise);
  }

  // 等待所有请求完成
  const results = await Promise.all(promises);
  const totalTime = performance.now() - startTime;

  // 计算统计数据
  const successfulResults = results.filter((r) => r.success);
  const responseTimes = successfulResults.map((r) => r.responseTime);

  const averageResponseTime =
    responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

  // 计算P95响应时间
  const sortedTimes = responseTimes.sort((a, b) => a - b);
  const p95Index = Math.floor(sortedTimes.length * 0.95);
  const p95ResponseTime = sortedTimes.length > 0 ? sortedTimes[p95Index] || 0 : 0;

  const requestsPerSecond = (successfulResults.length / totalTime) * 1000;

  return {
    concurrency,
    totalRequests: concurrency,
    successfulRequests: successfulResults.length,
    averageResponseTime,
    p95ResponseTime,
    totalTime,
    requestsPerSecond,
  };
}

async function runDatabaseConcurrencyTests(): Promise<void> {
  console.log('🔍 数据库高并发性能测试\n');

  // 测试用词汇
  const testWords = [
    'apple',
    'banana',
    'cherry',
    'dog',
    'elephant',
    'fish',
    'grape',
    'house',
    'ice',
    'juice',
    'key',
    'lemon',
    'mouse',
    'night',
    'orange',
    'paper',
    'queen',
    'rain',
    'sun',
    'tree',
    'umbrella',
    'voice',
    'water',
    'yellow',
    'zebra',
    'book',
    'car',
    'door',
    'fire',
    'green',
    'happy',
    'island',
    'jump',
    'king',
    'light',
    'moon',
    'number',
    'ocean',
    'phone',
    'quick',
    'red',
    'star',
    'table',
    'under',
    'village',
    'wind',
    'box',
    'year',
    'zero',
    'blue',
  ];

  // 测试不同的并发级别
  const concurrencyLevels = [1, 5, 10, 20, 30, 50, 75, 100];
  const results: DbConcurrencyTestResult[] = [];

  for (const concurrency of concurrencyLevels) {
    try {
      // 等待一下让系统稳定
      await new Promise((resolve) => setTimeout(resolve, 1000));

      const result = await testDatabaseConcurrency(concurrency, testWords);
      results.push(result);

      console.log(`✅ ${concurrency} 并发完成:`);
      console.log(
        `   - 成功率: ${Math.round((result.successfulRequests / result.totalRequests) * 100)}%`
      );
      console.log(`   - 平均响应时间: ${Math.round(result.averageResponseTime)}ms`);
      console.log(`   - P95响应时间: ${Math.round(result.p95ResponseTime)}ms`);
      console.log(`   - 总耗时: ${Math.round(result.totalTime)}ms`);
      console.log(`   - RPS: ${Math.round(result.requestsPerSecond)}\n`);

      // 如果成功率低于90%，停止测试
      if (result.successfulRequests / result.totalRequests < 0.9) {
        console.log('⚠️ 成功率过低，停止测试');
        break;
      }
    } catch (error) {
      console.error(`❌ ${concurrency} 并发测试失败:`, error);
      break;
    }
  }

  // 分析结果
  console.log('📊 测试结果分析:\n');
  console.log('并发数 | 成功率 | 平均响应时间 | P95响应时间 | RPS');
  console.log('-------|--------|-------------|------------|----');

  results.forEach((result) => {
    const successRate = Math.round((result.successfulRequests / result.totalRequests) * 100);
    const avgTime = Math.round(result.averageResponseTime);
    const p95Time = Math.round(result.p95ResponseTime);
    const rps = Math.round(result.requestsPerSecond);

    console.log(
      `${result.concurrency.toString().padStart(6)} | ${successRate.toString().padStart(6)}% | ${avgTime.toString().padStart(11)}ms | ${p95Time.toString().padStart(10)}ms | ${rps.toString().padStart(3)}`
    );
  });

  // 找出性能拐点
  console.log('\n🔍 性能分析:');

  let performanceDropPoint = null;
  for (let i = 1; i < results.length; i++) {
    const current = results[i];
    const previous = results[i - 1];

    // 如果平均响应时间增加超过50%，认为是性能拐点
    if (current.averageResponseTime > previous.averageResponseTime * 1.5) {
      performanceDropPoint = current.concurrency;
      break;
    }
  }

  if (performanceDropPoint) {
    console.log(`⚠️ 性能拐点: ${performanceDropPoint} 并发`);
    console.log('   建议优化数据库连接池或查询性能');
  } else {
    console.log('✅ 在测试范围内性能表现稳定');
  }

  // 找出最佳性能点
  const bestPerformance = results.reduce((best, current) => {
    const currentScore = current.requestsPerSecond / (current.averageResponseTime / 100);
    const bestScore = best.requestsPerSecond / (best.averageResponseTime / 100);
    return currentScore > bestScore ? current : best;
  });

  console.log(`🎯 最佳性能点: ${bestPerformance.concurrency} 并发`);
  console.log(`   - RPS: ${Math.round(bestPerformance.requestsPerSecond)}`);
  console.log(`   - 平均响应时间: ${Math.round(bestPerformance.averageResponseTime)}ms`);

  // 性能建议
  console.log('\n💡 优化建议:');
  if (results[results.length - 1]?.averageResponseTime > 500) {
    console.log('🔴 高并发下响应时间过长，建议:');
    console.log('   - 增加数据库连接池大小');
    console.log('   - 优化数据库查询（添加索引）');
    console.log('   - 考虑读写分离');
    console.log('   - 实现连接池预热');
  } else {
    console.log('✅ 性能表现良好');
  }
}

// 运行测试
runDatabaseConcurrencyTests().catch(console.error);
