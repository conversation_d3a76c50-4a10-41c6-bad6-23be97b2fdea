#!/usr/bin/env tsx

/**
 * 运行频率词汇同步脚本的入口文件
 *
 * 使用方法:
 * pnpm tsx app/lib/scripts/sync6w/run-sync.ts
 *
 * 或者:
 * node --loader ts-node/esm app/lib/scripts/sync6w/run-sync.ts
 */

import { main } from './sync-frequency-words.js';

console.log('🚀 启动频率词汇同步脚本...\n');

main()
  .then(() => {
    console.log('\n✅ 脚本执行完成');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ 脚本执行失败:', error);
    process.exit(1);
  });
