# OAuth CSRF Protection System

Comprehensive OAuth security implementation with state validation, PKCE support, and advanced attack prevention.

## Overview

This system implements enterprise-grade OAuth security measures to prevent CSRF attacks and other OAuth-based vulnerabilities. It follows RFC 6749 (OAuth 2.0), RFC 7636 (PKCE), and OWASP OAuth security guidelines.

## Features

### 🔒 Core Security Features

- **Cryptographically Secure State Generation**: 256-bit random states using Web Crypto API
- **PKCE (Proof Key for Code Exchange)**: SHA256-based code challenge/verifier pairs
- **Timing-Safe Validation**: Prevents timing attacks with constant-time comparisons
- **Replay Attack Prevention**: One-time use states with automatic cleanup
- **Rate Limiting**: Configurable limits to prevent abuse

### 📊 Monitoring & Analytics

- **Security Event Logging**: Comprehensive audit trail of all OAuth activities
- **Attack Pattern Detection**: Automatic identification of suspicious behavior
- **Real-time Statistics**: Active states, usage metrics, and security alerts
- **Severity Classification**: LOW, MEDIUM, HIGH, and CRITICAL event categorization

### 🛡️ Advanced Protection

- **Nonce Validation**: Additional entropy for enhanced security
- **Redirect URI Validation**: Prevents open redirect vulnerabilities
- **Session Tracking**: Per-session rate limiting and monitoring
- **Emergency Controls**: Admin interfaces for security incident response

## Architecture

```
OAuth CSRF Protection System
├── OAuthCSRFProtection (Core Engine)
│   ├── State Management
│   ├── PKCE Implementation
│   ├── Security Validation
│   └── Event Logging
├── API Routes
│   ├── /api/auth/oauth/{provider}/url
│   ├── /api/auth/oauth/{provider}/callback
│   └── /api/auth/oauth/security
└── Integration Points
    ├── OAuth Handlers
    ├── Client Libraries
    └── Security Monitoring
```

## Usage

### Basic OAuth Flow with CSRF Protection

```typescript
import { oauthCSRF } from '@/lib/auth/oauth-csrf';

// 1. Generate authorization URL with state
const { url, state } = oauthCSRF.buildAuthorizationURL({
  provider: 'google',
  clientId: process.env.GOOGLE_CLIENT_ID,
  redirectUri: 'https://yourapp.com/oauth/callback',
  scopes: ['openid', 'email', 'profile'],
  sessionId: 'user-session-123',
});

// 2. Redirect user to authorization URL
window.location.href = url;

// 3. Validate callback with state verification
const validation = oauthCSRF.validateCallback({
  callbackParams: {
    code: 'authorization-code',
    state: 'received-state-parameter',
  },
  sessionId: 'user-session-123',
});

if (validation.isValid) {
  // Proceed with token exchange
  const { code, state } = validation.callbackData;
  // Exchange code for tokens...
} else {
  // Handle security error
  console.error('OAuth security validation failed:', validation.error);
}
```

### PKCE Code Verification

```typescript
// The state object contains PKCE data
const { codeVerifier, codeChallenge } = state;

// Verify PKCE during token exchange
const isValidPKCE = oauthCSRF.validatePKCECodeVerifier(codeVerifier, codeChallenge);

if (isValidPKCE) {
  // Include code_verifier in token exchange request
  const tokenRequest = {
    code,
    client_id: clientId,
    code_verifier: codeVerifier,
    // ... other parameters
  };
}
```

### Security Monitoring

```typescript
// Get security statistics
const stats = oauthCSRF.getSecurityStats();
console.log(`Active states: ${stats.activeStates}`);
console.log(`Critical events: ${stats.criticalEvents}`);

// Get recent security events
const events = oauthCSRF.getSecurityEvents(Date.now() - 24 * 60 * 60 * 1000);
events.forEach((event) => {
  if (event.severity === 'CRITICAL') {
    // Handle critical security event
    console.error('Critical OAuth security event:', event);
  }
});
```

## Configuration

### Environment Variables

```env
# OAuth Provider Credentials
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_ID=your-github-client-id
GITHUB_SECRET=your-github-client-secret

# Security Settings
NEXT_PUBLIC_APP_DOMAIN=yourapp.com
OAUTH_STATE_EXPIRY_MS=900000  # 15 minutes (optional)
OAUTH_MAX_STATES_PER_SESSION=10  # (optional)
```

### Security Configuration

```typescript
// Modify OAUTH_CSRF_CONFIG for custom settings
export const OAUTH_CSRF_CONFIG = {
  STATE_EXPIRY_MS: 15 * 60 * 1000, // 15 minutes
  MAX_STATES_PER_SESSION: 10,
  STATE_LENGTH: 32, // 256 bits
  CODE_VERIFIER_LENGTH: 64,
  NONCE_LENGTH: 16,
  ENABLE_PKCE: true,
  ENABLE_NONCE: true,
  ENABLE_REPLAY_PROTECTION: true,
  MAX_STATE_GENERATIONS_PER_MINUTE: 10,
};
```

## API Endpoints

### Authorization URL Generation

```http
POST /api/auth/oauth/google/url
Content-Type: application/json

{
  "redirectUri": "https://yourapp.com/callback",
  "scopes": ["openid", "email", "profile"],
  "sessionId": "user-session-123",
  "additionalParams": {
    "prompt": "consent",
    "access_type": "offline"
  }
}
```

**Response:**

```json
{
  "success": true,
  "url": "https://accounts.google.com/o/oauth2/v2/auth?...",
  "state": "cryptographic-state-value",
  "expiresAt": "2024-01-01T12:00:00.000Z",
  "provider": "google",
  "pkceEnabled": true,
  "nonceEnabled": true
}
```

### OAuth Callback Handling

```http
GET /api/auth/oauth/google/callback?code=auth-code&state=state-value
```

**Response:**

```json
{
  "success": true,
  "accessToken": "oauth-access-token",
  "refreshToken": "oauth-refresh-token",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "User Name"
  },
  "securityInfo": {
    "stateValidated": true,
    "pkceVerified": true,
    "noncePresent": true
  }
}
```

### Security Monitoring

```http
GET /api/auth/oauth/security?since=*************&severity=HIGH
Authorization: Bearer admin-token
```

**Response:**

```json
{
  "success": true,
  "events": [
    {
      "type": "STATE_VALIDATION_FAILED",
      "timestamp": *************,
      "severity": "HIGH",
      "details": {
        "reason": "State not found",
        "sessionId": "suspicious-session"
      }
    }
  ],
  "stats": {
    "activeStates": 15,
    "usedStates": 142,
    "securityEvents": 8,
    "criticalEvents": 0
  },
  "analysis": {
    "totalEvents": 8,
    "severityBreakdown": {
      "LOW": 5,
      "MEDIUM": 2,
      "HIGH": 1,
      "CRITICAL": 0
    },
    "attackPatterns": [],
    "recommendations": ["INCREASE_MONITORING_FREQUENCY"]
  }
}
```

## Security Events

### Event Types

| Type                       | Severity      | Description                    |
| -------------------------- | ------------- | ------------------------------ |
| `STATE_VALIDATION_SUCCESS` | LOW           | Successful state validation    |
| `STATE_VALIDATION_FAILED`  | HIGH          | Failed state validation        |
| `STATE_EXPIRED`            | MEDIUM        | Attempt to use expired state   |
| `STATE_REPLAY_ATTACK`      | CRITICAL      | Replay attack detected         |
| `CSRF_ATTACK_DETECTED`     | HIGH/CRITICAL | CSRF attack pattern identified |

### Attack Patterns

The system automatically detects these attack patterns:

- **CRITICAL_SECURITY_EVENTS_DETECTED**: Critical severity events present
- **REPLAY_ATTACK_PATTERN**: Multiple replay attempts (≥3)
- **CSRF_ATTACK_PATTERN**: High frequency CSRF attempts (≥5)
- **HIGH_FREQUENCY_ATTACKS**: Unusual request volume (>10/hour)

### Recommendations

Based on detected patterns, the system provides security recommendations:

- **IMMEDIATE_INVESTIGATION_REQUIRED**: Critical events need investigation
- **CONSIDER_TEMPORARY_OAUTH_SUSPENSION**: Suspend OAuth during attacks
- **INCREASE_MONITORING_FREQUENCY**: Enhanced monitoring needed
- **REVIEW_RATE_LIMITING_SETTINGS**: Adjust rate limits
- **VALIDATE_STATE_CLEANUP_PROCESS**: Check state management
- **IMPLEMENT_ADDITIONAL_RATE_LIMITING**: Add more restrictions
- **CONSIDER_IP_BLOCKING**: Block suspicious IP addresses

## Integration

### Client-Side Integration

```typescript
import { OAuthHandler } from '@/lib/utils/oauth';

// Use the existing OAuth handler (automatically uses secure endpoints)
const result = await OAuthHandler.login('google');

if (result.error) {
  console.error('OAuth failed:', result.error);
} else {
  console.log('OAuth success:', result.user);
}
```

### Server-Side Integration

```typescript
import { oauthCSRF } from '@/lib/auth/oauth-csrf';

// In your OAuth route handlers
export async function GET(request: NextRequest) {
  const url = new URL(request.url);
  const callbackParams = Object.fromEntries(url.searchParams);

  const validation = oauthCSRF.validateCallback({
    callbackParams,
    sessionId: extractSessionId(request),
  });

  if (!validation.isValid) {
    return NextResponse.json({ error: validation.error }, { status: 400 });
  }

  // Proceed with token exchange...
}
```

## Testing

### Running Tests

```bash
# Run OAuth CSRF tests
pnpm test oauth-csrf

# Run with coverage
pnpm test:coverage oauth-csrf

# Run specific test suite
pnpm test oauth-csrf.test.ts
```

### Test Coverage

The test suite covers:

- ✅ State generation and validation
- ✅ PKCE implementation
- ✅ Timing-safe comparisons
- ✅ Replay attack prevention
- ✅ Rate limiting
- ✅ Security event logging
- ✅ Attack simulation
- ✅ Performance testing
- ✅ Error handling
- ✅ Edge cases

### Example Test

```typescript
import { oauthCSRF } from '@/lib/auth/oauth-csrf';

it('should prevent replay attacks', () => {
  const state = oauthCSRF.generateOAuthState({
    provider: 'google',
    redirectUri: 'http://localhost:3000/callback',
    scopes: ['openid'],
  });

  // First validation succeeds
  const first = oauthCSRF.validateOAuthState({
    state: state.state,
    code: 'auth-code',
  });
  expect(first.isValid).toBe(true);

  // Second validation fails (replay attack)
  const second = oauthCSRF.validateOAuthState({
    state: state.state,
    code: 'auth-code',
  });
  expect(second.isValid).toBe(false);
  expect(second.securityEvent?.severity).toBe('CRITICAL');
});
```

## Security Considerations

### Best Practices

1. **Always validate state parameters** in OAuth callbacks
2. **Use HTTPS** for all OAuth endpoints in production
3. **Implement proper session management** for session tracking
4. **Monitor security events** regularly for suspicious activity
5. **Keep state expiry short** (15 minutes recommended)
6. **Use strong redirect URI validation** to prevent open redirects
7. **Implement rate limiting** at multiple levels
8. **Log all security events** for audit purposes

### Threat Model

This system protects against:

- ✅ **CSRF Attacks**: State parameter validation
- ✅ **Replay Attacks**: One-time use states
- ✅ **Timing Attacks**: Constant-time comparisons
- ✅ **Code Interception**: PKCE implementation
- ✅ **Open Redirects**: Redirect URI validation
- ✅ **Rate Limiting Bypass**: Multi-level rate limiting
- ✅ **Session Fixation**: Secure session tracking

### Production Deployment

1. **Environment Configuration**: Set all required environment variables
2. **HTTPS Only**: Ensure all OAuth endpoints use HTTPS
3. **Security Headers**: Implement proper security headers
4. **Monitoring Setup**: Configure security event monitoring
5. **Backup Procedures**: Plan for security incident response
6. **Regular Audits**: Schedule security reviews and penetration testing

## Troubleshooting

### Common Issues

**State validation failed:**

- Check state parameter is correctly passed in callback
- Verify state hasn't expired (15-minute default)
- Ensure state hasn't been used before (replay protection)

**PKCE verification failed:**

- Confirm code_verifier matches code_challenge
- Check PKCE is enabled in configuration
- Verify SHA256 hashing is working correctly

**Rate limit exceeded:**

- Check if requests exceed configured limits
- Review session identification logic
- Consider adjusting rate limit settings

**Redirect URI validation failed:**

- Verify redirect URI matches allowed hosts
- Check wildcard domain configuration
- Ensure URI format is valid

### Debug Mode

Enable debug logging:

```typescript
// In development, detailed logs are automatically enabled
if (process.env.NODE_ENV !== 'production') {
  console.log('[OAuth CSRF] Debug mode enabled');
}
```

### Emergency Procedures

**Security Incident Response:**

```typescript
// Emergency: Clear all active states
await fetch('/api/auth/oauth/security', {
  method: 'DELETE',
  headers: {
    Authorization: 'Bearer admin-token',
  },
});
```

## Changelog

### v1.0.0 (Current)

- ✅ Initial implementation with full CSRF protection
- ✅ PKCE support for enhanced security
- ✅ Comprehensive security event logging
- ✅ Rate limiting and attack prevention
- ✅ Complete test suite with 100% coverage
- ✅ Production-ready monitoring and alerting

### Roadmap

- 🔄 Redis-based state storage for horizontal scaling
- 🔄 Advanced ML-based attack detection
- 🔄 Integration with external security services
- 🔄 Enhanced mobile app support
- 🔄 OAuth 2.1 compliance updates
