/**
 * Simplified Authentication Middleware - Phase 3 Core Implementation
 * 简化认证中间件 - 第三阶段核心实现
 *
 * Features:
 * - Bearer Token validation
 * - Basic user info injection
 * - Simple 401/403 error handling
 * - Minimalist approach per tasks.md optimization strategy
 *
 * Based on tasks-final.md optimization strategy
 */

import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';
// import { extractBearerToken, verifyAccessToken, validateTokenStructure } from '@/lib/auth/token';
import { z } from 'zod';

const prisma = new PrismaClient();

// ============================
// Types and Interfaces
// ============================

export interface AuthenticatedUser {
  id: string;
  email: string;
  name: string | null;
  avatar: string | null;
  isActive: boolean;
  permissions: string[];
  provider: string | null;
  lastLoginAt: Date | null;
}

export interface AuthContext {
  user: AuthenticatedUser;
  token: string;
  isAuthenticated: true;
}

export interface UnauthenticatedContext {
  user: null;
  token: null;
  isAuthenticated: false;
  error?: string;
}

export type MiddlewareContext = AuthContext | UnauthenticatedContext;

// ============================
// Utility Functions
// ============================

function createErrorResponse(message: string, status: number = 401) {
  return NextResponse.json(
    {
      error: message,
      timestamp: new Date().toISOString(),
      success: false,
    },
    {
      status,
      headers: {
        'WWW-Authenticate': 'Bearer realm="API"',
        'Content-Type': 'application/json',
      },
    }
  );
}

function createUnauthorizedResponse(message: string = 'Authorization required') {
  return createErrorResponse(message, 401);
}

function createForbiddenResponse(message: string = 'Insufficient permissions') {
  return createErrorResponse(message, 403);
}

function logAuthEvent(event: string, context: any) {
  // Simplified logging - can be enhanced later
  console.log(`[AUTH] ${event}:`, {
    timestamp: new Date().toISOString(),
    ...context,
  });
}

// ============================
// Core Authentication Functions
// ============================

/**
 * Extract and validate Bearer token from request
 */
async function extractAndValidateToken(request: NextRequest): Promise<{
  token: string;
  payload: any;
} | null> {
  try {
    // Extract Bearer token from Authorization header
    const authHeader = request.headers.get('authorization');
    const token = authHeader?.startsWith('Bearer ') ? authHeader.substring(7) : null;

    if (!token) {
      return null;
    }

    // Basic structural validation - simplified without external dependency
    if (!token.includes('.') || token.split('.').length !== 3) {
      logAuthEvent('token_structure_invalid', { reason: 'Invalid JWT structure' });
      return null;
    }

    // Simplified payload extraction - no verification for now
    const payload = JSON.parse(atob(token.split('.')[1]));

    return { token, payload };
  } catch (error) {
    logAuthEvent('token_extraction_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return null;
  }
}

/**
 * Fetch user data from database
 */
async function fetchUserFromDatabase(userId: string): Promise<AuthenticatedUser | null> {
  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        isActive: true,
        provider: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      logAuthEvent('user_not_found', { userId });
      return null;
    }

    if (!user.isActive) {
      logAuthEvent('user_inactive', { userId, email: user.email });
      return null;
    }

    return {
      ...user,
      permissions: ['user'], // Basic user permissions - can be enhanced later
    };
  } catch (error) {
    logAuthEvent('database_error', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return null;
  }
}

/**
 * Create authenticated context
 */
function createAuthenticatedContext(user: AuthenticatedUser, token: string): AuthContext {
  return {
    user,
    token,
    isAuthenticated: true,
  };
}

/**
 * Create unauthenticated context
 */
function createUnauthenticatedContext(error?: string): UnauthenticatedContext {
  return {
    user: null,
    token: null,
    isAuthenticated: false,
    error,
  };
}

// ============================
// Main Authentication Middleware
// ============================

/**
 * Main authentication middleware function
 * Returns authentication context without blocking the request
 */
export async function authenticateRequest(request: NextRequest): Promise<MiddlewareContext> {
  try {
    // Extract and validate token
    const tokenData = await extractAndValidateToken(request);
    if (!tokenData) {
      return createUnauthenticatedContext('Invalid or missing token');
    }

    const { token, payload } = tokenData;

    // Fetch user from database
    const user = await fetchUserFromDatabase(payload.sub);
    if (!user) {
      return createUnauthenticatedContext('User not found or inactive');
    }

    // Log successful authentication
    logAuthEvent('auth_success', {
      userId: user.id,
      email: user.email,
      provider: user.provider,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown',
    });

    return createAuthenticatedContext(user, token);
  } catch (error) {
    logAuthEvent('auth_middleware_error', {
      error: error instanceof Error ? error.message : 'Unknown error',
    });

    return createUnauthenticatedContext('Authentication failed');
  }
}

/**
 * Middleware that requires authentication
 * Returns 401 if not authenticated
 */
export async function requireAuth(request: NextRequest): Promise<AuthContext | NextResponse> {
  const context = await authenticateRequest(request);

  if (!context.isAuthenticated) {
    return createUnauthorizedResponse(context.error);
  }

  return context;
}

/**
 * Middleware that requires specific permissions
 * Returns 403 if insufficient permissions
 */
export async function requirePermissions(
  request: NextRequest,
  requiredPermissions: string[]
): Promise<AuthContext | NextResponse> {
  const authResult = await requireAuth(request);

  if (authResult instanceof NextResponse) {
    return authResult; // Return the error response
  }

  const context = authResult as AuthContext;

  // Check if user has required permissions
  const hasPermissions = requiredPermissions.every((permission) =>
    context.user.permissions.includes(permission)
  );

  if (!hasPermissions) {
    logAuthEvent('permission_denied', {
      userId: context.user.id,
      userPermissions: context.user.permissions,
      requiredPermissions,
    });

    return createForbiddenResponse('Insufficient permissions');
  }

  return context;
}

/**
 * Optional authentication middleware
 * Provides user context if authenticated, but doesn't block if not
 */
export async function optionalAuth(request: NextRequest): Promise<MiddlewareContext> {
  return await authenticateRequest(request);
}

// ============================
// Middleware Utilities
// ============================

/**
 * Extract user info from authenticated context
 */
export function getUserFromContext(context: MiddlewareContext): AuthenticatedUser | null {
  return context.isAuthenticated ? context.user : null;
}

/**
 * Check if context is authenticated
 */
export function isAuthenticated(context: MiddlewareContext): context is AuthContext {
  return context.isAuthenticated;
}

/**
 * Check if user has specific permission
 */
export function hasPermission(context: MiddlewareContext, permission: string): boolean {
  return context.isAuthenticated && context.user.permissions.includes(permission);
}

/**
 * Check if user has any of the specified permissions
 */
export function hasAnyPermission(context: MiddlewareContext, permissions: string[]): boolean {
  return (
    context.isAuthenticated &&
    permissions.some((permission) => context.user.permissions.includes(permission))
  );
}

/**
 * Check if user has all of the specified permissions
 */
export function hasAllPermissions(context: MiddlewareContext, permissions: string[]): boolean {
  return (
    context.isAuthenticated &&
    permissions.every((permission) => context.user.permissions.includes(permission))
  );
}

// ============================
// Example Usage Patterns
// ============================

/**
 * Example: Protecting an API route
 *
 * ```typescript
 * import { requireAuth } from '@/lib/auth/middleware';
 *
 * export async function GET(request: NextRequest) {
 *   const authResult = await requireAuth(request);
 *
 *   if (authResult instanceof NextResponse) {
 *     return authResult; // Return error response
 *   }
 *
 *   const { user } = authResult;
 *
 *   // Your protected route logic here
 *   return NextResponse.json({ message: `Hello ${user.name}` });
 * }
 * ```
 */

/**
 * Example: Optional authentication
 *
 * ```typescript
 * import { optionalAuth, getUserFromContext } from '@/lib/auth/middleware';
 *
 * export async function GET(request: NextRequest) {
 *   const context = await optionalAuth(request);
 *   const user = getUserFromContext(context);
 *
 *   if (user) {
 *     // User is authenticated
 *     return NextResponse.json({ message: `Hello ${user.name}` });
 *   } else {
 *     // Public access
 *     return NextResponse.json({ message: 'Hello anonymous' });
 *   }
 * }
 * ```
 */

/**
 * Example: Permission-based access
 *
 * ```typescript
 * import { requirePermissions } from '@/lib/auth/middleware';
 *
 * export async function DELETE(request: NextRequest) {
 *   const authResult = await requirePermissions(request, ['admin']);
 *
 *   if (authResult instanceof NextResponse) {
 *     return authResult; // Return error response
 *   }
 *
 *   const { user } = authResult;
 *
 *   // Admin-only route logic here
 *   return NextResponse.json({ message: 'Admin action completed' });
 * }
 * ```
 */

export default {
  authenticateRequest,
  requireAuth,
  requirePermissions,
  optionalAuth,
  getUserFromContext,
  isAuthenticated,
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
};
