/**
 * 用户收藏句子列表 API
 * GET /api/user/favorites/sentences
 * 
 * 获取用户收藏的句子列表，支持筛选、排序和分页
 */

import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { z } from 'zod';
import { UserSavedSentenceQueryOptions } from '@/lib/2-repositories/UserSavedSentenceRepository';

// 请求参数验证 schema
const getFavoriteSentencesQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1).optional(),
  limit: z.coerce.number().min(1).max(100).default(10).optional(),
  sortBy: z.enum(['savedAt', 'practiceCount', 'lastPracticedAt', 'sentence.difficulty', 'sentence.length']).default('savedAt').optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc').optional(),
  difficulty: z.coerce.number().min(1).max(10).optional()
    .or(z.string().transform(val => val.split(',').map(Number))).optional(),
  category: z.string().optional()
    .or(z.string().transform(val => val.split(','))).optional(),
  practiceFrequency: z.enum(['never', 'rarely', 'sometimes', 'often', 'frequently']).optional(),
  needsPractice: z.string().transform(val => val.toLowerCase() === 'true').optional()
});

export async function GET(request: NextRequest) {
  try {
    // 用户认证
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { searchParams } = new URL(request.url);
    
    // 验证查询参数
    const queryResult = getFavoriteSentencesQuerySchema.safeParse(
      Object.fromEntries(searchParams.entries())
    );

    if (!queryResult.success) {
      return NextResponse.json(
        { 
          error: 'Invalid query parameters',
          details: queryResult.error.flatten().fieldErrors
        },
        { status: 400 }
      );
    }

    const options: UserSavedSentenceQueryOptions = {
      userId,
      ...queryResult.data
    };

    const practiceService = createPracticeService();

    // 获取用户收藏的句子列表
    const favoriteSentences = await practiceService.getUserFavoriteSentences(userId, options);
    
    // 获取总数（用于分页）
    const totalCount = await practiceService.getUserFavoriteSentenceCount(userId, {
      difficulty: options.difficulty,
      category: options.category,
      practiceFrequency: options.practiceFrequency,
      needsPractice: options.needsPractice
    });

    const page = options.page || 1;
    const limit = options.limit || 10;
    const totalPages = Math.ceil(totalCount / limit);
    const hasMore = page < totalPages;

    return NextResponse.json({
      success: true,
      data: {
        sentences: favoriteSentences,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages,
          hasMore
        },
        message: 'Favorite sentences retrieved successfully'
      }
    });

  } catch (error) {
    console.error('Error fetching favorite sentences:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to retrieve favorite sentences'
      },
      { status: 500 }
    );
  }
}