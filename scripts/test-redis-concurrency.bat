@echo off
REM Redis高并发测试运行脚本 (Windows版本)
REM 用于快速启动Redis高并发性能测试

echo 🚀 Redis高并发性能测试启动脚本
echo ==================================

REM 检查环境
echo 📋 检查测试环境...

REM 检查Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装，请先安装Node.js 18+
    pause
    exit /b 1
)

REM 检查npx
npx --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npx未找到，请确保npm已正确安装
    pause
    exit /b 1
)

REM 检查应用服务
echo 🔍 检查应用服务状态...
curl -s http://localhost:3000/api/health >nul 2>&1
if errorlevel 1 (
    echo ❌ 应用服务未运行，请先启动应用服务 ^(npm run dev^)
    echo    或检查服务是否运行在 http://localhost:3000
    pause
    exit /b 1
)

echo ✅ 环境检查通过
echo.

REM 设置测试环境变量
set NODE_ENV=test
set ENABLE_CACHE_LOGS=true
set ENABLE_DETAILED_CACHE_LOGS=false
set HIGH_CONCURRENCY_MODE=true

echo 🎯 开始执行Redis高并发测试...
echo 测试目标: 100并发稳定在500ms内
echo 测试环境变量已设置:
echo   - NODE_ENV=test
echo   - ENABLE_CACHE_LOGS=true
echo   - HIGH_CONCURRENCY_MODE=true
echo.

REM 运行测试
npx tsx scripts/test-redis-high-concurrency.ts

echo.
echo 🎉 测试完成!
echo 📊 如需查看详细分析，请检查控制台输出
echo 💡 如需优化建议，请参考测试报告中的建议部分
pause
