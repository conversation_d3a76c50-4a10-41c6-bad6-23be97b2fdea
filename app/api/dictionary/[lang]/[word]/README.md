# Dictionary API 词典接口文档

高性能的英语词典查询API，支持多层缓存架构和丰富的词汇信息。

## 🚀 基本信息

- **接口地址**: `GET /api/dictionary/{lang}/{word}`
- **服务端口**: `3001`
- **响应格式**: JSON
- **编码**: UTF-8

## 📋 请求参数

### 路径参数

| 参数   | 类型   | 必需 | 描述       | 示例                    |
| ------ | ------ | ---- | ---------- | ----------------------- |
| `lang` | string | ✅   | 语言代码   | `en`, `english`         |
| `word` | string | ✅   | 查询的单词 | `did`, `hello`, `don't` |

### 查询参数

| 参数    | 类型    | 必需 | 默认值  | 描述             |
| ------- | ------- | ---- | ------- | ---------------- |
| `fresh` | boolean | ❌   | `false` | 是否强制刷新缓存 |

### 请求示例

```bash
# 基本查询
GET http://localhost:3001/api/dictionary/en/did

# 强制刷新缓存
GET http://localhost:3001/api/dictionary/en/did?fresh=true
```

## 📊 响应格式

### 成功响应 (200 OK)

```json
{
  "success": true,
  "data": {
    "words": [
      {
        "word": "did",
        "explain": [
          {
            "pos": "v.",
            "definitions": [
              {
                "definition": "Past tense of 'do'.",
                "chinese": "do的过去式",
                "chinese_short": "做（过去式）"
              }
            ]
          }
        ],
        "phonetic": {
          "us": "dɪd",
          "uk": "dɪd"
        },
        "forms": [
          {
            "name": "原型",
            "form": "do"
          },
          {
            "name": "过去式",
            "form": "did"
          }
        ]
      }
    ],
    "wordCount": 1,
    "queryMetadata": {
      "searchTerm": "did",
      "matchedForm": "did",
      "searchStrategy": "exact_vocabulary",
      "isBaseFormQuery": true,
      "processingTimeMs": 2,
      "databaseQueries": 2,
      "cacheHit": true,
      "cacheSource": "redis_cache",
      "cacheHitTime": 2.4,
      "cacheLayer": "redis-distributed"
    },
    "cacheMetadata": {
      "source": "redis_cache",
      "hitTime": 2.4,
      "key": "search:did",
      "layer": "redis-distributed"
    }
  },
  "timestamp": "2025-07-11T13:42:07.745Z"
}
```

## 🔍 字段详解

### 根级字段

| 字段        | 类型    | 描述                  |
| ----------- | ------- | --------------------- |
| `success`   | boolean | 请求是否成功处理      |
| `data`      | object  | 响应数据主体          |
| `timestamp` | string  | 响应时间戳 (ISO 8601) |

### data 对象

| 字段            | 类型   | 描述           |
| --------------- | ------ | -------------- |
| `words`         | array  | 词汇数据数组   |
| `wordCount`     | number | 返回的词汇数量 |
| `queryMetadata` | object | 查询元数据     |
| `cacheMetadata` | object | 缓存元数据     |

### words[].word 词汇对象

| 字段       | 类型   | 描述           |
| ---------- | ------ | -------------- |
| `word`     | string | 单词本身       |
| `explain`  | array  | 词性和释义数组 |
| `phonetic` | object | 音标信息       |
| `forms`    | array  | 词形变化数组   |

### words[].explain[] 释义对象

| 字段          | 类型   | 描述                          |
| ------------- | ------ | ----------------------------- |
| `pos`         | string | 词性 (如: `v.`, `n.`, `adj.`) |
| `definitions` | array  | 该词性下的定义数组            |

### words[].explain[].definitions[] 定义对象

| 字段            | 类型   | 描述               |
| --------------- | ------ | ------------------ |
| `definition`    | string | 英文释义           |
| `chinese`       | string | 中文释义（完整版） |
| `chinese_short` | string | 中文释义（简短版） |

### words[].phonetic 音标对象

| 字段 | 类型   | 描述     |
| ---- | ------ | -------- |
| `us` | string | 美式音标 |
| `uk` | string | 英式音标 |

### words[].forms[] 词形对象

| 字段   | 类型   | 描述                                    |
| ------ | ------ | --------------------------------------- |
| `name` | string | 词形名称 (如: `原型`, `过去式`, `复数`) |
| `form` | string | 具体的词形                              |

### queryMetadata 查询元数据

| 字段               | 类型    | 描述                |
| ------------------ | ------- | ------------------- |
| `searchTerm`       | string  | 原始搜索词          |
| `matchedForm`      | string  | 匹配到的词形        |
| `searchStrategy`   | string  | 搜索策略            |
| `isBaseFormQuery`  | boolean | 是否为基础形式查询  |
| `processingTimeMs` | number  | 处理时间 (毫秒)     |
| `databaseQueries`  | number  | 数据库查询次数      |
| `cacheHit`         | boolean | 是否命中缓存        |
| `cacheSource`      | string  | 缓存来源            |
| `cacheHitTime`     | number  | 缓存命中时间 (毫秒) |
| `cacheLayer`       | string  | 缓存层级            |

### searchStrategy 搜索策略类型

| 值                  | 描述           |
| ------------------- | -------------- |
| `exact_vocabulary`  | 精确匹配词汇表 |
| `word_format_match` | 词形变化匹配   |
| `not_found`         | 未找到匹配结果 |

### cacheMetadata 缓存元数据

| 字段      | 类型   | 描述                |
| --------- | ------ | ------------------- |
| `source`  | string | 缓存来源类型        |
| `hitTime` | number | 缓存命中时间 (毫秒) |
| `key`     | string | 缓存键名            |
| `layer`   | string | 缓存层级标识        |

## 📡 响应头部

| 头部               | 描述                                 |
| ------------------ | ------------------------------------ |
| `x-cache-status`   | 缓存状态: `hit` / `miss`             |
| `x-cache-source`   | 缓存来源: `redis_cache` / `database` |
| `x-cache-layer`    | 缓存层级: `redis-distributed`        |
| `x-cache-hit-time` | 缓存命中时间 (如: `1.35ms`)          |

## 🚫 错误响应

### 400 Bad Request - 参数错误

```json
{
  "error": {
    "message": "不支持的语言: fr。目前支持: en, english",
    "statusCode": 400,
    "details": {
      "supportedLanguages": ["en", "english"]
    },
    "timestamp": "2025-07-11T13:42:07.745Z"
  }
}
```

### 400 Bad Request - 无效单词格式

```json
{
  "error": {
    "message": "无效的单词格式: do123。单词只能包含字母、连字符和撇号，长度1-50字符",
    "statusCode": 400,
    "details": {
      "pattern": "^[a-zA-Z'-]+$",
      "minLength": 1,
      "maxLength": 50
    },
    "timestamp": "2025-07-11T13:42:07.745Z"
  }
}
```

### 405 Method Not Allowed - 不支持的HTTP方法

```json
{
  "error": {
    "message": "不支持的HTTP方法: POST",
    "statusCode": 405,
    "timestamp": "2025-07-11T13:42:07.745Z"
  }
}
```

### 500 Internal Server Error - 服务器错误

```json
{
  "error": {
    "message": "服务器内部错误，请稍后重试",
    "statusCode": 500,
    "timestamp": "2025-07-11T13:42:07.745Z"
  }
}
```

## 📚 使用示例

### 1. 基本词汇查询

```bash
curl -X GET "http://localhost:3001/api/dictionary/en/hello"
```

### 2. 复数形式查询

```bash
curl -X GET "http://localhost:3001/api/dictionary/en/cats"
```

### 3. 过去式查询

```bash
curl -X GET "http://localhost:3001/api/dictionary/en/went"
```

### 4. 包含撇号的词

```bash
curl -X GET "http://localhost:3001/api/dictionary/en/don't"
```

### 5. 强制刷新缓存

```bash
curl -X GET "http://localhost:3001/api/dictionary/en/hello?fresh=true"
```

## ⚡ 性能特性

### 多层缓存架构

1. **Application Cache** - 应用内存缓存 (最快)
2. **Redis Cache** - 分布式缓存 (快速)
3. **Database** - PostgreSQL数据库 (基础)

### 性能指标

- **平均响应时间**: 1-5ms (缓存命中时)
- **缓存命中率**: >95%
- **支持并发**: 高并发请求处理
- **数据库查询优化**: 平均2次查询

## 🔧 开发配置

### 支持的语言

- `en` - 英语
- `english` - 英语 (别名)

### 单词格式验证

- **允许字符**: 字母 (`a-zA-Z`)、连字符 (`-`)、撇号 (`'`)
- **长度限制**: 1-50 字符
- **正则表达式**: `^[a-zA-Z'-]+$`

## 🐛 常见问题

### Q: 为什么有些单词查不到？

A: 词典数据基于SUBTLEX频率词汇表，覆盖了常用的高频词汇。罕见词汇可能不在数据库中。

### Q: 缓存多久更新一次？

A:

- Redis缓存: 1小时TTL
- 应用缓存: 内存允许时持久保存
- 使用 `fresh=true` 可强制刷新

### Q: 支持哪些词形变化？

A: 支持常见的词形变化，包括：

- 动词: 原型、过去式、过去分词、现在分词
- 名词: 单数、复数
- 形容词: 原级、比较级、最高级

### Q: API有频率限制吗？

A: 内置并发控制，正常使用无需担心频率限制。

## 📞 技术支持

如有技术问题，请检查：

1. 服务是否在端口3001运行
2. 请求格式是否正确
3. 网络连接是否正常
4. 查看服务器日志获取详细错误信息
