/**
 * UserSavedSentenceRepository
 * 
 * Data access layer for user saved sentence operations.
 * Handles the many-to-many relationship between users and sentences.
 */

import { prisma } from '@/lib/4-infrastructure/database/prisma';
import type { 
  UserSavedSentence, 
  CreateUserSavedSentenceInput, 
  UpdateUserSavedSentenceInput,
  UserSavedSentenceWithSentence,
  UserSentencePracticeStats
} from '@/lib/3-domain/entities/UserSavedSentence';

export interface UserSavedSentenceQueryOptions {
  userId: string;
  page?: number;
  limit?: number;
  sortBy?: 'savedAt' | 'practiceCount' | 'lastPracticedAt' | 'sentence.difficulty' | 'sentence.length';
  sortOrder?: 'asc' | 'desc';
  difficulty?: number | number[];
  category?: string | string[];
  practiceFrequency?: 'never' | 'rarely' | 'sometimes' | 'often' | 'frequently';
  needsPractice?: boolean;
}

export class UserSavedSentenceRepository {
  /**
   * Save a sentence for a user
   */
  async save(input: CreateUserSavedSentenceInput): Promise<UserSavedSentence> {
    const userSavedSentence = await prisma.userSavedSentence.create({
      data: {
        userId: input.userId,
        sentenceId: input.sentenceId
      }
    });

    // Increment the sentence's favorite count
    await prisma.sentence.update({
      where: { id: input.sentenceId },
      data: {
        favoriteCount: {
          increment: 1
        }
      }
    });

    return this.mapPrismaToUserSavedSentence(userSavedSentence);
  }

  /**
   * Remove a saved sentence for a user
   */
  async remove(userId: string, sentenceId: number): Promise<boolean> {
    try {
      await prisma.userSavedSentence.delete({
        where: {
          userId_sentenceId: {
            userId,
            sentenceId
          }
        }
      });

      // Decrement the sentence's favorite count
      await prisma.sentence.update({
        where: { id: sentenceId },
        data: {
          favoriteCount: {
            decrement: 1
          }
        }
      });

      return true;
    } catch (error: any) {
      if (error.code === 'P2025') {
        return false; // Record not found
      }
      throw error;
    }
  }

  /**
   * Check if a sentence is saved by a user
   */
  async isSaved(userId: string, sentenceId: number): Promise<boolean> {
    const userSavedSentence = await prisma.userSavedSentence.findUnique({
      where: {
        userId_sentenceId: {
          userId,
          sentenceId
        }
      }
    });

    return !!userSavedSentence;
  }

  /**
   * Find user saved sentence by user and sentence IDs
   */
  async findByUserAndSentence(userId: string, sentenceId: number): Promise<UserSavedSentence | null> {
    const userSavedSentence = await prisma.userSavedSentence.findUnique({
      where: {
        userId_sentenceId: {
          userId,
          sentenceId
        }
      }
    });

    return userSavedSentence ? this.mapPrismaToUserSavedSentence(userSavedSentence) : null;
  }

  /**
   * Update user saved sentence (practice stats)
   */
  async update(
    userId: string, 
    sentenceId: number, 
    input: UpdateUserSavedSentenceInput
  ): Promise<UserSavedSentence | null> {
    try {
      const userSavedSentence = await prisma.userSavedSentence.update({
        where: {
          userId_sentenceId: {
            userId,
            sentenceId
          }
        },
        data: input
      });

      return this.mapPrismaToUserSavedSentence(userSavedSentence);
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Increment practice count and update last practiced time
   */
  async incrementPracticeCount(userId: string, sentenceId: number): Promise<UserSavedSentence | null> {
    try {
      const userSavedSentence = await prisma.userSavedSentence.update({
        where: {
          userId_sentenceId: {
            userId,
            sentenceId
          }
        },
        data: {
          practiceCount: {
            increment: 1
          },
          lastPracticedAt: new Date()
        }
      });

      return this.mapPrismaToUserSavedSentence(userSavedSentence);
    } catch (error: any) {
      if (error.code === 'P2025') {
        return null; // Record not found
      }
      throw error;
    }
  }

  /**
   * Find user's saved sentences with options
   */
  async findByUser(options: UserSavedSentenceQueryOptions): Promise<UserSavedSentence[]> {
    const {
      userId,
      page = 1,
      limit = 10,
      sortBy = 'savedAt',
      sortOrder = 'desc',
      difficulty,
      category,
      practiceFrequency,
      needsPractice
    } = options;

    const where: any = {
      userId
    };

    // Filter by sentence properties
    if (difficulty || category) {
      where.sentence = {};
      
      if (difficulty) {
        where.sentence.difficulty = Array.isArray(difficulty) 
          ? { in: difficulty }
          : difficulty;
      }
      
      if (category) {
        where.sentence.category = Array.isArray(category)
          ? { in: category }
          : category;
      }
    }

    // Filter by practice frequency
    if (practiceFrequency) {
      switch (practiceFrequency) {
        case 'never':
          where.practiceCount = 0;
          break;
        case 'rarely':
          where.practiceCount = { gte: 1, lte: 2 };
          break;
        case 'sometimes':
          where.practiceCount = { gte: 3, lte: 5 };
          break;
        case 'often':
          where.practiceCount = { gte: 6, lte: 10 };
          break;
        case 'frequently':
          where.practiceCount = { gt: 10 };
          break;
      }
    }

    // Filter by sentences that need practice
    if (needsPractice) {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      
      where.OR = [
        { practiceCount: { lt: 3 } },
        { 
          AND: [
            { lastPracticedAt: { not: null } },
            { lastPracticedAt: { lt: oneWeekAgo } }
          ]
        },
        { lastPracticedAt: null }
      ];
    }

    let orderBy: any = {};
    if (sortBy.includes('.')) {
      // Handle nested sorting (e.g., 'sentence.difficulty')
      const [relation, field] = sortBy.split('.');
      orderBy[relation] = { [field]: sortOrder };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    const userSavedSentences = await prisma.userSavedSentence.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      include: {
        sentence: true
      }
    });

    return userSavedSentences.map(this.mapPrismaToUserSavedSentence);
  }

  /**
   * Find user's saved sentences with full sentence data
   */
  async findByUserWithSentences(options: UserSavedSentenceQueryOptions): Promise<UserSavedSentenceWithSentence[]> {
    const {
      userId,
      page = 1,
      limit = 10,
      sortBy = 'savedAt',
      sortOrder = 'desc',
      difficulty,
      category,
      practiceFrequency,
      needsPractice
    } = options;

    const where: any = {
      userId
    };

    // Filter by sentence properties
    if (difficulty || category) {
      where.sentence = {};
      
      if (difficulty) {
        where.sentence.difficulty = Array.isArray(difficulty) 
          ? { in: difficulty }
          : difficulty;
      }
      
      if (category) {
        where.sentence.category = Array.isArray(category)
          ? { in: category }
          : category;
      }
    }

    // Filter by practice frequency
    if (practiceFrequency) {
      switch (practiceFrequency) {
        case 'never':
          where.practiceCount = 0;
          break;
        case 'rarely':
          where.practiceCount = { gte: 1, lte: 2 };
          break;
        case 'sometimes':
          where.practiceCount = { gte: 3, lte: 5 };
          break;
        case 'often':
          where.practiceCount = { gte: 6, lte: 10 };
          break;
        case 'frequently':
          where.practiceCount = { gt: 10 };
          break;
      }
    }

    // Filter by sentences that need practice
    if (needsPractice) {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      
      where.OR = [
        { practiceCount: { lt: 3 } },
        { 
          AND: [
            { lastPracticedAt: { not: null } },
            { lastPracticedAt: { lt: oneWeekAgo } }
          ]
        },
        { lastPracticedAt: null }
      ];
    }

    let orderBy: any = {};
    if (sortBy.includes('.')) {
      // Handle nested sorting (e.g., 'sentence.difficulty')
      const [relation, field] = sortBy.split('.');
      orderBy[relation] = { [field]: sortOrder };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    const userSavedSentences = await prisma.userSavedSentence.findMany({
      where,
      orderBy,
      skip: (page - 1) * limit,
      take: limit,
      include: {
        sentence: {
          select: {
            id: true,
            content: true,
            source: true,
            difficulty: true,
            category: true,
            length: true
          }
        }
      }
    });

    return userSavedSentences.map(item => ({
      ...this.mapPrismaToUserSavedSentence(item),
      sentence: {
        id: item.sentence.id,
        content: item.sentence.content,
        source: item.sentence.source || undefined,
        difficulty: item.sentence.difficulty,
        category: item.sentence.category || undefined,
        length: item.sentence.length
      }
    }));
  }

  /**
   * Get user's sentence practice statistics
   */
  async getUserPracticeStats(userId: string): Promise<UserSentencePracticeStats> {
    const [
      totalSavedSentences,
      practiceStats,
      mostPracticedSentences,
      recentlyPracticedSentences,
      difficultyStats
    ] = await Promise.all([
      // Total saved sentences count
      prisma.userSavedSentence.count({
        where: { userId }
      }),
      
      // Practice statistics
      prisma.userSavedSentence.aggregate({
        where: { userId },
        _sum: { practiceCount: true },
        _avg: { practiceCount: true }
      }),
      
      // Most practiced sentences
      prisma.userSavedSentence.findMany({
        where: { userId },
        orderBy: { practiceCount: 'desc' },
        take: 5,
        include: {
          sentence: {
            select: {
              id: true,
              content: true,
              source: true,
              difficulty: true,
              category: true,
              length: true
            }
          }
        }
      }),
      
      // Recently practiced sentences
      prisma.userSavedSentence.findMany({
        where: { 
          userId,
          lastPracticedAt: { not: null }
        },
        orderBy: { lastPracticedAt: 'desc' },
        take: 5,
        include: {
          sentence: {
            select: {
              id: true,
              content: true,
              source: true,
              difficulty: true,
              category: true,
              length: true
            }
          }
        }
      }),
      
      // Difficulty distribution
      prisma.userSavedSentence.groupBy({
        by: ['sentenceId'],
        where: { userId },
        _sum: { practiceCount: true },
        _count: true
      }).then(async (groups) => {
        // We need to get the difficulty from the sentence table
        const sentenceIds = groups.map(g => g.sentenceId);
        const sentences = await prisma.sentence.findMany({
          where: { id: { in: sentenceIds } },
          select: { id: true, difficulty: true }
        });
        
        const difficultyMap: { [difficulty: number]: { count: number; totalPractices: number } } = {};
        
        groups.forEach(group => {
          const sentence = sentences.find(s => s.id === group.sentenceId);
          if (sentence) {
            const difficulty = sentence.difficulty;
            if (!difficultyMap[difficulty]) {
              difficultyMap[difficulty] = { count: 0, totalPractices: 0 };
            }
            difficultyMap[difficulty].count += group._count;
            difficultyMap[difficulty].totalPractices += group._sum.practiceCount || 0;
          }
        });
        
        return difficultyMap;
      })
    ]);

    return {
      totalSavedSentences,
      totalPracticeCount: practiceStats._sum.practiceCount || 0,
      averagePracticeCount: practiceStats._avg.practiceCount || 0,
      mostPracticedSentences: mostPracticedSentences.map(item => ({
        ...this.mapPrismaToUserSavedSentence(item),
        sentence: {
          id: item.sentence.id,
          content: item.sentence.content,
          source: item.sentence.source || undefined,
          difficulty: item.sentence.difficulty,
          category: item.sentence.category || undefined,
          length: item.sentence.length
        }
      })),
      recentlyPracticedSentences: recentlyPracticedSentences.map(item => ({
        ...this.mapPrismaToUserSavedSentence(item),
        sentence: {
          id: item.sentence.id,
          content: item.sentence.content,
          source: item.sentence.source || undefined,
          difficulty: item.sentence.difficulty,
          category: item.sentence.category || undefined,
          length: item.sentence.length
        }
      })),
      difficultySummary: await difficultyStats
    };
  }

  /**
   * Get count of user's saved sentences
   */
  async countByUser(userId: string, filters?: {
    difficulty?: number | number[];
    category?: string | string[];
    practiceFrequency?: 'never' | 'rarely' | 'sometimes' | 'often' | 'frequently';
    needsPractice?: boolean;
  }): Promise<number> {
    const where: any = { userId };

    if (filters) {
      const { difficulty, category, practiceFrequency, needsPractice } = filters;

      // Filter by sentence properties
      if (difficulty || category) {
        where.sentence = {};
        
        if (difficulty) {
          where.sentence.difficulty = Array.isArray(difficulty) 
            ? { in: difficulty }
            : difficulty;
        }
        
        if (category) {
          where.sentence.category = Array.isArray(category)
            ? { in: category }
            : category;
        }
      }

      // Filter by practice frequency
      if (practiceFrequency) {
        switch (practiceFrequency) {
          case 'never':
            where.practiceCount = 0;
            break;
          case 'rarely':
            where.practiceCount = { gte: 1, lte: 2 };
            break;
          case 'sometimes':
            where.practiceCount = { gte: 3, lte: 5 };
            break;
          case 'often':
            where.practiceCount = { gte: 6, lte: 10 };
            break;
          case 'frequently':
            where.practiceCount = { gt: 10 };
            break;
        }
      }

      // Filter by sentences that need practice
      if (needsPractice) {
        const oneWeekAgo = new Date();
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
        
        where.OR = [
          { practiceCount: { lt: 3 } },
          { 
            AND: [
              { lastPracticedAt: { not: null } },
              { lastPracticedAt: { lt: oneWeekAgo } }
            ]
          },
          { lastPracticedAt: null }
        ];
      }
    }

    return prisma.userSavedSentence.count({ where });
  }

  /**
   * Get sentences that need practice for a user
   */
  async getSentencesNeedingPractice(
    userId: string, 
    limit: number = 10
  ): Promise<UserSavedSentenceWithSentence[]> {
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const userSavedSentences = await prisma.userSavedSentence.findMany({
      where: {
        userId,
        OR: [
          { practiceCount: { lt: 3 } },
          { 
            AND: [
              { lastPracticedAt: { not: null } },
              { lastPracticedAt: { lt: oneWeekAgo } }
            ]
          },
          { lastPracticedAt: null }
        ]
      },
      orderBy: [
        { practiceCount: 'asc' },
        { lastPracticedAt: 'asc' }
      ],
      take: limit,
      include: {
        sentence: {
          select: {
            id: true,
            content: true,
            source: true,
            difficulty: true,
            category: true,
            length: true
          }
        }
      }
    });

    return userSavedSentences.map(item => ({
      ...this.mapPrismaToUserSavedSentence(item),
      sentence: {
        id: item.sentence.id,
        content: item.sentence.content,
        source: item.sentence.source || undefined,
        difficulty: item.sentence.difficulty,
        category: item.sentence.category || undefined,
        length: item.sentence.length
      }
    }));
  }

  /**
   * Map Prisma result to domain entity
   */
  private mapPrismaToUserSavedSentence(userSavedSentence: any): UserSavedSentence {
    return {
      id: userSavedSentence.id,
      userId: userSavedSentence.userId,
      sentenceId: userSavedSentence.sentenceId,
      savedAt: userSavedSentence.savedAt,
      practiceCount: userSavedSentence.practiceCount,
      lastPracticedAt: userSavedSentence.lastPracticedAt,
      createdAt: userSavedSentence.createdAt,
      updatedAt: userSavedSentence.updatedAt
    };
  }
}