import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { WordListRepository } from '@/lib/2-repositories/WordListRepository';
import { WordListEntryRepository } from '@/lib/2-repositories/WordListEntryRepository';
import { requireAuth, AuthContext } from '@/lib/auth/route-protection';

/**
 * 获取特定词库单词API
 * GET /api/practice/wordlist/[listId]
 */

// Query参数验证schema
const QuerySchema = z.object({
  page: z.string().transform(val => Math.max(1, parseInt(val) || 1)).optional(),
  limit: z.string().transform(val => Math.max(1, Math.min(100, parseInt(val) || 20))).optional(),
  random: z.string().transform(val => val === 'true').optional(),
  word: z.string().optional(), // 搜索特定单词
});

// 路径参数验证schema
const ParamsSchema = z.object({
  listId: z.string().transform(val => parseInt(val))
});

const protectedHandler = async (
  request: NextRequest,
  context: AuthContext,
  { params }: { params: { listId: string } }
) => {
  try {
    // 验证路径参数
    const { listId } = ParamsSchema.parse(params);

    // 解析查询参数
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    const validatedQuery = QuerySchema.parse(queryParams);

    // 创建Repository实例
    const wordListRepo = new WordListRepository();
    const wordListEntryRepo = new WordListEntryRepository();

    // 检查词库是否存在
    const wordList = await wordListRepo.findById(listId);
    if (!wordList) {
      return NextResponse.json({
        success: false,
        error: 'Word list not found',
        message: `Word list with ID ${listId} does not exist`
      }, { status: 404 });
    }

    if (!wordList.isActive) {
      return NextResponse.json({
        success: false,
        error: 'Word list not active',
        message: `Word list "${wordList.name}" is not currently active`
      }, { status: 403 });
    }

    // 构建查询选项
    const page = validatedQuery.page || 1;
    const limit = validatedQuery.limit || 20;
    const offset = (page - 1) * limit;

    const queryOptions = {
      wordListIds: [listId],
      limit,
      offset,
      random: validatedQuery.random || false,
      ...(validatedQuery.word && { word: validatedQuery.word })
    };

    // 获取练习会话数据（使用认证的用户ID）
    const practiceSession = await wordListEntryRepo.getPracticeSession(
      listId,
      context.user.id,
      queryOptions
    );

    // 计算总页数
    const totalPages = Math.ceil(practiceSession.sessionConfig.totalWords / limit);

    // 返回成功响应
    return NextResponse.json({
      success: true,
      data: {
        wordList: {
          id: wordList.id,
          name: wordList.name,
          description: wordList.description,
          difficulty: wordList.difficulty,
          totalWords: wordList.totalWords
        },
        words: practiceSession.words,
        pagination: {
          currentPage: page,
          pageSize: limit,
          totalWords: practiceSession.sessionConfig.totalWords,
          totalPages,
          hasNextPage: page < totalPages,
          hasPreviousPage: page > 1
        },
        config: {
          isRandomized: practiceSession.sessionConfig.isRandomized,
          filterDifficult: practiceSession.sessionConfig.filterDifficult,
          skipMastered: practiceSession.sessionConfig.skipMastered
        }
      },
      message: `Retrieved ${practiceSession.words.length} words from "${wordList.name}"`
    });

  } catch (error) {
    console.error('获取词库单词失败:', error);

    // Zod验证错误
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid parameters',
        details: error.errors
      }, { status: 400 });
    }

    // 通用错误处理
    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve word list words',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
};

export const GET = requireAuth()(protectedHandler);