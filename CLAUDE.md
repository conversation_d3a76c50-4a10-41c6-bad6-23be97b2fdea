# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Setup

### Package Manager

- Always use `pnpm` instead of `npm` for package management
- Dependencies are managed in `package.json` and locked with `pnpm-lock.yaml`
- 除非我需要你 dev 测试，一般情况下我都默认持续运行 `pnpm run dev`，如果不知道端口号可以向我提问

### Environment Configuration

The project includes a powerful environment configuration system for different development scenarios:

```bash
# View current configuration
pnpm run env:show

# Switch environments
pnpm run env:dev     # Development mode (INFO logging, 10% sampling)
pnpm run env:debug   # Debug mode (DEBUG logging, 100% sampling)
pnpm run env:perf    # High-concurrency mode (WARN logging, 1% sampling)
pnpm run env:prod    # Production mode (WARN logging, 1% sampling)
pnpm run env:test    # Performance testing mode (INFO logging, 5% sampling)
```

### Development Commands

```bash
# Development
pnpm dev              # Start development server with Turbopack
pnpm build           # Build for production
pnpm start           # Start production server

# Code Quality
pnpm lint            # ESLint with custom config in .config/eslint.config.js
pnpm format          # Prettier formatting
pnpm test            # Run Vitest tests
pnpm test:watch      # Run tests in watch mode
pnpm test:coverage   # Generate coverage report

# Database
pnpm prisma migrate dev       # Create and apply migration
pnpm prisma generate         # Generate Prisma client
pnpm prisma studio          # Open database browser

# Cache Management
pnpm cache:clear             # Clear Redis cache
pnpm cache:stats             # View cache statistics

# Redis Services
pnpm redis:start             # Start Redis and RedisInsight with Docker
pnpm redis:stop              # Stop Redis services
pnpm redisinsight:open       # Open RedisInsight UI
```

### Testing Framework

- Uses **Vitest** for unit and integration testing
- Test timeout is set to 180000 seconds (300 minutes) for long-running tests
- Path alias `@` points to `./app` directory
- Test files: `**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}`

## Architecture Overview

### Domain-Driven Design (DDD) Structure

The codebase follows a strict layered architecture organized by DDD principles:

```
app/lib/
├── 1-services/          # Business Services Layer
│   └── dictionary/DictionaryService.ts
├── 2-repositories/      # Data Access Layer
│   ├── VocabularyRepository.ts
│   ├── OptimizedVocabularyRepository.ts
│   ├── CacheRepository.ts
│   └── WordFormatRepository.ts
├── 3-domain/           # Domain Model Layer
│   ├── entities/Vocabulary.ts
│   └── value-objects/SearchResult.ts
├── 4-infrastructure/   # Infrastructure Layer
│   ├── database/prisma.ts
│   └── cache/redis.ts
├── 5-providers/        # External Services Layer
│   ├── gemini.ts
│   ├── openai.ts
│   └── dict-openai.ts
└── 6-scripts/          # Scripts and Tools
    ├── sync6w/         # Word synchronization
    └── llm-response-to-db/ # Data import
```

### Core Components

#### Dictionary System

- **Multi-tier caching**: Application memory → Redis → PostgreSQL
- **LLM providers**: Gemini, OpenAI with fallback strategy
- **Word forms**: Handles inflections and variations
- **Frequency ranking**: SUBTLEX word frequency integration

#### Data Model

- **Vocabulary**: Base word entries with phonetics and frequency ranking
- **Explain**: Part-of-speech explanations linked to vocabulary
- **Definition**: Detailed meanings with Chinese translations
- **WordFormat**: Word variations (plural, past tense, etc.)
- **UserWordStat**: User-specific lookup history and learning progress

#### Performance Features

- **Connection pooling**: Database connection optimization
- **Concurrency control**: Rate limiting and monitoring
- **Cache warming**: Preloading of frequent words
- **Response compression**: Gzip for API responses

## Language and Communication

- **Primary language**: Simplified Chinese (简体中文) for all communications
- Code comments and documentation should be in Chinese when appropriate
- API responses include both English definitions and Chinese translations

## Technology Stack

### Core Technologies

- **Framework**: Next.js 15 with App Router
- **Runtime**: Node.js 20+
- **Database**: PostgreSQL 16 with Prisma ORM
- **Cache**: Redis 7 with ioredis client
- **Testing**: Vitest with coverage reports
- **Styling**: Tailwind CSS 4
- **Type Safety**: TypeScript with strict configuration

### External Services

- **Google Gemini API**: Primary LLM provider for word definitions
- **OpenAI GPT-4**: Secondary LLM provider
- **Authentication**: NextAuth.js (planned)

### Development Tools

- **Linting**: ESLint with TypeScript rules
- **Formatting**: Prettier with automated fixes
- **Git Hooks**: Husky with lint-staged
- **Containerization**: Docker Compose for local development

## Configuration Files

- Environment presets managed via `scripts/configure-env.ts`
- ESLint config in `.config/eslint.config.js`
- Prisma schema in `prisma/schema.prisma`
- Docker services in `docker-compose.yml` and `docker/prod.yml`

## Common Patterns

### Error Handling

- Use Zod for input validation at API boundaries
- Structured error responses with proper HTTP status codes
- Graceful fallbacks for external service failures

### Database Operations

- Always use Prisma for database interactions
- Implement proper connection pooling
- Add database indexes for frequently queried fields

### Caching Strategy

- Check cache first, fallback to database, then to LLM providers
- Use consistent cache key patterns (e.g., `vocab:${word}`)
- Implement cache invalidation for data updates

### API Design

- RESTful endpoints under `/api/` directory
- Consistent response format with error handling
- Support for batch operations where appropriate

## Development Best Practices

- Follow the existing DDD layer boundaries strictly
- Write tests for new business logic and API endpoints
- Use environment configurations for different testing scenarios
- Document complex algorithms and business rules in Chinese
- Prefer composition over inheritance in TypeScript
- Use connection pooling for database operations
- Implement proper logging with structured data

## 个人项目开发经验

### 第三阶段完成总结 (2025-07-17)

**🎯 Sequential Spawn 工作流成果**

在第三阶段中，我们通过 `/spawn` 工作流完成了企业级轻量安全中间件系统的构建：

1. **Build Agent**: 实现了3级安全中间件（minimal <5ms, standard <10ms, strict <25ms）
2. **Optimizer Agent**: 创建了多级缓存系统（L1+L2 Redis，>90%命中率）
3. **Testing Agent**: 完成了综合负载测试（1-150并发请求验证）

**📊 性能成果**

- 响应时间：从12-31ms降到3-8ms（70-80%提升）
- 最大并发：从20-30提升到100-150（400%提升）
- 缓存命中率：>90%
- 内存使用：<50MB（150并发请求）

### 复杂度平衡经验教训

**⚠️ 关键问题识别**

1. **过度工程化风险**

   - 实现了企业级多级缓存系统（L1+L2+Redis）
   - 创建了复杂的对象池化机制
   - 添加了完整的性能监控和告警系统
   - 对个人项目来说维护成本过高

2. **技术探索 vs 实用性平衡**
   - ✅ 技术学习价值高，掌握了企业级架构设计
   - ⚠️ 实际应用价值对个人项目过剩
   - 💡 需要在技术深度和项目需求之间找到平衡

### 个人项目复杂度指导原则

**🎯 KISS原则（Keep It Simple, Stupid）**

- 优先选择简单、直接的解决方案
- 避免过度抽象和复杂的架构模式
- 先实现最小可行产品（MVP），再考虑优化

**🔧 技术选择原则**

- 选择成熟、文档完善的技术栈
- 避免引入过多依赖和复杂的中间件
- 优先选择维护成本低的方案

**⚡ 性能优化原则**

- 首先测量，然后优化（避免过早优化）
- 关注关键路径的性能瓶颈
- 考虑个人时间和精力投入

**🚀 扩展性原则**

- 为未来扩展预留接口，但不过度设计
- 优先考虑垂直扩展而非水平扩展
- 保持代码模块化，便于后期重构

**📚 学习与实用平衡**

- 技术学习项目可以适当增加复杂度
- 生产项目优先考虑稳定性和维护性
- 记录技术决策的权衡过程

**👤 个人项目特定原则**

- 考虑个人时间和精力投入
- 优先实现核心功能
- 避免需要24/7监控的复杂系统
- 选择可以长期维护的技术方案

### 简化策略建议

对于个人项目，建议采用以下简化策略：

**🔒 安全功能**

- 保留核心安全功能（基本威胁检测）
- 简化为单级Redis缓存
- 移除复杂的对象池化
- 简化监控为基本指标收集

**📈 性能优化**

- 专注于关键安全需求而非企业级完整解决方案
- 保持20-50并发处理能力即可满足个人项目需求
- 使用简单的内存缓存替代复杂的多级缓存

**🛠️ 维护性考虑**

- 选择能够独立维护的技术组件
- 避免过度依赖外部服务
- 保持系统的可观测性但不过度复杂

### 经验总结

通过第三阶段的开发，我们成功掌握了企业级安全中间件的设计和实现，但也认识到了在个人项目中需要更好地平衡技术探索和实用性。这些经验将指导我们在后续开发中做出更合适的技术决策。
