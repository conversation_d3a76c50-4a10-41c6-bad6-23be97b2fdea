import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright 端到端测试配置
 * 支持多浏览器、多视口、并行执行和完整的认证流程测试
 */
export default defineConfig({
  // 测试目录
  testDir: './e2e',

  // 单个测试超时时间 (5分钟)
  timeout: 5 * 60 * 1000,

  // 期望超时时间 (30秒)
  expect: {
    timeout: 30 * 1000,
  },

  // 失败时的重试次数
  retries: process.env.CI ? 2 : 1,

  // 并行执行的worker数量
  workers: process.env.CI ? 2 : 4,

  // 全局设置
  use: {
    // 基础URL
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',

    // 操作超时时间
    actionTimeout: 15 * 1000,

    // 导航超时时间
    navigationTimeout: 30 * 1000,

    // 失败时截图
    screenshot: 'only-on-failure',

    // 失败时录制视频
    video: 'retain-on-failure',

    // 失败时记录追踪
    trace: 'retain-on-failure',

    // 忽略HTTPS错误
    ignoreHTTPSErrors: true,

    // 地理位置 (默认为中国北京)
    locale: 'zh-CN',
    timezoneId: 'Asia/Shanghai',

    // 额外的HTTP头
    extraHTTPHeaders: {
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    },
  },

  // 测试项目配置 - 多浏览器和视口测试
  projects: [
    // Desktop浏览器测试
    {
      name: 'chromium-desktop',
      use: {
        ...devices['Desktop Chrome'],
        viewport: { width: 1920, height: 1080 },
      },
    },
    {
      name: 'firefox-desktop',
      use: {
        ...devices['Desktop Firefox'],
        viewport: { width: 1920, height: 1080 },
      },
    },
    {
      name: 'webkit-desktop',
      use: {
        ...devices['Desktop Safari'],
        viewport: { width: 1920, height: 1080 },
      },
    },

    // Mobile设备测试
    {
      name: 'mobile-chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'mobile-safari',
      use: { ...devices['iPhone 12'] },
    },

    // Tablet设备测试
    {
      name: 'tablet-chrome',
      use: { ...devices['iPad Pro'] },
    },

    // 认证状态测试项目
    {
      name: 'authenticated-user',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'e2e/auth/storage-states/authenticated-user.json',
      },
      dependencies: ['setup'],
    },
    {
      name: 'admin-user',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'e2e/auth/storage-states/admin-user.json',
      },
      dependencies: ['setup'],
    },

    // 设置项目 - 用于准备认证状态
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  // 报告器配置
  reporter: [
    // HTML报告
    [
      'html',
      {
        outputFolder: 'e2e-results/html-report',
        open: 'never',
      },
    ],
    // JUnit XML报告 (CI/CD集成)
    [
      'junit',
      {
        outputFile: 'e2e-results/junit-report.xml',
      },
    ],
    // JSON报告
    [
      'json',
      {
        outputFile: 'e2e-results/test-results.json',
      },
    ],
    // 控制台输出
    ['list'],
    // GitHub Actions集成
    ...(process.env.CI ? [['github']] : []),
  ],

  // 输出目录
  outputDir: 'e2e-results/test-artifacts',

  // 全局设置和清理
  globalSetup: require.resolve('./e2e/global-setup.ts'),
  globalTeardown: require.resolve('./e2e/global-teardown.ts'),

  // Web服务器配置 - 自动启动开发服务器
  webServer: process.env.CI
    ? undefined
    : {
        command: 'pnpm run dev',
        url: 'http://localhost:3000',
        reuseExistingServer: !process.env.CI,
        timeout: 120 * 1000, // 2分钟启动超时
        stdout: 'ignore',
        stderr: 'pipe',
      },

  // 测试匹配模式
  testMatch: ['**/e2e/**/*.spec.ts', '**/e2e/**/*.test.ts'],

  // 忽略的文件
  testIgnore: ['**/e2e/utils/**', '**/e2e/fixtures/**', '**/e2e/mocks/**'],

  // 元数据
  metadata: {
    'project-name': 'Lucid-BD E2E Tests',
    'test-environment': process.env.NODE_ENV || 'development',
    'base-url': process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
  },
});
