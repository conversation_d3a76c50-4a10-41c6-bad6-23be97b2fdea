/**
 * @fileoverview AuthProvider组件测试套件
 *
 * 测试认证Provider的核心功能，包括：
 * - 初始化和状态管理
 * - 登录/注册流程
 * - OAuth认证流程
 * - 令牌管理和刷新
 * - 错误处理
 * - 安全性验证
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AuthProvider, useAuthContext } from '../AuthProvider';
import { quickApiMocks, testServer, authApiMocks } from '@/test/utils/api-mocks';
import { createMockUser } from '@/test/utils/auth-test-utils';

// 测试组件用于验证Context
const TestComponent: React.FC = () => {
  const auth = useAuthContext();

  return (
    <div>
      <div data-testid="auth-status">
        {auth.isAuthenticated ? 'authenticated' : 'unauthenticated'}
      </div>
      <div data-testid="loading-status">{auth.loading ? 'loading' : 'loaded'}</div>
      <div data-testid="user-email">{auth.user?.email || 'no-user'}</div>
      <div data-testid="error-message">{auth.error || 'no-error'}</div>
      <button
        data-testid="login-button"
        onClick={() => auth.login({ email: '<EMAIL>', password: 'password123' })}
      >
        Login
      </button>
      <button
        data-testid="signup-button"
        onClick={() =>
          auth.signup({
            email: '<EMAIL>',
            password: 'password123',
            confirmPassword: 'password123',
            name: 'New User',
          })
        }
      >
        Signup
      </button>
      <button data-testid="oauth-login-button" onClick={() => auth.loginWithOAuth('google')}>
        OAuth Login
      </button>
      <button data-testid="logout-button" onClick={() => auth.logout()}>
        Logout
      </button>
      <button data-testid="refresh-button" onClick={() => auth.refreshToken()}>
        Refresh
      </button>
    </div>
  );
};

// Mock secure storage
const mockSecureTokenStorage = {
  getAuthStatus: vi.fn(),
  setTokens: vi.fn(),
  clearTokens: vi.fn(),
  refreshToken: vi.fn(),
};

const mockAutoTokenManager = {
  start: vi.fn(),
  stop: vi.fn(),
};

// Mock toast notifications
const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
};

// Mock router
const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

describe('AuthProvider', () => {
  const user = userEvent.setup();

  beforeAll(() => {
    testServer.listen();
  });

  afterAll(() => {
    testServer.close();
  });

  beforeEach(() => {
    // Mock dependencies
    vi.mock('@/lib/utils/secure-auth', () => ({
      SecureTokenStorage: mockSecureTokenStorage,
      AutoTokenManager: mockAutoTokenManager,
    }));

    vi.mock('sonner', () => ({
      toast: mockToast,
    }));

    vi.mock('next/navigation', () => ({
      useRouter: () => mockRouter,
    }));

    // Reset all mocks
    vi.clearAllMocks();

    // Default mock implementations
    mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
      isAuthenticated: false,
      user: null,
      hasValidRefreshToken: false,
    });
    mockSecureTokenStorage.setTokens.mockResolvedValue(true);
    mockSecureTokenStorage.clearTokens.mockResolvedValue(undefined);
    mockSecureTokenStorage.refreshToken.mockResolvedValue(true);

    // Setup API mocks
    quickApiMocks.cleanup();
  });

  afterEach(() => {
    testServer.resetHandlers();
    quickApiMocks.cleanup();
  });

  describe('初始化和状态管理', () => {
    it('应该正确初始化未认证状态', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // 等待初始化完成
      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      expect(screen.getByTestId('auth-status')).toHaveTextContent('unauthenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent('no-user');
      expect(screen.getByTestId('error-message')).toHaveTextContent('no-error');
    });

    it('应该正确初始化已认证状态', async () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      expect(screen.getByTestId('user-email')).toHaveTextContent(testUser.email);
      expect(mockAutoTokenManager.start).toHaveBeenCalled();
    });

    it('应该处理存储访问错误', async () => {
      mockSecureTokenStorage.getAuthStatus.mockRejectedValue(new Error('Storage error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      expect(screen.getByTestId('auth-status')).toHaveTextContent('unauthenticated');
      expect(mockSecureTokenStorage.clearTokens).toHaveBeenCalled();
    });
  });

  describe('登录功能', () => {
    it('应该处理成功登录', async () => {
      const testUser = createMockUser();
      quickApiMocks.setupSuccessfulLogin(testUser);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      // 等待初始化完成
      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      // 触发登录
      await act(async () => {
        await user.click(screen.getByTestId('login-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      expect(screen.getByTestId('user-email')).toHaveTextContent(testUser.email);
      expect(mockSecureTokenStorage.setTokens).toHaveBeenCalledWith(
        authApiMocks.tokens.accessToken,
        authApiMocks.tokens.refreshToken,
        expect.objectContaining({ email: testUser.email })
      );
      expect(mockAutoTokenManager.start).toHaveBeenCalled();
      expect(mockToast.success).toHaveBeenCalledWith('Successfully logged in!');
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
    });

    it('应该处理登录失败', async () => {
      quickApiMocks.setupFailedLogin('Invalid credentials');

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      // 触发登录
      await act(async () => {
        await user.click(screen.getByTestId('login-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Invalid credentials');
      });

      expect(screen.getByTestId('auth-status')).toHaveTextContent('unauthenticated');
      expect(mockToast.error).toHaveBeenCalledWith('Invalid credentials');
    });

    it('应该处理令牌存储失败', async () => {
      const testUser = createMockUser();
      quickApiMocks.setupSuccessfulLogin(testUser);
      mockSecureTokenStorage.setTokens.mockResolvedValue(false);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      await act(async () => {
        await user.click(screen.getByTestId('login-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toContain(
          'Failed to store authentication tokens'
        );
      });

      expect(mockToast.error).toHaveBeenCalled();
    });
  });

  describe('注册功能', () => {
    it('应该处理成功注册', async () => {
      const testUser = createMockUser({ email: '<EMAIL>' });
      quickApiMocks.setupSuccessfulSignup(testUser);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      await act(async () => {
        await user.click(screen.getByTestId('signup-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      expect(mockToast.success).toHaveBeenCalledWith('Account created successfully!');
      expect(mockRouter.push).toHaveBeenCalledWith('/dashboard');
    });

    it('应该处理密码不匹配', async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      // Mock signup with mismatched passwords
      const authContext = vi.fn();
      const signup = vi.fn().mockImplementation(async (credentials) => {
        if (credentials.password !== credentials.confirmPassword) {
          throw new Error('Passwords do not match');
        }
      });

      // This test needs to be refined based on actual implementation
      // For now, we can test the error handling logic
    });
  });

  describe('OAuth功能', () => {
    it('应该处理OAuth登录流程', async () => {
      const testUser = createMockUser();

      // Mock window.open
      const mockPopup = {
        closed: false,
        close: vi.fn(),
      };
      global.window.open = vi.fn().mockReturnValue(mockPopup);

      // Mock message event
      const messageEvent = new MessageEvent('message', {
        origin: window.location.origin,
        data: {
          type: 'oauth-success',
          tokens: {
            accessToken: authApiMocks.tokens.accessToken,
            refreshToken: authApiMocks.tokens.refreshToken,
          },
        },
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      // Setup user profile mock
      quickApiMocks.setupSuccessfulLogin(testUser);

      // Trigger OAuth login
      await act(async () => {
        await user.click(screen.getByTestId('oauth-login-button'));

        // Simulate OAuth callback
        setTimeout(() => {
          window.dispatchEvent(messageEvent);
        }, 100);
      });

      expect(global.window.open).toHaveBeenCalledWith(
        '/api/auth/token/google',
        'oauth-popup',
        'width=600,height=600,scrollbars=yes,resizable=yes'
      );
    });

    it('应该处理弹窗被阻止的情况', async () => {
      global.window.open = vi.fn().mockReturnValue(null);

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      await act(async () => {
        await user.click(screen.getByTestId('oauth-login-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toContain('Popup blocked');
      });

      expect(mockToast.error).toHaveBeenCalled();
    });
  });

  describe('登出功能', () => {
    it('应该正确处理登出', async () => {
      const testUser = createMockUser();

      // 设置初始已认证状态
      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      // 触发登出
      await act(async () => {
        await user.click(screen.getByTestId('logout-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('unauthenticated');
      });

      expect(mockSecureTokenStorage.clearTokens).toHaveBeenCalled();
      expect(mockAutoTokenManager.stop).toHaveBeenCalled();
      expect(mockToast.success).toHaveBeenCalledWith('Successfully logged out');
      expect(mockRouter.push).toHaveBeenCalledWith('/login');
    });

    it('应该在API调用失败时仍然清理本地状态', async () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      // Mock API error
      global.fetch = vi.fn().mockRejectedValue(new Error('Network error'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      await act(async () => {
        await user.click(screen.getByTestId('logout-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('unauthenticated');
      });

      expect(mockSecureTokenStorage.clearTokens).toHaveBeenCalled();
    });
  });

  describe('令牌刷新功能', () => {
    it('应该成功刷新令牌', async () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      await act(async () => {
        await user.click(screen.getByTestId('refresh-button'));
      });

      expect(mockSecureTokenStorage.refreshToken).toHaveBeenCalled();
    });

    it('应该处理令牌刷新失败', async () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      mockSecureTokenStorage.refreshToken.mockRejectedValue(new Error('Refresh failed'));

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      await act(async () => {
        try {
          await user.click(screen.getByTestId('refresh-button'));
        } catch (error) {
          // Expected to throw
        }
      });

      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('unauthenticated');
      });

      expect(mockSecureTokenStorage.clearTokens).toHaveBeenCalled();
      expect(mockRouter.push).toHaveBeenCalledWith('/login');
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理网络错误', async () => {
      quickApiMocks.setupNetworkError();

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('loaded');
      });

      await act(async () => {
        await user.click(screen.getByTestId('login-button'));
      });

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).not.toHaveTextContent('no-error');
      });
    });

    it('应该在组件卸载时停止令牌管理', () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      const { unmount } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );

      unmount();

      expect(mockAutoTokenManager.stop).toHaveBeenCalled();
    });
  });

  describe('Context错误处理', () => {
    it('应该在Provider外使用时抛出错误', () => {
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAuthContext must be used within an AuthProvider');

      consoleSpy.mockRestore();
    });
  });
});
