#!/bin/bash

echo "🧪 Testing CORS Configuration"
echo "================================"

BASE_URL="http://localhost:4000"

echo ""
echo "1. Testing Chrome Extension Origin..."
curl -s -H "Origin: chrome-extension://abcdefghijklmnop" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS "$BASE_URL/api/test-cors" \
     -I | grep -E "(Access-Control|HTTP)"

echo ""
echo "2. Testing HTTPS Origin..."
curl -s -H "Origin: https://example.com" \
     -H "Access-Control-Request-Method: POST" \
     -X OPTIONS "$BASE_URL/api/test-cors" \
     -I | grep -E "(Access-Control|HTTP)"

echo ""
echo "3. Testing Localhost Origin..."
curl -s -H "Origin: http://localhost:8080" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS "$BASE_URL/api/test-cors" \
     -I | grep -E "(Access-Control|HTTP)"

echo ""
echo "4. Testing Blocked Origin..."
curl -s -H "Origin: http://evil.com" \
     -H "Access-Control-Request-Method: GET" \
     -X OPTIONS "$BASE_URL/api/test-cors" \
     -I | grep -E "(Access-Control|HTTP)"

echo ""
echo "5. Testing GET Request with CORS..."
curl -s -H "Origin: https://example.com" \
     "$BASE_URL/api/test-cors" | jq .

echo ""
echo "✅ CORS testing complete!"
