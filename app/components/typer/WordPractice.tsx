'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { useTypingEngine, State } from '@/hooks/use-typing-engine';
import { Character, Caret } from './shared';
import { cn } from '@/lib/utils';

// 常量定义
const WORD_PRACTICE_TIME_LIMIT = 90;
const PRACTICE_WORDS_COUNT = 20;

// 类型定义
interface WordWithTranslation {
  word: string;
  translation: string;
}

interface CardProps {
  wordData?: WordWithTranslation;
  type: 'primary' | 'secondary';
  isMarked?: boolean;
  label: string;
  shortcut?: string;
  onToggleMark?: () => void;
}

// 单词翻译数据
const WORDS_WITH_TRANSLATIONS: WordWithTranslation[] = [
  { word: 'ubiquitous', translation: '无处不在的' },
  { word: 'ephemeral', translation: '短暂的' },
  { word: 'mellifluous', translation: '悦耳的' },
  { word: 'serendipity', translation: '意外发现' },
  { word: 'petrichor', translation: '雨后泥土的气味' },
  { word: 'eloquence', translation: '口才' },
  { word: 'nefarious', translation: '邪恶的' },
  { word: 'cacophony', translation: '刺耳的声音' },
  { word: 'epiphany', translation: '顿悟' },
  { word: 'languid', translation: '慵懒的' },
  { word: 'panacea', translation: '万能药' },
  { word: 'gregarious', translation: '合群的' },
  { word: 'ostracize', translation: '排斥' },
  { word: 'quixotic', translation: '不切实际的' },
  { word: 'resilience', translation: '恢复力' },
  { word: 'sycophant', translation: '马屁精' },
  { word: 'verbose', translation: '啰嗦的' },
  { word: 'zealous', translation: '热情的' },
  { word: 'acumen', translation: '敏锐' },
  { word: 'benevolent', translation: '仁慈的' },
  { word: 'cognizant', translation: '认识到的' },
  { word: 'diligent', translation: '勤奋的' },
  { word: 'empathy', translation: '同理心' },
  { word: 'fortitude', translation: '刚毅' },
  { word: 'hegemony', translation: '霸权' },
  { word: 'incognito', translation: '隐姓埋名的' },
  { word: 'juxtaposition', translation: '并列' },
  { word: 'loquacious', translation: '多话的' },
  { word: 'magnanimous', translation: '宽宏大量的' },
  { word: 'novice', translation: '新手' },
  { word: 'obfuscate', translation: '使模糊' },
  { word: 'proclivity', translation: '倾向' },
  { word: 'quintessential', translation: '精髓的' },
  { word: 'rhetoric', translation: '修辞' },
  { word: 'stoic', translation: '坚忍的' },
  { word: 'tenacious', translation: '顽强的' },
  { word: 'usurp', translation: '篡夺' },
  { word: 'vex', translation: '使烦恼' },
  { word: 'wane', translation: '衰落' },
  { word: 'xenophobia', translation: '仇外' },
  { word: 'yearn', translation: '渴望' },
  { word: 'zenith', translation: '天顶' },
  { word: 'aberration', translation: '异常' },
  { word: 'candor', translation: '坦率' },
  { word: 'ebullient', translation: '热情洋溢的' },
  { word: 'fastidious', translation: '挑剔的' },
  { word: 'garrulous', translation: '唠唠不休的' },
];

/**
 * 生成练习用的随机单词
 */
const generatePracticeWords = (count: number): WordWithTranslation[] => {
  const shuffled = WORDS_WITH_TRANSLATIONS.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

/**
 * 翻译卡片组件
 */
const TranslationCard: React.FC<CardProps> = ({
  wordData,
  type,
  isMarked = false,
  label,
  shortcut,
  onToggleMark,
}) => {
  const isPrimary = type === 'primary';

  const containerClasses = cn(
    'flex flex-col justify-between p-4 rounded-lg bg-muted transition-all duration-300 h-36',
    isPrimary
      ? 'w-1/2 max-w-sm border-2 border-primary shadow-lg'
      : 'w-1/4 max-w-xs opacity-60 border border-muted-foreground'
  );

  return (
    <div className={containerClasses}>
      {wordData ? (
        <>
          <div className="flex-grow">
            <p
              className={cn(
                'font-bold',
                isPrimary ? 'text-2xl' : 'text-xl',
                isMarked ? 'text-orange-500' : 'text-primary'
              )}
            >
              {wordData.word}
            </p>
            <p className={cn('mt-1 text-muted-foreground', isPrimary ? 'text-lg' : 'text-base')}>
              {wordData.translation}
            </p>
          </div>
          <div className="flex items-center justify-between text-muted-foreground text-xs">
            <span>{label}</span>
            {shortcut && (
              <span className="font-mono bg-background text-foreground px-1.5 py-0.5 rounded text-xs">
                {shortcut}
              </span>
            )}
          </div>
          {isPrimary && onToggleMark && (
            <div className="mt-2 text-center">
              <Button variant="ghost" size="sm" className="h-6 px-2 text-xs" onClick={onToggleMark}>
                {isMarked ? '取消标记' : '标记'}
              </Button>
            </div>
          )}
        </>
      ) : (
        <div className="h-full"></div>
      )}
    </div>
  );
};

/**
 * 翻译卡片组
 */
const TranslationCards: React.FC<{
  words: WordWithTranslation[];
  currentIndex: number;
  markedWords: Set<string>;
  onToggleMark: () => void;
}> = ({ words, currentIndex, markedWords, onToggleMark }) => {
  const prevWord = words[currentIndex - 1];
  const currentWord = words[currentIndex];
  const nextWord = words[currentIndex + 1];

  return (
    <div className="w-full flex items-start justify-center gap-4 mt-8 px-4 select-none">
      <TranslationCard wordData={prevWord} type="secondary" label="上一个" shortcut="Alt + H" />
      <TranslationCard
        wordData={currentWord}
        type="primary"
        isMarked={!!currentWord && markedWords.has(currentWord.word)}
        label="当前"
        shortcut="Alt + J"
        onToggleMark={onToggleMark}
      />
      <TranslationCard wordData={nextWord} type="secondary" label="下一个" shortcut="Alt + L" />
    </div>
  );
};

/**
 * 练习结果组件
 */
const WordPracticeResults: React.FC<{
  words: WordWithTranslation[];
  wpm: number;
  accuracy: number;
  errors: number;
  markedWords: Set<string>;
  onRestart: () => void;
  onUnmarkWord: (word: string) => void;
}> = ({ words, wpm, accuracy, errors, markedWords, onRestart, onUnmarkWord }) => {
  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <div className="bg-muted p-6 rounded-lg">
        <h2 className="text-3xl font-bold text-center text-primary mb-6">练习完成</h2>
        <div className="flex justify-around mb-6 text-center">
          <div>
            <p className="text-lg text-muted-foreground">WPM</p>
            <p className="text-4xl font-bold text-primary">{wpm}</p>
          </div>
          <div>
            <p className="text-lg text-muted-foreground">准确率</p>
            <p className="text-4xl font-bold text-primary">{accuracy}%</p>
          </div>
          <div>
            <p className="text-lg text-muted-foreground">错误数</p>
            <p className="text-4xl font-bold text-destructive">{errors}</p>
          </div>
        </div>
      </div>

      {Array.from(markedWords).length > 0 && (
        <div className="bg-muted p-6 rounded-lg">
          <h3 className="text-2xl font-bold text-center text-primary mb-4">标记词汇</h3>
          <div className="flex flex-wrap justify-center gap-3">
            {Array.from(markedWords).map((word) => {
              const wordData = words.find((w) => w.word === word);
              return (
                <div key={word} className="bg-background p-3 rounded-md border relative group">
                  <p className="font-semibold text-orange-500">{word}</p>
                  {wordData && (
                    <p className="text-sm text-muted-foreground">{wordData.translation}</p>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={() => onUnmarkWord(word)}
                  >
                    ×
                  </Button>
                </div>
              );
            })}
          </div>
        </div>
      )}

      <div className="flex justify-center">
        <Button onClick={onRestart}>重新开始</Button>
      </div>
    </div>
  );
};

/**
 * 生词练习组件
 * 提供英语生词的打字练习，包含翻译提示和标记功能
 */
export const WordPractice: React.FC = () => {
  const [words, setWords] = useState<WordWithTranslation[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);
  const [markedWords, setMarkedWords] = useState<Set<string>>(new Set());

  // Initialize words on client side to avoid SSR/CSR mismatch
  useEffect(() => {
    setWords(generatePracticeWords(PRACTICE_WORDS_COUNT));
    setIsInitialized(true);
  }, []);

  const wordList = useMemo(() => words.map((w) => w.word), [words]);

  const {
    state,
    typed,
    timeLeft,
    wpm,
    accuracy,
    errors,
    restart,
    fullText,
    skipWord,
    previousWord,
  } = useTypingEngine(wordList, WORD_PRACTICE_TIME_LIMIT);

  const currentWordIndex = useMemo(() => {
    const typedWords = typed.split(' ');
    return Math.min(typedWords.length - 1, words.length - 1);
  }, [typed, words.length]);

  const toggleMarkCurrentWord = useCallback(() => {
    const wordToMark = words[currentWordIndex]?.word;
    if (!wordToMark) return;

    // 检查当前是否已经标记
    const wasAlreadyMarked = markedWords.has(wordToMark);

    setMarkedWords((prev) => {
      const newSet = new Set(prev);
      if (wasAlreadyMarked) {
        newSet.delete(wordToMark);
      } else {
        newSet.add(wordToMark);
      }
      return newSet;
    });

    // 只有在新标记时才自动跳到下一个单词，取消标记时不跳转
    if (!wasAlreadyMarked) {
      skipWord();
    }
  }, [words, currentWordIndex, markedWords, skipWord]);

  // 快捷键支持
  useEffect(() => {
    const handleShortcuts = (e: KeyboardEvent) => {
      if (state !== State.Run) return;
      if (e.altKey) {
        if (e.code === 'KeyJ') {
          e.preventDefault();
          toggleMarkCurrentWord();
        } else if (e.code === 'KeyL') {
          e.preventDefault();
          skipWord();
        } else if (e.code === 'KeyH') {
          e.preventDefault();
          previousWord();
        }
      }
    };
    window.addEventListener('keydown', handleShortcuts);
    return () => window.removeEventListener('keydown', handleShortcuts);
  }, [state, toggleMarkCurrentWord, skipWord, previousWord]);

  const handleRestart = useCallback(() => {
    setMarkedWords(new Set());
    setWords(generatePracticeWords(PRACTICE_WORDS_COUNT));
    restart();
  }, [restart]);

  const unmarkWord = useCallback((word: string) => {
    setMarkedWords((prev) => {
      const newSet = new Set(prev);
      newSet.delete(word);
      return newSet;
    });
  }, []);

  const characters = useMemo(() => {
    if (!fullText) return [];

    const wordBoundaries = new Map<number, string>();
    let currentIndex = 0;
    for (const word of fullText.split(' ')) {
      for (let i = 0; i < word.length; i++) {
        wordBoundaries.set(currentIndex + i, word);
      }
      currentIndex += word.length + 1;
    }

    return fullText.split('').map((char: string, index: number) => {
      const word = wordBoundaries.get(index);
      const isMarked = !!word && markedWords.has(word);
      return (
        <Character
          key={`${char}-${index}`}
          expected={char}
          actual={typed[index]}
          isTyped={index < typed.length}
          isMarked={isMarked}
        />
      );
    });
  }, [fullText, typed, markedWords]);

  // Show loading state during initialization
  if (!isInitialized) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-muted-foreground animate-pulse">Loading...</p>
      </div>
    );
  }

  if (state === State.Finish) {
    return (
      <WordPracticeResults
        words={words}
        wpm={wpm}
        accuracy={accuracy}
        errors={errors}
        markedWords={markedWords}
        onRestart={handleRestart}
        onUnmarkWord={unmarkWord}
      />
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-around items-center p-4 bg-muted rounded-lg">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Time Left</p>
          <p className="text-2xl font-bold text-primary">{timeLeft}</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Progress</p>
          <p className="text-2xl font-bold">
            {currentWordIndex + 1}/{words.length}
          </p>
        </div>
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Errors</p>
          <p className="text-2xl font-bold text-destructive">{errors}</p>
        </div>
      </div>

      <div className="text-2xl leading-relaxed p-6 border rounded-md relative select-none font-mono">
        {typed.length === 0 && state === State.Start && <Caret />}
        {characters.map((charComponent, index) => (
          <React.Fragment key={index}>
            {charComponent}
            {index === typed.length - 1 && state === State.Run && <Caret />}
          </React.Fragment>
        ))}
        {state === State.Start && (
          <div className="absolute inset-0 flex items-center justify-center bg-background/80">
            <p className="text-muted-foreground animate-pulse">Start typing to begin...</p>
          </div>
        )}
      </div>

      <TranslationCards
        words={words}
        currentIndex={currentWordIndex}
        markedWords={markedWords}
        onToggleMark={toggleMarkCurrentWord}
      />

      <div className="flex gap-4 justify-center">
        <Button onClick={handleRestart}>Restart</Button>
      </div>
    </div>
  );
};
