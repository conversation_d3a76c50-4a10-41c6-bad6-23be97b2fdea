'use client';

import { useEffect, useState } from 'react';

export default function HealthPage() {
  const [status, setStatus] = useState<'loading' | 'ok' | 'error'>('loading');
  const [message, setMessage] = useState('');

  useEffect(() => {
    fetch('/api/health')
      .then(async (res) => {
        if (!res.ok) throw new Error('Network error');
        const data = await res.json();
        if (data.ok) {
          setStatus('ok');
          setMessage(data.message);
        } else {
          setStatus('error');
          setMessage(data.message || 'Unknown error');
        }
      })
      .catch(() => {
        setStatus('error');
        setMessage('无法连接到健康检查接口');
      });
  }, []);

  return (
    <main style={{ padding: 32 }}>
      <h1>健康检查</h1>
      {status === 'loading' && <p>加载中...</p>}
      {status === 'ok' && <p style={{ color: 'green' }}>✅ {message}</p>}
      {status === 'error' && <p style={{ color: 'red' }}>❌ {message}</p>}
    </main>
  );
}
