/**
 * 字符串 token 合法性检查与 prompt 批量校验工具
 * 用于判断单词或短语是否符合字典查询要求。
 */

/**
 * 判断单词或词组 token 是否有效
 * 规则：
 * 1. 允许字母、数字、空格、连字符、撇号。
 * 2. 必须至少包含一个英文字母。
 * 3. 不能以连字符或撇号开头或结尾 (仍允许以数字开头，如 1password)。
 * 4. 长度 1-45
 * 5. 不允许出现三个及以上连续相同字母（忽略空格/连字符/撇号）
 */
export const isValidToken = (token: string): boolean => {
  const trimmed = token.trim();
  if (trimmed.length === 0 || trimmed.length > 45) return false;

  // 新规则：
  // 1. 允许字母、数字、空格、连字符、撇号。
  // 2. 必须至少包含一个英文字母。
  // 3. 不能以连字符或撇号开头或结尾 (仍允许以数字开头，如 1password)。
  if (!/^[a-zA-Z0-9\s\-']+$/.test(trimmed)) return false; // 检查整体字符集
  if (!/[a-zA-Z]/.test(trimmed)) return false; // 必须至少包含一个字母
  if (/^[\-']|[\-']$/.test(trimmed)) return false; // 不能以连字符或撇号开头或结尾

  // 去除非字母数字字符后检查重复字符 (空格、连字符、撇号)
  const collapsed = trimmed.replace(/[\s\-']/g, '').toLowerCase(); // 转为小写以进行不区分大小写的重复检查
  if (/(.)\1{2,}/.test(collapsed)) return false; // 重复字符检查现在不区分大小写

  return true;
};

/**
 * 对逗号分隔 prompt 中的 token 进行批量校验
 * @param prompt 逗号分隔的单词/词组列表
 * @returns { valid: boolean; invalidTokens: string[] }
 */
export const validatePrompt = (prompt: string): { valid: boolean; invalidTokens: string[] } => {
  const tokens = prompt
    .split(',')
    .map((t) => t.trim())
    .filter(Boolean);
  const invalid: string[] = tokens.filter((t) => !isValidToken(t));
  return { valid: invalid.length === 0, invalidTokens: invalid };
};
