import net from 'net';
import { spawn } from 'child_process';

const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 4000;

function startServer(port) {
  const server = net.createServer();
  server.once('error', (err) => {
    if (err.code === 'EADDRINUSE') {
      console.log(`Port ${port} is in use, trying next port...`);
      startServer(port + 1);
    }
  });

  server.once('listening', () => {
    server.close();
    const child = spawn('next', ['dev', '-p', port], { stdio: 'inherit' });
    child.on('error', (err) => {
      console.error('Failed to start subprocess.', err);
    });
  });

  server.listen(port);
}

startServer(port);
