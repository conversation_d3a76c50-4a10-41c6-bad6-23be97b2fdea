/**
 * PracticeService 词库功能单元测试
 */
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PracticeService } from '../PracticeService';
import { WordListRepository } from '@/lib/2-repositories/WordListRepository';
import { WordListEntryRepository } from '@/lib/2-repositories/WordListEntryRepository';
import { UserWordProficiencyRepository } from '@/lib/2-repositories/UserWordProficiencyRepository';
import { VocabularyRepository } from '@/lib/2-repositories/VocabularyRepository';
import { ProficiencyCalculationService } from '../ProficiencyCalculationService';
import { NotFoundError, ValidationError } from '@/lib/types/api-types';

// Mock所有依赖
vi.mock('@/lib/2-repositories/WordListRepository');
vi.mock('@/lib/2-repositories/WordListEntryRepository');
vi.mock('@/lib/2-repositories/UserWordProficiencyRepository');
vi.mock('@/lib/2-repositories/VocabularyRepository');
vi.mock('../ProficiencyCalculationService');
vi.mock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: {
    vocabulary: {
      create: vi.fn(),
      findUnique: vi.fn()
    },
    userWordProficiency: {
      findMany: vi.fn(),
      upsert: vi.fn()
    }
  }
}));

describe('PracticeService - 词库功能', () => {
  let practiceService: PracticeService;
  let mockWordListRepo: any;
  let mockWordListEntryRepo: any;
  let mockUserProficiencyRepo: any;
  let mockVocabularyRepo: any;
  let mockProficiencyService: any;

  beforeEach(() => {
    // 创建Mock实例
    mockWordListRepo = {
      findById: vi.fn(),
      getStatsWithUserProgress: vi.fn()
    };
    mockWordListEntryRepo = {
      getPracticeWords: vi.fn(),
      getPracticeSession: vi.fn(),
      findByWord: vi.fn(),
      findMany: vi.fn()
    };
    mockUserProficiencyRepo = {
      findByUserAndWord: vi.fn(),
      upsert: vi.fn(),
      create: vi.fn(),
      update: vi.fn()
    };
    mockVocabularyRepo = {
      findByWord: vi.fn(),
      findManyByWords: vi.fn()
    };
    mockProficiencyService = {};

    // 创建service实例
    practiceService = new PracticeService(
      mockUserProficiencyRepo,
      mockVocabularyRepo,
      mockProficiencyService,
      mockWordListRepo,
      mockWordListEntryRepo
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('getAvailableWordLists', () => {
    it('应该返回所有可用词库统计', async () => {
      const mockStats = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          userProgress: null
        },
        {
          id: 2,
          name: 'CET-4',
          description: '大学英语四级词汇',
          difficulty: 4,
          totalWords: 2000,
          isActive: true,
          userProgress: {
            practicedWords: 150,
            masteredWords: 50,
            progressPercentage: 15
          }
        }
      ];

      mockWordListRepo.getStatsWithUserProgress.mockResolvedValue(mockStats);

      const result = await practiceService.getAvailableWordLists('user-123');

      expect(mockWordListRepo.getStatsWithUserProgress).toHaveBeenCalledWith('user-123');
      expect(result).toEqual(mockStats);
    });

    it('应该支持无用户ID的调用', async () => {
      const mockStats = [
        {
          id: 1,
          name: '初中词汇',
          description: '初中英语词汇',
          difficulty: 2,
          totalWords: 1000,
          isActive: true,
          userProgress: null
        }
      ];

      mockWordListRepo.getStatsWithUserProgress.mockResolvedValue(mockStats);

      const result = await practiceService.getAvailableWordLists();

      expect(mockWordListRepo.getStatsWithUserProgress).toHaveBeenCalledWith(undefined);
      expect(result).toEqual(mockStats);
    });
  });

  describe('getWordListById', () => {
    it('应该返回指定ID的词库', async () => {
      const mockWordList = {
        id: 1,
        name: '初中词汇',
        description: '初中英语词汇',
        difficulty: 2,
        totalWords: 1000,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);

      const result = await practiceService.getWordListById(1);

      expect(mockWordListRepo.findById).toHaveBeenCalledWith(1);
      expect(result).toEqual(mockWordList);
    });

    it('应该在词库不存在时返回null', async () => {
      mockWordListRepo.findById.mockResolvedValue(null);

      const result = await practiceService.getWordListById(999);

      expect(result).toBeNull();
    });
  });

  describe('getWordListWords', () => {
    it('应该返回词库的练习单词', async () => {
      const mockWordList = {
        id: 1,
        name: '初中词汇',
        isActive: true
      };
      const mockWords = [
        {
          id: 1,
          word: 'hello',
          phonetics: ['/həˈloʊ/'],
          freqRank: 100,
          userProficiency: null
        },
        {
          id: 2,
          word: 'world',
          phonetics: ['/wɜrld/'],
          freqRank: 200,
          userProficiency: {
            practiceCount: 5,
            proficiencyScore: 0.8
          }
        }
      ];

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.getPracticeWords.mockResolvedValue(mockWords);

      const options = { limit: 20, offset: 0 };
      const result = await practiceService.getWordListWords(1, 'user-123', options);

      expect(mockWordListRepo.findById).toHaveBeenCalledWith(1);
      expect(mockWordListEntryRepo.getPracticeWords).toHaveBeenCalledWith(1, 'user-123', options);
      expect(result).toEqual(mockWords);
    });

    it('应该在词库不存在时抛出NotFoundError', async () => {
      mockWordListRepo.findById.mockResolvedValue(null);

      await expect(
        practiceService.getWordListWords(999, 'user-123', { limit: 20, offset: 0 })
      ).rejects.toThrow(NotFoundError);
    });

    it('应该在词库未激活时抛出ValidationError', async () => {
      const mockWordList = {
        id: 1,
        name: '初中词汇',
        isActive: false
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);

      await expect(
        practiceService.getWordListWords(1, 'user-123', { limit: 20, offset: 0 })
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('getWordListPracticeSession', () => {
    it('应该返回完整的练习会话数据', async () => {
      const mockWordList = {
        id: 1,
        name: '初中词汇',
        isActive: true
      };
      const mockSession = {
        wordListId: 1,
        words: [
          {
            id: 1,
            word: 'hello',
            phonetics: ['/həˈloʊ/'],
            freqRank: 100,
            userProficiency: null
          }
        ],
        sessionConfig: {
          totalWords: 1000,
          isRandomized: false,
          filterDifficult: false,
          skipMastered: false
        }
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.getPracticeSession.mockResolvedValue(mockSession);

      const options = { limit: 20, offset: 0, random: true };
      const result = await practiceService.getWordListPracticeSession(1, 'user-123', options);

      expect(mockWordListRepo.findById).toHaveBeenCalledWith(1);
      expect(mockWordListEntryRepo.getPracticeSession).toHaveBeenCalledWith(1, 'user-123', options);
      expect(result).toEqual(mockSession);
    });
  });

  describe('recordWordListProgress', () => {
    it('应该成功记录练习进度并返回熟练度数据', async () => {
      const mockWordList = { id: 1, name: '初中词汇' };
      const mockWordEntry = {
        word: 'hello',
        wordListIds: [1],
        wordListId: 1
      };
      const mockVocabulary = {
        id: 1,
        word: 'hello',
        phonetics: [],
        freqRank: null
      };
      const mockUserProficiency = {
        userId: 'user-123',
        wordId: 1,
        practiceCount: 2,
        errorCount: 0,
        averageTime: 2000,
        proficiencyScore: 0.3,
        isMarked: false,
        lastPracticed: new Date()
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.findByWord.mockResolvedValue(mockWordEntry);
      mockVocabularyRepo.findByWord.mockResolvedValue(mockVocabulary);
      // 使用更具体的mock设置
      mockUserProficiencyRepo.findByUserAndWord.mockImplementation((userId, wordId) => {
        if (userId === 'user-123' && wordId === 1) {
          return Promise.resolve(mockUserProficiency);
        }
        return Promise.resolve(null);
      });
      mockUserProficiencyRepo.upsert.mockResolvedValue(mockUserProficiency);
      mockUserProficiencyRepo.update.mockResolvedValue(mockUserProficiency);

      const practiceResult = {
        isCorrect: true,
        timeTaken: 2000,
        errorCount: 0,
        sessionType: 'typing' as const
      };

      const result = await practiceService.recordWordListProgress(
        'user-123',
        1,
        'hello',
        practiceResult
      );

      expect(result).toHaveProperty('proficiency');
      expect(result).toHaveProperty('improvement');
      expect(result).toHaveProperty('suggestion');
      expect(result.proficiency.practiceCount).toBe(2);
      expect(result.suggestion).toBe('good_progress');
    });

    it('应该在词库不存在时抛出NotFoundError', async () => {
      mockWordListRepo.findById.mockResolvedValue(null);

      const practiceResult = {
        isCorrect: true,
        timeTaken: 2000
      };

      await expect(
        practiceService.recordWordListProgress('user-123', 999, 'hello', practiceResult)
      ).rejects.toThrow(NotFoundError);
    });

    it('应该在单词不在词库中时抛出ValidationError', async () => {
      const mockWordList = { id: 1, name: '初中词汇' };
      const mockWordEntry = {
        word: 'hello',
        wordListIds: [2], // 不包含词库ID 1
        wordListId: 2
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.findByWord.mockResolvedValue(mockWordEntry);

      const practiceResult = {
        isCorrect: true,
        timeTaken: 2000
      };

      await expect(
        practiceService.recordWordListProgress('user-123', 1, 'hello', practiceResult)
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('addWordListWordToUserWordBook', () => {
    it('应该成功将词库单词加入用户生词本', async () => {
      const mockWordList = { id: 1, name: '初中词汇' };
      const mockWordEntry = {
        word: 'hello',
        wordListIds: [1],
        wordListId: 1
      };
      const mockVocabulary = {
        id: 1,
        word: 'hello',
        phonetics: [],
        freqRank: null
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.findByWord.mockResolvedValue(mockWordEntry);
      mockVocabularyRepo.findByWord.mockResolvedValue(mockVocabulary);
      mockUserProficiencyRepo.findByUserAndWord.mockResolvedValue(null);
      mockUserProficiencyRepo.create.mockResolvedValue({
        userId: 'user-123',
        wordId: 1,
        practiceCount: 0,
        errorCount: 0,
        averageTime: 0,
        proficiencyScore: 0.0,
        isMarked: true,
        lastPracticed: undefined
      });

      const result = await practiceService.addWordListWordToUserWordBook('user-123', 1, 'hello');

      expect(result).toBe(true);
      expect(mockUserProficiencyRepo.create).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: 'user-123',
          wordId: 1,
          isMarked: true
        })
      );
    });

    it('应该更新已存在的用户熟练度记录', async () => {
      const mockWordList = { id: 1, name: '初中词汇' };
      const mockWordEntry = {
        word: 'hello',
        wordListIds: [1],
        wordListId: 1
      };
      const mockVocabulary = {
        id: 1,
        word: 'hello',
        phonetics: [],
        freqRank: null
      };
      const existingProficiency = {
        userId: 'user-123',
        wordId: 1,
        practiceCount: 5,
        errorCount: 1,
        averageTime: 2500,
        proficiencyScore: 0.7,
        isMarked: false,
        lastPracticed: new Date()
      };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.findByWord.mockResolvedValue(mockWordEntry);
      mockVocabularyRepo.findByWord.mockResolvedValue(mockVocabulary);
      mockUserProficiencyRepo.findByUserAndWord.mockResolvedValue(existingProficiency);
      // 使用update方法而不是upsert，因为实际代码中使用的是update
      mockUserProficiencyRepo.update.mockResolvedValue({
        userId: 'user-123',
        wordId: 1,
        practiceCount: 5,
        errorCount: 1,
        averageTime: 2500,
        proficiencyScore: 0.7,
        isMarked: true,
        lastPracticed: new Date()
      });

      const result = await practiceService.addWordListWordToUserWordBook('user-123', 1, 'hello');

      expect(result).toBe(true);
      // 检查update方法的调用，传递的是userId、wordId和更新数据
      expect(mockUserProficiencyRepo.update).toHaveBeenCalledWith('user-123', 1, {
        isMarked: true
      });
    });
  });

  describe('getUserWordListProgress', () => {
    it('应该返回用户在词库中的详细进度统计', async () => {
      const mockWordList = { id: 1, name: '初中词汇' };
      const mockWordEntries = [
        { word: 'hello' },
        { word: 'world' },
        { word: 'test' }
      ];
      const mockVocabularies = [
        { id: 1, word: 'hello' },
        { id: 2, word: 'world' },
        { id: 3, word: 'test' }
      ];
      const mockUserProficiencies = [
        { userId: 'user-123', wordId: 1, proficiencyScore: 0.8 },
        { userId: 'user-123', wordId: 2, proficiencyScore: 0.6 }
      ];

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.findMany.mockResolvedValue(mockWordEntries);
      mockVocabularyRepo.findManyByWords.mockResolvedValue(mockVocabularies);

      // Mock prisma直接调用
      const { prisma } = await import('@/lib/4-infrastructure/database/prisma');
      (prisma.userWordProficiency.findMany as any).mockResolvedValue(mockUserProficiencies);

      const result = await practiceService.getUserWordListProgress('user-123', 1);

      expect(result).toEqual({
        totalWords: 3,
        practicedWords: 2,
        masteredWords: 1, // proficiencyScore >= 0.7
        progressPercentage: 67, // Math.round((2/3) * 100)
        averageProficiency: 0.7 // (0.8 + 0.6) / 2
      });
    });

    it('应该处理空词库的情况', async () => {
      const mockWordList = { id: 1, name: '空词库' };

      mockWordListRepo.findById.mockResolvedValue(mockWordList);
      mockWordListEntryRepo.findMany.mockResolvedValue([]);

      const result = await practiceService.getUserWordListProgress('user-123', 1);

      expect(result).toEqual({
        totalWords: 0,
        practicedWords: 0,
        masteredWords: 0,
        progressPercentage: 0,
        averageProficiency: 0
      });
    });

    it('应该在词库不存在时抛出NotFoundError', async () => {
      mockWordListRepo.findById.mockResolvedValue(null);

      await expect(
        practiceService.getUserWordListProgress('user-123', 999)
      ).rejects.toThrow(NotFoundError);
    });
  });
});