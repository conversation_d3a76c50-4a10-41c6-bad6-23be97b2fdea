/**
 * @fileoverview Next.js应用程序配置文件
 *
 * 本文件配置Next.js应用的构建和运行时行为，主要包括：
 * - 独立输出模式配置（Docker支持）
 * - CSS优化实验性功能
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import type { NextConfig } from 'next';

/**
 * Next.js应用程序主配置对象
 *
 * 该配置针对生产环境优化，支持容器化部署：
 *
 * **输出配置**：
 * - `standalone`: 生成包含所有依赖的独立可执行文件，适用于Docker容器
 *
 * **实验性功能**：
 * - `optimizeCss`: 启用styled-jsx的CSS优化，减少包大小和提升性能
 *
 * **依赖关系**：
 * - 导入: NextConfig类型定义
 * - 导出: 作为默认配置被Next.js构建系统使用
 *
 * **性能影响**：
 * - 独立模式减少部署复杂度
 * - CSS优化提升页面加载速度
 *
 * @complexity O(1) - 静态配置对象
 */
const nextConfig: NextConfig = {
  output: 'standalone',
  // Enable Docker support
  experimental: {
    // Enables the styled-jsx in CSS optimization
    optimizeCss: true,
  },
  async redirects() {
    return [
      {
        source: '/home/<USER>',
        destination: '/home/<USER>/typing-test',
        permanent: true,
      },
    ];
  },
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
          },
        ],
      },
      // API routes CORS headers (fallback for routes not handled by middleware)
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token',
          },
          {
            key: 'Access-Control-Max-Age',
            value: '86400',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
