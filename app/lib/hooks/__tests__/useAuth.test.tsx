/**
 * @fileoverview useAuth Hook测试套件
 *
 * 测试认证Hook的功能，包括：
 * - Hook基础功能
 * - 与AuthProvider的集成
 * - 类型安全性
 * - 错误处理
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useAuth } from '../useAuth';
import { AuthProvider } from '@/components/providers/AuthProvider';
import { createMockUser, testScenarios } from '@/test/utils/auth-test-utils';
import React from 'react';

// Mock dependencies
const mockSecureTokenStorage = {
  getAuthStatus: vi.fn(),
  setTokens: vi.fn(),
  clearTokens: vi.fn(),
  refreshToken: vi.fn(),
};

const mockAutoTokenManager = {
  start: vi.fn(),
  stop: vi.fn(),
};

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
};

const mockToast = {
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
};

// Mock modules before any imports that use them
vi.mock('@/lib/utils/secure-auth', () => ({
  SecureTokenStorage: mockSecureTokenStorage,
  AutoTokenManager: mockAutoTokenManager,
}));

vi.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
}));

vi.mock('sonner', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

describe('useAuth Hook', () => {
  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();

    // Default mock implementations
    mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
      isAuthenticated: false,
      user: null,
      hasValidRefreshToken: false,
    });
    mockSecureTokenStorage.setTokens.mockResolvedValue(true);
    mockSecureTokenStorage.clearTokens.mockResolvedValue(undefined);
    mockSecureTokenStorage.refreshToken.mockResolvedValue(true);
  });

  describe('Hook基础功能', () => {
    it('应该返回AuthContext的所有属性', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Check all required properties are present
      expect(result.current).toHaveProperty('user');
      expect(result.current).toHaveProperty('loading');
      expect(result.current).toHaveProperty('error');
      expect(result.current).toHaveProperty('isAuthenticated');
      expect(result.current).toHaveProperty('isLoading');
      expect(result.current).toHaveProperty('login');
      expect(result.current).toHaveProperty('signup');
      expect(result.current).toHaveProperty('loginWithOAuth');
      expect(result.current).toHaveProperty('logout');
      expect(result.current).toHaveProperty('refreshToken');
    });

    it('应该返回正确的类型', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Check types
      expect(typeof result.current.isAuthenticated).toBe('boolean');
      expect(typeof result.current.isLoading).toBe('boolean');
      expect(typeof result.current.loading).toBe('boolean');
      expect(typeof result.current.login).toBe('function');
      expect(typeof result.current.signup).toBe('function');
      expect(typeof result.current.loginWithOAuth).toBe('function');
      expect(typeof result.current.logout).toBe('function');
      expect(typeof result.current.refreshToken).toBe('function');
    });
  });

  describe('未认证状态', () => {
    it('应该在未认证时返回正确的状态', async () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Wait for initialization to complete
      await vi.waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.user).toBe(null);
      expect(result.current.error).toBe(null);
    });
  });

  describe('已认证状态', () => {
    it('应该在已认证时返回正确的状态', async () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      await vi.waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.user).toEqual(testUser);
      expect(result.current.error).toBe(null);
    });
  });

  describe('加载状态', () => {
    it('应该在初始化时显示加载状态', () => {
      // Mock a slow initialization
      mockSecureTokenStorage.getAuthStatus.mockImplementation(
        () =>
          new Promise((resolve) =>
            setTimeout(
              () =>
                resolve({
                  isAuthenticated: false,
                  user: null,
                  hasValidRefreshToken: false,
                }),
              100
            )
          )
      );

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      expect(result.current.loading).toBe(true);
      expect(result.current.isLoading).toBe(true);
    });
  });

  describe('错误状态', () => {
    it('应该在发生错误时返回错误信息', async () => {
      mockSecureTokenStorage.getAuthStatus.mockRejectedValue(new Error('Storage error'));

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      await vi.waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      // After storage error, should clear tokens and show unauthenticated state
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockSecureTokenStorage.clearTokens).toHaveBeenCalled();
    });
  });

  describe('Hook方法调用', () => {
    it('应该正确调用login方法', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({
          accessToken: 'test-token',
          refreshToken: 'test-refresh-token',
          user: createMockUser(),
          userId: 'test-user-id',
        }),
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      await vi.waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const credentials = { email: '<EMAIL>', password: 'password123' };

      // Call login method
      await result.current.login(credentials);

      expect(global.fetch).toHaveBeenCalledWith('/api/auth/token/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(credentials),
      });
    });

    it('应该正确调用signup方法', async () => {
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 201,
        json: vi.fn().mockResolvedValue({
          accessToken: 'test-token',
          refreshToken: 'test-refresh-token',
          user: createMockUser(),
          userId: 'test-user-id',
        }),
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      await vi.waitFor(() => {
        expect(result.current.loading).toBe(false);
      });

      const credentials = {
        email: '<EMAIL>',
        password: 'password123',
        confirmPassword: 'password123',
        name: 'Test User',
      };

      await result.current.signup(credentials);

      expect(global.fetch).toHaveBeenCalledWith('/api/auth/token/signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: credentials.email,
          password: credentials.password,
          name: credentials.name,
        }),
      });
    });

    it('应该正确调用logout方法', async () => {
      const testUser = createMockUser();

      // Start with authenticated user
      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({ message: 'Logged out successfully' }),
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      await vi.waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
      });

      await result.current.logout();

      expect(global.fetch).toHaveBeenCalledWith('/api/auth/secure-token', {
        method: 'DELETE',
        credentials: 'include',
      });
      expect(mockSecureTokenStorage.clearTokens).toHaveBeenCalled();
      expect(mockAutoTokenManager.stop).toHaveBeenCalled();
    });

    it('应该正确调用refreshToken方法', async () => {
      const testUser = createMockUser();

      mockSecureTokenStorage.getAuthStatus.mockResolvedValue({
        isAuthenticated: true,
        user: testUser,
        hasValidRefreshToken: true,
      });

      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      await vi.waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true);
      });

      const refreshResult = await result.current.refreshToken();

      expect(mockSecureTokenStorage.refreshToken).toHaveBeenCalled();
      expect(refreshResult).toBe('refreshed');
    });
  });

  describe('Hook在Provider外使用', () => {
    it('应该在Provider外使用时抛出错误', () => {
      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        renderHook(() => useAuth());
      }).toThrow('useAuthContext must be used within an AuthProvider');

      consoleSpy.mockRestore();
    });
  });

  describe('类型导出', () => {
    it('应该正确导出User类型', () => {
      // This is a compile-time check, but we can verify the import works
      expect(() => {
        const { User, AuthContextType } = require('../useAuth');
        // If import works without error, types are properly exported
      }).not.toThrow();
    });
  });

  describe('向后兼容性', () => {
    it('应该提供与旧版本API兼容的接口', () => {
      const wrapper = ({ children }: { children: React.ReactNode }) => (
        <AuthProvider>{children}</AuthProvider>
      );

      const { result } = renderHook(() => useAuth(), { wrapper });

      // Verify all expected methods are available
      const expectedMethods = ['login', 'signup', 'loginWithOAuth', 'logout', 'refreshToken'];
      expectedMethods.forEach((method) => {
        expect(typeof result.current[method as keyof typeof result.current]).toBe('function');
      });

      // Verify all expected properties are available
      const expectedProperties = ['user', 'loading', 'error', 'isAuthenticated', 'isLoading'];
      expectedProperties.forEach((prop) => {
        expect(result.current).toHaveProperty(prop);
      });
    });
  });
});
