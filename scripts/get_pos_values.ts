import pkg from '@prisma/client';
const { PrismaClient } = pkg;
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const prisma = new PrismaClient();

async function main() {
  try {
    const distinctPosValues = await prisma.explain.findMany({
      select: {
        pos: true,
      },
      distinct: ['pos'],
    });

    const posValues = distinctPosValues.map((item) => item.pos);

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = path.dirname(__filename);
    const outputFilePath = path.join(__dirname, 'pos_values.md');
    const markdownContent = posValues.join('\n');

    fs.writeFileSync(outputFilePath, markdownContent);

    console.log(`Successfully wrote distinct pos values to ${outputFilePath}`);
  } catch (error) {
    console.error('Error fetching distinct pos values:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
