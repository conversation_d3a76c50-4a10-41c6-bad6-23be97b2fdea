# Story 1.4: 个性化句子练习API实现

## Status

📝 **Draft** - 2025-07-30

## Story

**As a** 打字练习系统用户,
**I want** 能够收藏自己喜欢的句子并专门练习这些收藏的句子,
**so that** 我可以针对个人感兴趣的内容进行反复练习，提高学习效果和打字乐趣。

## Acceptance Criteria

1. 实现 `GET /api/practice/sentences` 接口，获取一条随机练习句子（来自系统句库或用户收藏）
2. 实现 `POST /api/practice/sentences/favorites` 接口，允许用户收藏当前练习的句子
3. 实现 `DELETE /api/practice/sentences/favorites/:sentenceId` 接口，允许用户取消收藏句子
4. 实现 `GET /api/practice/sentences/favorites` 接口，获取用户的所有收藏句子列表
5. 系统应建立用户与句子的多对多收藏关系，支持持久化存储
6. 收藏句子练习模式应支持只练习用户收藏过的句子
7. 所有接口都应包含适当的错误处理和数据验证
8. API响应格式应遵循项目的标准RESTful设计规范
9. 接口应整合现有的用户认证系统（Session + JWT双认证策略）
10. 系统应提供一个中央句库，包含高质量的练习句子

## Tasks / Subtasks

- [ ] 句子数据模型和数据库设计 (AC: 5, 10)
  - [ ] 设计 `Sentence` 数据模型，包含句子内容、来源、收藏计数等字段
  - [ ] 设计 `UserSavedSentence` 关联表，建立用户与句子的多对多收藏关系
  - [ ] 创建数据库migration添加新的句子相关表
  - [ ] 设计句子数据结构，支持内容、分类、难度等属性
  - [ ] 实现句子数据初始化脚本，导入初始的中央句库数据

- [ ] 句子练习API控制器实现 (AC: 1, 2, 3, 4, 7, 8, 9)
  - [ ] 创建 `app/api/practice/sentences/route.ts` 获取练习句子
  - [ ] 创建 `app/api/practice/sentences/favorites/route.ts` 管理收藏句子
  - [ ] 创建 `app/api/practice/sentences/favorites/[sentenceId]/route.ts` 取消收藏
  - [ ] 实现用户认证集成（复用现有的requireAuth装饰器）
  - [ ] 添加输入数据验证（Zod schema）和完整错误处理策略
  - [ ] 实现智能句子选择逻辑（收藏优先、难度适配、避免重复）

- [ ] Service层业务逻辑实现 (AC: 1, 2, 3, 6, 10)
  - [ ] 扩展 `PracticeService` 添加句子练习相关方法
  - [ ] 实现句子收藏管理业务逻辑（添加、移除、查询）
  - [ ] 实现收藏句子专项练习模式
  - [ ] 实现智能句子推荐算法（基于用户收藏历史和练习偏好）
  - [ ] 集成中央句库管理功能

- [ ] Repository层数据访问 (AC: 4, 5, 10)
  - [ ] 创建 `SentenceRepository` 处理句子数据CRUD操作
  - [ ] 创建 `UserSavedSentenceRepository` 处理用户收藏关联查询
  - [ ] 实现高效的句子查询和分页逻辑
  - [ ] 实现收藏句子的快速检索和过滤功能
  - [ ] 优化句子数据查询性能（数据库索引、缓存策略）

- [ ] 单元测试和集成测试 (AC: 1-10)
  - [ ] 为API路由编写单元测试
  - [ ] 为Service方法编写单元测试
  - [ ] 为Repository层编写单元测试
  - [ ] 编写API集成测试，验证完整的请求-响应流程
  - [ ] 测试句子收藏和取消收藏逻辑
  - [ ] 测试认证集成和权限控制
  - [ ] 测试句子练习模式的智能选择算法

## Dev Notes

### Previous Story Insights

从故事1.1、1.2、1.3学到的关键经验：

- UserWordProficiency表和相关业务逻辑已建立，可作为设计参考
- 双认证策略（Session + JWT）已就绪，可直接复用认证装饰器
- PracticeService业务服务层已建立，可以扩展句子练习逻辑
- 完整的测试框架和模式已确立，可以复用测试模式
- DDD分层架构已成熟，严格遵循Domain-Repository-Service-API模式

### Data Models

需要新增的数据模型：

**`Sentence` 句子表**

- `id` - 句子唯一标识
- `content` - 句子内容（英文文本）
- `source` - 句子来源（书籍、文章、用户创建等）
- `difficulty` - 难度级别 (1-10)
- `category` - 句子分类（商务、文学、日常等）
- `favoriteCount` - 被收藏次数（用于流行度统计）
- `length` - 句子字符长度（用于练习难度评估）
- `isActive` - 是否启用
- `createdAt` - 创建时间

[Source: docs/打字机功能/architecture/4-数据模型与变更-data-models-and-schema-changes.md]

**`UserSavedSentence` 用户收藏句子关联表**

- `id` - 关联记录唯一标识
- `userId` - 用户ID（外键关联User表）
- `sentenceId` - 句子ID（外键关联Sentence表）
- `savedAt` - 收藏时间
- `practiceCount` - 练习次数（用于个性化推荐）
- `lastPracticedAt` - 最后练习时间

[Source: docs/打字机功能/architecture/4-数据模型与变更-data-models-and-schema-changes.md]

**数据关系设计**：
- User ←→ UserSavedSentence ←→ Sentence (多对多关系)
- 支持用户收藏多个句子，一个句子可被多个用户收藏
- 包含收藏时间和练习统计，便于个性化推荐

### API Specifications

需要实现的API端点：

**句子练习API:**

- `GET /api/practice/sentences` - 获取练习句子
  - Query参数: `mode` (random|favorites), `difficulty`, `category`
  - 返回句子详细信息，包含用户收藏状态
  - 支持智能选择（收藏优先、避免重复、难度适配）
- `POST /api/practice/sentences/favorites` - 收藏句子
  - Body: `{ sentenceId, practiceContext }` 
  - 创建用户与句子的收藏关联
  - 更新句子的收藏统计
- `DELETE /api/practice/sentences/favorites/:sentenceId` - 取消收藏
  - 删除用户与句子的收藏关联
  - 更新句子的收藏统计
- `GET /api/practice/sentences/favorites` - 获取收藏句子列表
  - Query参数: `page`, `limit`, `sortBy` (savedAt|practiceCount)
  - 返回用户的所有收藏句子及练习统计

[Source: docs/打字机功能/architecture/5-核心api接口设计-core-api-interface-design.md#句子练习API]

### Component Specifications

**PracticeService 扩展** （基于架构设计）

需要添加的方法：
- `getPracticeSentence(userId, options)` - 获取练习句子（支持收藏优先模式）
- `saveSentenceToFavorites(userId, sentenceId)` - 收藏句子
- `removeSentenceFromFavorites(userId, sentenceId)` - 取消收藏句子
- `getUserFavoriteSentences(userId, options)` - 获取用户收藏句子列表
- `getSentenceRecommendations(userId)` - 基于用户历史的句子推荐

[Source: docs/打字机功能/architecture/3-组件架构-component-architecture.md]

### File Locations

基于项目DDD架构，相关文件位置：

- API路由: 
  - `app/api/practice/sentences/route.ts`
  - `app/api/practice/sentences/favorites/route.ts`
  - `app/api/practice/sentences/favorites/[sentenceId]/route.ts`
- Service扩展: `app/lib/1-services/practice/PracticeService.ts`
- Repository: 
  - `app/lib/2-repositories/SentenceRepository.ts` (新增)
  - `app/lib/2-repositories/UserSavedSentenceRepository.ts` (新增)
- Domain实体: 
  - `app/lib/3-domain/entities/Sentence.ts` (新增)
  - `app/lib/3-domain/entities/UserSavedSentence.ts` (新增)
- 数据库Schema: `prisma/schema.prisma`
- 数据初始化脚本: `app/lib/6-scripts/import-sentences-data.ts`
- 类型定义: `app/lib/types/sentence-types.ts`
- 测试文件: 
  - `app/api/practice/sentences/__tests__/route.test.ts`
  - `app/api/practice/sentences/favorites/__tests__/route.test.ts`
  - `app/lib/1-services/practice/__tests__/PracticeService.sentence.test.ts`

[Source: CLAUDE.md#Domain-Driven Design (DDD) Structure]

### Testing Requirements

- 使用 **Vitest** 框架进行单元和集成测试
- 测试文件命名: `**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}`
- 路径别名: `@` 指向 `./app` 目录
- 测试超时设置: 180000秒（5分钟）

[Source: CLAUDE.md#Testing Framework]

### User Authentication Implementation

复用现有的双认证策略：

- **Web应用**: Session Cookie认证（NextAuth.js）
- **移动端/扩展**: JWT Bearer Token认证
- **认证装饰器**: 使用现有的 `requireAuth()` 装饰器
- **统一用户ID获取**: 通过 `context.user.id` 获取当前认证用户

[Source: 从故事1.2和1.3的实现经验]

### Performance Requirements

**API响应时间目标**:
- 句子获取查询: < 300ms
- 收藏操作: < 200ms  
- 收藏列表查询: < 400ms

**数据处理性能**:
- 句子查询支持: 1000+句子库高效检索
- 并发支持: 100个同时API请求
- 数据库查询优化: 为content、userId、sentenceId字段添加索引

### Technical Constraints

- 使用 **Next.js 15 App Router** 进行API路由实现
- 数据库操作通过 **Prisma ORM**
- 数据库: **PostgreSQL 16**
- 用户认证: 复用现有的 `requireAuth()` 装饰器
- 输入验证使用 **Zod** 库
- 错误处理遵循项目统一标准
- 严格的TypeScript配置
- 遵循DDD分层架构原则

[Source: CLAUDE.md#Technology Stack]

### Testing

#### Test file location

- `app/api/practice/sentences/__tests__/route.test.ts`
- `app/api/practice/sentences/favorites/__tests__/route.test.ts`
- `app/lib/1-services/practice/__tests__/PracticeService.sentence.test.ts`
- `app/lib/2-repositories/__tests__/SentenceRepository.test.ts`
- `app/lib/2-repositories/__tests__/UserSavedSentenceRepository.test.ts`

#### Test standards

- 使用Vitest框架
- 包含单元测试和集成测试
- 测试API路由的完整请求-响应周期
- Mock数据库和外部依赖
- 测试句子收藏和取消收藏逻辑

#### Testing frameworks and patterns to use

- Vitest for unit testing
- 为API测试创建测试数据库
- 使用 `@/lib` 路径别名
- Mock Prisma客户端进行单元测试
- 集成测试使用真实数据库连接

#### Specific testing requirements for this story

- 测试句子获取API的正确性（返回格式、数据完整性）
- 测试句子收藏和取消收藏功能的完整流程
- 测试收藏句子列表API的分页和排序功能
- 测试智能句子选择算法（收藏优先、避免重复）
- 测试认证集成（Session + JWT双认证策略）
- 测试多对多关联查询功能（用户-句子收藏关系）
- 测试API错误处理和状态码返回
- 测试输入验证和数据清理
- 测试用户数据隔离（确保用户只能访问自己的收藏数据）
- 测试句子练习模式切换（随机模式 vs 收藏模式）
- 测试并发收藏操作的数据一致性
- 测试句子推荐算法的个性化效果

## Change Log

| Date       | Version | Description                                                | Author              |
| ---------- | ------- | ---------------------------------------------------------- | ------------------- |
| 2025-07-30 | 1.0     | Initial story creation for personalized sentence practice  | Scrum Master (Bob)  |

## Dev Agent Record

### Agent Model Used

[To be filled by Dev Agent]

### Debug Log References

[To be filled by Dev Agent]

### Completion Notes List

[To be filled by Dev Agent]

### File List

[To be filled by Dev Agent]

## QA Results

[To be filled by QA Agent]