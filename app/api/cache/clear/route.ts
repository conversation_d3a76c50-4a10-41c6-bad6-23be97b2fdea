import { NextRequest, NextResponse } from 'next/server';
import { CacheRepository } from '../../../lib/2-repositories/CacheRepository';
import { redis, CacheKeys } from '../../../lib/4-infrastructure/cache/redis';

/**
 * 缓存清理API
 * POST /api/cache/clear
 *
 * 支持的清理操作：
 * - 清空所有缓存
 * - 按模式清理特定类型的缓存
 * - 清理单个缓存键
 */

interface ClearCacheRequest {
  action: 'all' | 'pattern' | 'key';
  pattern?: string;
  key?: string;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const body: ClearCacheRequest = await request.json();
    const { action, pattern, key } = body;

    // 验证请求参数
    if (!action || !['all', 'pattern', 'key'].includes(action)) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid action. Must be one of: all, pattern, key',
          validActions: ['all', 'pattern', 'key'],
        },
        { status: 400 }
      );
    }

    const cacheRepo = new CacheRepository();
    let result: any = {};

    switch (action) {
      case 'all':
        result = await clearAllCache(cacheRepo);
        break;

      case 'pattern':
        if (!pattern) {
          return NextResponse.json(
            {
              success: false,
              message: 'Pattern is required for pattern-based clearing',
            },
            { status: 400 }
          );
        }
        result = await clearCacheByPattern(pattern);
        break;

      case 'key':
        if (!key) {
          return NextResponse.json(
            {
              success: false,
              message: 'Key is required for key-based clearing',
            },
            { status: 400 }
          );
        }
        result = await clearCacheByKey(cacheRepo, key);
        break;

      default:
        return NextResponse.json(
          {
            success: false,
            message: 'Unsupported action',
          },
          { status: 400 }
        );
    }

    const processingTime = Date.now() - startTime;

    return NextResponse.json(
      {
        success: true,
        message: 'Cache cleared successfully',
        timestamp: new Date().toISOString(),
        processingTime: `${processingTime}ms`,
        action,
        result,
      },
      { status: 200 }
    );
  } catch (error) {
    const processingTime = Date.now() - startTime;

    console.error('Cache clear failed:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'Failed to clear cache',
        timestamp: new Date().toISOString(),
        processingTime: `${processingTime}ms`,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * 清空所有缓存
 */
async function clearAllCache(cacheRepo: CacheRepository): Promise<any> {
  const cleared = await cacheRepo.clear();
  return {
    type: 'all',
    cleared,
    message: cleared ? 'All cache cleared' : 'Failed to clear cache',
  };
}

/**
 * 按模式清理缓存
 */
async function clearCacheByPattern(pattern: string): Promise<any> {
  try {
    const keys = await redis.keys(pattern);
    let deletedCount = 0;

    if (keys.length > 0) {
      deletedCount = await redis.del(...keys);
    }

    return {
      type: 'pattern',
      pattern,
      keysFound: keys.length,
      keysDeleted: deletedCount,
      message: `Cleared ${deletedCount} keys matching pattern: ${pattern}`,
    };
  } catch (error) {
    throw new Error(`Failed to clear cache by pattern: ${error}`);
  }
}

/**
 * 清理单个缓存键
 */
async function clearCacheByKey(cacheRepo: CacheRepository, key: string): Promise<any> {
  const deleted = await cacheRepo.delete(key);
  return {
    type: 'key',
    key,
    deleted,
    message: deleted ? `Key '${key}' cleared` : `Key '${key}' not found or failed to clear`,
  };
}

/**
 * GET方法：返回可用的清理选项
 */
export async function GET() {
  return NextResponse.json(
    {
      success: true,
      message: 'Cache clear API information',
      usage: {
        method: 'POST',
        endpoint: '/api/cache/clear',
        actions: {
          all: {
            description: 'Clear all cache entries',
            body: { action: 'all' },
          },
          pattern: {
            description: 'Clear cache entries matching a pattern',
            body: { action: 'pattern', pattern: 'search:*' },
            availablePatterns: ['vocab:*', 'forms:*', 'search:*', 'suggest:*', 'stats:*'],
          },
          key: {
            description: 'Clear a specific cache key',
            body: { action: 'key', key: 'search:example' },
          },
        },
      },
    },
    { status: 200 }
  );
}

/**
 * 处理不支持的HTTP方法
 */
export async function PUT() {
  return NextResponse.json({ success: false, message: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ success: false, message: 'Method not allowed' }, { status: 405 });
}

export async function PATCH() {
  return NextResponse.json({ success: false, message: 'Method not allowed' }, { status: 405 });
}
