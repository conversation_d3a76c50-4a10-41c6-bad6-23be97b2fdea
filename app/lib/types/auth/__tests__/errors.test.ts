/**
 * Authentication Error Types Unit Tests
 * 认证错误类型单元测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  AuthError,
  ValidationError,
  AuthApiError,
  RateLimitError,
  TokenError,
  ValidationErrorResponse,
  AuthErrorCode,
  AUTH_ERROR_MESSAGES,
  AuthenticationError,
} from '../errors';

describe('Authentication Error Types', () => {
  describe('AuthError', () => {
    it('should have required fields', () => {
      const error: AuthError = {
        code: 'AUTH_001',
        message: 'Authentication failed',
        timestamp: new Date().toISOString(),
      };

      expect(error.code).toBe('AUTH_001');
      expect(error.message).toBe('Authentication failed');
      expect(error.timestamp).toBeDefined();
    });

    it('should support optional details field', () => {
      const error: AuthError = {
        code: 'AUTH_001',
        message: 'Authentication failed',
        timestamp: new Date().toISOString(),
        details: { reason: 'invalid_credentials' },
      };

      expect(error.details).toEqual({ reason: 'invalid_credentials' });
    });
  });

  describe('ValidationError', () => {
    it('should extend AuthError with validation specific fields', () => {
      const error: ValidationError = {
        code: 'VALIDATION_ERROR',
        message: 'Email format is invalid',
        timestamp: new Date().toISOString(),
        field: 'email',
        rule: 'email_format',
      };

      expect(error.field).toBe('email');
      expect(error.rule).toBe('email_format');
      expect(error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('AuthApiError', () => {
    it('should have required fields', () => {
      const error: AuthApiError = {
        error: true,
        message: 'Authentication failed',
        code: 'INVALID_CREDENTIALS',
        timestamp: new Date().toISOString(),
      };

      expect(error.error).toBe(true);
      expect(error.message).toBe('Authentication failed');
      expect(error.code).toBe('INVALID_CREDENTIALS');
      expect(error.timestamp).toBeDefined();
    });

    it('should support optional details field', () => {
      const error: AuthApiError = {
        error: true,
        message: 'Authentication failed',
        code: 'INVALID_CREDENTIALS',
        timestamp: new Date().toISOString(),
        details: { attempts: 3 },
      };

      expect(error.details).toEqual({ attempts: 3 });
    });
  });

  describe('RateLimitError', () => {
    it('should extend AuthApiError with rate limit specific fields', () => {
      const error: RateLimitError = {
        error: true,
        message: 'Too many requests',
        code: 'RATE_LIMIT_EXCEEDED',
        timestamp: new Date().toISOString(),
        retryAfter: 60,
        remainingAttempts: 2,
      };

      expect(error.retryAfter).toBe(60);
      expect(error.remainingAttempts).toBe(2);
    });
  });

  describe('TokenError', () => {
    it('should extend AuthApiError with token specific fields', () => {
      const error: TokenError = {
        error: true,
        message: 'Token expired',
        code: 'TOKEN_EXPIRED',
        timestamp: new Date().toISOString(),
        tokenType: 'access',
        expiredAt: new Date().toISOString(),
      };

      expect(error.tokenType).toBe('access');
      expect(error.expiredAt).toBeDefined();
    });

    it('should support refresh token type', () => {
      const error: TokenError = {
        error: true,
        message: 'Refresh token invalid',
        code: 'REFRESH_TOKEN_INVALID',
        timestamp: new Date().toISOString(),
        tokenType: 'refresh',
      };

      expect(error.tokenType).toBe('refresh');
    });
  });

  describe('ValidationErrorResponse', () => {
    it('should extend AuthApiError with validation errors array', () => {
      const validationError: ValidationError = {
        code: 'EMAIL_INVALID',
        message: 'Email format is invalid',
        timestamp: new Date().toISOString(),
        field: 'email',
        rule: 'email_format',
      };

      const response: ValidationErrorResponse = {
        error: true,
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        timestamp: new Date().toISOString(),
        errors: [validationError],
      };

      expect(response.errors).toHaveLength(1);
      expect(response.errors[0]).toEqual(validationError);
    });
  });
});

describe('AuthErrorCode', () => {
  it('should have all required error codes', () => {
    expect(AuthErrorCode.INVALID_CREDENTIALS).toBe('INVALID_CREDENTIALS');
    expect(AuthErrorCode.ACCOUNT_DISABLED).toBe('ACCOUNT_DISABLED');
    expect(AuthErrorCode.ACCOUNT_LOCKED).toBe('ACCOUNT_LOCKED');
    expect(AuthErrorCode.TOKEN_EXPIRED).toBe('TOKEN_EXPIRED');
    expect(AuthErrorCode.TOKEN_INVALID).toBe('TOKEN_INVALID');
    expect(AuthErrorCode.TOKEN_REVOKED).toBe('TOKEN_REVOKED');
    expect(AuthErrorCode.REFRESH_TOKEN_EXPIRED).toBe('REFRESH_TOKEN_EXPIRED');
    expect(AuthErrorCode.REFRESH_TOKEN_INVALID).toBe('REFRESH_TOKEN_INVALID');
    expect(AuthErrorCode.VALIDATION_ERROR).toBe('VALIDATION_ERROR');
    expect(AuthErrorCode.EMAIL_INVALID).toBe('EMAIL_INVALID');
    expect(AuthErrorCode.PASSWORD_WEAK).toBe('PASSWORD_WEAK');
    expect(AuthErrorCode.EMAIL_ALREADY_EXISTS).toBe('EMAIL_ALREADY_EXISTS');
    expect(AuthErrorCode.RATE_LIMIT_EXCEEDED).toBe('RATE_LIMIT_EXCEEDED');
    expect(AuthErrorCode.TOO_MANY_REQUESTS).toBe('TOO_MANY_REQUESTS');
    expect(AuthErrorCode.INSUFFICIENT_PERMISSIONS).toBe('INSUFFICIENT_PERMISSIONS');
    expect(AuthErrorCode.FORBIDDEN).toBe('FORBIDDEN');
    expect(AuthErrorCode.INTERNAL_SERVER_ERROR).toBe('INTERNAL_SERVER_ERROR');
    expect(AuthErrorCode.SERVICE_UNAVAILABLE).toBe('SERVICE_UNAVAILABLE');
    expect(AuthErrorCode.OAUTH_ERROR).toBe('OAUTH_ERROR');
    expect(AuthErrorCode.PROVIDER_ERROR).toBe('PROVIDER_ERROR');
  });
});

describe('AUTH_ERROR_MESSAGES', () => {
  it('should have Chinese messages for all error codes', () => {
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.INVALID_CREDENTIALS]).toBe('邮箱或密码错误');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.ACCOUNT_DISABLED]).toBe('账户已被禁用');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.ACCOUNT_LOCKED]).toBe('账户已被锁定');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.TOKEN_EXPIRED]).toBe('访问令牌已过期');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.TOKEN_INVALID]).toBe('访问令牌无效');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.TOKEN_REVOKED]).toBe('访问令牌已被撤销');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.REFRESH_TOKEN_EXPIRED]).toBe('刷新令牌已过期');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.REFRESH_TOKEN_INVALID]).toBe('刷新令牌无效');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.VALIDATION_ERROR]).toBe('数据验证失败');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.EMAIL_INVALID]).toBe('邮箱格式无效');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.PASSWORD_WEAK]).toBe('密码强度不够');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.EMAIL_ALREADY_EXISTS]).toBe('邮箱已存在');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.RATE_LIMIT_EXCEEDED]).toBe('请求过于频繁');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.TOO_MANY_REQUESTS]).toBe('请求次数过多');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.INSUFFICIENT_PERMISSIONS]).toBe('权限不足');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.FORBIDDEN]).toBe('禁止访问');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.INTERNAL_SERVER_ERROR]).toBe('服务器内部错误');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.SERVICE_UNAVAILABLE]).toBe('服务暂时不可用');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.OAUTH_ERROR]).toBe('第三方认证错误');
    expect(AUTH_ERROR_MESSAGES[AuthErrorCode.PROVIDER_ERROR]).toBe('认证提供商错误');
  });

  it('should have messages for all error codes', () => {
    Object.values(AuthErrorCode).forEach((code) => {
      expect(AUTH_ERROR_MESSAGES[code]).toBeDefined();
      expect(AUTH_ERROR_MESSAGES[code]).toBeTruthy();
    });
  });
});

describe('AuthenticationError', () => {
  describe('constructor', () => {
    it('should create error with code and default message', () => {
      const error = new AuthenticationError(AuthErrorCode.INVALID_CREDENTIALS);

      expect(error.code).toBe(AuthErrorCode.INVALID_CREDENTIALS);
      expect(error.message).toBe(AUTH_ERROR_MESSAGES[AuthErrorCode.INVALID_CREDENTIALS]);
      expect(error.timestamp).toBeDefined();
      expect(error.name).toBe('AuthenticationError');
    });

    it('should create error with custom message', () => {
      const customMessage = 'Custom authentication error';
      const error = new AuthenticationError(AuthErrorCode.INVALID_CREDENTIALS, customMessage);

      expect(error.code).toBe(AuthErrorCode.INVALID_CREDENTIALS);
      expect(error.message).toBe(customMessage);
      expect(error.timestamp).toBeDefined();
    });

    it('should create error with details', () => {
      const details = { userId: 'user123', attempts: 3 };
      const error = new AuthenticationError(AuthErrorCode.INVALID_CREDENTIALS, undefined, details);

      expect(error.code).toBe(AuthErrorCode.INVALID_CREDENTIALS);
      expect(error.details).toEqual(details);
      expect(error.timestamp).toBeDefined();
    });
  });

  describe('toJSON', () => {
    it('should return proper JSON representation', () => {
      const details = { userId: 'user123' };
      const error = new AuthenticationError(AuthErrorCode.TOKEN_EXPIRED, 'Token expired', details);
      const json = error.toJSON();

      expect(json).toEqual({
        code: AuthErrorCode.TOKEN_EXPIRED,
        message: 'Token expired',
        details: details,
        timestamp: error.timestamp,
      });
    });

    it('should handle error without details', () => {
      const error = new AuthenticationError(AuthErrorCode.INVALID_CREDENTIALS);
      const json = error.toJSON();

      expect(json).toEqual({
        code: AuthErrorCode.INVALID_CREDENTIALS,
        message: AUTH_ERROR_MESSAGES[AuthErrorCode.INVALID_CREDENTIALS],
        details: undefined,
        timestamp: error.timestamp,
      });
    });
  });

  describe('inheritance', () => {
    it('should be instance of Error', () => {
      const error = new AuthenticationError(AuthErrorCode.INVALID_CREDENTIALS);
      expect(error instanceof Error).toBe(true);
      expect(error instanceof AuthenticationError).toBe(true);
    });

    it('should have correct stack trace', () => {
      const error = new AuthenticationError(AuthErrorCode.INVALID_CREDENTIALS);
      expect(error.stack).toBeDefined();
      expect(error.stack).toContain('AuthenticationError');
    });
  });
});
