/**
 * 数据库连接池预热脚本
 * 在高并发测试前预热连接池，建立足够的数据库连接
 */

import { prisma } from '../app/lib/4-infrastructure/database/prisma';

async function warmupConnectionPool(targetConnections: number = 50): Promise<void> {
  console.log(`🔥 开始连接池预热，目标连接数: ${targetConnections}`);

  try {
    // 1. 基础连接测试
    await prisma.$connect();
    console.log('✅ 基础连接建立成功');

    // 2. 创建并发查询来强制建立多个连接
    console.log(`🚀 创建 ${targetConnections} 个并发查询以预热连接池...`);

    const warmupQueries = Array.from(
      { length: targetConnections },
      (_, i) =>
        // 使用简单的查询，主要目的是建立连接
        prisma.$queryRaw`SELECT ${i + 1} as connection_id, NOW() as timestamp`
    );

    const startTime = performance.now();
    const results = await Promise.all(warmupQueries);
    const warmupTime = performance.now() - startTime;

    console.log(`✅ 预热完成，耗时: ${Math.round(warmupTime)}ms`);
    console.log(`📊 成功建立 ${results.length} 个连接`);

    // 3. 检查连接池状态
    console.log('\n📊 预热后连接池状态:');
    try {
      const metrics = await (prisma as any).$metrics?.json();
      if (metrics) {
        const poolMetrics =
          metrics.gauges?.filter((gauge: any) => gauge.key.includes('pool_connections')) || [];

        poolMetrics.forEach((metric: any) => {
          console.log(`   ${metric.description}: ${metric.value}`);
        });
      } else {
        console.log('   ⚠️ 连接池指标不可用');
      }
    } catch (error) {
      console.log('   ⚠️ 无法获取连接池指标');
    }

    // 4. 保持连接活跃一段时间
    console.log('\n⏳ 保持连接活跃 5 秒...');
    await new Promise((resolve) => setTimeout(resolve, 5000));

    console.log('🎉 连接池预热完成！现在可以进行高并发测试');
  } catch (error) {
    console.error('❌ 连接池预热失败:', error);
    throw error;
  }
}

async function testWithWarmup(): Promise<void> {
  console.log('🔍 连接池预热性能测试\n');

  try {
    // 1. 预热连接池
    await warmupConnectionPool(50);

    // 2. 立即进行高并发测试
    console.log('\n🚀 开始高并发测试（预热后）...');

    const testWords = ['test', 'apple', 'book', 'car', 'dog'];
    const concurrency = 50;

    const startTime = performance.now();
    const promises = Array.from({ length: concurrency }, (_, i) => {
      const word = testWords[i % testWords.length];
      const requestStart = performance.now();

      return fetch(`http://localhost:3000/api/dictionary/en/${word}?fresh=true`)
        .then(async (response) => {
          const responseTime = performance.now() - requestStart;
          const data = await response.json();
          return {
            word,
            success: response.ok,
            responseTime,
            processingTime: data.data?.queryMetadata?.processingTimeMs || 0,
          };
        })
        .catch((error) => {
          const responseTime = performance.now() - requestStart;
          return {
            word,
            success: false,
            responseTime,
            processingTime: 0,
            error: error.message,
          };
        });
    });

    const results = await Promise.all(promises);
    const totalTime = performance.now() - startTime;

    const successful = results.filter((r) => r.success);
    const avgResponseTime =
      successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length;

    console.log(`\n✅ 预热后 ${concurrency} 并发测试完成:`);
    console.log(`   - 成功率: ${Math.round((successful.length / concurrency) * 100)}%`);
    console.log(`   - 总耗时: ${Math.round(totalTime)}ms`);
    console.log(`   - 平均响应时间: ${Math.round(avgResponseTime)}ms`);
    console.log(`   - RPS: ${Math.round((successful.length / totalTime) * 1000)}`);

    // 3. 对比测试（冷启动）
    console.log('\n❄️ 等待连接池冷却...');
    await prisma.$disconnect();
    await new Promise((resolve) => setTimeout(resolve, 3000));

    console.log('🚀 开始高并发测试（冷启动）...');

    const coldStartTime = performance.now();
    const coldPromises = Array.from({ length: concurrency }, (_, i) => {
      const word = testWords[i % testWords.length];
      const requestStart = performance.now();

      return fetch(`http://localhost:3000/api/dictionary/en/${word}?fresh=true`)
        .then(async (response) => {
          const responseTime = performance.now() - requestStart;
          return {
            success: response.ok,
            responseTime,
          };
        })
        .catch(() => ({
          success: false,
          responseTime: performance.now() - requestStart,
        }));
    });

    const coldResults = await Promise.all(coldPromises);
    const coldTotalTime = performance.now() - coldStartTime;

    const coldSuccessful = coldResults.filter((r) => r.success);
    const coldAvgResponseTime =
      coldSuccessful.reduce((sum, r) => sum + r.responseTime, 0) / coldSuccessful.length;

    console.log(`\n✅ 冷启动 ${concurrency} 并发测试完成:`);
    console.log(`   - 成功率: ${Math.round((coldSuccessful.length / concurrency) * 100)}%`);
    console.log(`   - 总耗时: ${Math.round(coldTotalTime)}ms`);
    console.log(`   - 平均响应时间: ${Math.round(coldAvgResponseTime)}ms`);
    console.log(`   - RPS: ${Math.round((coldSuccessful.length / coldTotalTime) * 1000)}`);

    // 4. 性能对比
    console.log('\n📊 性能对比:');
    console.log(`   预热模式平均响应时间: ${Math.round(avgResponseTime)}ms`);
    console.log(`   冷启动平均响应时间: ${Math.round(coldAvgResponseTime)}ms`);

    const improvement = ((coldAvgResponseTime - avgResponseTime) / coldAvgResponseTime) * 100;
    if (improvement > 0) {
      console.log(`   🎉 预热提升: ${improvement.toFixed(1)}%`);
    } else {
      console.log(`   ⚠️ 预热无明显效果`);
    }
  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 运行测试
if (process.argv[2] === 'test') {
  testWithWarmup().catch(console.error);
} else {
  warmupConnectionPool(parseInt(process.argv[2]) || 50).catch(console.error);
}
