/**
 * Performance Profiler
 * Comprehensive performance testing and profiling system
 *
 * Features:
 * - Load testing with configurable scenarios
 * - Performance regression detection
 * - Cache efficiency analysis
 * - Concurrent request testing
 * - Response time analysis
 * - Memory usage monitoring
 * - CPU utilization tracking
 *
 * Usage:
 * npx ts-node scripts/performance-profiler.ts
 */

import { createCacheOptimizer } from '../app/lib/auth/cache-optimizer';
import { createConcurrentProcessor } from '../app/lib/auth/concurrent-processor';
import { createAdaptiveSecurityMiddleware } from '../app/lib/auth/adaptive-security-middleware';
import { Logger } from '../app/lib/utils/logger';
import { performance } from 'perf_hooks';

// ============================================================================
// Types and Interfaces
// ============================================================================

export interface PerformanceTestConfig {
  name: string;
  description: string;
  duration: number; // Test duration in milliseconds
  concurrency: number; // Number of concurrent requests
  rampUp: number; // Ramp up time in milliseconds
  rampDown: number; // Ramp down time in milliseconds
  targetRps: number; // Target requests per second
  timeout: number; // Request timeout in milliseconds

  scenarios: TestScenario[];
  assertions: PerformanceAssertion[];
}

export interface TestScenario {
  name: string;
  weight: number; // Percentage of total requests
  endpoint: string;
  method: string;
  headers?: Record<string, string>;
  body?: any;
  cacheKey?: string;
  expectedResponseTime?: number;
  expectedCacheHit?: boolean;
}

export interface PerformanceAssertion {
  metric: string;
  operator: 'lt' | 'lte' | 'gt' | 'gte' | 'eq' | 'ne';
  value: number;
  description: string;
}

export interface PerformanceResult {
  testName: string;
  startTime: number;
  endTime: number;
  duration: number;

  requests: {
    total: number;
    successful: number;
    failed: number;
    rps: number;
    errorRate: number;
  };

  responseTime: {
    min: number;
    max: number;
    avg: number;
    median: number;
    p95: number;
    p99: number;
  };

  cache: {
    hitRate: number;
    l1HitRate: number;
    l2HitRate: number;
    l3HitRate: number;
    avgCacheTime: number;
  };

  concurrency: {
    maxConcurrent: number;
    avgConcurrent: number;
    workerUtilization: number;
    queueSize: number;
  };

  resources: {
    maxMemory: number;
    avgMemory: number;
    maxCpu: number;
    avgCpu: number;
  };

  assertions: AssertionResult[];
  scenarioResults: Map<string, ScenarioResult>;
}

export interface AssertionResult {
  assertion: PerformanceAssertion;
  passed: boolean;
  actualValue: number;
  message: string;
}

export interface ScenarioResult {
  name: string;
  requests: number;
  avgResponseTime: number;
  errorRate: number;
  cacheHitRate: number;
}

export interface LoadTestRequest {
  id: string;
  scenario: TestScenario;
  startTime: number;
  endTime?: number;
  responseTime?: number;
  success: boolean;
  error?: string;
  cacheHit?: boolean;
}

// ============================================================================
// Performance Metrics Collector
// ============================================================================

class PerformanceMetricsCollector {
  private metrics: {
    responseTimes: number[];
    cacheHits: number;
    cacheMisses: number;
    errors: number;
    requests: number;
    concurrentRequests: number;
    maxConcurrentRequests: number;
    memoryUsage: number[];
    cpuUsage: number[];
    queueSizes: number[];
  };

  private startTime: number;
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.metrics = {
      responseTimes: [],
      cacheHits: 0,
      cacheMisses: 0,
      errors: 0,
      requests: 0,
      concurrentRequests: 0,
      maxConcurrentRequests: 0,
      memoryUsage: [],
      cpuUsage: [],
      queueSizes: [],
    };

    this.startTime = Date.now();
  }

  startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
    }, 1000); // Collect every second
  }

  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  private collectSystemMetrics(): void {
    const memoryUsage = process.memoryUsage();
    this.metrics.memoryUsage.push(memoryUsage.heapUsed / 1024 / 1024); // MB

    // CPU usage would require more complex calculation
    // For now, we'll use a placeholder
    this.metrics.cpuUsage.push(0);
  }

  recordRequest(responseTime: number, cacheHit: boolean, error?: string): void {
    this.metrics.requests++;
    this.metrics.responseTimes.push(responseTime);

    if (cacheHit) {
      this.metrics.cacheHits++;
    } else {
      this.metrics.cacheMisses++;
    }

    if (error) {
      this.metrics.errors++;
    }
  }

  recordConcurrentRequest(isStart: boolean): void {
    if (isStart) {
      this.metrics.concurrentRequests++;
      this.metrics.maxConcurrentRequests = Math.max(
        this.metrics.maxConcurrentRequests,
        this.metrics.concurrentRequests
      );
    } else {
      this.metrics.concurrentRequests--;
    }
  }

  recordQueueSize(size: number): void {
    this.metrics.queueSizes.push(size);
  }

  getMetrics(): typeof this.metrics {
    return { ...this.metrics };
  }

  calculatePercentile(values: number[], percentile: number): number {
    const sorted = values.slice().sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[index] || 0;
  }

  getResults(): PerformanceResult['responseTime'] &
    PerformanceResult['cache'] &
    PerformanceResult['resources'] {
    const responseTimes = this.metrics.responseTimes;
    const totalCacheRequests = this.metrics.cacheHits + this.metrics.cacheMisses;

    return {
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      avg: responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length,
      median: this.calculatePercentile(responseTimes, 50),
      p95: this.calculatePercentile(responseTimes, 95),
      p99: this.calculatePercentile(responseTimes, 99),

      hitRate: totalCacheRequests > 0 ? (this.metrics.cacheHits / totalCacheRequests) * 100 : 0,
      l1HitRate: 0, // Would need more detailed tracking
      l2HitRate: 0, // Would need more detailed tracking
      l3HitRate: 0, // Would need more detailed tracking
      avgCacheTime: 0, // Would need more detailed tracking

      maxMemory: Math.max(...this.metrics.memoryUsage),
      avgMemory:
        this.metrics.memoryUsage.reduce((sum, mem) => sum + mem, 0) /
        this.metrics.memoryUsage.length,
      maxCpu: Math.max(...this.metrics.cpuUsage),
      avgCpu:
        this.metrics.cpuUsage.reduce((sum, cpu) => sum + cpu, 0) / this.metrics.cpuUsage.length,
    };
  }
}

// ============================================================================
// Load Test Runner
// ============================================================================

class LoadTestRunner {
  private cacheOptimizer: any;
  private concurrentProcessor: any;
  private adaptiveMiddleware: any;
  private metricsCollector: PerformanceMetricsCollector;

  constructor() {
    this.cacheOptimizer = createCacheOptimizer();
    this.concurrentProcessor = createConcurrentProcessor(this.cacheOptimizer);
    this.adaptiveMiddleware = createAdaptiveSecurityMiddleware();
    this.metricsCollector = new PerformanceMetricsCollector();
  }

  async runTest(config: PerformanceTestConfig): Promise<PerformanceResult> {
    Logger.info(`Starting performance test: ${config.name}`);

    const startTime = Date.now();
    this.metricsCollector.startMonitoring();

    try {
      const requests = await this.generateRequests(config);
      const results = await this.executeRequests(requests, config);

      const endTime = Date.now();
      const duration = endTime - startTime;

      const performanceResult = this.analyzeResults(config, results, startTime, endTime, duration);

      Logger.info(`Performance test completed: ${config.name}`);
      Logger.info(
        `Duration: ${duration}ms, Requests: ${results.length}, RPS: ${((results.length / duration) * 1000).toFixed(2)}`
      );

      return performanceResult;
    } finally {
      this.metricsCollector.stopMonitoring();
    }
  }

  private async generateRequests(config: PerformanceTestConfig): Promise<LoadTestRequest[]> {
    const requests: LoadTestRequest[] = [];
    const totalRequests = Math.floor(config.targetRps * (config.duration / 1000));

    for (let i = 0; i < totalRequests; i++) {
      const scenario = this.selectScenario(config.scenarios);
      const startTime = this.calculateStartTime(i, totalRequests, config);

      requests.push({
        id: `req-${i}`,
        scenario,
        startTime,
        success: false,
      });
    }

    return requests.sort((a, b) => a.startTime - b.startTime);
  }

  private selectScenario(scenarios: TestScenario[]): TestScenario {
    const totalWeight = scenarios.reduce((sum, scenario) => sum + scenario.weight, 0);
    const random = Math.random() * totalWeight;

    let currentWeight = 0;
    for (const scenario of scenarios) {
      currentWeight += scenario.weight;
      if (random <= currentWeight) {
        return scenario;
      }
    }

    return scenarios[0]; // Fallback
  }

  private calculateStartTime(index: number, total: number, config: PerformanceTestConfig): number {
    const baseTime = Date.now();
    const testDuration = config.duration;
    const rampUpTime = config.rampUp;
    const rampDownTime = config.rampDown;

    const steadyTime = testDuration - rampUpTime - rampDownTime;
    const timeInterval = testDuration / total;

    let startTime = baseTime + index * timeInterval;

    // Apply ramp up
    if (index < (total * rampUpTime) / testDuration) {
      const rampUpFactor = index / ((total * rampUpTime) / testDuration);
      startTime += rampUpTime * (1 - rampUpFactor);
    }

    return startTime;
  }

  private async executeRequests(
    requests: LoadTestRequest[],
    config: PerformanceTestConfig
  ): Promise<LoadTestRequest[]> {
    const concurrentRequests = new Set<string>();
    const results: LoadTestRequest[] = [];

    // Create promise for each request
    const requestPromises = requests.map(async (request) => {
      // Wait until it's time to start this request
      const delay = request.startTime - Date.now();
      if (delay > 0) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      return this.executeRequest(request, concurrentRequests);
    });

    // Wait for all requests to complete
    const completedRequests = await Promise.all(requestPromises);

    return completedRequests;
  }

  private async executeRequest(
    request: LoadTestRequest,
    concurrentRequests: Set<string>
  ): Promise<LoadTestRequest> {
    const startTime = performance.now();

    try {
      concurrentRequests.add(request.id);
      this.metricsCollector.recordConcurrentRequest(true);

      // Simulate request execution
      const result = await this.simulateRequest(request.scenario);

      const endTime = performance.now();
      const responseTime = endTime - startTime;

      request.endTime = endTime;
      request.responseTime = responseTime;
      request.success = result.success;
      request.cacheHit = result.cacheHit;
      request.error = result.error;

      this.metricsCollector.recordRequest(responseTime, result.cacheHit, result.error);

      return request;
    } catch (error) {
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      request.endTime = endTime;
      request.responseTime = responseTime;
      request.success = false;
      request.error = error instanceof Error ? error.message : 'Unknown error';

      this.metricsCollector.recordRequest(responseTime, false, request.error);

      return request;
    } finally {
      concurrentRequests.delete(request.id);
      this.metricsCollector.recordConcurrentRequest(false);
    }
  }

  private async simulateRequest(
    scenario: TestScenario
  ): Promise<{ success: boolean; cacheHit: boolean; error?: string }> {
    try {
      // Simulate cache lookup
      let cacheHit = false;
      if (scenario.cacheKey) {
        const cached = await this.cacheOptimizer.get(scenario.cacheKey);
        if (cached) {
          cacheHit = true;
          return { success: true, cacheHit };
        }
      }

      // Simulate processing
      const processingTime = Math.random() * 50 + 10; // 10-60ms
      await new Promise((resolve) => setTimeout(resolve, processingTime));

      // Simulate cache set
      if (scenario.cacheKey) {
        await this.cacheOptimizer.set(scenario.cacheKey, { data: 'test' });
      }

      // Simulate random failures
      const errorRate = 0.02; // 2% error rate
      if (Math.random() < errorRate) {
        throw new Error('Simulated error');
      }

      return { success: true, cacheHit };
    } catch (error) {
      return {
        success: false,
        cacheHit: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private analyzeResults(
    config: PerformanceTestConfig,
    requests: LoadTestRequest[],
    startTime: number,
    endTime: number,
    duration: number
  ): PerformanceResult {
    const successful = requests.filter((req) => req.success).length;
    const failed = requests.filter((req) => !req.success).length;
    const cacheHits = requests.filter((req) => req.cacheHit).length;

    const metricsResults = this.metricsCollector.getResults();

    // Calculate scenario results
    const scenarioResults = new Map<string, ScenarioResult>();
    config.scenarios.forEach((scenario) => {
      const scenarioRequests = requests.filter((req) => req.scenario.name === scenario.name);
      const scenarioSuccess = scenarioRequests.filter((req) => req.success).length;
      const scenarioErrors = scenarioRequests.filter((req) => !req.success).length;
      const scenarioCacheHits = scenarioRequests.filter((req) => req.cacheHit).length;

      scenarioResults.set(scenario.name, {
        name: scenario.name,
        requests: scenarioRequests.length,
        avgResponseTime:
          scenarioRequests.reduce((sum, req) => sum + (req.responseTime || 0), 0) /
          scenarioRequests.length,
        errorRate:
          scenarioRequests.length > 0 ? (scenarioErrors / scenarioRequests.length) * 100 : 0,
        cacheHitRate:
          scenarioRequests.length > 0 ? (scenarioCacheHits / scenarioRequests.length) * 100 : 0,
      });
    });

    // Evaluate assertions
    const assertionResults = config.assertions.map((assertion) => {
      const actualValue = this.getMetricValue(assertion.metric, metricsResults, requests);
      const passed = this.evaluateAssertion(assertion, actualValue);

      return {
        assertion,
        passed,
        actualValue,
        message: passed
          ? 'Assertion passed'
          : `Expected ${assertion.metric} to be ${assertion.operator} ${assertion.value}, got ${actualValue}`,
      };
    });

    return {
      testName: config.name,
      startTime,
      endTime,
      duration,

      requests: {
        total: requests.length,
        successful,
        failed,
        rps: (requests.length / duration) * 1000,
        errorRate: requests.length > 0 ? (failed / requests.length) * 100 : 0,
      },

      responseTime: metricsResults,

      cache: {
        hitRate: requests.length > 0 ? (cacheHits / requests.length) * 100 : 0,
        l1HitRate: 0, // Would need detailed cache stats
        l2HitRate: 0, // Would need detailed cache stats
        l3HitRate: 0, // Would need detailed cache stats
        avgCacheTime: 0, // Would need detailed cache stats
      },

      concurrency: {
        maxConcurrent: this.metricsCollector.getMetrics().maxConcurrentRequests,
        avgConcurrent: this.metricsCollector.getMetrics().concurrentRequests,
        workerUtilization: 0, // Would need processor stats
        queueSize: 0, // Would need processor stats
      },

      resources: metricsResults,

      assertions: assertionResults,
      scenarioResults,
    };
  }

  private getMetricValue(metric: string, metricsResults: any, requests: LoadTestRequest[]): number {
    switch (metric) {
      case 'avg_response_time':
        return metricsResults.avg;
      case 'p95_response_time':
        return metricsResults.p95;
      case 'p99_response_time':
        return metricsResults.p99;
      case 'error_rate':
        return (requests.filter((req) => !req.success).length / requests.length) * 100;
      case 'cache_hit_rate':
        return (requests.filter((req) => req.cacheHit).length / requests.length) * 100;
      case 'max_memory':
        return metricsResults.maxMemory;
      case 'avg_memory':
        return metricsResults.avgMemory;
      default:
        return 0;
    }
  }

  private evaluateAssertion(assertion: PerformanceAssertion, actualValue: number): boolean {
    switch (assertion.operator) {
      case 'lt':
        return actualValue < assertion.value;
      case 'lte':
        return actualValue <= assertion.value;
      case 'gt':
        return actualValue > assertion.value;
      case 'gte':
        return actualValue >= assertion.value;
      case 'eq':
        return actualValue === assertion.value;
      case 'ne':
        return actualValue !== assertion.value;
      default:
        return false;
    }
  }
}

// ============================================================================
// Performance Test Configurations
// ============================================================================

const cachePerformanceTest: PerformanceTestConfig = {
  name: 'Cache Performance Test',
  description: 'Test cache hit rates and response times',
  duration: 60000, // 1 minute
  concurrency: 50,
  rampUp: 10000, // 10 seconds
  rampDown: 10000, // 10 seconds
  targetRps: 100,
  timeout: 5000,

  scenarios: [
    {
      name: 'cache_hit',
      weight: 70,
      endpoint: '/api/cache/test',
      method: 'GET',
      cacheKey: 'test-key-1',
      expectedResponseTime: 1,
      expectedCacheHit: true,
    },
    {
      name: 'cache_miss',
      weight: 30,
      endpoint: '/api/cache/test',
      method: 'GET',
      cacheKey: 'test-key-' + Math.random(),
      expectedResponseTime: 50,
      expectedCacheHit: false,
    },
  ],

  assertions: [
    {
      metric: 'avg_response_time',
      operator: 'lt',
      value: 10,
      description: 'Average response time should be less than 10ms',
    },
    {
      metric: 'cache_hit_rate',
      operator: 'gt',
      value: 60,
      description: 'Cache hit rate should be greater than 60%',
    },
    {
      metric: 'error_rate',
      operator: 'lt',
      value: 1,
      description: 'Error rate should be less than 1%',
    },
  ],
};

const concurrencyTest: PerformanceTestConfig = {
  name: 'High Concurrency Test',
  description: 'Test system under high concurrent load',
  duration: 30000, // 30 seconds
  concurrency: 200,
  rampUp: 5000, // 5 seconds
  rampDown: 5000, // 5 seconds
  targetRps: 500,
  timeout: 10000,

  scenarios: [
    {
      name: 'high_load',
      weight: 100,
      endpoint: '/api/test',
      method: 'GET',
      cacheKey: 'concurrent-test-' + Math.random(),
    },
  ],

  assertions: [
    {
      metric: 'p95_response_time',
      operator: 'lt',
      value: 100,
      description: 'P95 response time should be less than 100ms',
    },
    {
      metric: 'error_rate',
      operator: 'lt',
      value: 5,
      description: 'Error rate should be less than 5%',
    },
    {
      metric: 'max_memory',
      operator: 'lt',
      value: 200,
      description: 'Max memory usage should be less than 200MB',
    },
  ],
};

const enduranceTest: PerformanceTestConfig = {
  name: 'Endurance Test',
  description: 'Test system stability over extended period',
  duration: 300000, // 5 minutes
  concurrency: 100,
  rampUp: 30000, // 30 seconds
  rampDown: 30000, // 30 seconds
  targetRps: 200,
  timeout: 15000,

  scenarios: [
    {
      name: 'mixed_load',
      weight: 50,
      endpoint: '/api/test',
      method: 'GET',
      cacheKey: 'endurance-test-' + Math.random(),
    },
    {
      name: 'cached_load',
      weight: 50,
      endpoint: '/api/cached',
      method: 'GET',
      cacheKey: 'endurance-cached',
    },
  ],

  assertions: [
    {
      metric: 'avg_response_time',
      operator: 'lt',
      value: 50,
      description: 'Average response time should remain stable',
    },
    {
      metric: 'cache_hit_rate',
      operator: 'gt',
      value: 40,
      description: 'Cache hit rate should be maintained',
    },
    {
      metric: 'error_rate',
      operator: 'lt',
      value: 2,
      description: 'Error rate should remain low',
    },
  ],
};

// ============================================================================
// Main Execution
// ============================================================================

export async function runPerformanceTests(): Promise<void> {
  const runner = new LoadTestRunner();

  const tests = [cachePerformanceTest, concurrencyTest, enduranceTest];

  const results: PerformanceResult[] = [];

  for (const test of tests) {
    try {
      const result = await runner.runTest(test);
      results.push(result);

      // Log results
      console.log(`\n=== ${result.testName} Results ===`);
      console.log(`Duration: ${result.duration}ms`);
      console.log(
        `Requests: ${result.requests.total} (${result.requests.successful} successful, ${result.requests.failed} failed)`
      );
      console.log(`RPS: ${result.requests.rps.toFixed(2)}`);
      console.log(`Error Rate: ${result.requests.errorRate.toFixed(2)}%`);
      console.log(`Cache Hit Rate: ${result.cache.hitRate.toFixed(2)}%`);
      console.log(
        `Response Time - Avg: ${result.responseTime.avg.toFixed(2)}ms, P95: ${result.responseTime.p95.toFixed(2)}ms, P99: ${result.responseTime.p99.toFixed(2)}ms`
      );
      console.log(
        `Memory - Max: ${result.resources.maxMemory.toFixed(2)}MB, Avg: ${result.resources.avgMemory.toFixed(2)}MB`
      );

      // Check assertions
      const failedAssertions = result.assertions.filter((a) => !a.passed);
      if (failedAssertions.length > 0) {
        console.log('\n❌ Failed Assertions:');
        failedAssertions.forEach((assertion) => {
          console.log(`  - ${assertion.message}`);
        });
      } else {
        console.log('\n✅ All assertions passed');
      }

      console.log('\n');
    } catch (error) {
      console.error(`❌ Test ${test.name} failed:`, error);
    }
  }

  // Generate summary report
  console.log('\n=== Performance Test Summary ===');
  results.forEach((result) => {
    const passedAssertions = result.assertions.filter((a) => a.passed).length;
    const totalAssertions = result.assertions.length;
    const status = passedAssertions === totalAssertions ? '✅ PASSED' : '❌ FAILED';

    console.log(
      `${result.testName}: ${status} (${passedAssertions}/${totalAssertions} assertions passed)`
    );
  });

  console.log('\nPerformance testing completed!');
}

// Run if executed directly
if (require.main === module) {
  runPerformanceTests().catch(console.error);
}
