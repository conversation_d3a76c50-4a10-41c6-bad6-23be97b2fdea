/**
 * JWT验证缓存服务
 * 缓存JWT验证结果以提高认证性能
 */

import { redisCacheManager } from '@/lib/utils/redis';
import { hashString } from '@/lib/utils/crypto';
import { JWTPayload } from '@/lib/types/api-types';

export interface CachedJWTValidation {
  userId: string;
  email: string;
  clientType: 'mobile' | 'extension';
  scopes: string[];
  isValid: boolean;
  cachedAt: number;
}

export class JWTValidationCache {
  private cache = redisCacheManager;
  private readonly CACHE_TTL = 300; // 5分钟缓存
  private readonly NEGATIVE_CACHE_TTL = 60; // 失败结果缓存1分钟

  /**
   * 生成缓存键
   */
  private generateCacheKey(jti: string): string {
    return `jwt_validation:${jti}`;
  }

  /**
   * 缓存JWT验证结果
   */
  async cacheValidation(
    jti: string,
    payload: JWTPayload,
    isValid: boolean
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(jti);
    const ttl = isValid ? this.CACHE_TTL : this.NEGATIVE_CACHE_TTL;
    
    const cachedData: CachedJWTValidation = {
      userId: payload.sub,
      email: payload.email,
      clientType: payload.clientType,
      scopes: payload.scopes || [],
      isValid,
      cachedAt: Date.now(),
    };

    await this.cache.set(cacheKey, cachedData, ttl);
  }

  /**
   * 获取缓存的JWT验证结果
   */
  async getCachedValidation(jti: string): Promise<CachedJWTValidation | null> {
    const cacheKey = this.generateCacheKey(jti);
    return await this.cache.get<CachedJWTValidation>(cacheKey);
  }

  /**
   * 清除特定JWT的缓存
   */
  async invalidateJWT(jti: string): Promise<void> {
    const cacheKey = this.generateCacheKey(jti);
    await this.cache.del(cacheKey);
  }

  /**
   * 批量清除用户的所有JWT缓存（用于密码重置等场景）
   */
  async invalidateUserJWTs(userId: string): Promise<void> {
    // 注意：这个实现需要遍历所有键，在大规模应用中需要优化
    // 可以考虑使用Redis的SCAN命令或维护用户->JWT映射
    console.log(`清除用户 ${userId} 的JWT缓存`);
    // 实际实现中可以维护一个反向映射来高效清除
  }

  /**
   * 检查JWT是否在缓存中且仍然有效
   */
  async isJWTCachedAndValid(jti: string): Promise<boolean> {
    const cached = await this.getCachedValidation(jti);
    return cached !== null && cached.isValid;
  }

  /**
   * 预热JWT缓存（用于批量验证）
   */
  async warmupJWTCache(tokens: Array<{ jti: string; payload: JWTPayload }>): Promise<void> {
    const promises = tokens.map(({ jti, payload }) =>
      this.cacheValidation(jti, payload, true)
    );
    
    await Promise.all(promises);
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{
    totalKeys: number;
    validTokens: number;
    invalidTokens: number;
  }> {
    // 这是一个简化的实现，实际中需要更复杂的统计逻辑
    return {
      totalKeys: 0,
      validTokens: 0,
      invalidTokens: 0,
    };
  }
}

// 导出单例实例
export const jwtValidationCache = new JWTValidationCache();