/**
 * Redis高并发性能测试脚本
 * 测试目标：100并发都能稳定在500ms内
 *
 * 测试场景：
 * 1. 预热阶段：填充Redis缓存和L2应用层缓存
 * 2. API级别缓存测试：测试完整API响应缓存 (search:* keys)
 * 3. L2单词缓存测试：清理API缓存后测试单词级别缓存 (vocab:*, forms:*)
 * 4. 冷启动测试：清理所有缓存后测试数据库性能
 */

interface RedisConcurrencyTestResult {
  scenario: string;
  concurrency: number;
  avgResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  successRate: number;
  rps: number;
  cacheHitRate: number;
  cacheSource: string;
  errorRate: number;
  timeoutRate: number;
}

interface TestConfig {
  concurrencyLevels: number[];
  iterations: number;
  requestTimeout: number;
  targetResponseTime: number;
  warmupRequests: number;
}

class RedisHighConcurrencyTester {
  private baseUrl = 'http://localhost:3000';
  private testWords = [
    // 高频词汇 - 模拟热点数据
    'test',
    'apple',
    'book',
    'car',
    'dog',
    'cat',
    'run',
    'walk',
    'eat',
    'drink',
    'house',
    'tree',
    'water',
    'fire',
    'love',
    'time',
    'work',
    'play',
    'read',
    'write',
    'big',
    'small',
    'good',
    'bad',
    'new',
    'old',
    'hot',
    'cold',
    'fast',
    'slow',
    // 中频词汇
    'computer',
    'internet',
    'software',
    'database',
    'network',
    'system',
    'program',
    'application',
    'development',
    'technology',
    'information',
    'communication',
    // 低频词汇 - 模拟冷数据
    'serendipity',
    'ephemeral',
    'ubiquitous',
    'paradigm',
    'algorithm',
    'infrastructure',
  ];

  private config: TestConfig = {
    concurrencyLevels: [1, 5, 10, 20, 50, 100],
    iterations: 3,
    requestTimeout: 10000, // 10秒超时
    targetResponseTime: 500, // 目标500ms
    warmupRequests: 50,
  };

  /**
   * 预热缓存 - 填充Redis和L2缓存
   */
  async warmupCaches(): Promise<void> {
    console.log('🔥 开始缓存预热...');
    const startTime = performance.now();

    try {
      // 第一轮：填充API级别缓存 (search:* keys)
      console.log('   📊 预热API级别缓存 (search:* keys)...');
      const apiWarmupPromises = this.testWords
        .slice(0, this.config.warmupRequests)
        .map(async (word) => {
          try {
            const response = await fetch(`${this.baseUrl}/api/dictionary/en/${word}`, {
              method: 'GET',
              headers: { Accept: 'application/json' },
            });
            return response.ok;
          } catch {
            return false;
          }
        });

      const apiResults = await Promise.all(apiWarmupPromises);
      const apiSuccessRate = (apiResults.filter(Boolean).length / apiResults.length) * 100;
      console.log(`   ✅ API缓存预热完成: ${Math.round(apiSuccessRate)}% 成功率`);

      // 等待缓存写入完成
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // 第二轮：确保L2缓存也被填充 (vocab:*, forms:* keys)
      console.log('   🏗️ 预热L2单词缓存 (vocab:*, forms:* keys)...');
      const l2WarmupPromises = this.testWords
        .slice(0, this.config.warmupRequests)
        .map(async (word) => {
          try {
            const response = await fetch(`${this.baseUrl}/api/dictionary/en/${word}`, {
              method: 'GET',
              headers: { Accept: 'application/json' },
            });
            return response.ok;
          } catch {
            return false;
          }
        });

      const l2Results = await Promise.all(l2WarmupPromises);
      const l2SuccessRate = (l2Results.filter(Boolean).length / l2Results.length) * 100;

      const warmupTime = performance.now() - startTime;
      console.log(`   ✅ L2缓存预热完成: ${Math.round(l2SuccessRate)}% 成功率`);
      console.log(`🎉 缓存预热总耗时: ${Math.round(warmupTime)}ms`);
    } catch (error) {
      console.error('❌ 缓存预热失败:', error);
      throw error;
    }
  }

  /**
   * 清理API级别缓存（保留L2缓存）- 只清理search:*键
   */
  async clearApiCache(): Promise<void> {
    console.log('🧹 清理API级别缓存 (search:* keys)...');
    try {
      const response = await fetch(`${this.baseUrl}/api/cache/clear`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'pattern',
          pattern: 'search:*',
        }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ API缓存已清理: ${result.result?.keysDeleted || 0} 个键`);
      } else {
        console.log('⚠️ API缓存清理可能失败，继续测试');
      }
    } catch (error) {
      console.log('⚠️ 无法清理API缓存，继续测试');
    }
  }

  /**
   * 清理所有缓存
   */
  async clearAllCaches(): Promise<void> {
    console.log('🧹 清理所有缓存...');
    try {
      // 清理所有Redis缓存
      const response = await fetch(`${this.baseUrl}/api/cache/clear`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'all' }),
      });

      if (response.ok) {
        const result = await response.json();
        console.log(`✅ 所有缓存已清理: ${result.result?.keysDeleted || 0} 个键`);
      } else {
        console.log('⚠️ 缓存清理可能失败，继续测试');
      }
    } catch (error) {
      console.log('⚠️ 缓存清理失败，继续测试');
    }
  }

  /**
   * 执行并发测试
   */
  async runConcurrencyTest(
    scenario: string,
    concurrency: number,
    iterations: number = 3
  ): Promise<RedisConcurrencyTestResult> {
    console.log(`🚀 ${scenario} - ${concurrency}个并发测试...`);

    const allResults: Array<{
      responseTime: number;
      success: boolean;
      cacheSource?: string;
      isTimeout: boolean;
      isError: boolean;
    }> = [];

    for (let iteration = 1; iteration <= iterations; iteration++) {
      const promises = Array.from({ length: concurrency }, (_, i) => {
        const word = this.testWords[i % this.testWords.length];
        const startTime = performance.now();

        // 添加超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.requestTimeout);

        return fetch(`${this.baseUrl}/api/dictionary/en/${word}`, {
          signal: controller.signal,
          headers: { Accept: 'application/json' },
        })
          .then(async (response) => {
            clearTimeout(timeoutId);
            const responseTime = performance.now() - startTime;
            const cacheSource = response.headers.get('x-cache-source') || 'unknown';

            return {
              responseTime,
              success: response.ok,
              cacheSource,
              isTimeout: false,
              isError: false,
            };
          })
          .catch((error) => {
            clearTimeout(timeoutId);
            const responseTime = performance.now() - startTime;
            const isTimeout = error.name === 'AbortError';

            return {
              responseTime,
              success: false,
              cacheSource: 'error',
              isTimeout,
              isError: !isTimeout,
            };
          });
      });

      const results = await Promise.all(promises);
      allResults.push(...results);

      const successful = results.filter((r) => r.success);
      const timeouts = results.filter((r) => r.isTimeout);
      const errors = results.filter((r) => r.isError);
      const avgResponseTime =
        successful.length > 0
          ? successful.reduce((sum, r) => sum + r.responseTime, 0) / successful.length
          : 0;

      console.log(
        `   迭代 ${iteration}/${iterations}: ${successful.length}/${concurrency} 成功, ${timeouts.length} 超时, ${errors.length} 错误, 平均${Math.round(avgResponseTime)}ms`
      );
    }

    // 计算统计数据
    const successful = allResults.filter((r) => r.success);
    const timeouts = allResults.filter((r) => r.isTimeout);
    const errors = allResults.filter((r) => r.isError);

    const responseTimes = successful.map((r) => r.responseTime);
    const avgResponseTime =
      responseTimes.length > 0
        ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
        : 0;

    // 计算P95和P99
    const sortedTimes = responseTimes.sort((a, b) => a - b);
    const p95Index = Math.floor(sortedTimes.length * 0.95);
    const p99Index = Math.floor(sortedTimes.length * 0.99);
    const p95ResponseTime = sortedTimes[p95Index] || 0;
    const p99ResponseTime = sortedTimes[p99Index] || 0;

    // 计算缓存命中率
    const cacheHits = successful.filter((r) => r.cacheSource !== 'database').length;
    const cacheHitRate = successful.length > 0 ? (cacheHits / successful.length) * 100 : 0;

    // 计算RPS（基于成功的请求）
    const totalTime =
      allResults.length > 0 ? Math.max(...allResults.map((r) => r.responseTime)) : 1000;
    const rps = Math.round((successful.length / totalTime) * 1000);

    // 计算错误率
    const errorRate = (errors.length / allResults.length) * 100;
    const timeoutRate = (timeouts.length / allResults.length) * 100;
    const successRate = (successful.length / allResults.length) * 100;

    // 获取主要缓存来源
    const cacheSourceCounts = successful.reduce(
      (acc, r) => {
        acc[r.cacheSource || 'unknown'] = (acc[r.cacheSource || 'unknown'] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const primaryCacheSource =
      Object.entries(cacheSourceCounts).sort(([, a], [, b]) => b - a)[0]?.[0] || 'unknown';

    const result: RedisConcurrencyTestResult = {
      scenario,
      concurrency,
      avgResponseTime: Math.round(avgResponseTime),
      p95ResponseTime: Math.round(p95ResponseTime),
      p99ResponseTime: Math.round(p99ResponseTime),
      successRate: Math.round(successRate * 100) / 100,
      rps,
      cacheHitRate: Math.round(cacheHitRate * 100) / 100,
      cacheSource: primaryCacheSource,
      errorRate: Math.round(errorRate * 100) / 100,
      timeoutRate: Math.round(timeoutRate * 100) / 100,
    };

    return result;
  }

  /**
   * 运行完整的Redis高并发测试套件
   */
  async runFullTestSuite(): Promise<void> {
    console.log('🚀 开始Redis高并发测试套件');
    console.log(
      `📊 测试配置: 并发级别 [${this.config.concurrencyLevels.join(', ')}], 目标响应时间 ${this.config.targetResponseTime}ms\n`
    );

    const allResults: RedisConcurrencyTestResult[] = [];

    try {
      // 1. 预热缓存
      await this.warmupCaches();
      console.log('');

      // 2. API级别缓存测试 (search:* keys)
      console.log('📊 场景1: API级别缓存测试 (search:* keys)');
      for (const concurrency of this.config.concurrencyLevels) {
        const result = await this.runConcurrencyTest(
          'API缓存',
          concurrency,
          this.config.iterations
        );
        allResults.push(result);

        // 检查是否达到目标性能
        const status = result.avgResponseTime <= this.config.targetResponseTime ? '✅' : '❌';
        console.log(
          `   ${status} ${concurrency}并发: ${result.avgResponseTime}ms (目标: ${this.config.targetResponseTime}ms)`
        );
      }
      console.log('');

      // 3. L2单词缓存测试 (vocab:*, forms:* keys)
      console.log('📊 场景2: L2单词缓存测试 (vocab:*, forms:* keys)');
      await this.clearApiCache(); // 只清理API缓存，保留L2缓存

      for (const concurrency of this.config.concurrencyLevels) {
        const result = await this.runConcurrencyTest('L2缓存', concurrency, this.config.iterations);
        allResults.push(result);

        const status = result.avgResponseTime <= this.config.targetResponseTime ? '✅' : '❌';
        console.log(
          `   ${status} ${concurrency}并发: ${result.avgResponseTime}ms (目标: ${this.config.targetResponseTime}ms)`
        );
      }
      console.log('');

      // 4. 冷启动测试 (数据库性能)
      console.log('📊 场景3: 冷启动测试 (数据库性能)');
      await this.clearAllCaches(); // 清理所有缓存

      for (const concurrency of this.config.concurrencyLevels) {
        const result = await this.runConcurrencyTest('数据库', concurrency, this.config.iterations);
        allResults.push(result);

        const status = result.avgResponseTime <= this.config.targetResponseTime ? '✅' : '❌';
        console.log(
          `   ${status} ${concurrency}并发: ${result.avgResponseTime}ms (目标: ${this.config.targetResponseTime}ms)`
        );
      }
      console.log('');

      // 5. 分析和报告结果
      this.analyzeResults(allResults);
    } catch (error) {
      console.error('❌ 测试套件执行失败:', error);
      throw error;
    }
  }

  /**
   * 分析测试结果
   */
  private analyzeResults(results: RedisConcurrencyTestResult[]): void {
    console.log('📊 测试结果分析:\n');

    // 按场景分组
    const groupedResults = results.reduce(
      (acc, result) => {
        if (!acc[result.scenario]) acc[result.scenario] = [];
        acc[result.scenario].push(result);
        return acc;
      },
      {} as Record<string, RedisConcurrencyTestResult[]>
    );

    // 输出详细表格
    console.log(
      '场景     | 并发 | 成功率 | 平均响应 | P95响应 | P99响应 | RPS | 缓存命中率 | 缓存来源'
    );
    console.log(
      '---------|------|--------|----------|---------|---------|-----|------------|----------'
    );

    Object.entries(groupedResults).forEach(([scenario, scenarioResults]) => {
      scenarioResults.forEach((result) => {
        const scenarioName = scenario.padEnd(8);
        const concurrency = result.concurrency.toString().padStart(4);
        const successRate = `${result.successRate}%`.padStart(6);
        const avgTime = `${result.avgResponseTime}ms`.padStart(8);
        const p95Time = `${result.p95ResponseTime}ms`.padStart(7);
        const p99Time = `${result.p99ResponseTime}ms`.padStart(7);
        const rps = result.rps.toString().padStart(3);
        const hitRate = `${result.cacheHitRate}%`.padStart(10);
        const source = result.cacheSource.padEnd(10);

        console.log(
          `${scenarioName} | ${concurrency} | ${successRate} | ${avgTime} | ${p95Time} | ${p99Time} | ${rps} | ${hitRate} | ${source}`
        );
      });
    });

    // 性能分析
    this.performanceAnalysis(groupedResults);
  }

  /**
   * 性能分析和建议
   */
  private performanceAnalysis(groupedResults: Record<string, RedisConcurrencyTestResult[]>): void {
    console.log('\n🔍 性能分析:');

    Object.entries(groupedResults).forEach(([scenario, results]) => {
      console.log(`\n📈 ${scenario}场景分析:`);

      // 找出性能拐点
      let performanceDropPoint = null;
      for (let i = 1; i < results.length; i++) {
        const current = results[i];
        const previous = results[i - 1];

        if (current.avgResponseTime > previous.avgResponseTime * 1.5) {
          performanceDropPoint = current.concurrency;
          break;
        }
      }

      if (performanceDropPoint) {
        console.log(`   ⚠️ 性能拐点: ${performanceDropPoint} 并发`);
      } else {
        console.log(`   ✅ 在测试范围内性能表现稳定`);
      }

      // 目标达成情况
      const targetAchieved = results.filter(
        (r) => r.avgResponseTime <= this.config.targetResponseTime
      );
      const maxConcurrencyWithTarget =
        targetAchieved.length > 0 ? Math.max(...targetAchieved.map((r) => r.concurrency)) : 0;

      if (maxConcurrencyWithTarget >= 100) {
        console.log(
          `   🎯 目标达成: 支持${maxConcurrencyWithTarget}并发在${this.config.targetResponseTime}ms内`
        );
      } else {
        console.log(
          `   ❌ 目标未达成: 最大支持${maxConcurrencyWithTarget}并发在${this.config.targetResponseTime}ms内`
        );
      }

      // 缓存效果分析
      const avgCacheHitRate = results.reduce((sum, r) => sum + r.cacheHitRate, 0) / results.length;
      console.log(`   📊 平均缓存命中率: ${Math.round(avgCacheHitRate)}%`);
    });

    // 总体建议
    console.log('\n💡 优化建议:');
    const allResults = Object.values(groupedResults).flat();
    const highConcurrencyResults = allResults.filter((r) => r.concurrency >= 50);
    const avgHighConcurrencyTime =
      highConcurrencyResults.reduce((sum, r) => sum + r.avgResponseTime, 0) /
      highConcurrencyResults.length;

    if (avgHighConcurrencyTime > this.config.targetResponseTime) {
      console.log('🔴 高并发下响应时间过长，建议:');
      console.log('   - 优化Redis连接池配置');
      console.log('   - 增加数据库连接池大小');
      console.log('   - 优化数据库查询和索引');
      console.log('   - 考虑实现读写分离');
      console.log('   - 添加应用层缓存预热机制');
    } else {
      console.log('✅ 性能表现良好，已达到目标要求');
    }
  }
}

/**
 * 主函数 - 运行Redis高并发测试
 */
async function runRedisHighConcurrencyTests(): Promise<void> {
  const tester = new RedisHighConcurrencyTester();

  console.log('🎯 Redis高并发性能测试');
  console.log('目标: 100并发都能稳定在500ms内\n');

  const startTime = performance.now();

  try {
    await tester.runFullTestSuite();

    const totalTime = performance.now() - startTime;
    console.log(`\n🎉 测试完成! 总耗时: ${Math.round(totalTime / 1000)}秒`);
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
runRedisHighConcurrencyTests().catch(console.error);
