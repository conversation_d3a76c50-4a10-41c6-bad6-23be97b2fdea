/**
 * @fileoverview OAuth适配器测试套件
 *
 * 测试OAuth认证适配器，包括：
 * - WebOAuthAdapter: Web端弹窗OAuth
 * - ExtensionOAuthAdapter: 浏览器扩展OAuth
 * - MobileOAuthAdapter: 移动端OAuth
 * - OAuthAdapterFactory: OAuth适配器工厂
 * - UniversalOAuthHandler: 通用OAuth处理器
 * - OAuth工具函数
 * - 安全性和错误处理测试
 *
 * <AUTHOR> Dictionary Team
 * @version 1.0.0
 * @since 2024
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  OAuthResult,
  OAuthConfig,
  WebOAuthAdapter,
  ExtensionOAuthAdapter,
  MobileOAuthAdapter,
  OAuthAdapterFactory,
  UniversalOAuthHandler,
  isOAuthCallback,
  getOAuthCallbackParams,
  clearOAuthParams,
  OAUTH_PROVIDERS,
} from '../oauth';

// Mock window.open
const mockWindowOpen = vi.fn();

// Mock chrome.identity
const mockChromeIdentity = {
  launchWebAuthFlow: vi.fn(),
};

// Mock chrome.runtime
const mockChromeRuntime = {
  lastError: null,
  id: 'test-extension-id',
};

// Mock fetch
const mockFetch = vi.fn();

// Mock navigator
const mockNavigator = {
  userAgent: '',
};

// Mock crypto.getRandomValues
const mockCrypto = {
  getRandomValues: vi.fn(),
};

describe('OAuth Adapters', () => {
  beforeEach(() => {
    // Mock global objects
    global.window = {
      open: mockWindowOpen,
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      location: {
        origin: 'https://test.example.com',
        href: 'https://test.example.com/app',
        search: '',
      },
      history: {
        replaceState: vi.fn(),
      },
      screenX: 100,
      screenY: 100,
      outerWidth: 1200,
      outerHeight: 800,
      chrome: {
        identity: mockChromeIdentity,
        runtime: mockChromeRuntime,
      },
      navigator: mockNavigator,
      crypto: mockCrypto,
    } as any;

    global.fetch = mockFetch;
    global.URL = URL;
    global.URLSearchParams = URLSearchParams;

    // Reset mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('WebOAuthAdapter', () => {
    let adapter: WebOAuthAdapter;

    beforeEach(() => {
      adapter = new WebOAuthAdapter();
    });

    describe('弹窗登录', () => {
      it('应该打开OAuth弹窗并处理成功响应', async () => {
        const mockPopup = {
          closed: false,
          close: vi.fn(),
        };
        mockWindowOpen.mockReturnValue(mockPopup);

        const mockResult: OAuthResult = {
          accessToken: 'test-access-token',
          refreshToken: 'test-refresh-token',
          user: { id: '123', email: '<EMAIL>' },
        };

        // 模拟OAuth成功消息
        const messagePromise = adapter.login('google');

        // 模拟接收到成功消息
        setTimeout(() => {
          const messageEvent = new MessageEvent('message', {
            origin: 'https://test.example.com',
            data: {
              type: 'oauth-success',
              tokens: {
                accessToken: mockResult.accessToken,
                refreshToken: mockResult.refreshToken,
              },
              user: mockResult.user,
            },
          });

          // 触发消息事件处理器
          const addEventListener = window.addEventListener as any;
          const messageHandler = addEventListener.mock.calls.find(
            (call) => call[0] === 'message'
          )?.[1];

          if (messageHandler) {
            messageHandler(messageEvent);
          }
        }, 10);

        const result = await messagePromise;

        expect(mockWindowOpen).toHaveBeenCalledWith(
          '/api/auth/token/google',
          'oauth-google',
          expect.stringContaining('width=600')
        );
        expect(result).toEqual(mockResult);
        expect(mockPopup.close).toHaveBeenCalled();
      });

      it('应该处理OAuth错误响应', async () => {
        const mockPopup = {
          closed: false,
          close: vi.fn(),
        };
        mockWindowOpen.mockReturnValue(mockPopup);

        const messagePromise = adapter.login('github');

        setTimeout(() => {
          const messageEvent = new MessageEvent('message', {
            origin: 'https://test.example.com',
            data: {
              type: 'oauth-error',
              error: 'access_denied',
            },
          });

          const addEventListener = window.addEventListener as any;
          const messageHandler = addEventListener.mock.calls.find(
            (call) => call[0] === 'message'
          )?.[1];

          if (messageHandler) {
            messageHandler(messageEvent);
          }
        }, 10);

        await expect(messagePromise).rejects.toThrow('access_denied');
        expect(mockPopup.close).toHaveBeenCalled();
      });

      it('应该处理弹窗被阻止的情况', async () => {
        mockWindowOpen.mockReturnValue(null);

        await expect(adapter.login('google')).rejects.toThrow('Popup blocked');
      });

      it('应该处理用户取消的情况', async () => {
        const mockPopup = {
          closed: false,
          close: vi.fn(),
        };
        mockWindowOpen.mockReturnValue(mockPopup);

        const messagePromise = adapter.login('google');

        // 模拟用户关闭弹窗
        setTimeout(() => {
          mockPopup.closed = true;
        }, 500);

        await expect(messagePromise).rejects.toThrow('OAuth cancelled by user');
      });

      it('应该忽略来自其他域的消息', async () => {
        const mockPopup = {
          closed: false,
          close: vi.fn(),
        };
        mockWindowOpen.mockReturnValue(mockPopup);

        const messagePromise = adapter.login('google');

        setTimeout(() => {
          const maliciousEvent = new MessageEvent('message', {
            origin: 'https://malicious.com',
            data: {
              type: 'oauth-success',
              tokens: { accessToken: 'fake-token' },
            },
          });

          const addEventListener = window.addEventListener as any;
          const messageHandler = addEventListener.mock.calls.find(
            (call) => call[0] === 'message'
          )?.[1];

          if (messageHandler) {
            messageHandler(maliciousEvent);
          }

          // 应该继续等待，不被恶意消息影响
          expect(mockPopup.close).not.toHaveBeenCalled();

          // 发送正确的消息
          const validEvent = new MessageEvent('message', {
            origin: 'https://test.example.com',
            data: {
              type: 'oauth-success',
              tokens: { accessToken: 'valid-token' },
              user: { id: '123' },
            },
          });
          messageHandler(validEvent);
        }, 10);

        const result = await messagePromise;
        expect(result.accessToken).toBe('valid-token');
      });
    });

    describe('回调处理', () => {
      it('应该处理成功的OAuth回调', () => {
        const mockOpener = {
          postMessage: vi.fn(),
        };
        window.opener = mockOpener;

        const params = new URLSearchParams('code=auth_code_123&state=test_state');
        adapter.handleCallback(params);

        expect(mockOpener.postMessage).toHaveBeenCalledWith(
          {
            type: 'oauth-success',
            code: 'auth_code_123',
          },
          'https://test.example.com'
        );
      });

      it('应该处理OAuth错误回调', () => {
        const mockOpener = {
          postMessage: vi.fn(),
        };
        window.opener = mockOpener;

        const params = new URLSearchParams('error=access_denied&error_description=User denied');
        adapter.handleCallback(params);

        expect(mockOpener.postMessage).toHaveBeenCalledWith(
          {
            type: 'oauth-error',
            error: 'access_denied',
          },
          'https://test.example.com'
        );
      });

      it('应该处理缺少授权码的情况', () => {
        const mockOpener = {
          postMessage: vi.fn(),
        };
        window.opener = mockOpener;

        const params = new URLSearchParams('state=test_state');
        adapter.handleCallback(params);

        expect(mockOpener.postMessage).toHaveBeenCalledWith(
          {
            type: 'oauth-error',
            error: 'No authorization code received',
          },
          'https://test.example.com'
        );
      });

      it('应该使用parent窗口作为fallback', () => {
        window.opener = null;
        const mockParent = {
          postMessage: vi.fn(),
        };
        window.parent = mockParent;

        const params = new URLSearchParams('code=auth_code_123');
        adapter.handleCallback(params);

        expect(mockParent.postMessage).toHaveBeenCalledWith(
          {
            type: 'oauth-success',
            code: 'auth_code_123',
          },
          'https://test.example.com'
        );
      });
    });

    describe('弹窗位置计算', () => {
      it('应该正确计算弹窗居中位置', () => {
        adapter.login('google').catch(() => {}); // 忽略promise rejection

        const expectedLeft = 100 + (1200 - 600) / 2; // 400
        const expectedTop = 100 + (800 - 600) / 2; // 200

        expect(mockWindowOpen).toHaveBeenCalledWith(
          '/api/auth/token/google',
          'oauth-google',
          `width=600,height=600,left=${expectedLeft},top=${expectedTop},scrollbars=yes,resizable=yes`
        );
      });
    });
  });

  describe('ExtensionOAuthAdapter', () => {
    let adapter: ExtensionOAuthAdapter;

    beforeEach(() => {
      adapter = new ExtensionOAuthAdapter();
    });

    describe('扩展环境检测', () => {
      it('应该在非扩展环境中抛出错误', async () => {
        window.chrome = undefined;

        await expect(adapter.login('google')).rejects.toThrow(
          'Extension OAuth adapter can only be used in extension environment'
        );
      });

      it('应该在缺少chrome.identity时抛出错误', async () => {
        window.chrome = {
          runtime: mockChromeRuntime,
        } as any;

        await expect(adapter.login('google')).rejects.toThrow(
          'Extension OAuth adapter can only be used in extension environment'
        );
      });
    });

    describe('扩展OAuth流程', () => {
      beforeEach(() => {
        mockFetch.mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({
            url: 'https://accounts.google.com/oauth/authorize?client_id=test',
          }),
        });
      });

      it('应该成功处理扩展OAuth登录', async () => {
        const redirectUrl =
          'https://test.example.com/oauth/callback?access_token=test_token&refresh_token=refresh_token';

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback(redirectUrl);
        });

        const result = await adapter.login('google');

        expect(mockFetch).toHaveBeenCalledWith('/api/auth/oauth/google/url', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sessionId: expect.stringMatching(/^ext-\d+-[a-z0-9]+$/),
          }),
        });

        expect(mockChromeIdentity.launchWebAuthFlow).toHaveBeenCalledWith(
          {
            url: 'https://accounts.google.com/oauth/authorize?client_id=test',
            interactive: true,
          },
          expect.any(Function)
        );

        expect(result).toEqual({
          accessToken: 'test_token',
          refreshToken: 'refresh_token',
        });
      });

      it('应该处理扩展OAuth错误', async () => {
        mockChromeRuntime.lastError = { message: 'User cancelled' };

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback(null);
        });

        await expect(adapter.login('github')).rejects.toThrow('User cancelled');
      });

      it('应该处理取消的OAuth流程', async () => {
        mockChromeRuntime.lastError = null;

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback(null);
        });

        await expect(adapter.login('google')).rejects.toThrow('OAuth cancelled');
      });

      it('应该处理重定向URL中的错误', async () => {
        const redirectUrl = 'https://test.example.com/oauth/callback?error=access_denied';

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback(redirectUrl);
        });

        await expect(adapter.login('google')).rejects.toThrow('access_denied');
      });

      it('应该处理缺少访问令牌的情况', async () => {
        const redirectUrl = 'https://test.example.com/oauth/callback';

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback(redirectUrl);
        });

        await expect(adapter.login('google')).rejects.toThrow('No access token received');
      });

      it('应该处理获取Auth URL失败', async () => {
        mockFetch.mockResolvedValue({
          ok: false,
          status: 500,
        });

        await expect(adapter.login('google')).rejects.toThrow('Failed to get google auth URL');
      });
    });

    describe('会话ID生成', () => {
      it('应该生成唯一的会话ID', async () => {
        mockFetch.mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({ url: 'test-url' }),
        });

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback('https://test.com?access_token=token');
        });

        await adapter.login('google');
        await adapter.login('github');

        const calls = mockFetch.mock.calls;
        const sessionId1 = JSON.parse(calls[0][1].body).sessionId;
        const sessionId2 = JSON.parse(calls[1][1].body).sessionId;

        expect(sessionId1).toMatch(/^ext-\d+-[a-z0-9]+$/);
        expect(sessionId2).toMatch(/^ext-\d+-[a-z0-9]+$/);
        expect(sessionId1).not.toBe(sessionId2);
      });
    });
  });

  describe('MobileOAuthAdapter', () => {
    let adapter: MobileOAuthAdapter;

    beforeEach(() => {
      adapter = new MobileOAuthAdapter();
    });

    describe('移动端OAuth', () => {
      it('应该在移动Web环境中重定向到授权URL', async () => {
        mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)';

        mockFetch.mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({
            url: 'https://accounts.google.com/oauth/authorize?client_id=mobile',
          }),
        });

        // Mock window.location.href setter
        let redirectUrl = '';
        Object.defineProperty(window.location, 'href', {
          set: (url) => {
            redirectUrl = url;
          },
          get: () => redirectUrl,
        });

        const loginPromise = adapter.login('google');

        // 等待异步操作完成
        await new Promise((resolve) => setTimeout(resolve, 10));

        expect(mockFetch).toHaveBeenCalledWith('/api/auth/oauth/google/url', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            sessionId: expect.stringMatching(/^mobile-\d+-[a-z0-9]+$/),
          }),
        });

        expect(redirectUrl).toBe('https://accounts.google.com/oauth/authorize?client_id=mobile');

        // Promise永远不会resolve，因为页面重定向了
        // 这里我们不等待Promise完成
      });

      it('应该在非移动Web环境中抛出错误', async () => {
        mockNavigator.userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)';

        await expect(adapter.login('google')).rejects.toThrow(
          'Native mobile OAuth not implemented in web context'
        );
      });

      it('应该处理获取Auth URL失败', async () => {
        mockNavigator.userAgent = 'Mozilla/5.0 (Android 11; Mobile)';

        mockFetch.mockResolvedValue({
          ok: false,
          status: 400,
        });

        await expect(adapter.login('google')).rejects.toThrow('Failed to get google auth URL');
      });
    });

    describe('回调处理', () => {
      it('应该处理成功的OAuth回调', () => {
        const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

        const params = new URLSearchParams('code=mobile_auth_code');
        adapter.handleCallback(params);

        expect(consoleSpy).toHaveBeenCalledWith('Mobile OAuth code received:', 'mobile_auth_code');

        consoleSpy.mockRestore();
      });

      it('应该处理OAuth错误回调', () => {
        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        const params = new URLSearchParams('error=user_cancelled');
        adapter.handleCallback(params);

        expect(consoleSpy).toHaveBeenCalledWith('OAuth error:', 'user_cancelled');

        consoleSpy.mockRestore();
      });
    });

    describe('会话ID生成', () => {
      it('应该生成移动端特定的会话ID', async () => {
        mockNavigator.userAgent = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)';

        mockFetch.mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({ url: 'test-url' }),
        });

        Object.defineProperty(window.location, 'href', {
          set: () => {},
          get: () => '',
        });

        adapter.login('google').catch(() => {}); // 忽略promise

        await new Promise((resolve) => setTimeout(resolve, 10));

        const sessionId = JSON.parse(mockFetch.mock.calls[0][1].body).sessionId;
        expect(sessionId).toMatch(/^mobile-\d+-[a-z0-9]+$/);
      });
    });
  });

  describe('OAuthAdapterFactory', () => {
    it('应该为Web环境创建WebOAuthAdapter', () => {
      vi.doMock('./storage', () => ({
        PlatformDetector: {
          platform: 'web',
        },
      }));

      const adapter = OAuthAdapterFactory.createAdapter();
      expect(adapter).toBeInstanceOf(WebOAuthAdapter);
    });

    it('应该为扩展环境创建ExtensionOAuthAdapter', () => {
      vi.doMock('./storage', () => ({
        PlatformDetector: {
          platform: 'extension',
        },
      }));

      const adapter = OAuthAdapterFactory.createAdapter();
      expect(adapter).toBeInstanceOf(ExtensionOAuthAdapter);
    });

    it('应该为移动环境创建MobileOAuthAdapter', () => {
      vi.doMock('./storage', () => ({
        PlatformDetector: {
          platform: 'mobile',
        },
      }));

      const adapter = OAuthAdapterFactory.createAdapter();
      expect(adapter).toBeInstanceOf(MobileOAuthAdapter);
    });

    it('应该为未知环境创建WebOAuthAdapter', () => {
      vi.doMock('./storage', () => ({
        PlatformDetector: {
          platform: 'unknown',
        },
      }));

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      const adapter = OAuthAdapterFactory.createAdapter();

      expect(adapter).toBeInstanceOf(WebOAuthAdapter);
      expect(consoleSpy).toHaveBeenCalledWith('Unknown platform, falling back to web OAuth');

      consoleSpy.mockRestore();
    });
  });

  describe('UniversalOAuthHandler', () => {
    let handler: UniversalOAuthHandler;

    beforeEach(() => {
      handler = new UniversalOAuthHandler();
    });

    describe('通用登录处理', () => {
      it('应该委托给适配器处理登录', async () => {
        const mockAdapter = {
          login: vi.fn().mockResolvedValue({
            accessToken: 'universal-token',
            user: { id: '123' },
          }),
          handleCallback: vi.fn(),
        };

        // 替换适配器
        (handler as any).adapter = mockAdapter;

        const result = await handler.login('google');

        expect(mockAdapter.login).toHaveBeenCalledWith('google');
        expect(result).toEqual({
          accessToken: 'universal-token',
          user: { id: '123' },
        });
      });

      it('应该处理适配器登录错误', async () => {
        const mockAdapter = {
          login: vi.fn().mockRejectedValue(new Error('Adapter error')),
          handleCallback: vi.fn(),
        };

        (handler as any).adapter = mockAdapter;

        const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

        await expect(handler.login('github')).rejects.toThrow('Adapter error');
        expect(consoleSpy).toHaveBeenCalledWith(
          'OAuth login failed for github:',
          expect.any(Error)
        );

        consoleSpy.mockRestore();
      });
    });

    describe('回调处理', () => {
      it('应该委托给适配器处理回调', () => {
        const mockAdapter = {
          login: vi.fn(),
          handleCallback: vi.fn(),
        };

        (handler as any).adapter = mockAdapter;

        const params = new URLSearchParams('code=test_code');
        handler.handleCallback(params);

        expect(mockAdapter.handleCallback).toHaveBeenCalledWith(params);
      });

      it('应该处理没有handleCallback方法的适配器', () => {
        const mockAdapter = {
          login: vi.fn(),
        };

        (handler as any).adapter = mockAdapter;

        const params = new URLSearchParams('code=test_code');

        expect(() => handler.handleCallback(params)).not.toThrow();
      });
    });

    describe('提供商配置', () => {
      it('应该返回Google的配置', () => {
        const config = UniversalOAuthHandler.getProviderConfig('google');

        expect(config).toEqual({
          provider: 'google',
          scope: 'openid email profile',
        });
      });

      it('应该返回GitHub的配置', () => {
        const config = UniversalOAuthHandler.getProviderConfig('github');

        expect(config).toEqual({
          provider: 'github',
          scope: 'user:email',
        });
      });
    });

    describe('CSRF保护 (已弃用功能)', () => {
      it('应该生成CSRF状态并显示弃用警告', () => {
        mockCrypto.getRandomValues.mockImplementation((array) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = i % 256;
          }
          return array;
        });

        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        const state = UniversalOAuthHandler.generateState();

        expect(consoleSpy).toHaveBeenCalledWith(
          '[DEPRECATED] UniversalOAuthHandler.generateState() is deprecated. Use oauthCSRF.generateOAuthState() instead.'
        );
        expect(state).toMatch(/^[0-9a-f]{64}$/);

        consoleSpy.mockRestore();
      });

      it('应该验证CSRF状态并显示弃用警告', () => {
        const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

        const result1 = UniversalOAuthHandler.validateState('test-state', 'test-state');
        const result2 = UniversalOAuthHandler.validateState('state1', 'state2');

        expect(consoleSpy).toHaveBeenCalledWith(
          '[DEPRECATED] UniversalOAuthHandler.validateState() is deprecated. Use oauthCSRF.validateOAuthState() instead.'
        );
        expect(result1).toBe(true);
        expect(result2).toBe(false);

        consoleSpy.mockRestore();
      });
    });
  });

  describe('OAuth工具函数', () => {
    describe('isOAuthCallback', () => {
      it('应该检测OAuth回调URL', () => {
        window.location.search = '?code=auth_code_123&state=test_state';
        expect(isOAuthCallback()).toBe(true);

        window.location.search = '?error=access_denied';
        expect(isOAuthCallback()).toBe(true);

        window.location.search = '?foo=bar';
        expect(isOAuthCallback()).toBe(false);

        window.location.search = '';
        expect(isOAuthCallback()).toBe(false);
      });

      it('应该在服务器端环境返回false', () => {
        const originalWindow = global.window;
        delete (global as any).window;

        expect(isOAuthCallback()).toBe(false);

        global.window = originalWindow;
      });
    });

    describe('getOAuthCallbackParams', () => {
      it('应该返回OAuth回调参数', () => {
        window.location.search = '?code=auth_code_123&state=test_state&foo=bar';

        const params = getOAuthCallbackParams();

        expect(params.get('code')).toBe('auth_code_123');
        expect(params.get('state')).toBe('test_state');
        expect(params.get('foo')).toBe('bar');
      });

      it('应该在服务器端环境返回空参数', () => {
        const originalWindow = global.window;
        delete (global as any).window;

        const params = getOAuthCallbackParams();

        expect(params.toString()).toBe('');

        global.window = originalWindow;
      });
    });

    describe('clearOAuthParams', () => {
      it('应该清除OAuth相关的URL参数', () => {
        window.location.href =
          'https://test.example.com/app?code=123&state=abc&error=denied&other=keep';

        clearOAuthParams();

        expect(window.history.replaceState).toHaveBeenCalledWith(
          {},
          '',
          'https://test.example.com/app?other=keep'
        );
      });

      it('应该在服务器端环境中静默处理', () => {
        const originalWindow = global.window;
        delete (global as any).window;

        expect(() => clearOAuthParams()).not.toThrow();

        global.window = originalWindow;
      });
    });
  });

  describe('OAuth提供商配置', () => {
    it('应该包含Google配置', () => {
      const googleConfig = OAUTH_PROVIDERS.google;

      expect(googleConfig).toEqual({
        name: 'Google',
        icon: 'google',
        color: '#4285f4',
        scopes: {
          web: ['openid', 'email', 'profile'],
          extension: ['openid', 'email', 'profile'],
          mobile: ['openid', 'email', 'profile'],
        },
      });
    });

    it('应该包含GitHub配置', () => {
      const githubConfig = OAUTH_PROVIDERS.github;

      expect(githubConfig).toEqual({
        name: 'GitHub',
        icon: 'github',
        color: '#333',
        scopes: {
          web: ['user:email'],
          extension: ['user:email'],
          mobile: ['user:email'],
        },
      });
    });
  });

  describe('安全性测试', () => {
    describe('跨域消息验证', () => {
      it('应该验证消息来源域', async () => {
        const adapter = new WebOAuthAdapter();
        const mockPopup = {
          closed: false,
          close: vi.fn(),
        };
        mockWindowOpen.mockReturnValue(mockPopup);

        const loginPromise = adapter.login('google');

        // 发送来自恶意域的消息
        setTimeout(() => {
          const maliciousEvent = new MessageEvent('message', {
            origin: 'https://evil.com',
            data: {
              type: 'oauth-success',
              tokens: { accessToken: 'stolen-token' },
            },
          });

          const addEventListener = window.addEventListener as any;
          const messageHandler = addEventListener.mock.calls.find(
            (call) => call[0] === 'message'
          )?.[1];

          if (messageHandler) {
            messageHandler(maliciousEvent);
          }

          // 弹窗不应该被关闭
          expect(mockPopup.close).not.toHaveBeenCalled();

          // 取消OAuth以结束测试
          mockPopup.closed = true;
        }, 10);

        await expect(loginPromise).rejects.toThrow('OAuth cancelled by user');
      });
    });

    describe('URL参数注入防护', () => {
      it('应该安全处理恶意重定向URL', async () => {
        const adapter = new ExtensionOAuthAdapter();

        mockFetch.mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({ url: 'test-url' }),
        });

        const maliciousUrl =
          'https://test.example.com/oauth/callback?access_token=token&javascript:alert(1)=value';

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback(maliciousUrl);
        });

        const result = await adapter.login('google');

        // 应该正常解析访问令牌，忽略恶意参数
        expect(result.accessToken).toBe('token');
      });
    });

    describe('会话固定攻击防护', () => {
      it('应该为每次OAuth请求生成唯一会话ID', async () => {
        const adapter = new ExtensionOAuthAdapter();

        mockFetch.mockResolvedValue({
          ok: true,
          json: vi.fn().mockResolvedValue({ url: 'test-url' }),
        });

        mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
          callback('https://test.com?access_token=token');
        });

        await adapter.login('google');
        await adapter.login('google');

        const sessionIds = mockFetch.mock.calls.map((call) => JSON.parse(call[1].body).sessionId);

        expect(sessionIds[0]).not.toBe(sessionIds[1]);
        expect(sessionIds[0]).toMatch(/^ext-\d+-[a-z0-9]+$/);
        expect(sessionIds[1]).toMatch(/^ext-\d+-[a-z0-9]+$/);
      });
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理网络请求失败', async () => {
      const adapter = new ExtensionOAuthAdapter();

      mockFetch.mockRejectedValue(new Error('Network error'));

      await expect(adapter.login('google')).rejects.toThrow('Extension OAuth failed');
    });

    it('应该处理无效的URL响应', async () => {
      const adapter = new ExtensionOAuthAdapter();

      mockFetch.mockResolvedValue({
        ok: true,
        json: vi.fn().mockResolvedValue({}), // 缺少url字段
      });

      mockChromeIdentity.launchWebAuthFlow.mockImplementation((options, callback) => {
        callback('https://test.com?access_token=token');
      });

      // 应该继续执行，即使URL响应无效
      await expect(adapter.login('google')).resolves.toBeDefined();
    });

    it('应该处理空的弹窗响应', async () => {
      const adapter = new WebOAuthAdapter();
      const mockPopup = {
        closed: false,
        close: vi.fn(),
      };
      mockWindowOpen.mockReturnValue(mockPopup);

      const loginPromise = adapter.login('google');

      setTimeout(() => {
        const emptyEvent = new MessageEvent('message', {
          origin: 'https://test.example.com',
          data: null,
        });

        const addEventListener = window.addEventListener as any;
        const messageHandler = addEventListener.mock.calls.find(
          (call) => call[0] === 'message'
        )?.[1];

        if (messageHandler) {
          messageHandler(emptyEvent);
        }

        // 应该继续等待有效消息
        expect(mockPopup.close).not.toHaveBeenCalled();

        // 关闭弹窗结束测试
        mockPopup.closed = true;
      }, 10);

      await expect(loginPromise).rejects.toThrow('OAuth cancelled by user');
    });
  });

  describe('性能测试', () => {
    it('应该高效处理多个并发OAuth请求', async () => {
      const adapter = new WebOAuthAdapter();

      // Mock多个弹窗
      const mockPopups = Array.from({ length: 5 }, () => ({
        closed: false,
        close: vi.fn(),
      }));

      let popupIndex = 0;
      mockWindowOpen.mockImplementation(() => mockPopups[popupIndex++]);

      const loginPromises = Array.from({ length: 5 }, (_, i) =>
        adapter.login('google').catch(() => ({ index: i }))
      );

      // 同时关闭所有弹窗
      setTimeout(() => {
        mockPopups.forEach((popup) => {
          popup.closed = true;
        });
      }, 50);

      const results = await Promise.allSettled(loginPromises);

      expect(results).toHaveLength(5);
      results.forEach((result) => {
        expect(result.status).toBe('rejected');
      });
    });

    it('应该及时清理事件监听器', async () => {
      const adapter = new WebOAuthAdapter();
      const mockPopup = {
        closed: false,
        close: vi.fn(),
      };
      mockWindowOpen.mockReturnValue(mockPopup);

      const loginPromise = adapter.login('google');

      setTimeout(() => {
        const successEvent = new MessageEvent('message', {
          origin: 'https://test.example.com',
          data: {
            type: 'oauth-success',
            tokens: { accessToken: 'token' },
            user: { id: '123' },
          },
        });

        const addEventListener = window.addEventListener as any;
        const messageHandler = addEventListener.mock.calls.find(
          (call) => call[0] === 'message'
        )?.[1];

        if (messageHandler) {
          messageHandler(successEvent);
        }
      }, 10);

      await loginPromise;

      // 验证事件监听器被移除
      expect(window.removeEventListener).toHaveBeenCalledWith('message', expect.any(Function));
    });
  });
});
