import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { importFromDictionaryData } from '../lib/6-scripts/llm-response-to-db/importDictionaryData.js';
import type { DictionaryResponseWithWordCountType } from '../lib/utils/dict-types';

describe('WordFormat Duplicate Creation Fix', () => {
  const prismaClient = new PrismaClient();

  beforeEach(async () => {
    // 清理测试数据
    await prismaClient.definition.deleteMany();
    await prismaClient.explain.deleteMany();
    await prismaClient.wordFormat.deleteMany();
    await prismaClient.vocabulary.deleteMany();
  });

  afterEach(async () => {
    // 清理测试数据
    await prismaClient.definition.deleteMany();
    await prismaClient.explain.deleteMany();
    await prismaClient.wordFormat.deleteMany();
    await prismaClient.vocabulary.deleteMany();
  });

  it('should not create duplicate WordFormat records for the same vocabulary', async () => {
    // 创建测试数据：banana和bananas互相引用的情况
    const testData: DictionaryResponseWithWordCountType = {
      words: [
        {
          word: 'banana',
          explain: [
            {
              pos: 'noun',
              definitions: [
                {
                  definition: 'A long curved fruit',
                  chinese: '香蕉',
                  chinese_short: '香蕉',
                },
              ],
            },
          ],
          wordFormats: [
            {
              name: '复数',
              form: 'bananas',
            },
          ],
          phonetic: {
            us: '/bəˈnænə/',
            uk: '/bəˈnɑːnə/',
          },
        },
        {
          word: 'bananas',
          explain: [
            {
              pos: 'noun',
              definitions: [
                {
                  definition: 'Plural of banana',
                  chinese: '香蕉（复数）',
                  chinese_short: '香蕉',
                },
              ],
            },
          ],
          wordFormats: [
            {
              name: '原型',
              form: 'banana',
            },
          ],
          phonetic: {
            us: '/bəˈnænəz/',
            uk: '/bəˈnɑːnəz/',
          },
        },
      ],
      wordCount: 2,
      wordList: ['banana', 'bananas'],
    };

    // 执行导入
    const result = await importFromDictionaryData(testData, prismaClient);

    // 验证导入结果
    expect(result.successCount).toBe(2);
    expect(result.failureCount).toBe(0);
    expect(result.errors).toHaveLength(0);

    // 验证Vocabulary记录
    const vocabularies = await prismaClient.vocabulary.findMany({
      orderBy: { word: 'asc' },
    });
    expect(vocabularies).toHaveLength(2);
    expect(vocabularies[0].word).toBe('banana');
    expect(vocabularies[1].word).toBe('bananas');

    // 验证WordFormat记录 - 每个vocabulary应该只有一个直接关联的WordFormat
    const wordFormats = await prismaClient.wordFormat.findMany({
      where: {
        vocabularyId: { not: null },
      },
      include: {
        vocabulary: true,
      },
      orderBy: { vocabularyId: 'asc' },
    });

    expect(wordFormats).toHaveLength(2); // 每个vocabulary一个
    expect(wordFormats[0].vocabulary?.word).toBe('banana');
    expect(wordFormats[1].vocabulary?.word).toBe('bananas');

    // 验证派生形式记录
    const derivedFormats = await prismaClient.wordFormat.findMany({
      where: {
        vocabularyId: null,
        baseFormId: { not: null },
      },
    });

    expect(derivedFormats.length).toBeGreaterThan(0); // 应该有派生形式
  });

  it('should skip self-referencing word forms to avoid duplicates', async () => {
    // 测试words1.json中的情况：bananas的wordFormats包含自己
    const testData: DictionaryResponseWithWordCountType = {
      words: [
        {
          word: 'bananas',
          explain: [
            {
              pos: 'noun',
              definitions: [
                {
                  definition: 'The plural form of banana',
                  chinese: '香蕉（复数）',
                  chinese_short: '香蕉',
                },
              ],
            },
          ],
          wordFormats: [
            {
              name: '原型',
              form: 'banana',
            },
            {
              name: '复数',
              form: 'bananas', // 这个与主词条相同，应该被跳过
            },
          ],
          phonetic: {
            us: '/bəˈnænəz/',
            uk: '/bəˈnɑːnəz/',
          },
        },
      ],
      wordCount: 1,
      wordList: ['bananas'],
    };

    // 执行导入
    const result = await importFromDictionaryData(testData, prismaClient);

    // 验证导入结果
    expect(result.successCount).toBe(1);
    expect(result.failureCount).toBe(0);
    expect(result.errors).toHaveLength(0);

    // 验证只有一个Vocabulary记录
    const vocabularies = await prismaClient.vocabulary.findMany();
    expect(vocabularies).toHaveLength(1);
    expect(vocabularies[0].word).toBe('bananas');

    // 验证只有一个主WordFormat记录（直接关联Vocabulary）
    const mainWordFormats = await prismaClient.wordFormat.findMany({
      where: { vocabularyId: { not: null } },
    });
    expect(mainWordFormats).toHaveLength(1);
    expect(mainWordFormats[0].form).toBe('bananas');
    expect(mainWordFormats[0].name).toBe('原型');

    // 验证只有一个派生WordFormat记录（banana，不包括自引用的bananas）
    const derivedFormats = await prismaClient.wordFormat.findMany({
      where: { vocabularyId: null, baseFormId: { not: null } },
    });
    expect(derivedFormats).toHaveLength(1);
    expect(derivedFormats[0].form).toBe('banana');
    expect(derivedFormats[0].name).toBe('原型');
  });

  it('should handle multiple imports without creating duplicates', async () => {
    const testData: DictionaryResponseWithWordCountType = {
      words: [
        {
          word: 'apple',
          explain: [
            {
              pos: 'noun',
              definitions: [
                {
                  definition: 'A round fruit',
                  chinese: '苹果',
                  chinese_short: '苹果',
                },
              ],
            },
          ],
          wordFormats: [
            {
              name: '复数',
              form: 'apples',
            },
          ],
          phonetic: {
            us: '/ˈæpəl/',
            uk: '/ˈæpəl/',
          },
        },
      ],
      wordCount: 1,
      wordList: ['apple'],
    };

    // 第一次导入
    const result1 = await importFromDictionaryData(testData, prismaClient);
    expect(result1.successCount).toBe(1);
    expect(result1.failureCount).toBe(0);

    // 第二次导入相同数据
    const result2 = await importFromDictionaryData(testData, prismaClient);
    expect(result2.successCount).toBe(1); // 应该跳过已存在的记录
    expect(result2.failureCount).toBe(0);

    // 验证只有一个vocabulary记录
    const vocabularies = await prismaClient.vocabulary.findMany();
    expect(vocabularies).toHaveLength(1);

    // 验证只有一个主WordFormat记录
    const mainWordFormats = await prismaClient.wordFormat.findMany({
      where: { vocabularyId: { not: null } },
    });
    expect(mainWordFormats).toHaveLength(1);
  });
});
