#!/usr/bin/env tsx

/**
 * 从SUBTLEX频率词汇表中读取前100个单词，并行调用词典API，导入数据库并设置频率排名
 * 1. 并行API调用 (5个并发) → 2. 合并结果 → 3. 统一数据库写入 → 4. 设置频率排名
 *
 * 功能：
 * 1. 从Excel文件读取前100个单词
 * 2. 并行调用Gemini词典API（初始2个并行请求）
 * 3. 导入数据到数据库，设置频率排名
 * 4. 提供详细的日志记录和错误处理
 */

import { readFile, mkdir, appendFile, writeFile, access } from 'fs/promises';
import { join, dirname } from 'path';
import XLSX from 'xlsx';
import { PrismaClient } from '@prisma/client';
import { fetchDictionary } from '../../5-providers/gemini.js';
import { importFromDictionaryData } from '../llm-response-to-db/importDictionaryData.js';
import type { DictionaryQueryResult } from '../../utils/dict-types.js';

// 配置常量
const EXCEL_FILE_PATH = './SUBTLEXusfrequencyabove1.xls';
const WORDS_TO_EXTRACT = 60000; // 总共提取60000个单词
const PARALLEL_REQUESTS = 4; // 并发处理5个请求 目前稳定的方案
const WORDS_PER_REQUEST = 100; // 每个请求处理100个单词

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-26 18:50:00 +08:00]
// Reason: 添加分批处理配置，支持大批量数据的分批处理和断点续传
// Principle_Applied: KISS (简单配置), YAGNI (按需配置), Single Responsibility (分离配置关注点)
// Optimization: 分批处理减少内存压力，提高处理稳定性
// Architectural_Note (AR): 引入批次管理架构，支持大规模数据处理
// Documentation_Note (DW): 新增批次处理配置和进度管理功能
// }}
// 分批处理配置
const BATCH_SIZE = 5000; // 每批次处理5000个单词
const BATCH_DELAY = 30000; // 批次间延迟30秒，避免API限制
const PROGRESS_FILE_PATH = './log/batch-progress.json'; // 进度文件路径

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-27 14:30:00 +08:00]
// Reason: 添加增量导入配置，支持跳过已存在的单词，减少重复处理
// Principle_Applied: KISS (简单配置), YAGNI (按需配置), Efficiency (避免重复工作)
// Optimization: 跳过已存在单词，减少API调用次数和数据库写入
// Architectural_Note (AR): 增量处理策略，提高导入效率
// Documentation_Note (DW): 新增增量导入功能，支持智能跳过已存在记录
// }}
// 增量导入配置
const SKIP_EXISTING = true; // 是否跳过已存在的单词

// 初始化Prisma客户端
const prismaClient = new PrismaClient();

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-27 01:00:00 +08:00]
// Reason: 根据用户请求，实现将脚本的控制台输出重定向到带时间戳的日志文件。
// Principle_Applied: Observability (持久化脚本运行日志以供分析), KISS (手动实现，避免额外依赖)
// Optimization: N/A
// Architectural_Note (AR): 改进脚本的可维护性和问题追溯能力。
// Documentation_Note (DW): 新增日志文件存储功能。
// }}
let logFilePathGlobal: string | null = null;

async function setupFileLogger() {
  const logDir = join(process.cwd(), 'log');
  try {
    await mkdir(logDir, { recursive: true });
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    logFilePathGlobal = join(logDir, `sync-run-${timestamp}.log`);
    console.log(`📝 所有日志将被记录到: ${logFilePathGlobal}`);

    const originalConsoleLog = console.log;
    const originalConsoleWarn = console.warn;
    const originalConsoleError = console.error;
    const originalConsoleInfo = console.info;
    const originalConsoleDebug = console.debug;

    const appendToLog = async (level: string, ...args: any[]) => {
      if (!logFilePathGlobal) return;
      const message = args
        .map((arg) => (typeof arg === 'string' ? arg : JSON.stringify(arg, null, 2)))
        .join(' ');
      const logMessage = `${new Date().toISOString()} [${level.toUpperCase()}] ${message}\n`;
      try {
        await appendFile(logFilePathGlobal, logMessage);
      } catch (err) {
        originalConsoleError('无法写入日志文件:', err);
      }
    };

    console.log = async (...args: any[]) => {
      originalConsoleLog(...args);
      await appendToLog('log', ...args);
    };
    console.warn = async (...args: any[]) => {
      originalConsoleWarn(...args);
      await appendToLog('warn', ...args);
    };
    console.error = async (...args: any[]) => {
      originalConsoleError(...args);
      await appendToLog('error', ...args);
    };
    console.info = async (...args: any[]) => {
      originalConsoleInfo(...args);
      await appendToLog('info', ...args);
    };
    console.debug = async (...args: any[]) => {
      originalConsoleDebug(...args);
      await appendToLog('debug', ...args);
    };
  } catch (error) {
    console.error('❌ 初始化文件日志记录器失败:', error);
    // 如果日志设置失败，脚本仍应继续，但日志仅输出到控制台
  }
}

/**
 * 从Excel文件中读取并提取前N个单词
 * @param filePath Excel文件路径
 * @param count 要提取的单词数量
 * @returns 单词数组
 */
async function extractWordsFromExcel(filePath: string, count: number): Promise<string[]> {
  try {
    console.log(`📁 正在读取Excel文件: ${filePath}`);

    // 读取Excel文件
    const workbook = XLSX.readFile(filePath);
    const sheetName = workbook.SheetNames[0]; // 使用第一个工作表
    const worksheet = workbook.Sheets[sheetName];

    console.log(`📊 工作表名称: ${sheetName}`);

    // 将工作表转换为JSON数组
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (!Array.isArray(jsonData) || jsonData.length === 0) {
      throw new Error('Excel文件为空或格式不正确');
    }

    console.log(`📋 Excel文件总行数: ${jsonData.length}`);

    // 提取单词（假设单词在第一列，跳过标题行）
    const words: string[] = [];
    let extractedCount = 0;

    // 从第二行开始（跳过标题行）
    for (let i = 1; i < jsonData.length && extractedCount < count; i++) {
      const row = jsonData[i] as any[];
      if (row && row[0] && typeof row[0] === 'string') {
        const word = row[0].trim().toLowerCase();
        if (word && /^[a-zA-Z]+$/.test(word)) {
          // 只接受纯字母单词
          words.push(word);
          extractedCount++;
        }
      }
    }

    console.log(`✅ 成功提取 ${words.length} 个单词`);
    console.log(`🔤 前10个单词: ${words.slice(0, 10).join(', ')}`);

    return words;
  } catch (error) {
    console.error('❌ 读取Excel文件失败:', error);
    throw new Error(`无法读取Excel文件: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 错误信息分析结果接口
 */
interface ErrorAnalysis {
  type: string;
  message: string;
  suggestion: string;
  details?: any;
}

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-26 18:52:00 +08:00]
// Reason: 添加批次进度管理接口，支持断点续传和进度跟踪
// Principle_Applied: SOLID (Interface Segregation), KISS (简单接口设计)
// Optimization: 结构化进度数据，便于状态管理和恢复
// Architectural_Note (AR): 定义批次处理的核心数据结构
// Documentation_Note (DW): 新增进度管理相关接口定义
// }}
/**
 * 批次进度信息接口
 */
interface BatchProgress {
  totalBatches: number;
  completedBatches: number;
  currentBatch: number;
  startTime: string;
  lastUpdateTime: string;
  batchResults: BatchResult[];
  totalWordsProcessed: number;
  totalWordsSuccessful: number;
  totalWordsFailed: number;
}

/**
 * 单个批次结果接口
 */
interface BatchResult {
  batchIndex: number;
  startIndex: number;
  endIndex: number;
  wordsCount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  successCount: number;
  failureCount: number;
  apiCallsCount: number;
  errorMessage?: string;
}

/**
 * 分析错误并提供详细信息和建议
 * @param error 错误对象
 * @returns 错误分析结果
 */
function analyzeError(error: any): ErrorAnalysis {
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-05-26 23:57:00 +08:00]
  // Reason: 添加错误分析函数，提供更详细的错误信息和解决建议
  // Principle_Applied: KISS (简单直接的错误分类), Single Responsibility (专门的错误分析函数)
  // Optimization: 结构化错误信息，便于问题诊断和解决
  // Architectural_Note (AR): 提高系统可观测性和可维护性
  // Documentation_Note (DW): 新增错误分析功能，改善调试体验
  // }}

  if (!error) {
    return {
      type: '未知错误',
      message: '错误对象为空',
      suggestion: '检查代码逻辑',
    };
  }

  // 处理 Error 实例
  if (error instanceof Error) {
    const errorMessage = error.message.toLowerCase();

    // 网络相关错误
    if (errorMessage.includes('fetch failed')) {
      if (errorMessage.includes('enotfound')) {
        return {
          type: 'DNS解析失败',
          message: 'DNS无法解析域名',
          suggestion: '检查网络连接和DNS设置',
          details: { originalMessage: error.message, stack: error.stack },
        };
      }
      if (errorMessage.includes('econnrefused')) {
        return {
          type: '连接被拒绝',
          message: '目标服务器拒绝连接',
          suggestion: '检查服务器状态和防火墙设置',
          details: { originalMessage: error.message },
        };
      }
      if (errorMessage.includes('timeout')) {
        return {
          type: '连接超时',
          message: '请求超时',
          suggestion: '增加超时时间或检查网络稳定性',
          details: { originalMessage: error.message },
        };
      }
      if (errorMessage.includes('certificate') || errorMessage.includes('ssl')) {
        return {
          type: 'SSL证书错误',
          message: 'SSL/TLS证书验证失败',
          suggestion: '检查证书配置或网络安全设置',
          details: { originalMessage: error.message },
        };
      }
      // 特殊处理 Gemini API 的 fetch failed 错误
      if (errorMessage.includes('sending request')) {
        return {
          type: 'Gemini API 连接失败',
          message: 'Gemini API 服务连接失败，可能是网络问题或服务暂时不可用',
          suggestion: '1. 检查网络连接 2. 验证 API 密钥 3. 检查 Gemini 服务状态 4. 稍后重试',
          details: { originalMessage: error.message, stack: error.stack },
        };
      }
      return {
        type: '网络请求失败',
        message: '网络层面的请求失败',
        suggestion: '检查网络连接、代理设置或稍后重试',
        details: { originalMessage: error.message, stack: error.stack },
      };
    }

    // API相关错误
    if (errorMessage.includes('api') || errorMessage.includes('status')) {
      return {
        type: 'API调用错误',
        message: error.message,
        suggestion: '检查API密钥、配额或服务状态',
        details: { originalMessage: error.message },
      };
    }

    // 认证错误
    if (
      errorMessage.includes('auth') ||
      errorMessage.includes('unauthorized') ||
      errorMessage.includes('forbidden')
    ) {
      return {
        type: '认证错误',
        message: '身份验证失败',
        suggestion: '检查API密钥是否正确和有效',
        details: { originalMessage: error.message },
      };
    }

    // 限流错误
    if (
      errorMessage.includes('rate limit') ||
      errorMessage.includes('quota') ||
      errorMessage.includes('429')
    ) {
      return {
        type: 'API限流',
        message: 'API调用频率超限',
        suggestion: '降低请求频率或等待配额重置',
        details: { originalMessage: error.message },
      };
    }

    // 通用Error
    return {
      type: '程序错误',
      message: error.message,
      suggestion: '检查代码逻辑或联系开发者',
      details: { name: error.name, stack: error.stack },
    };
  }

  // 处理对象类型错误
  if (typeof error === 'object') {
    // HTTP状态码错误
    if ('status' in error || 'statusCode' in error) {
      const status = error.status || error.statusCode;
      const statusMessage = error.message || error.statusText || '未知状态错误';

      if (status >= 400 && status < 500) {
        return {
          type: '客户端错误',
          message: `HTTP ${status}: ${statusMessage}`,
          suggestion:
            status === 401 ? '检查API密钥' : status === 429 ? '降低请求频率' : '检查请求参数',
          details: error,
        };
      }
      if (status >= 500) {
        return {
          type: '服务器错误',
          message: `HTTP ${status}: ${statusMessage}`,
          suggestion: '服务器内部错误，稍后重试',
          details: error,
        };
      }
    }

    // 尝试提取错误信息
    const message = error.message || error.error || JSON.stringify(error);
    return {
      type: '对象错误',
      message: message,
      suggestion: '检查错误对象详情',
      details: error,
    };
  }

  // 其他类型错误
  return {
    type: '未分类错误',
    message: String(error),
    suggestion: '检查错误类型和内容',
    details: { type: typeof error, value: error },
  };
}

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-26 18:54:00 +08:00]
// Reason: 添加进度管理核心函数，支持进度保存、加载和更新
// Principle_Applied: SOLID (Single Responsibility), DRY (复用进度逻辑), Error Handling
// Optimization: 持久化进度状态，支持断点续传，提高处理可靠性
// Architectural_Note (AR): 实现进度管理的核心功能，支持大规模数据处理的状态管理
// Documentation_Note (DW): 新增进度文件管理功能，支持批次处理的状态持久化
// }}
/**
 * 加载批次进度信息
 * @returns 进度信息，如果文件不存在则返回null
 */
async function loadBatchProgress(): Promise<BatchProgress | null> {
  try {
    const progressPath = join(process.cwd(), PROGRESS_FILE_PATH);
    await access(progressPath);
    const progressData = await readFile(progressPath, 'utf-8');
    return JSON.parse(progressData) as BatchProgress;
  } catch (error) {
    // 文件不存在或读取失败，返回null
    return null;
  }
}

/**
 * 保存批次进度信息
 * @param progress 进度信息
 */
async function saveBatchProgress(progress: BatchProgress): Promise<void> {
  try {
    const progressPath = join(process.cwd(), PROGRESS_FILE_PATH);
    const progressDir = dirname(progressPath);

    // 确保目录存在
    await mkdir(progressDir, { recursive: true });

    // 更新最后更新时间
    progress.lastUpdateTime = new Date().toISOString();

    // 保存进度文件
    await writeFile(progressPath, JSON.stringify(progress, null, 2), 'utf-8');
    console.log(`📊 进度已保存: ${progress.completedBatches}/${progress.totalBatches} 批次完成`);
  } catch (error) {
    console.error('❌ 保存进度文件失败:', error);
  }
}

/**
 * 初始化批次进度信息
 * @param totalWords 总单词数
 * @returns 初始化的进度信息
 */
function initializeBatchProgress(totalWords: number): BatchProgress {
  const totalBatches = Math.ceil(totalWords / BATCH_SIZE);
  const batchResults: BatchResult[] = [];

  // 初始化所有批次结果
  for (let i = 0; i < totalBatches; i++) {
    const startIndex = i * BATCH_SIZE;
    const endIndex = Math.min(startIndex + BATCH_SIZE - 1, totalWords - 1);
    const wordsCount = endIndex - startIndex + 1;

    batchResults.push({
      batchIndex: i,
      startIndex,
      endIndex,
      wordsCount,
      status: 'pending',
      successCount: 0,
      failureCount: 0,
      apiCallsCount: 0,
    });
  }

  return {
    totalBatches,
    completedBatches: 0,
    currentBatch: 0,
    startTime: new Date().toISOString(),
    lastUpdateTime: new Date().toISOString(),
    batchResults,
    totalWordsProcessed: 0,
    totalWordsSuccessful: 0,
    totalWordsFailed: 0,
  };
}

/**
 * 将单词数组分割成指定大小的块
 * @param words 单词数组
 * @param chunkSize 每块的大小
 * @returns 分割后的单词块数组
 */
function chunkWords(words: string[], chunkSize: number): string[][] {
  const chunks: string[][] = [];
  for (let i = 0; i < words.length; i += chunkSize) {
    chunks.push(words.slice(i, i + chunkSize));
  }
  return chunks;
}

/**
 * 带重试机制的词典API调用
 * @param wordList 单词列表
 * @param requestIndex 请求索引（用于日志）
 * @param maxRetries 最大重试次数
 * @returns 词典查询结果
 */
async function retryFetchDictionary(
  wordList: string,
  requestIndex: number,
  maxRetries: number = 3
): Promise<DictionaryQueryResult> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`🔄 请求 ${requestIndex} 尝试 ${attempt}/${maxRetries}`);
      const result = await fetchDictionary(wordList);

      if (result.statusCode === 200 && result.data) {
        if (attempt > 1) {
          console.log(`✅ 请求 ${requestIndex} 重试成功 (第${attempt}次尝试)`);
        }
        return result;
      } else {
        throw new Error(`API返回错误状态: ${result.statusCode}`);
      }
    } catch (error) {
      lastError = error;

      // {{CHENGQI:
      // Action: Modified
      // Timestamp: [2025-05-26 23:58:00 +08:00]
      // Reason: 使用新的错误分析函数，提供更详细的错误信息和解决建议
      // Principle_Applied: KISS (简单直接的错误显示), DRY (复用错误分析逻辑)
      // Optimization: 结构化错误信息显示，便于问题诊断
      // Architectural_Note (AR): 提高系统可观测性，便于运维和调试
      // Documentation_Note (DW): 改进错误日志格式，提供更有用的调试信息
      // }}
      const errorInfo = analyzeError(error);
      console.warn(`⚠️  请求 ${requestIndex} 第${attempt}次尝试失败:`);
      console.warn(`   🏷️  错误类型: ${errorInfo.type}`);
      console.warn(`   📝 错误描述: ${errorInfo.message}`);
      console.warn(`   🔧 建议操作: ${errorInfo.suggestion}`);

      // 只在详细模式下显示完整错误详情
      if (process.env.NODE_ENV === 'development' && errorInfo.details) {
        console.warn(`   📊 详细信息:`, errorInfo.details);
      }

      if (attempt < maxRetries) {
        // 根据错误类型调整延迟策略
        let baseDelay = 2000; // 默认2秒

        if (
          errorInfo.type.includes('网络') ||
          errorInfo.type.includes('DNS') ||
          errorInfo.type.includes('连接') ||
          errorInfo.type.includes('Gemini API 连接失败')
        ) {
          baseDelay = 15000; // 网络问题和 Gemini API 连接问题延迟15秒
        } else if (errorInfo.type.includes('限流') || errorInfo.type.includes('429')) {
          baseDelay = 30000; // API限流延迟30秒
        } else if (errorInfo.type.includes('服务器错误')) {
          baseDelay = 8000; // 服务器错误延迟8秒
        }

        const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), 60000); // 最大60秒
        console.log(`⏳ 等待 ${delay / 1000} 秒后重试...`);
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
}

/**
 * 并行调用词典API - 每个请求处理100个单词，5个并发请求
 * @param words 单词数组
 * @param parallelCount 并行请求数量
 * @returns 词典查询结果数组
 */
async function fetchDictionaryParallel(
  words: string[],
  parallelCount: number
): Promise<DictionaryQueryResult[]> {
  console.log(`🚀 开始并行调用词典API，并行数: ${parallelCount}`);
  console.log(`📝 总单词数: ${words.length}`);
  console.log(`📦 每个请求处理: ${WORDS_PER_REQUEST} 个单词`);

  // 按100个单词为一组分割
  const wordChunks = chunkWords(words, WORDS_PER_REQUEST);
  console.log(`📦 分割为 ${wordChunks.length} 个请求块`);

  const startTime = Date.now();
  const results: DictionaryQueryResult[] = [];

  try {
    // 按并发数量分批处理
    for (let i = 0; i < wordChunks.length; i += parallelCount) {
      const currentBatch = wordChunks.slice(i, i + parallelCount);
      console.log(
        `\n🔄 处理第 ${Math.floor(i / parallelCount) + 1} 批，包含 ${currentBatch.length} 个请求`
      );

      // 创建当前批次的并行请求
      const batchPromises = currentBatch.map((chunk, batchIndex) => {
        const globalIndex = i + batchIndex + 1;
        const wordList = chunk.join(',');
        console.log(
          `📤 请求 ${globalIndex}: ${chunk.length} 个单词 (${chunk.slice(0, 3).join(', ')}...)`
        );

        return retryFetchDictionary(wordList, globalIndex, 3).catch((error) => {
          // {{CHENGQI:
          // Action: Modified
          // Timestamp: [2025-05-26 23:59:00 +08:00]
          // Reason: 改进最终失败时的错误处理，使用错误分析函数提供更详细信息
          // Principle_Applied: KISS (简单直接的错误显示), DRY (复用错误分析逻辑)
          // Optimization: 统一错误处理格式，便于问题诊断
          // Architectural_Note (AR): 保持错误处理的一致性
          // Documentation_Note (DW): 统一错误日志格式
          // }}
          const errorInfo = analyzeError(error);
          console.error(`❌ 请求 ${globalIndex} 最终失败:`);
          console.error(`   🏷️  错误类型: ${errorInfo.type}`);
          console.error(`   📝 错误描述: ${errorInfo.message}`);
          console.error(`   🔧 建议操作: ${errorInfo.suggestion}`);

          return {
            error: error,
            statusCode: 500,
            data: undefined,
          } as DictionaryQueryResult;
        });
      });

      // 等待当前批次完成
      const batchResults = await Promise.all(batchPromises);

      // 验证批次结果
      batchResults.forEach((result, batchIndex) => {
        const globalIndex = i + batchIndex + 1;
        if (result.statusCode === 200 && result.data) {
          console.log(`✅ 请求 ${globalIndex} 成功: ${result.data.wordCount} 个单词`);
        } else {
          // {{CHENGQI:
          // Action: Modified
          // Timestamp: [2025-05-27 00:00:00 +08:00]
          // Reason: 改进批次结果验证的错误处理，使用错误分析函数
          // Principle_Applied: KISS (简单直接的错误显示), DRY (复用错误分析逻辑)
          // Optimization: 统一错误处理格式，避免重复的JSON序列化
          // Architectural_Note (AR): 保持错误处理的一致性
          // Documentation_Note (DW): 统一错误日志格式，减少冗余信息
          // }}
          if (result.error) {
            const errorInfo = analyzeError(result.error);
            console.error(`❌ 请求 ${globalIndex} 失败:`);
            console.error(`   🏷️  错误类型: ${errorInfo.type}`);
            console.error(`   📝 错误描述: ${errorInfo.message}`);
            console.error(`   🔧 建议操作: ${errorInfo.suggestion}`);
          } else {
            console.error(`❌ 请求 ${globalIndex} 失败: 未知错误`);
          }
        }
      });

      results.push(...batchResults);

      // 如果不是最后一批，延迟避免API限制和网络问题
      if (i + parallelCount < wordChunks.length) {
        console.log('⏳ 等待10秒后处理下一批...');
        await new Promise((resolve) => setTimeout(resolve, 10000));
      }
    }

    const endTime = Date.now();
    const executionTime = (endTime - startTime) / 1000;

    console.log(`\n✅ 所有并行API调用完成，总耗时: ${executionTime.toFixed(2)}秒`);

    // 统计成功和失败的请求
    const successCount = results.filter((r) => r.statusCode === 200 && r.data).length;
    const failureCount = results.length - successCount;
    console.log(`📊 成功请求: ${successCount}/${results.length}, 失败请求: ${failureCount}`);

    return results;
  } catch (error) {
    console.error('❌ 并行API调用失败:', error);
    throw new Error(`词典API调用失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 合并多个词典查询结果
 * @param results 词典查询结果数组
 * @returns 合并后的词典数据
 */
function mergeDictionaryResults(results: DictionaryQueryResult[]) {
  const mergedWords: any[] = [];
  const mergedWordList: string[] = [];
  const mergedSuggests: any[] = [];
  const mergedIllegalWords: string[] = [];
  let totalWordCount = 0;

  for (const result of results) {
    if (result.statusCode === 200 && result.data) {
      mergedWords.push(...result.data.words);
      mergedWordList.push(...result.data.wordList);
      totalWordCount += result.data.wordCount;

      // 合并suggests字段
      if (result.data.suggests && Array.isArray(result.data.suggests)) {
        mergedSuggests.push(...result.data.suggests);
      }

      // 合并illegalWords字段
      if (result.data.illegalWords && Array.isArray(result.data.illegalWords)) {
        mergedIllegalWords.push(...result.data.illegalWords);
      }
    }
  }

  return {
    words: mergedWords,
    wordList: mergedWordList,
    wordCount: totalWordCount,
    suggests: mergedSuggests,
    illegalWords: mergedIllegalWords,
  };
}

// {{CHENGQI:
// Action: Added
// Timestamp: [2025-05-27 14:32:00 +08:00]
// Reason: 添加查询已存在单词的函数，支持增量导入功能
// Principle_Applied: SOLID (Single Responsibility), DRY (避免重复代码), Efficiency (批量查询)
// Optimization: 批量查询优化，避免单个单词多次查询数据库
// Architectural_Note (AR): 增量导入的基础函数，支持高效过滤
// Documentation_Note (DW): 新增数据库查询函数，用于识别已存在的单词记录
// }}
/**
 * 查询数据库中已存在的单词
 * @param words 待检查的单词数组
 * @returns 已存在单词的集合
 */
async function findExistingWords(words: string[]): Promise<Set<string>> {
  console.log(`🔍 检查数据库中已存在的单词 (${words.length} 个单词)...`);

  // 转换所有单词为小写
  const normalizedWords = words.map((word) => word.toLowerCase());

  // 批量查询数据库中已存在的单词
  const existingRecords = await prismaClient.vocabulary.findMany({
    where: {
      word: {
        in: normalizedWords,
      },
    },
    select: {
      word: true,
    },
  });

  // 创建已存在单词的集合（用于O(1)查找）
  const existingWordsSet = new Set(existingRecords.map((record: { word: string }) => record.word));

  console.log(`✅ 数据库中已存在 ${existingWordsSet.size} 个单词`);

  return existingWordsSet;
}

/**
 * 处理单个批次的单词
 * @param words 当前批次的单词数组
 * @param batchIndex 批次索引
 * @param progress 进度信息
 * @returns 批次处理结果
 */
async function processBatch(
  words: string[],
  batchIndex: number,
  progress: BatchProgress
): Promise<{ success: boolean; importResult?: any; error?: any }> {
  const batchResult = progress.batchResults[batchIndex];

  try {
    console.log(`\n🚀 开始处理第 ${batchIndex + 1}/${progress.totalBatches} 批次`);
    console.log(`📝 批次范围: ${batchResult.startIndex + 1} - ${batchResult.endIndex + 1}`);
    console.log(`📦 单词数量: ${words.length} 个`);
    console.log(`🔤 前5个单词: ${words.slice(0, 5).join(', ')}`);

    // 更新批次状态为处理中
    batchResult.status = 'processing';
    batchResult.startTime = new Date().toISOString();
    progress.currentBatch = batchIndex;
    await saveBatchProgress(progress);

    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-05-27 14:35:00 +08:00]
    // Reason: 实现增量导入功能，过滤掉已存在的单词
    // Principle_Applied: SOLID (Single Responsibility), Efficiency (避免重复处理)
    // Optimization: 跳过已存在单词，减少API调用和数据库写入
    // Architectural_Note (AR): 增量处理实现，提高导入效率
    // Documentation_Note (DW): 实现增量导入核心逻辑，智能跳过已存在记录
    // }}
    // 如果启用了增量导入，过滤掉已存在的单词
    let wordsToProcess = words;
    if (SKIP_EXISTING) {
      const existingWords = await findExistingWords(words);
      const filteredWords = words.filter((word) => !existingWords.has(word.toLowerCase()));

      const skippedCount = words.length - filteredWords.length;
      console.log(
        `⏭️ 跳过 ${skippedCount} 个已存在的单词，实际处理 ${filteredWords.length} 个新单词`
      );

      if (filteredWords.length === 0) {
        console.log(`✅ 批次 ${batchIndex + 1} 中所有单词已存在，无需处理`);

        // 更新批次状态为完成
        batchResult.status = 'completed';
        batchResult.endTime = new Date().toISOString();
        batchResult.successCount = skippedCount; // 计入跳过的单词为成功
        batchResult.failureCount = 0;

        // 更新总体进度
        progress.completedBatches++;
        progress.totalWordsProcessed += words.length;
        progress.totalWordsSuccessful += skippedCount;

        // 保存进度
        await saveBatchProgress(progress);

        return { success: true, importResult: { successCount: skippedCount, failureCount: 0 } };
      }

      wordsToProcess = filteredWords;
    }

    // 调用现有的并行API处理逻辑
    const apiResults = await fetchDictionaryParallel(wordsToProcess, PARALLEL_REQUESTS);

    // 统计API调用结果
    const successfulResults = apiResults.filter((r) => r.statusCode === 200 && r.data);
    batchResult.apiCallsCount = apiResults.length;

    if (successfulResults.length === 0) {
      throw new Error('所有API调用都失败了');
    }

    // 合并API结果
    const mergedData = mergeDictionaryResults(apiResults);
    console.log(`📊 批次 ${batchIndex + 1} API结果:`);
    console.log(`  ✅ 有效单词: ${mergedData.wordCount} 个`);
    console.log(`  💡 建议单词: ${mergedData.suggests?.length || 0} 个`);
    console.log(`  ❌ 不合法单词: ${mergedData.illegalWords?.length || 0} 个`);

    // 导入数据到数据库
    console.log(`💾 批次 ${batchIndex + 1}: 导入数据到数据库`);
    const importResult = await importFromDictionaryData(mergedData, prismaClient, {
      forceOverwrite: false,
      verbose: false,
    });

    // {{CHENGQI:
    // Action: Modified
    // Timestamp: [2025-05-27 14:38:00 +08:00]
    // Reason: 更新批次结果计算，考虑跳过的单词数量
    // Principle_Applied: SOLID (Single Responsibility), Correctness (正确统计)
    // Optimization: 准确计算处理结果，包括跳过的单词
    // Architectural_Note (AR): 完善统计逻辑，确保指标准确性
    // Documentation_Note (DW): 更新统计逻辑，考虑增量导入场景
    // }}
    // 更新批次结果（如果启用了增量导入，需要加上跳过的单词数）
    const skippedCount = SKIP_EXISTING ? words.length - wordsToProcess.length : 0;
    batchResult.successCount =
      importResult.successCount + importResult.suggestsSuccessCount + skippedCount;
    batchResult.failureCount = importResult.failureCount;
    batchResult.status = 'completed';
    batchResult.endTime = new Date().toISOString();

    // 更新总体进度
    progress.completedBatches++;
    progress.totalWordsProcessed += words.length;
    progress.totalWordsSuccessful += batchResult.successCount;
    progress.totalWordsFailed += batchResult.failureCount;

    console.log(`✅ 批次 ${batchIndex + 1} 完成:`);
    if (SKIP_EXISTING && skippedCount > 0) {
      console.log(`  ⏭️ 跳过已存在: ${skippedCount} 个单词`);
    }
    console.log(
      `  📈 导入成功: ${importResult.successCount + importResult.suggestsSuccessCount} 个单词`
    );
    console.log(`  ❌ 导入失败: ${batchResult.failureCount} 个单词`);
    console.log(`  📊 总体进度: ${progress.completedBatches}/${progress.totalBatches} 批次`);

    // 保存进度
    await saveBatchProgress(progress);

    return { success: true, importResult };
  } catch (error) {
    console.error(`❌ 批次 ${batchIndex + 1} 处理失败:`, error);

    // 更新批次状态为失败
    batchResult.status = 'failed';
    batchResult.endTime = new Date().toISOString();
    batchResult.errorMessage = error instanceof Error ? error.message : String(error);

    // 保存进度（即使失败也要保存状态）
    await saveBatchProgress(progress);

    return { success: false, error };
  }
}

/**
 * 设置词汇的频率排名
 * @param originalWords 原始单词数组（按频率排序）
 * @param prismaClient Prisma客户端
 */
async function setFrequencyRanks(
  originalWords: string[],
  prismaClient: PrismaClient
): Promise<void> {
  console.log('🏆 开始设置频率排名...');

  let updatedCount = 0;
  let notFoundCount = 0;
  const notFoundWords: string[] = [];
  const failedWords: { word: string; error: string }[] = [];

  for (let i = 0; i < originalWords.length; i++) {
    const word = originalWords[i];
    const rank = i + 1; // 排名从1开始

    try {
      const result = await prismaClient.vocabulary.updateMany({
        where: { word: word },
        data: { freqRank: rank },
      });

      if (result.count > 0) {
        updatedCount++;
        // 显示进度条样式的日志，每100个或每10%显示一次
        const progressInterval = Math.max(100, Math.floor(originalWords.length / 10));
        if (rank % progressInterval === 0 || rank <= 10 || rank === originalWords.length) {
          const percent = ((rank / originalWords.length) * 100).toFixed(1);
          console.log(
            `📊 排名设置进度: ${rank}/${originalWords.length} (${percent}%) - 最新: ${word}`
          );
        }
      } else {
        notFoundCount++;
        notFoundWords.push(word);

        // 只在前几个未找到的单词时显示详细日志
        if (notFoundCount <= 5) {
          console.warn(`⚠️  未找到单词: "${word}" (排名 ${rank})`);
        }
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      failedWords.push({ word, error: errorMsg });
      console.error(`❌ 设置排名失败 "${word}":`, errorMsg);
    }
  }

  // 汇总报告
  console.log(`\n✅ 频率排名设置完成:`);
  console.log(`  📈 成功设置: ${updatedCount.toLocaleString()} 个单词`);
  console.log(`  ⚠️  未找到: ${notFoundCount.toLocaleString()} 个单词`);
  console.log(`  ❌ 设置失败: ${failedWords.length} 个单词`);

  if (notFoundCount > 0) {
    console.log(`  📋 未找到单词统计: ${Math.min(notFoundWords.length, 10)} 个示例:`);
    console.log(
      `     ${notFoundWords.slice(0, 10).join(', ')}${notFoundWords.length > 10 ? '...' : ''}`
    );

    if (notFoundCount > 5) {
      console.log(`     (省略了 ${notFoundCount - 5} 个未找到单词的详细日志)`);
    }
  }

  if (failedWords.length > 0) {
    console.log(`  🔧 失败详情:`);
    failedWords.slice(0, 3).forEach(({ word, error }) => {
      console.log(`     - ${word}: ${error}`);
    });
    if (failedWords.length > 3) {
      console.log(`     (省略了 ${failedWords.length - 3} 个失败详情)`);
    }
  }
}

/**
 * 主函数 - 分批处理版本
 */
async function main() {
  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-05-26 18:58:00 +08:00]
  // Reason: 重写主函数以支持分批处理，添加断点续传和进度管理功能
  // Principle_Applied: SOLID (Single Responsibility), KISS (简化主流程), DRY (复用批次处理逻辑)
  // Optimization: 分批处理减少内存压力，支持断点续传，提高处理稳定性
  // Architectural_Note (AR): 实现分批处理架构，支持大规模数据处理和错误恢复
  // Documentation_Note (DW): 重构主函数以支持分批处理和进度管理
  // }}
  await setupFileLogger(); // 设置文件日志记录器

  console.log('🎯 开始分批同步频率词汇数据...\n');
  console.log(`📊 配置信息:`);
  console.log(`  📝 总单词数: ${WORDS_TO_EXTRACT.toLocaleString()}`);
  console.log(`  📦 批次大小: ${BATCH_SIZE.toLocaleString()}`);
  console.log(`  ⏱️  批次延迟: ${BATCH_DELAY / 1000} 秒`);
  console.log(`  🔄 并发请求: ${PARALLEL_REQUESTS}`);
  console.log(`  📄 每请求单词数: ${WORDS_PER_REQUEST}`);
  // {{CHENGQI:
  // Action: Added
  // Timestamp: [2025-05-27 14:40:00 +08:00]
  // Reason: 在配置信息中显示增量导入设置状态
  // Principle_Applied: Visibility (增加配置可见性), Observability (监控处理策略)
  // Optimization: N/A
  // Architectural_Note (AR): 配置状态可见性，改善用户体验
  // Documentation_Note (DW): 显示增量导入配置状态
  // }}
  console.log(`  🔄 增量导入: ${SKIP_EXISTING ? '启用' : '禁用'}\n`);

  try {
    // 1. 从Excel文件读取单词
    console.log('📖 步骤1: 读取Excel文件');
    const excelPath = join(process.cwd(), EXCEL_FILE_PATH);
    const allWords = await extractWordsFromExcel(excelPath, WORDS_TO_EXTRACT);

    if (allWords.length === 0) {
      throw new Error('未能从Excel文件中提取到任何单词');
    }

    console.log(`✅ 成功提取 ${allWords.length.toLocaleString()} 个单词`);

    // 2. 加载或初始化进度信息
    console.log('\n📊 步骤2: 检查处理进度');
    let progress = await loadBatchProgress();

    if (progress) {
      console.log(`🔄 发现现有进度文件:`);
      console.log(`  📈 已完成批次: ${progress.completedBatches}/${progress.totalBatches}`);
      console.log(`  已处理单词: ${progress.totalWordsProcessed.toLocaleString()}`);
      console.log(`  ✅ 成功单词: ${progress.totalWordsSuccessful.toLocaleString()}`);
      console.log(`  ❌ 失败单词: ${progress.totalWordsFailed.toLocaleString()}`);
      console.log(`  🕐 开始时间: ${progress.startTime}`);
      console.log(`  🕐 最后更新: ${progress.lastUpdateTime}`);

      // 验证进度文件是否与当前配置匹配
      const expectedBatches = Math.ceil(allWords.length / BATCH_SIZE);
      if (progress.totalBatches !== expectedBatches) {
        console.log(`⚠️  进度文件批次数不匹配，重新初始化进度`);
        progress = initializeBatchProgress(allWords.length);
      }
    } else {
      console.log('🆕 初始化新的进度跟踪');
      progress = initializeBatchProgress(allWords.length);
    }

    await saveBatchProgress(progress);

    // {{CHENGQI:
    // Action: Added
    // Timestamp: [2025-05-27 14:42:00 +08:00]
    // Reason: 如果启用了增量导入，提前显示数据库中已有的单词数量
    // Principle_Applied: Visibility (提前显示关键信息), Observability (监控导入状态)
    // Optimization: 提供数据预览，增强用户体验
    // Architectural_Note (AR): 增加预处理状态反馈，改善用户体验
    // Documentation_Note (DW): 增加数据库已有单词统计信息
    // }}
    // 2.5 如果启用了增量导入，提前统计数据库中已有的单词
    if (SKIP_EXISTING) {
      console.log('\n📊 步骤2.5: 预检查数据库中已有单词');
      // 为了提高效率，只取一个小样本进行检查
      const sampleSize = Math.min(1000, allWords.length);
      const sampleWords = allWords.slice(0, sampleSize);
      const existingWords = await findExistingWords(sampleWords);
      const existingPercent = ((existingWords.size / sampleSize) * 100).toFixed(2);

      console.log(`📈 样本检查: ${existingWords.size}/${sampleSize} 已存在 (${existingPercent}%)`);

      // 估算可能跳过的单词数量
      const estimatedSkip = Math.floor((allWords.length * existingWords.size) / sampleSize);
      console.log(`📊 预估已存在: 约 ${estimatedSkip.toLocaleString()} 个单词可能会被跳过`);
      console.log(
        `📊 预估新单词: 约 ${(allWords.length - estimatedSkip).toLocaleString()} 个单词需要处理`
      );
    }

    // 3. 分批处理
    console.log('\n🔄 步骤3: 开始分批处理');
    const totalBatches = progress.totalBatches;
    let successfulBatches = 0;
    let failedBatches = 0;

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const batchResult = progress.batchResults[batchIndex];

      // 跳过已完成的批次（断点续传）
      if (batchResult.status === 'completed') {
        console.log(`⏭️  跳过已完成的批次 ${batchIndex + 1}/${totalBatches}`);
        successfulBatches++;
        continue;
      }

      // 获取当前批次的单词
      const startIndex = batchResult.startIndex;
      const endIndex = batchResult.endIndex;
      const batchWords = allWords.slice(startIndex, endIndex + 1);

      // 处理当前批次
      const result = await processBatch(batchWords, batchIndex, progress);

      if (result.success) {
        successfulBatches++;
        console.log(`🎉 批次 ${batchIndex + 1} 处理成功`);
      } else {
        failedBatches++;
        console.error(`💥 批次 ${batchIndex + 1} 处理失败:`, result.error);

        // 可以选择继续处理下一批次或停止
        console.log('⚠️  继续处理下一批次...');
      }

      // 批次间延迟（除了最后一批次）
      if (batchIndex < totalBatches - 1) {
        console.log(`⏳ 等待 ${BATCH_DELAY / 1000} 秒后处理下一批次...`);
        await new Promise((resolve) => setTimeout(resolve, BATCH_DELAY));
      }

      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }
    }

    // 4. 设置频率排名
    console.log('\n🏆 步骤4: 设置频率排名');
    await setFrequencyRanks(allWords, prismaClient);

    // 5. 最终统计
    console.log('\n🎉 处理完成统计:');
    console.log(`  ✅ 成功批次: ${successfulBatches}/${totalBatches}`);
    console.log(`  ❌ 失败批次: ${failedBatches}/${totalBatches}`);
    console.log(`  📝 总处理单词: ${progress.totalWordsProcessed.toLocaleString()}`);
    console.log(`  ✅ 成功导入: ${progress.totalWordsSuccessful.toLocaleString()}`);
    console.log(`  ❌ 导入失败: ${progress.totalWordsFailed.toLocaleString()}`);

    const successRate =
      progress.totalWordsProcessed > 0
        ? ((progress.totalWordsSuccessful / progress.totalWordsProcessed) * 100).toFixed(2)
        : '0.00';
    console.log(`  📈 成功率: ${successRate}%`);

    console.log('\n🎉 分批同步完成！');
  } catch (error) {
    console.error('\n💥 同步失败:', error);
    process.exit(1);
  } finally {
    await prismaClient.$disconnect();
  }
}

// 运行主函数
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch((error) => {
    console.error('未处理的错误:', error);
    process.exit(1);
  });
}

export { main, extractWordsFromExcel, fetchDictionaryParallel, setFrequencyRanks };
