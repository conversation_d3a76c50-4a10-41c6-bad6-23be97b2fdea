/**
 * Application-Level Memory Cache
 * High-performance in-memory caching for frequently accessed data
 * Implements LRU eviction and TTL expiration
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

interface CacheStats {
  size: number;
  maxSize: number;
  hits: number;
  misses: number;
  hitRate: number;
  evictions: number;
  totalRequests: number;
}

/**
 * High-performance LRU cache with TTL support
 */
export class ApplicationCache<T> {
  private cache = new Map<string, CacheEntry<T>>();
  private accessOrder = new Map<string, number>(); // For LRU tracking
  private stats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalRequests: 0,
  };

  constructor(
    private maxSize: number = 1000,
    public defaultTTL: number = 5 * 60 * 1000 // 5 minutes
  ) {}

  /**
   * Get value from cache
   */
  get(key: string): T | null {
    this.stats.totalRequests++;

    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.misses++;
      return null;
    }

    // Check TTL expiration
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      this.stats.misses++;
      return null;
    }

    // Update access tracking for LRU
    entry.accessCount++;
    entry.lastAccessed = now;
    this.accessOrder.set(key, now);

    this.stats.hits++;
    return entry.data;
  }

  /**
   * Set value in cache
   */
  set(key: string, value: T, ttl?: number): void {
    const now = Date.now();
    const entryTTL = ttl || this.defaultTTL;

    // Check if we need to evict entries
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const entry: CacheEntry<T> = {
      data: value,
      timestamp: now,
      ttl: entryTTL,
      accessCount: 1,
      lastAccessed: now,
    };

    this.cache.set(key, entry);
    this.accessOrder.set(key, now);
  }

  /**
   * Get or compute value (cache-aside pattern)
   */
  async getOrCompute<R extends T>(
    key: string,
    computeFn: () => Promise<R>,
    ttl?: number
  ): Promise<R> {
    const cached = this.get(key);
    if (cached !== null) {
      return cached as R;
    }

    const computed = await computeFn();
    this.set(key, computed, ttl);
    return computed;
  }

  /**
   * Delete entry from cache
   */
  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    this.accessOrder.delete(key);
    return deleted;
  }

  /**
   * Clear all entries
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder.clear();
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalRequests: 0,
    };
  }

  /**
   * Check if key exists (without affecting LRU order)
   */
  has(key: string): boolean {
    const entry = this.cache.get(key);
    if (!entry) return false;

    // Check TTL
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      this.accessOrder.delete(key);
      return false;
    }

    return true;
  }

  /**
   * Get cache statistics
   */
  getStats(): CacheStats {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hits: this.stats.hits,
      misses: this.stats.misses,
      hitRate:
        this.stats.totalRequests > 0 ? (this.stats.hits / this.stats.totalRequests) * 100 : 0,
      evictions: this.stats.evictions,
      totalRequests: this.stats.totalRequests,
    };
  }

  /**
   * Evict least recently used entry
   */
  private evictLRU(): void {
    if (this.accessOrder.size === 0) return;

    // Find the entry with the oldest access time
    let oldestKey: string | null = null;
    let oldestTime = Date.now();

    for (const [key, accessTime] of this.accessOrder.entries()) {
      if (accessTime < oldestTime) {
        oldestTime = accessTime;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
      this.accessOrder.delete(oldestKey);
      this.stats.evictions++;
    }
  }

  /**
   * Clean up expired entries (call periodically)
   */
  cleanup(): number {
    const now = Date.now();
    let cleaned = 0;

    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
        this.accessOrder.delete(key);
        cleaned++;
      }
    }

    return cleaned;
  }

  /**
   * Get all keys (for debugging)
   */
  keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * Get cache size
   */
  size(): number {
    return this.cache.size;
  }
}

/**
 * Global application cache instances
 */
export const vocabularyCache = new ApplicationCache<any>(500, 10 * 60 * 1000); // 10 minutes TTL
export const queryResultCache = new ApplicationCache<any>(200, 5 * 60 * 1000); // 5 minutes TTL
export const relationshipCache = new ApplicationCache<any>(300, 15 * 60 * 1000); // 15 minutes TTL
export const suggestionCache = new ApplicationCache<string[]>(300, 20 * 60 * 1000); // 20 minutes TTL for suggestions

/**
 * Cache key generators
 */
export class CacheKeys {
  static vocabulary(word: string): string {
    return `vocab:${word.toLowerCase()}`;
  }

  static queryResult(term: string, options?: any): string {
    const optionsHash = options ? JSON.stringify(options) : '';
    return `query:${term.toLowerCase()}:${optionsHash}`;
  }

  static relationships(vocabularyId: number): string {
    return `rel:${vocabularyId}`;
  }

  static wordForms(word: string): string {
    return `forms:${word.toLowerCase()}`;
  }

  static suggestions(word: string): string {
    return `sugg:${word.toLowerCase()}`;
  }
}

/**
 * Cache cleanup scheduler
 */
class CacheCleanupScheduler {
  private intervalId: NodeJS.Timeout | null = null;

  start(intervalMs: number = 5 * 60 * 1000): void {
    // 5 minutes
    if (this.intervalId) return;

    this.intervalId = setInterval(() => {
      const vocabCleaned = vocabularyCache.cleanup();
      const queryCleaned = queryResultCache.cleanup();
      const relCleaned = relationshipCache.cleanup();
      const suggCleaned = suggestionCache.cleanup();

      if (process.env.NODE_ENV === 'development') {
        const total = vocabCleaned + queryCleaned + relCleaned + suggCleaned;
        if (total > 0) {
          console.log(`🧹 Cache cleanup: ${total} expired entries removed`);
        }
      }
    }, intervalMs);
  }

  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }
}

export const cacheCleanupScheduler = new CacheCleanupScheduler();

// Start cleanup scheduler in production
if (process.env.NODE_ENV === 'production') {
  cacheCleanupScheduler.start();
}

/**
 * Cache statistics API helper
 */
export function getAllCacheStats(): {
  vocabulary: CacheStats;
  queryResult: CacheStats;
  relationship: CacheStats;
  suggestion: CacheStats;
  total: {
    totalSize: number;
    totalHits: number;
    totalMisses: number;
    overallHitRate: number;
  };
} {
  const vocabStats = vocabularyCache.getStats();
  const queryStats = queryResultCache.getStats();
  const relStats = relationshipCache.getStats();
  const suggStats = suggestionCache.getStats();

  const totalHits = vocabStats.hits + queryStats.hits + relStats.hits + suggStats.hits;
  const totalMisses = vocabStats.misses + queryStats.misses + relStats.misses + suggStats.misses;
  const totalRequests = totalHits + totalMisses;

  return {
    vocabulary: vocabStats,
    queryResult: queryStats,
    relationship: relStats,
    suggestion: suggStats,
    total: {
      totalSize: vocabStats.size + queryStats.size + relStats.size + suggStats.size,
      totalHits,
      totalMisses,
      overallHitRate: totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0,
    },
  };
}
