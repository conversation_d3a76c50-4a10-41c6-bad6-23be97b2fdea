#!/usr/bin/env node

/**
 * Basic Security Scan for Authentication System
 * 基础安全扫描 - 认证系统
 * 
 * This script performs basic security checks on the authentication system
 * to verify OWASP Top 10 compliance and common security vulnerabilities.
 * 
 * Based on tasks.md simplified security requirements
 */

const fs = require('fs');
const path = require('path');

// Security scan configuration
const SECURITY_CHECKS = {
  'A01:2021 - Broken Access Control': {
    files: ['app/lib/auth/middleware.ts', 'app/api/auth/token/route.ts'],
    patterns: ['requireAuth', 'requirePermissions', 'Bearer', 'isAuthenticated'],
    required: true,
    description: 'Check for proper authentication and authorization controls'
  },
  'A02:2021 - Cryptographic Failures': {
    files: ['app/lib/auth/token.ts'],
    patterns: ['RS256', 'crypto', 'generateKeyPair', 'sign', 'verify'],
    required: true,
    description: 'Verify cryptographic implementations'
  },
  'A03:2021 - Injection': {
    files: ['app/api/auth/token/route.ts', 'app/api/auth/token/google/route.ts'],
    patterns: ['z.', 'parse', 'validate', 'sanitize'],
    required: true,
    description: 'Check for input validation using Zod'
  },
  'A04:2021 - Insecure Design': {
    files: ['app/lib/auth/middleware.ts'],
    patterns: ['rate', 'limit', 'timeout', 'throttle'],
    required: false,
    description: 'Rate limiting and throttling mechanisms'
  },
  'A05:2021 - Security Misconfiguration': {
    files: ['middleware.ts'],
    patterns: ['X-Frame-Options', 'X-Content-Type-Options', 'X-XSS-Protection'],
    required: true,
    description: 'Security headers configuration'
  },
  'A06:2021 - Vulnerable Components': {
    files: ['package.json'],
    patterns: ['next-auth', 'jsonwebtoken', 'bcrypt', 'zod'],
    required: true,
    description: 'Check for secure dependencies'
  },
  'A07:2021 - Identity and Authentication Failures': {
    files: ['app/lib/auth/token.ts'],
    patterns: ['verifyPassword', 'hashPassword', 'refreshToken', 'rotate'],
    required: true,
    description: 'Password handling and token management'
  },
  'A08:2021 - Software and Data Integrity Failures': {
    files: ['app/lib/auth/token.ts'],
    patterns: ['integrity', 'fingerprint', 'validation'],
    required: true,
    description: 'Token integrity verification'
  },
  'A09:2021 - Security Logging and Monitoring': {
    files: ['app/lib/auth/middleware.ts', 'middleware.ts'],
    patterns: ['console.log', 'logSecurityEvent', 'audit'],
    required: true,
    description: 'Security event logging'
  },
  'A10:2021 - Server-Side Request Forgery': {
    files: ['app/api/auth/token/google/route.ts', 'app/api/auth/token/github/route.ts'],
    patterns: ['https://api.github.com', 'https://oauth2.googleapis.com'],
    required: true,
    description: 'External API calls validation'
  }
};

// Results storage
const scanResults = {
  passed: [],
  failed: [],
  warnings: [],
  summary: {
    totalChecks: 0,
    passedChecks: 0,
    failedChecks: 0,
    warningChecks: 0,
    overallStatus: 'unknown'
  }
};

/**
 * Check if a file exists
 */
function fileExists(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  return fs.existsSync(fullPath);
}

/**
 * Read file content
 */
function readFile(filePath) {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    return fs.readFileSync(fullPath, 'utf8');
  } catch (error) {
    return null;
  }
}

/**
 * Check if patterns exist in file content
 */
function checkPatterns(content, patterns) {
  const foundPatterns = [];
  const missingPatterns = [];
  
  patterns.forEach(pattern => {
    if (content.includes(pattern)) {
      foundPatterns.push(pattern);
    } else {
      missingPatterns.push(pattern);
    }
  });
  
  return { foundPatterns, missingPatterns };
}

/**
 * Perform security check
 */
function performSecurityCheck(checkName, checkConfig) {
  const results = {
    checkName,
    description: checkConfig.description,
    status: 'unknown',
    details: [],
    filesChecked: 0,
    filesFound: 0,
    patternsFound: [],
    patternsMissing: []
  };
  
  // Check each file
  checkConfig.files.forEach(filePath => {
    results.filesChecked++;
    
    if (!fileExists(filePath)) {
      results.details.push(`❌ File not found: ${filePath}`);
      return;
    }
    
    results.filesFound++;
    const content = readFile(filePath);
    
    if (!content) {
      results.details.push(`❌ Unable to read file: ${filePath}`);
      return;
    }
    
    const { foundPatterns, missingPatterns } = checkPatterns(content, checkConfig.patterns);
    
    results.patternsFound = [...new Set([...results.patternsFound, ...foundPatterns])];
    results.patternsMissing = [...new Set([...results.patternsMissing, ...missingPatterns])];
    
    if (foundPatterns.length > 0) {
      results.details.push(`✅ Found patterns in ${filePath}: ${foundPatterns.join(', ')}`);
    }
    
    if (missingPatterns.length > 0) {
      results.details.push(`⚠️  Missing patterns in ${filePath}: ${missingPatterns.join(', ')}`);
    }
  });
  
  // Determine status
  const hasRequiredPatterns = results.patternsFound.length > 0;
  const hasAllFiles = results.filesFound === results.filesChecked;
  
  if (checkConfig.required) {
    if (hasRequiredPatterns && hasAllFiles) {
      results.status = 'passed';
    } else {
      results.status = 'failed';
    }
  } else {
    if (hasRequiredPatterns) {
      results.status = 'passed';
    } else {
      results.status = 'warning';
    }
  }
  
  return results;
}

/**
 * Generate security report
 */
function generateSecurityReport() {
  const report = [];
  
  report.push('');
  report.push('🔒 OWASP Top 10 Security Scan Report');
  report.push('=====================================');
  report.push(`Generated: ${new Date().toISOString()}`);
  report.push('');
  
  // Summary
  report.push('📊 Summary');
  report.push('----------');
  report.push(`Total Checks: ${scanResults.summary.totalChecks}`);
  report.push(`✅ Passed: ${scanResults.summary.passedChecks}`);
  report.push(`❌ Failed: ${scanResults.summary.failedChecks}`);
  report.push(`⚠️  Warnings: ${scanResults.summary.warningChecks}`);
  report.push(`🎯 Overall Status: ${scanResults.summary.overallStatus}`);
  report.push('');
  
  // Detailed results
  report.push('📋 Detailed Results');
  report.push('-------------------');
  
  [...scanResults.passed, ...scanResults.failed, ...scanResults.warnings].forEach(result => {
    const statusIcon = result.status === 'passed' ? '✅' : 
                      result.status === 'failed' ? '❌' : '⚠️';
    
    report.push(`${statusIcon} ${result.checkName}`);
    report.push(`   ${result.description}`);
    report.push(`   Files checked: ${result.filesChecked}, Found: ${result.filesFound}`);
    report.push(`   Patterns found: ${result.patternsFound.length}`);
    
    if (result.details.length > 0) {
      result.details.forEach(detail => {
        report.push(`   ${detail}`);
      });
    }
    
    report.push('');
  });
  
  // Security recommendations
  report.push('🔧 Security Recommendations');
  report.push('---------------------------');
  
  if (scanResults.summary.failedChecks > 0) {
    report.push('❌ Critical Issues Found:');
    scanResults.failed.forEach(result => {
      report.push(`   - ${result.checkName}: ${result.description}`);
    });
    report.push('');
  }
  
  if (scanResults.summary.warningChecks > 0) {
    report.push('⚠️  Improvement Recommendations:');
    scanResults.warnings.forEach(result => {
      report.push(`   - ${result.checkName}: ${result.description}`);
    });
    report.push('');
  }
  
  if (scanResults.summary.failedChecks === 0) {
    report.push('✅ All critical security checks passed!');
    report.push('');
  }
  
  return report.join('\n');
}

/**
 * Main scan function
 */
function runSecurityScan() {
  console.log('🔍 Starting OWASP Top 10 Security Scan...');
  console.log('========================================');
  
  // Perform each security check
  Object.entries(SECURITY_CHECKS).forEach(([checkName, checkConfig]) => {
    console.log(`\n📋 Checking: ${checkName}`);
    
    const result = performSecurityCheck(checkName, checkConfig);
    scanResults.summary.totalChecks++;
    
    if (result.status === 'passed') {
      scanResults.passed.push(result);
      scanResults.summary.passedChecks++;
      console.log(`✅ PASSED: ${checkName}`);
    } else if (result.status === 'failed') {
      scanResults.failed.push(result);
      scanResults.summary.failedChecks++;
      console.log(`❌ FAILED: ${checkName}`);
    } else {
      scanResults.warnings.push(result);
      scanResults.summary.warningChecks++;
      console.log(`⚠️  WARNING: ${checkName}`);
    }
  });
  
  // Determine overall status
  if (scanResults.summary.failedChecks === 0) {
    scanResults.summary.overallStatus = 'PASSED';
  } else if (scanResults.summary.passedChecks > scanResults.summary.failedChecks) {
    scanResults.summary.overallStatus = 'MOSTLY_PASSED';
  } else {
    scanResults.summary.overallStatus = 'FAILED';
  }
  
  // Generate and display report
  const report = generateSecurityReport();
  console.log(report);
  
  // Save report to file
  const reportPath = path.join(process.cwd(), 'SECURITY_SCAN_REPORT.md');
  fs.writeFileSync(reportPath, report);
  console.log(`📄 Security report saved to: ${reportPath}`);
  
  // Exit with appropriate code
  const exitCode = scanResults.summary.failedChecks === 0 ? 0 : 1;
  process.exit(exitCode);
}

// Run the security scan
if (require.main === module) {
  runSecurityScan();
}

module.exports = {
  runSecurityScan,
  SECURITY_CHECKS,
  scanResults
};