/**
 * 浏览器扩展专用令牌刷新 API
 * 使用刷新令牌获取新的访问令牌
 */

import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import jwt from 'jsonwebtoken';

// 请求体类型定义
interface ExtensionRefreshRequest {
  refreshToken: string;
}

// 响应体类型定义
interface ExtensionRefreshResponse {
  success: boolean;
  accessToken?: string;
  expiresIn?: number;
  error?: string;
  message?: string;
  code?: string;
}

// JWT 载荷类型定义
interface JWTPayload {
  sub: string;
  type: 'refresh' | 'access';
  iat: number;
  exp: number;
}

/**
 * 浏览器扩展令牌刷新 API
 * POST /api/auth/extension-refresh
 */
export async function POST(request: NextRequest): Promise<NextResponse<ExtensionRefreshResponse>> {
  try {
    // 解析请求体
    const body: ExtensionRefreshRequest = await request.json();
    const { refreshToken } = body;

    // 输入验证
    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少刷新令牌',
          message: 'Missing refresh token',
          code: 'MISSING_REFRESH_TOKEN',
        },
        { status: 400 }
      );
    }

    // 获取 JWT 密钥
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      console.error('JWT_SECRET is not configured');
      return NextResponse.json(
        {
          success: false,
          error: '服务器配置错误',
          message: 'Server configuration error',
          code: 'SERVER_CONFIG_ERROR',
        },
        { status: 500 }
      );
    }

    // 验证刷新令牌
    let decoded: JWTPayload;
    try {
      decoded = jwt.verify(refreshToken, jwtSecret) as JWTPayload;
    } catch (error) {
      console.error('Invalid refresh token:', error);
      return NextResponse.json(
        {
          success: false,
          error: '无效的刷新令牌',
          message: 'Invalid refresh token',
          code: 'INVALID_REFRESH_TOKEN',
        },
        { status: 401 }
      );
    }

    // 检查令牌类型
    if (decoded.type !== 'refresh') {
      return NextResponse.json(
        {
          success: false,
          error: '令牌类型错误',
          message: 'Invalid token type',
          code: 'INVALID_TOKEN_TYPE',
        },
        { status: 401 }
      );
    }

    // 获取用户信息并验证用户状态
    const user = await prisma.user.findUnique({
      where: { id: decoded.sub },
      select: {
        id: true,
        email: true,
        name: true,
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在',
          message: 'User not found',
          code: 'USER_NOT_FOUND',
        },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: '账户已被禁用',
          message: 'Account is disabled',
          code: 'ACCOUNT_DISABLED',
        },
        { status: 401 }
      );
    }

    // 生成新的访问令牌
    const newAccessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        iat: Math.floor(Date.now() / 1000),
        type: 'access',
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    // 记录日志
    console.log(`Token refreshed for user: ${user.email} (${user.id})`);

    // 返回成功响应
    return NextResponse.json(
      {
        success: true,
        accessToken: newAccessToken,
        expiresIn: 3600, // 1 hour in seconds
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Token refresh error:', error);

    // 返回通用错误响应
    return NextResponse.json(
      {
        success: false,
        error: '令牌刷新失败',
        message: 'Token refresh failed',
        code: 'REFRESH_FAILED',
      },
      { status: 500 }
    );
  }
}

/**
 * 不支持 GET 请求
 */
export async function GET(): Promise<NextResponse> {
  return NextResponse.json(
    {
      error: '此端点仅支持 POST 请求',
      message: 'This endpoint only supports POST requests',
    },
    { status: 405 }
  );
}

/**
 * 不支持其他 HTTP 方法
 */
export async function PUT(): Promise<NextResponse> {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE(): Promise<NextResponse> {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function PATCH(): Promise<NextResponse> {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
