'use client';

import { useEffect, ReactNode } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useAuthContext } from '@/components/providers/AuthProvider';
import { Loader2 } from 'lucide-react';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

export function AuthGuard({
  children,
  fallback,
  redirectTo = '/login',
  requireAuth = true,
}: AuthGuardProps) {
  const { data: session, status } = useSession();
  const router = useRouter();

  const isAuthenticated = !!session;
  const isLoading = status === 'loading';

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        router.push(redirectTo);
      } else if (!requireAuth && isAuthenticated) {
        // Redirect authenticated users away from auth pages
        router.push('/dashboard');
      }
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, router]);

  // Show loading spinner while checking auth
  if (isLoading) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Loading...</p>
          </div>
        </div>
      )
    );
  }

  // Show children if auth requirements are met
  if (requireAuth && isAuthenticated) {
    return <>{children}</>;
  }

  if (!requireAuth && !isAuthenticated) {
    return <>{children}</>;
  }

  // Don't render anything while redirecting
  return null;
}

// HOC for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    redirectTo?: string;
    fallback?: ReactNode;
  } = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <AuthGuard redirectTo={options.redirectTo} fallback={options.fallback} requireAuth={true}>
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// HOC for preventing authenticated users from accessing pages
export function withoutAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    redirectTo?: string;
    fallback?: ReactNode;
  } = {}
) {
  return function UnauthenticatedComponent(props: P) {
    return (
      <AuthGuard
        redirectTo={options.redirectTo || '/dashboard'}
        fallback={options.fallback}
        requireAuth={false}
      >
        <Component {...props} />
      </AuthGuard>
    );
  };
}

// Route guard for protecting specific routes
export function ProtectedRoute({
  children,
  roles,
  fallback,
}: {
  children: ReactNode;
  roles?: string[];
  fallback?: ReactNode;
}) {
  const { data: session, status } = useSession();
  const router = useRouter();

  const user = session?.user;
  const isLoading = status === 'loading';

  useEffect(() => {
    if (!isLoading) {
      if (!user) {
        router.push('/login');
        return;
      }

      if (roles && (user as any).role && !roles.includes((user as any).role)) {
        router.push('/unauthorized');
        return;
      }
    }
  }, [user, isLoading, roles, router]);

  if (isLoading) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-muted-foreground">Checking permissions...</p>
          </div>
        </div>
      )
    );
  }

  if (!user) {
    return null;
  }

  if (roles && (user as any).role && !roles.includes((user as any).role)) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to access this page.
          </p>
          <button
            onClick={() => router.push('/dashboard')}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Go to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Admin route guard
export function AdminRoute({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <ProtectedRoute roles={['admin']} fallback={fallback}>
      {children}
    </ProtectedRoute>
  );
}

// User route guard (any authenticated user)
export function UserRoute({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return <ProtectedRoute fallback={fallback}>{children}</ProtectedRoute>;
}
