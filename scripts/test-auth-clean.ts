#!/usr/bin/env tsx
/**
 * 认证系统测试运行器（重构版）
 * 直接测试现有API，使用数据库操作进行数据清理
 */

import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

const BASE_URL = 'http://localhost:4000';

// 直接使用数据库客户端
const testDb = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://postgres:postgres@localhost:5432/dictionary',
    },
  },
});

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
};

/**
 * HTTP请求辅助函数
 */
async function apiRequest(endpoint: string, options: RequestInit = {}) {
  try {
    const response = await fetch(`${BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    let data;
    try {
      data = await response.json();
    } catch {
      data = {};
    }

    return {
      response,
      data,
      status: response.status,
      ok: response.ok,
    };
  } catch (error) {
    console.error(`${colors.red}请求失败:${colors.reset}`, error.message);
    return {
      response: null,
      data: { message: error.message },
      status: 500,
      ok: false,
    };
  }
}

/**
 * 数据库清理函数
 */
async function cleanupUser(email: string) {
  try {
    const user = await testDb.user.findUnique({ where: { email } });
    if (user) {
      await testDb.$transaction(async (tx) => {
        await tx.account.deleteMany({ where: { userId: user.id } });
        await tx.session.deleteMany({ where: { userId: user.id } });
        await tx.refreshToken.deleteMany({ where: { userId: user.id } });
        await tx.rateLimit.deleteMany({ where: { userId: user.id } });
        await tx.userWordStat.deleteMany({ where: { userId: user.id } });
        await tx.user.delete({ where: { id: user.id } });
      });
      return true;
    }
    return false;
  } catch (error) {
    console.log(`清理用户失败 ${email}:`, error.message);
    return false;
  }
}

/**
 * 验证用户凭据
 */
async function verifyCredentials(email: string, password: string) {
  try {
    const user = await testDb.user.findUnique({
      where: { email },
      select: { passwordHash: true, isActive: true },
    });

    if (!user || !user.passwordHash) return false;

    const isValid = await bcrypt.compare(password, user.passwordHash);
    return isValid && user.isActive;
  } catch (error) {
    return false;
  }
}

/**
 * 测试结果统计
 */
let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

/**
 * 断言函数
 */
function expect(actual: any, expected: any, message: string) {
  totalTests++;
  if (actual === expected) {
    console.log(`  ${colors.green}✅ ${message}${colors.reset}`);
    passedTests++;
    return true;
  } else {
    console.log(`  ${colors.red}❌ ${message}${colors.reset}`);
    console.log(`     期望: ${expected}, 实际: ${actual}`);
    failedTests++;
    return false;
  }
}

/**
 * 测试套件
 */
async function runAuthTests() {
  console.log(`${colors.bold}${colors.blue}🧪 认证系统自动化测试开始（重构版）${colors.reset}\n`);

  const testUsers: string[] = [];

  // 清理函数
  const cleanup = async () => {
    for (const email of testUsers) {
      await cleanupUser(email);
    }
  };

  try {
    // 1. 注册测试
    console.log(`${colors.yellow}📝 用户注册测试${colors.reset}`);

    const registerEmail = `test-register-${Date.now()}@example.com`;
    testUsers.push(registerEmail);

    // 成功注册
    const register = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: registerEmail,
        password: 'qwer1234',
        name: 'Test User',
      }),
    });

    expect(register.status, 201, '成功注册新用户');
    expect(register.data.message, 'User created successfully', '返回正确的成功消息');
    expect(register.data.user.email, registerEmail, '用户邮箱正确');

    // 验证用户存在于数据库
    const dbUser = await testDb.user.findUnique({ where: { email: registerEmail } });
    expect(dbUser ? true : false, true, '用户已存储到数据库');

    // 重复邮箱注册
    const duplicateRegister = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: registerEmail,
        password: 'different-password',
      }),
    });

    expect(duplicateRegister.status, 400, '拒绝重复邮箱注册');

    // 无效邮箱测试
    const invalidEmail = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: 'invalid-email',
        password: 'qwer1234',
      }),
    });

    expect(invalidEmail.status, 400, '拒绝无效邮箱格式');

    // 密码太短测试
    const shortPassword = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: `short-${Date.now()}@example.com`,
        password: '123',
      }),
    });

    expect(shortPassword.status, 400, '拒绝过短密码');

    // 智能登录测试：已注册邮箱+正确密码
    const smartLoginEmail = `smart-login-${Date.now()}@example.com`;
    const smartLoginPassword = 'qwer1234';
    testUsers.push(smartLoginEmail);

    // 先注册用户
    await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: smartLoginEmail,
        password: smartLoginPassword,
        name: 'Smart Login User',
      }),
    });

    // 在注册入口使用已注册邮箱+正确密码（模拟用户点错入口）
    const smartLogin = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: smartLoginEmail,
        password: smartLoginPassword,
      }),
    });

    expect(smartLogin.status, 200, '智能登录：已注册邮箱+正确密码应自动登录');
    expect(smartLogin.data.message, 'Login successful', '智能登录返回正确消息');
    expect(smartLogin.data.type, 'login', '智能登录返回正确类型');

    // 智能判断测试：已注册邮箱+错误密码
    const smartRejectEmail = `smart-reject-${Date.now()}@example.com`;
    testUsers.push(smartRejectEmail);

    // 先注册用户
    await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: smartRejectEmail,
        password: 'qwer1234',
      }),
    });

    // 在注册入口使用已注册邮箱+错误密码
    const smartReject = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: smartRejectEmail,
        password: 'wrong-password',
      }),
    });

    expect(smartReject.status, 400, '智能判断：已注册邮箱+错误密码应返回邮箱已注册');
    expect(smartReject.data.message, 'User already exists with this email', '智能判断返回正确消息');
    expect(smartReject.data.type, 'user_exists', '智能判断返回正确类型');

    // 2. 凭据验证测试
    console.log(`\n${colors.yellow}🔑 用户凭据验证测试${colors.reset}`);

    const loginEmail = `test-login-${Date.now()}@example.com`;
    const loginPassword = 'qwer1234';
    testUsers.push(loginEmail);

    // 先注册用户
    await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: loginEmail,
        password: loginPassword,
      }),
    });

    // 正确凭据验证
    const validCredentials = await verifyCredentials(loginEmail, loginPassword);
    expect(validCredentials, true, '验证正确的用户凭据');

    // 错误密码验证
    const invalidCredentials = await verifyCredentials(loginEmail, 'wrong-password');
    expect(invalidCredentials, false, '拒绝错误密码');

    // 不存在用户验证
    const nonExistentCredentials = await verifyCredentials(
      '<EMAIL>',
      loginPassword
    );
    expect(nonExistentCredentials, false, '拒绝不存在用户');

    // 3. 删除用户测试
    console.log(`\n${colors.yellow}🗑️ 用户删除测试${colors.reset}`);

    const deleteEmail = `test-delete-${Date.now()}@example.com`;

    // 先创建用户
    await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: deleteEmail,
        password: 'qwer1234',
      }),
    });

    // 成功删除
    const deleteUser = await apiRequest('/api/auth/delete-user', {
      method: 'DELETE',
      body: JSON.stringify({
        email: deleteEmail,
      }),
    });

    expect(deleteUser.status, 200, '成功删除已存在用户');
    expect(deleteUser.data.message, 'User deleted successfully', '返回正确的删除成功消息');

    // 验证用户已被删除
    const deletedUser = await testDb.user.findUnique({ where: { email: deleteEmail } });
    expect(deletedUser ? false : true, true, '用户已从数据库删除');

    // 删除不存在用户
    const deleteNonExistent = await apiRequest('/api/auth/delete-user', {
      method: 'DELETE',
      body: JSON.stringify({
        email: '<EMAIL>',
      }),
    });

    expect(deleteNonExistent.status, 404, '拒绝删除不存在的用户');

    // 4. 完整流程测试
    console.log(`\n${colors.yellow}🔄 完整认证流程测试${colors.reset}`);

    const flowEmail = `flow-${Date.now()}@example.com`;
    const flowPassword = 'qwer1234';

    // 注册 -> 验证凭据 -> 删除
    const flowRegister = await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: flowEmail,
        password: flowPassword,
      }),
    });

    const flowCredentials = await verifyCredentials(flowEmail, flowPassword);

    const flowDelete = await apiRequest('/api/auth/delete-user', {
      method: 'DELETE',
      body: JSON.stringify({
        email: flowEmail,
      }),
    });

    expect(flowRegister.status, 201, '完整流程：注册成功');
    expect(flowCredentials, true, '完整流程：凭据验证成功');
    expect(flowDelete.status, 200, '完整流程：删除成功');

    // 5. 数据一致性测试
    console.log(`\n${colors.yellow}🔒 数据一致性测试${colors.reset}`);

    const consistencyEmail = `consistency-${Date.now()}@example.com`;
    const consistencyPassword = 'qwer1234';
    testUsers.push(consistencyEmail);

    await apiRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify({
        email: consistencyEmail,
        password: consistencyPassword,
        name: 'Consistency Test',
      }),
    });

    const user = await testDb.user.findUnique({
      where: { email: consistencyEmail },
    });

    expect(user ? true : false, true, '用户数据存在');
    expect(user?.email, consistencyEmail, '邮箱数据正确');
    expect(user?.name, 'Consistency Test', '用户名数据正确');
    expect(user?.provider, 'credentials', '提供商数据正确');
    expect(user?.isActive, true, '激活状态正确');

    // 验证密码哈希
    if (user?.passwordHash) {
      const hashValid = await bcrypt.compare(consistencyPassword, user.passwordHash);
      expect(hashValid, true, '密码哈希验证正确');
      expect(user.passwordHash !== consistencyPassword, true, '密码已被正确哈希');
    }
  } catch (error) {
    console.error(`${colors.red}测试执行失败:${colors.reset}`, error);
    failedTests++;
  } finally {
    // 清理测试数据
    await cleanup();
    await testDb.$disconnect();
  }

  // 输出测试结果
  console.log(`\n${colors.bold}📊 测试结果统计${colors.reset}`);
  console.log(`总测试数: ${totalTests}`);
  console.log(`${colors.green}通过: ${passedTests}${colors.reset}`);
  console.log(`${colors.red}失败: ${failedTests}${colors.reset}`);

  if (failedTests === 0) {
    console.log(`\n${colors.green}${colors.bold}🎉 所有测试通过！认证系统工作正常${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`\n${colors.red}${colors.bold}💥 有测试失败，请检查认证系统${colors.reset}`);
    process.exit(1);
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    await fetch(`${BASE_URL}/api/auth/register`);
    return true;
  } catch (error) {
    console.error(`${colors.red}无法连接到服务器 ${BASE_URL}${colors.reset}`);
    console.error('请确保开发服务器正在运行: pnpm dev');
    return false;
  }
}

// 主函数
async function main() {
  console.log(`${colors.blue}检查服务器连接...${colors.reset}`);

  if (await checkServer()) {
    console.log(`${colors.green}服务器连接正常${colors.reset}\n`);
    await runAuthTests();
  } else {
    process.exit(1);
  }
}

main().catch(console.error);
