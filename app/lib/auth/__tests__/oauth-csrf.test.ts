/**
 * OAuth CSRF Protection Test Suite
 *
 * Comprehensive tests for OAuth security features:
 * - State generation and validation
 * - PKCE implementation
 * - Timing-safe comparisons
 * - Security event logging
 * - Attack simulation and prevention
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  OAuthCSRFProtection,
  oauthCSRF,
  generateSecureRandomString,
  validateRedirectUri,
  createOAuthError,
  OAUTH_CSRF_CONFIG,
} from '../oauth-csrf';

// Mock crypto for consistent testing
const mockCrypto = {
  getRandomValues: vi.fn((arr) => {
    for (let i = 0; i < arr.length; i++) {
      arr[i] = Math.floor(Math.random() * 256);
    }
    return arr;
  }),
};

// Use Object.defineProperty to properly mock crypto
Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
  configurable: true,
});

describe('OAuth CSRF Protection System', () => {
  let csrfProtection: OAuthCSRFProtection;

  beforeEach(() => {
    csrfProtection = OAuthCSRFProtection.getInstance();
    csrfProtection.clearAll(); // Reset state for each test
    vi.clearAllMocks();
  });

  afterEach(() => {
    csrfProtection.clearAll();
    vi.restoreAllMocks();
  });

  describe('State Generation', () => {
    it('should generate cryptographically secure state', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid', 'email'],
      });

      expect(state.state).toBeDefined();
      expect(state.state).toHaveLength(64); // 32 bytes = 64 hex chars
      expect(state.provider).toBe('google');
      expect(state.redirectUri).toBe('http://localhost:3000/callback');
      expect(state.scopes).toEqual(['openid', 'email']);
      expect(state.createdAt).toBeTypeOf('number');
      expect(state.expiresAt).toBeTypeOf('number');
      expect(state.expiresAt - state.createdAt).toBe(OAUTH_CSRF_CONFIG.STATE_EXPIRY_MS);
    });

    it('should generate unique states for each request', () => {
      const state1 = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      const state2 = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      expect(state1.state).not.toBe(state2.state);
    });

    it('should generate PKCE challenge when enabled', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
        enablePKCE: true,
      });

      expect(state.codeVerifier).toBeDefined();
      expect(state.codeChallenge).toBeDefined();
      expect(state.codeChallengeMethod).toBe('S256');
      expect(state.codeVerifier).toHaveLength(64);
    });

    it('should generate nonce when enabled', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
        enableNonce: true,
      });

      expect(state.nonce).toBeDefined();
      expect(state.nonce).toHaveLength(32); // 16 bytes = 32 hex chars
    });

    it('should validate input parameters', () => {
      expect(() => {
        csrfProtection.generateOAuthState({
          provider: '',
          redirectUri: 'http://localhost:3000/callback',
          scopes: ['openid'],
        });
      }).toThrow();

      expect(() => {
        csrfProtection.generateOAuthState({
          provider: 'google',
          redirectUri: '',
          scopes: ['openid'],
        });
      }).toThrow();

      expect(() => {
        csrfProtection.generateOAuthState({
          provider: 'google',
          redirectUri: 'http://localhost:3000/callback',
          scopes: [],
        });
      }).toThrow();
    });
  });

  describe('State Validation', () => {
    it('should validate correct state', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      const validation = csrfProtection.validateOAuthState({
        state: state.state,
        code: 'test-code',
      });

      expect(validation.isValid).toBe(true);
      expect(validation.state).toEqual(state);
      expect(validation.error).toBeUndefined();
    });

    it('should reject invalid state format', () => {
      const validation = csrfProtection.validateOAuthState({
        state: 'invalid-state',
      });

      expect(validation.isValid).toBe(false);
      expect(validation.error).toContain('Invalid state parameter format');
      expect(validation.securityEvent?.severity).toBe('MEDIUM');
    });

    it('should reject non-existent state', () => {
      const validation = csrfProtection.validateOAuthState({
        state: 'a'.repeat(64), // Valid format but doesn't exist
      });

      expect(validation.isValid).toBe(false);
      expect(validation.error).toContain('State not found or invalid');
      expect(validation.securityEvent?.severity).toBe('HIGH');
    });

    it('should detect replay attacks', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      // First validation should succeed
      const firstValidation = csrfProtection.validateOAuthState({
        state: state.state,
        code: 'test-code',
      });
      expect(firstValidation.isValid).toBe(true);

      // Second validation should fail (state not found since it was consumed)
      const secondValidation = csrfProtection.validateOAuthState({
        state: state.state,
        code: 'test-code',
      });
      expect(secondValidation.isValid).toBe(false);
      expect(secondValidation.error).toContain('State not found or invalid');
      expect(secondValidation.securityEvent?.severity).toBe('HIGH');
    });

    it('should reject expired state', async () => {
      // Mock Date.now to simulate expiry
      const originalNow = Date.now;
      const fixedTime = 1000000;
      Date.now = vi.fn(() => fixedTime);

      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      // Simulate time passing beyond expiry
      Date.now = vi.fn(() => fixedTime + OAUTH_CSRF_CONFIG.STATE_EXPIRY_MS + 1000);

      const validation = csrfProtection.validateOAuthState({
        state: state.state,
        code: 'test-code',
      });

      expect(validation.isValid).toBe(false);
      expect(validation.error).toContain('State expired');
      expect(validation.securityEvent?.severity).toBe('MEDIUM');

      // Restore original Date.now
      Date.now = originalNow;
    });
  });

  describe('PKCE Validation', () => {
    it('should validate correct PKCE code verifier', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
        enablePKCE: true,
      });

      const isValid = csrfProtection.validatePKCECodeVerifier(
        state.codeVerifier!,
        state.codeChallenge!
      );

      expect(isValid).toBe(true);
    });

    it('should reject incorrect PKCE code verifier', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
        enablePKCE: true,
      });

      const isValid = csrfProtection.validatePKCECodeVerifier(
        'wrong-code-verifier',
        state.codeChallenge!
      );

      expect(isValid).toBe(false);
    });

    it('should handle PKCE validation errors gracefully', () => {
      const isValid = csrfProtection.validatePKCECodeVerifier('invalid', 'invalid');

      expect(isValid).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within rate limit', () => {
      const sessionId = 'test-session';

      // Generate states up to the limit
      for (let i = 0; i < OAUTH_CSRF_CONFIG.MAX_STATE_GENERATIONS_PER_MINUTE; i++) {
        expect(() => {
          csrfProtection.generateOAuthState({
            provider: 'google',
            redirectUri: 'http://localhost:3000/callback',
            scopes: ['openid'],
            sessionId,
          });
        }).not.toThrow();
      }
    });

    it('should block requests exceeding rate limit', () => {
      const sessionId = 'test-session';

      // Fill up the rate limit
      for (let i = 0; i < OAUTH_CSRF_CONFIG.MAX_STATE_GENERATIONS_PER_MINUTE; i++) {
        csrfProtection.generateOAuthState({
          provider: 'google',
          redirectUri: 'http://localhost:3000/callback',
          scopes: ['openid'],
          sessionId,
        });
      }

      // Next request should be blocked
      expect(() => {
        csrfProtection.generateOAuthState({
          provider: 'google',
          redirectUri: 'http://localhost:3000/callback',
          scopes: ['openid'],
          sessionId,
        });
      }).toThrow('Rate limit exceeded');
    });
  });

  describe('Authorization URL Building', () => {
    it('should build complete authorization URL', () => {
      const result = csrfProtection.buildAuthorizationURL({
        provider: 'google',
        clientId: 'test-client-id',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid', 'email'],
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      });

      expect(result.url).toContain('https://accounts.google.com/o/oauth2/v2/auth');
      expect(result.url).toContain('client_id=test-client-id');
      expect(result.url).toContain('redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fcallback');
      expect(result.url).toContain('scope=openid+email');
      expect(result.url).toContain('state=');
      expect(result.url).toContain('code_challenge=');
      expect(result.url).toContain('code_challenge_method=S256');

      expect(result.state).toBeDefined();
      expect(result.state.provider).toBe('google');
    });

    it('should include additional parameters', () => {
      const result = csrfProtection.buildAuthorizationURL({
        provider: 'google',
        clientId: 'test-client-id',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
        additionalParams: {
          prompt: 'consent',
          access_type: 'offline',
        },
      });

      expect(result.url).toContain('prompt=consent');
      expect(result.url).toContain('access_type=offline');
    });
  });

  describe('Callback Validation', () => {
    it('should validate successful OAuth callback', () => {
      const authResult = csrfProtection.buildAuthorizationURL({
        provider: 'google',
        clientId: 'test-client-id',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
        authorizationEndpoint: 'https://accounts.google.com/o/oauth2/v2/auth',
      });

      const validation = csrfProtection.validateCallback({
        callbackParams: {
          code: 'test-authorization-code',
          state: authResult.state.state,
        },
      });

      expect(validation.isValid).toBe(true);
      expect(validation.callbackData?.code).toBe('test-authorization-code');
      expect(validation.callbackData?.state).toEqual(authResult.state);
    });

    it('should handle OAuth provider errors', () => {
      const validation = csrfProtection.validateCallback({
        callbackParams: {
          error: 'access_denied',
          error_description: 'User denied access',
          state: 'a'.repeat(64),
        },
      });

      expect(validation.isValid).toBe(false);
      expect(validation.error).toContain('OAuth provider error: access_denied');
    });

    it('should reject invalid callback parameters', () => {
      const validation = csrfProtection.validateCallback({
        callbackParams: {
          // Missing required code parameter
          state: 'a'.repeat(64),
        },
      });

      expect(validation.isValid).toBe(false);
      expect(validation.error).toContain('Invalid callback parameters');
    });
  });

  describe('Security Events', () => {
    it('should log security events', () => {
      const initialEvents = csrfProtection.getSecurityEvents();
      const initialCount = initialEvents.length;

      // Generate state to trigger event
      csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      const newEvents = csrfProtection.getSecurityEvents();
      expect(newEvents.length).toBeGreaterThan(initialCount);
    });

    it('should provide security statistics', () => {
      const stats = csrfProtection.getSecurityStats();

      expect(stats).toHaveProperty('activeStates');
      expect(stats).toHaveProperty('usedStates');
      expect(stats).toHaveProperty('securityEvents');
      expect(stats).toHaveProperty('criticalEvents');
      expect(typeof stats.activeStates).toBe('number');
      expect(typeof stats.usedStates).toBe('number');
      expect(typeof stats.securityEvents).toBe('number');
      expect(typeof stats.criticalEvents).toBe('number');
    });

    it('should filter security events by time', () => {
      const now = Date.now();
      const oneHourAgo = now - 60 * 60 * 1000;

      // Generate some events
      csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      const recentEvents = csrfProtection.getSecurityEvents(oneHourAgo);
      const allEvents = csrfProtection.getSecurityEvents();

      expect(recentEvents.length).toBeGreaterThanOrEqual(0);
      expect(recentEvents.length).toBeLessThanOrEqual(allEvents.length);
    });
  });

  describe('Utility Functions', () => {
    it('should generate secure random strings', () => {
      const str1 = generateSecureRandomString(16);
      const str2 = generateSecureRandomString(16);

      expect(str1).toHaveLength(32); // 16 bytes = 32 hex chars
      expect(str2).toHaveLength(32);
      expect(str1).not.toBe(str2);
    });

    it('should validate redirect URIs', () => {
      const allowedHosts = ['localhost', '*.example.com', 'api.service.com'];

      expect(validateRedirectUri('http://localhost:3000/callback', allowedHosts)).toBe(true);
      expect(validateRedirectUri('https://sub.example.com/callback', allowedHosts)).toBe(true);
      expect(validateRedirectUri('https://api.service.com/oauth/callback', allowedHosts)).toBe(
        true
      );

      expect(validateRedirectUri('https://malicious.com/callback', allowedHosts)).toBe(false);
      expect(validateRedirectUri('javascript:alert(1)', allowedHosts)).toBe(false);
      expect(validateRedirectUri('invalid-url', allowedHosts)).toBe(false);
    });

    it('should create OAuth error responses', () => {
      const error1 = createOAuthError('invalid_request');
      const error2 = createOAuthError('access_denied', 'User denied access');

      expect(error1.error).toBe('invalid_request');
      expect(error1.timestamp).toBeDefined();
      expect(error1.error_description).toBeUndefined();

      expect(error2.error).toBe('access_denied');
      expect(error2.error_description).toBe('User denied access');
      expect(error2.timestamp).toBeDefined();
    });
  });

  describe('Attack Simulation', () => {
    it('should prevent CSRF attacks with invalid state', () => {
      // Simulate attacker trying to use forged state
      const validation = csrfProtection.validateOAuthState({
        state: 'f'.repeat(64), // Valid format but forged
        code: 'malicious-code',
      });

      expect(validation.isValid).toBe(false);
      expect(validation.securityEvent?.severity).toBe('HIGH');
    });

    it('should prevent replay attacks', () => {
      const state = csrfProtection.generateOAuthState({
        provider: 'google',
        redirectUri: 'http://localhost:3000/callback',
        scopes: ['openid'],
      });

      // First use (legitimate)
      csrfProtection.validateOAuthState({
        state: state.state,
        code: 'auth-code',
      });

      // Replay attempt
      const replayValidation = csrfProtection.validateOAuthState({
        state: state.state,
        code: 'auth-code',
      });

      expect(replayValidation.isValid).toBe(false);
      expect(replayValidation.securityEvent?.type).toBe('STATE_VALIDATION_FAILED');
      expect(replayValidation.securityEvent?.severity).toBe('HIGH');
    });

    it('should handle rate limit attacks', () => {
      const sessionId = 'attacker-session';

      // Simulate rapid fire requests
      expect(() => {
        for (let i = 0; i <= OAUTH_CSRF_CONFIG.MAX_STATE_GENERATIONS_PER_MINUTE; i++) {
          csrfProtection.generateOAuthState({
            provider: 'google',
            redirectUri: 'http://localhost:3000/callback',
            scopes: ['openid'],
            sessionId,
          });
        }
      }).toThrow('Rate limit exceeded');

      const events = csrfProtection.getSecurityEvents();
      const csrfAttackEvents = events.filter((e) => e.type === 'CSRF_ATTACK_DETECTED');
      expect(csrfAttackEvents.length).toBeGreaterThan(0);
    });
  });

  describe('Performance', () => {
    it('should handle multiple concurrent state generations', async () => {
      const promises = Array(50)
        .fill(null)
        .map((_, i) =>
          Promise.resolve().then(() =>
            csrfProtection.generateOAuthState({
              provider: 'google',
              redirectUri: 'http://localhost:3000/callback',
              scopes: ['openid'],
              sessionId: `session-${i}`,
            })
          )
        );

      const states = await Promise.all(promises);

      expect(states).toHaveLength(50);

      // All states should be unique
      const stateValues = states.map((s) => s.state);
      const uniqueStates = new Set(stateValues);
      expect(uniqueStates.size).toBe(50);
    });

    it('should clean up expired states efficiently', () => {
      const originalNow = Date.now;
      const fixedTime = 1000000;
      Date.now = vi.fn(() => fixedTime);

      // Generate states that will expire
      const states = Array(10)
        .fill(null)
        .map(() =>
          csrfProtection.generateOAuthState({
            provider: 'google',
            redirectUri: 'http://localhost:3000/callback',
            scopes: ['openid'],
          })
        );

      expect(csrfProtection.getSecurityStats().activeStates).toBe(10);

      // Simulate time passing
      Date.now = vi.fn(() => fixedTime + OAUTH_CSRF_CONFIG.STATE_EXPIRY_MS + 1000);

      // Trigger cleanup by validating with expired state
      csrfProtection.validateOAuthState({
        state: states[0].state,
      });

      // Stats should reflect cleanup
      const stats = csrfProtection.getSecurityStats();
      expect(stats.activeStates).toBeLessThan(10);

      Date.now = originalNow;
    });
  });
});
