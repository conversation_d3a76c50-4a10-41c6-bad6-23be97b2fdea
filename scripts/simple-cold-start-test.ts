#!/usr/bin/env tsx

/**
 * 简化的冷启动测试脚本
 * 使用 ?fresh=true 参数直接忽略缓存，无需手动清理
 */

const baseUrl = 'http://localhost:3000';

interface TestResult {
  word: string;
  success: boolean;
  responseTime: number;
  cacheSource?: string;
  cacheStatus?: string;
  status?: number;
}

/**
 * 执行冷启动测试
 */
async function runColdStartTest(concurrency: number = 10): Promise<void> {
  console.log('🚀 简化冷启动测试');
  console.log('='.repeat(50));
  console.log(`💡 使用 ?fresh=true 参数强制忽略所有缓存层`);
  console.log(`🎯 并发数: ${concurrency}`);
  console.log('');

  // 测试单词
  const testWords = [
    'computer',
    'science',
    'technology',
    'database',
    'network',
    'algorithm',
    'programming',
    'software',
    'hardware',
    'internet',
    'application',
    'development',
    'framework',
    'library',
    'system',
  ];

  const startTime = performance.now();

  // 创建并发请求
  const promises = Array.from({ length: concurrency }, (_, i) => {
    const word = testWords[i % testWords.length];
    return makeRequest(word, i + 1);
  });

  console.log(`🌐 发起 ${concurrency} 个并发请求...`);
  const results = await Promise.all(promises);
  const totalTime = performance.now() - startTime;

  // 分析结果
  analyzeResults(results, totalTime);
}

/**
 * 发起单个API请求
 */
async function makeRequest(word: string, index: number): Promise<TestResult> {
  const startTime = performance.now();

  try {
    const response = await fetch(`${baseUrl}/api/dictionary/en/${word}?fresh=true`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'User-Agent': 'Simple-Cold-Start-Test/1.0',
      },
      signal: AbortSignal.timeout(15000), // 15秒超时
    });

    const responseTime = performance.now() - startTime;
    const cacheSource = response.headers.get('x-cache-source');
    const cacheStatus = response.headers.get('x-cache-status');

    console.log(
      `   ✅ 请求 ${index}: ${word} (${Math.round(responseTime)}ms, source: ${cacheSource || 'unknown'})`
    );

    return {
      word,
      success: response.ok,
      responseTime,
      cacheSource,
      cacheStatus,
      status: response.status,
    };
  } catch (error) {
    const responseTime = performance.now() - startTime;
    console.log(`   ❌ 请求 ${index}: ${word} 失败 (${Math.round(responseTime)}ms) - ${error}`);

    return {
      word,
      success: false,
      responseTime,
      cacheSource: 'error',
      cacheStatus: 'error',
    };
  }
}

/**
 * 分析测试结果
 */
function analyzeResults(results: TestResult[], totalTime: number): void {
  const successfulRequests = results.filter((r) => r.success);
  const failedRequests = results.filter((r) => !r.success);

  // 响应时间统计
  const responseTimes = successfulRequests.map((r) => r.responseTime);
  const avgResponseTime =
    responseTimes.length > 0
      ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
      : 0;

  const sortedTimes = responseTimes.sort((a, b) => a - b);
  const p95Index = Math.floor(sortedTimes.length * 0.95);
  const p95ResponseTime = sortedTimes[p95Index] || 0;

  // 缓存来源统计
  const sourceStats = results.reduce(
    (acc, result) => {
      const source = result.cacheSource || 'unknown';
      acc[source] = (acc[source] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  console.log('\n📊 测试结果分析:');
  console.log('='.repeat(50));
  console.log(`   总耗时: ${Math.round(totalTime)}ms`);
  console.log(
    `   成功率: ${successfulRequests.length}/${results.length} (${Math.round((successfulRequests.length / results.length) * 100)}%)`
  );
  console.log(`   平均响应时间: ${Math.round(avgResponseTime)}ms`);
  console.log(`   P95响应时间: ${Math.round(p95ResponseTime)}ms`);

  if (failedRequests.length > 0) {
    console.log(`   失败请求: ${failedRequests.length}`);
  }

  console.log('\n🔍 数据来源分析:');
  Object.entries(sourceStats).forEach(([source, count]) => {
    const percentage = Math.round((count / results.length) * 100);
    console.log(`   ${source}: ${count} (${percentage}%)`);
  });

  // 验证冷启动效果
  const databaseRequests = sourceStats['database'] || 0;
  console.log('\n✅ 冷启动验证:');

  if (databaseRequests === successfulRequests.length) {
    console.log('🎉 完美！所有成功请求都直接从数据库获取');
    console.log('   ✅ fresh=true 参数成功绕过了所有缓存层');
  } else if (databaseRequests > 0) {
    console.log(`🟡 部分请求绕过缓存 (${databaseRequests}/${successfulRequests.length})`);
    console.log('   ⚠️ 可能存在某些缓存层未被绕过');
  } else {
    console.log('❌ 没有请求直接从数据库获取');
    console.log('   ⚠️ fresh=true 参数可能未生效，或存在其他问题');
  }

  // 性能评估
  console.log('\n📈 性能评估:');
  if (avgResponseTime < 300) {
    console.log('🎉 优秀！平均响应时间 < 300ms');
  } else if (avgResponseTime < 500) {
    console.log('✅ 良好！平均响应时间 < 500ms');
  } else if (avgResponseTime < 1000) {
    console.log('🟡 一般，平均响应时间 < 1000ms');
  } else {
    console.log('❌ 需要优化，平均响应时间 > 1000ms');
  }

  if (p95ResponseTime < 500) {
    console.log('🎉 优秀！P95响应时间 < 500ms');
  } else if (p95ResponseTime < 1000) {
    console.log('✅ 良好！P95响应时间 < 1000ms');
  } else {
    console.log('🟡 需要关注P95响应时间');
  }
}

/**
 * 主函数
 */
async function main() {
  const concurrency = parseInt(process.argv[2]) || 10;

  console.log('🔧 简化冷启动数据库连接测试');
  console.log('='.repeat(60));
  console.log('💡 优势：');
  console.log('   - 使用 ?fresh=true 参数，无需手动清理缓存');
  console.log('   - 测试更简单、更可靠');
  console.log('   - 避免了缓存清理可能带来的副作用');
  console.log('');

  try {
    await runColdStartTest(concurrency);

    console.log('\n✅ 测试完成');
    console.log('\n💡 使用说明:');
    console.log('   - 默认并发数: 10');
    console.log('   - 自定义并发数: npx tsx scripts/simple-cold-start-test.ts 20');
    console.log('   - fresh=true 确保每次都从数据库获取最新数据');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
