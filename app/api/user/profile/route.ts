/**
 * Protected User Profile API
 * Demonstrates usage of route protection utilities
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth, requireAuth } from '@/lib/auth/route-protection';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import { z } from 'zod';

// Input validation schema
const UpdateProfileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long'),
  avatar: z.string().url('Invalid avatar URL').optional(),
});

// GET /api/user/profile - Get user profile
export const GET = requireAuth({
  rateLimit: { limit: 20, window: 60000 }, // 20 requests per minute
})(async (request: NextRequest, context) => {
  const { user } = context;

  try {
    const profile = await prisma.user.findUnique({
      where: { id: user.id },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        provider: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!profile) {
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    return NextResponse.json({ profile });
  } catch (error) {
    console.error('Profile GET error:', error);
    return NextResponse.json({ error: 'Failed to fetch profile' }, { status: 500 });
  }
});

// PUT /api/user/profile - Update user profile
export const PUT = requireAuth({
  rateLimit: { limit: 5, window: 60000 }, // 5 updates per minute
})(async (request: NextRequest, context) => {
  const { user } = context;

  try {
    const body = await request.json();
    const validatedData = UpdateProfileSchema.parse(body);

    const updatedProfile = await prisma.user.update({
      where: { id: user.id },
      data: {
        name: validatedData.name,
        avatar: validatedData.avatar,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        updatedAt: true,
      },
    });

    return NextResponse.json({ profile: updatedProfile });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          error: 'Validation failed',
          details: error.errors.map((e) => ({ field: e.path.join('.'), message: e.message })),
        },
        { status: 400 }
      );
    }

    console.error('Profile PUT error:', error);
    return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
  }
});
