# NextAuth.js 浏览器扩展 API 解决方案

## 🎯 问题根源分析

项目使用了 **NextAuth.js**，这就是重定向问题的根源：

### NextAuth.js 的默认行为

```typescript
// 当前: app/api/auth/[...nextauth]/route.ts
// NextAuth.js 默认处理所有 /api/auth/* 路径
// 包括 POST /api/auth/signin

// NextAuth.js 的内置行为：
POST /api/auth/signin → 302 重定向到错误页面或成功页面
```

### 为什么会重定向

1. **NextAuth.js 设计理念**：为传统网页应用设计，通过重定向处理认证流程
2. **CSRF 保护**：NextAuth.js 强制要求 CSRF 令牌，没有令牌就重定向
3. **会话管理**：依赖服务端会话和 cookie，不适合 API 调用

## 🚀 解决方案

### 方案1：创建专用的扩展 API 端点（推荐）

创建一个独立的API端点专门为浏览器扩展服务：

```typescript
// 新文件: app/api/auth/extension-signin/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/4-infrastructure/database/prisma';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';

export async function POST(request: NextRequest) {
  try {
    const { email, password, clientType } = await request.json();

    // 验证必填字段
    if (!email || !password) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少必填字段',
          message: 'Missing required fields: email, password',
          code: 'MISSING_FIELDS',
        },
        { status: 400 }
      );
    }

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        passwordHash: true,
        isActive: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在',
          message: 'User not found',
          code: 'USER_NOT_FOUND',
        },
        { status: 401 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: '账户已被禁用',
          message: 'Account is disabled',
          code: 'ACCOUNT_DISABLED',
        },
        { status: 401 }
      );
    }

    // 验证密码
    if (!user.passwordHash) {
      return NextResponse.json(
        {
          success: false,
          error: '该账户需要通过第三方登录',
          message: 'This account requires OAuth sign-in',
          code: 'NO_PASSWORD_SET',
        },
        { status: 401 }
      );
    }

    const isValidPassword = await bcrypt.compare(password, user.passwordHash);

    if (!isValidPassword) {
      return NextResponse.json(
        {
          success: false,
          error: '用户名或密码错误',
          message: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS',
        },
        { status: 401 }
      );
    }

    // 生成 JWT 令牌
    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET is not configured');
    }

    const accessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        iat: Math.floor(Date.now() / 1000),
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    const refreshToken = jwt.sign(
      {
        sub: user.id,
        type: 'refresh',
        iat: Math.floor(Date.now() / 1000),
      },
      jwtSecret,
      { expiresIn: '30d' }
    );

    // 更新最后登录时间
    await prisma.user.update({
      where: { id: user.id },
      data: {
        lastLoginAt: new Date(),
        provider: 'credentials',
      },
    });

    // 返回成功响应
    return NextResponse.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
          avatar: user.avatar,
        },
        accessToken,
        refreshToken,
        expiresIn: 3600, // 1 hour
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Extension signin error:', error);
    return NextResponse.json(
      {
        success: false,
        error: '服务器内部错误',
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
```

### 方案2：修改 NextAuth.js 配置（复杂度高）

在现有的 NextAuth.js 配置中添加 API 模式支持：

```typescript
// 修改: app/api/auth/[...nextauth]/route.ts
// 在 authOptions 中添加：

callbacks: {
  // ... 现有的 callbacks

  // 新增：处理 API 请求
  async signIn({ user, account, profile, email, credentials }) {
    // 检查是否是扩展客户端的 API 请求
    const request = req; // 需要获取原始请求对象
    const isApiRequest = request?.headers.get('content-type')?.includes('application/json') &&
                        request?.headers.get('x-client-type') === 'extension';

    if (isApiRequest && account?.provider === 'credentials') {
      // 对于 API 请求，我们需要返回 JSON 而不是重定向
      // 但这在 NextAuth.js 中很难实现...
    }

    // ... 原有逻辑
  }
}
```

### 🎯 推荐方案：专用扩展 API

**为什么推荐方案1：**

1. **简单清晰**：独立的端点，逻辑清晰
2. **完全控制**：可以完全控制请求和响应格式
3. **无副作用**：不影响现有的 NextAuth.js 网页认证
4. **易于调试**：独立的错误处理和日志
5. **性能更好**：避免 NextAuth.js 的额外处理开销

## 📝 具体实施步骤

### 步骤1：创建扩展登录 API

创建文件：`app/api/auth/extension-signin/route.ts`（见上面代码）

### 步骤2：创建扩展令牌刷新 API

```typescript
// 新文件: app/api/auth/extension-refresh/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { prisma } from '@/lib/4-infrastructure/database/prisma';

export async function POST(request: NextRequest) {
  try {
    const { refreshToken } = await request.json();

    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: '缺少刷新令牌',
          message: 'Missing refresh token',
          code: 'MISSING_REFRESH_TOKEN',
        },
        { status: 400 }
      );
    }

    const jwtSecret = process.env.NEXTAUTH_SECRET || process.env.JWT_SECRET;
    if (!jwtSecret) {
      throw new Error('JWT_SECRET is not configured');
    }

    // 验证刷新令牌
    const decoded = jwt.verify(refreshToken, jwtSecret) as any;

    if (decoded.type !== 'refresh') {
      return NextResponse.json(
        {
          success: false,
          error: '无效的刷新令牌',
          message: 'Invalid refresh token',
          code: 'INVALID_REFRESH_TOKEN',
        },
        { status: 401 }
      );
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: decoded.sub },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        isActive: true,
      },
    });

    if (!user || !user.isActive) {
      return NextResponse.json(
        {
          success: false,
          error: '用户不存在或已被禁用',
          message: 'User not found or disabled',
          code: 'USER_NOT_FOUND',
        },
        { status: 401 }
      );
    }

    // 生成新的访问令牌
    const newAccessToken = jwt.sign(
      {
        sub: user.id,
        email: user.email,
        name: user.name,
        iat: Math.floor(Date.now() / 1000),
      },
      jwtSecret,
      { expiresIn: '1h' }
    );

    return NextResponse.json(
      {
        success: true,
        accessToken: newAccessToken,
        expiresIn: 3600,
      },
      { status: 200 }
    );
  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json(
      {
        success: false,
        error: '令牌刷新失败',
        message: 'Token refresh failed',
        code: 'REFRESH_FAILED',
      },
      { status: 401 }
    );
  }
}
```

### 步骤3：更新前端调用

```typescript
// 浏览器扩展中的登录逻辑
const response = await fetch('http://localhost:4000/api/auth/extension-signin', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'x-client-type': 'extension',
  },
  body: JSON.stringify({
    email: '<EMAIL>',
    password: 'password123',
    clientType: 'extension',
  }),
});

const data = await response.json();

if (data.success) {
  // 保存令牌
  await browser.storage.local.set({
    accessToken: data.accessToken,
    refreshToken: data.refreshToken,
    user: data.user,
  });
} else {
  // 显示错误信息
  console.error('登录失败:', data.error);
}
```

## ✅ 测试验证

### 测试1：成功登录

```bash
curl -X POST http://localhost:4000/api/auth/extension-signin \
  -H "Content-Type: application/json" \
  -H "x-client-type: extension" \
  -d '{
    "email": "<EMAIL>",
    "password": "correctpassword",
    "clientType": "extension"
  }'

# 期望: HTTP 200 + JSON 用户信息和令牌
```

### 测试2：错误密码

```bash
curl -X POST http://localhost:4000/api/auth/extension-signin \
  -H "Content-Type: application/json" \
  -H "x-client-type: extension" \
  -d '{
    "email": "<EMAIL>",
    "password": "wrongpassword",
    "clientType": "extension"
  }'

# 期望: HTTP 401 + JSON 错误信息
```

## 🔄 与现有系统的兼容性

这个解决方案的优点：

1. **不影响现有功能**：NextAuth.js 的网页认证继续正常工作
2. **复用数据库**：使用相同的用户表和密码验证逻辑
3. **一致的安全性**：相同的密码哈希验证机制
4. **独立维护**：扩展 API 可以独立更新和调试

## 📋 总结

问题的根源是 NextAuth.js 的设计理念与 API 调用需求不匹配。通过创建专用的扩展 API 端点，我们可以：

- ✅ 解决重定向问题
- ✅ 提供标准的 JSON API 响应
- ✅ 保持与现有系统的兼容性
- ✅ 支持令牌刷新机制
- ✅ 完整的错误处理

这样既解决了浏览器扩展的集成问题，又不影响现有的网页应用功能。
