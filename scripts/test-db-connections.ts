#!/usr/bin/env tsx

/**
 * 简化的数据库连接测试脚本
 * 验证清空应用级缓存后的数据库连接行为
 */

const baseUrl = 'http://localhost:3000';

interface RequestResult {
  word: string;
  success: boolean;
  responseTime: number;
  status: number;
  error?: string;
}

/**
 * 执行单个请求
 */
async function makeRequest(word: string): Promise<RequestResult> {
  const startTime = performance.now();

  try {
    const response = await fetch(`${baseUrl}/api/dictionary/en/${word}`, {
      method: 'GET',
      headers: {
        Accept: 'application/json',
        'User-Agent': 'DB-Connection-Test/1.0',
      },
      signal: AbortSignal.timeout(10000),
    });

    const endTime = performance.now();
    return {
      word,
      success: response.ok,
      responseTime: endTime - startTime,
      status: response.status,
    };
  } catch (error) {
    const endTime = performance.now();
    return {
      word,
      success: false,
      responseTime: endTime - startTime,
      status: 0,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * 清空所有缓存
 */
async function clearAllCaches(): Promise<void> {
  console.log('🧹 清空所有缓存...');

  try {
    // 1. 清空Redis缓存
    const { redis } = await import('../app/lib/4-infrastructure/cache/redis');
    const keys = await redis.keys('lucid:*');
    if (keys.length > 0) {
      const deletedCount = await redis.del(...keys);
      console.log(`✅ 删除 ${deletedCount} 个Redis缓存键`);
    }

    // 2. 清空应用级缓存
    try {
      const { OptimizedVocabularyRepository } = await import(
        '../app/lib/2-repositories/OptimizedVocabularyRepository'
      );
      const tempRepo = new OptimizedVocabularyRepository();
      tempRepo.clearCache();
      console.log('✅ 应用级Repository缓存已清空');
    } catch (error) {
      console.log(`⚠️ 清空Repository缓存失败: ${error}`);
    }

    try {
      const { vocabularyCache, queryResultCache, relationshipCache } = await import(
        '../app/lib/4-infrastructure/cache/ApplicationCache'
      );
      vocabularyCache.clear();
      queryResultCache.clear();
      relationshipCache.clear();
      console.log('✅ 全局应用缓存已清空');
    } catch (error) {
      console.log(`⚠️ 清空全局缓存失败: ${error}`);
    }
  } catch (error) {
    console.error('❌ 清空缓存失败:', error);
    throw error;
  }
}

/**
 * 检查数据库连接池状态
 */
async function checkDbPoolStatus(): Promise<any> {
  try {
    const { prisma } = await import('../app/lib/4-infrastructure/database/prisma');
    const prismaWithMetrics = prisma as any;

    if (!prismaWithMetrics.$metrics) {
      console.log('⚠️ Prisma metrics 未启用');
      return null;
    }

    const metrics = await prismaWithMetrics.$metrics.json();
    const gauges = metrics.gauges;
    const counters = metrics.counters;

    const getGauge = (name: string) => gauges.find((g: any) => g.key === name)?.value ?? 0;
    const getCounter = (name: string) => counters.find((c: any) => c.key === name)?.value ?? 0;

    const stats = {
      open: getGauge('prisma_pool_connections_open'),
      idle: getGauge('prisma_pool_connections_idle'),
      busy: getGauge('prisma_pool_connections_busy'),
      waitedCount: getCounter('prisma_pool_wait_count'),
    };

    console.log('📊 数据库连接池状态:');
    console.log(
      `   总连接: ${stats.open} | 空闲: ${stats.idle} | 繁忙: ${stats.busy} | 等待数: ${stats.waitedCount}`
    );

    return stats;
  } catch (error) {
    console.error('❌ 检查数据库连接池失败:', error);
    return null;
  }
}

/**
 * 主测试函数
 */
async function main() {
  console.log('🔧 数据库连接测试 - 验证缓存清空效果\n');

  try {
    // 1. 检查初始状态
    console.log('📋 初始状态:');
    await checkDbPoolStatus();

    // 2. 清空所有缓存
    await clearAllCaches();

    // 3. 等待一下确保缓存清空完成
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 4. 检查清空后状态
    console.log('\n📋 清空缓存后状态:');
    await checkDbPoolStatus();

    // 5. 执行5个并发请求
    console.log('\n🚀 执行5个并发请求...');
    const testWords = ['test', 'hello', 'world', 'example', 'sample'];

    const startTime = performance.now();
    const promises = testWords.map((word) => makeRequest(word));
    const results = await Promise.all(promises);
    const endTime = performance.now();

    // 6. 分析结果
    const successful = results.filter((r) => r.success).length;
    const avgTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;

    console.log(`\n📊 测试结果:`);
    console.log(`   成功请求: ${successful}/${results.length}`);
    console.log(`   平均响应时间: ${Math.round(avgTime)}ms`);
    console.log(`   总耗时: ${Math.round(endTime - startTime)}ms`);

    // 7. 检查测试后的连接池状态
    console.log('\n📋 测试后连接池状态:');
    const finalStats = await checkDbPoolStatus();

    // 8. 分析连接池变化
    if (finalStats) {
      console.log('\n🔍 连接池分析:');
      if (finalStats.open > 1) {
        console.log(`✅ 成功！连接池创建了 ${finalStats.open} 个连接`);
        console.log('   这表明缓存清空生效，多个请求都访问了数据库');
      } else if (finalStats.open === 1) {
        console.log('🟡 只有1个连接，可能仍有缓存未清空或连接复用很高效');
      } else {
        console.log('⚠️ 没有活跃连接，这很奇怪');
      }

      if (finalStats.waitedCount > 0) {
        console.log(`🔴 有 ${finalStats.waitedCount} 个请求等待连接，说明连接池压力较大`);
      } else {
        console.log('✅ 没有请求等待连接');
      }
    }

    console.log('\n✅ 测试完成');
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 运行测试
main().catch(console.error);
