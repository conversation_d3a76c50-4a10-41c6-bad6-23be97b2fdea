/**
 * OAuth CSRF Protection System
 *
 * Comprehensive OAuth security implementation with:
 * - Cryptographically secure state generation
 * - PKCE (Proof Key for Code Exchange) support
 * - Timing-safe validation
 * - Security event logging
 * - Nonce validation
 *
 * Based on RFC 6749, RFC 7636 and OWASP OAuth Security Guidelines
 */

import { randomBytes, createHash, timingSafeEqual } from 'crypto';
import { z } from 'zod';

// ============================
// Types and Interfaces
// ============================

export interface OAuthState {
  state: string;
  codeVerifier?: string;
  codeChallenge?: string;
  codeChallengeMethod?: 'S256' | 'plain';
  nonce?: string;
  createdAt: number;
  expiresAt: number;
  provider: string;
  redirectUri: string;
  scopes: string[];
}

export interface StateValidationResult {
  isValid: boolean;
  state?: OAuthState;
  error?: string;
  securityEvent?: SecurityEvent;
}

export interface SecurityEvent {
  type:
    | 'STATE_VALIDATION_SUCCESS'
    | 'STATE_VALIDATION_FAILED'
    | 'STATE_EXPIRED'
    | 'STATE_REPLAY_ATTACK'
    | 'CSRF_ATTACK_DETECTED';
  timestamp: number;
  details: Record<string, any>;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface PKCEChallenge {
  codeVerifier: string;
  codeChallenge: string;
  codeChallengeMethod: 'S256';
}

// ============================
// Configuration
// ============================

export const OAUTH_CSRF_CONFIG = {
  // State expiration time (15 minutes)
  STATE_EXPIRY_MS: 15 * 60 * 1000,

  // Maximum states to store per session
  MAX_STATES_PER_SESSION: 10,

  // State parameter length (256 bits)
  STATE_LENGTH: 32,

  // Code verifier length (43-128 characters)
  CODE_VERIFIER_LENGTH: 64,

  // Nonce length
  NONCE_LENGTH: 16,

  // Storage keys
  STORAGE_KEYS: {
    STATES: 'oauth_states',
    USED_STATES: 'oauth_used_states',
  },

  // Security settings
  ENABLE_PKCE: true,
  ENABLE_NONCE: true,
  ENABLE_REPLAY_PROTECTION: true,

  // Rate limiting
  MAX_STATE_GENERATIONS_PER_MINUTE: 10,
} as const;

// ============================
// Validation Schemas
// ============================

const StateSchema = z.object({
  state: z.string().length(64), // 32 bytes = 64 hex chars
  codeVerifier: z.string().optional(),
  codeChallenge: z.string().optional(),
  codeChallengeMethod: z.enum(['S256', 'plain']).optional(),
  nonce: z.string().optional(),
  createdAt: z.number(),
  expiresAt: z.number(),
  provider: z.string(),
  redirectUri: z.string().url(),
  scopes: z.array(z.string()),
});

const CallbackParamsSchema = z
  .object({
    code: z.string().min(1).optional(),
    state: z.string().length(64),
    error: z.string().optional(),
    error_description: z.string().optional(),
  })
  .refine((data) => data.code || data.error, {
    message: "Either 'code' or 'error' must be present",
    path: ['code', 'error'],
  });

// ============================
// Core CSRF Protection Class
// ============================

export class OAuthCSRFProtection {
  private static instance: OAuthCSRFProtection;
  private stateStorage: Map<string, OAuthState> = new Map();
  private usedStates: Set<string> = new Set();
  private securityEvents: SecurityEvent[] = [];
  private rateLimitTracker: Map<string, number[]> = new Map();

  private constructor() {
    // Cleanup expired states every 5 minutes
    setInterval(() => this.cleanupExpiredStates(), 5 * 60 * 1000);

    // Cleanup old security events every hour
    setInterval(() => this.cleanupSecurityEvents(), 60 * 60 * 1000);
  }

  public static getInstance(): OAuthCSRFProtection {
    if (!OAuthCSRFProtection.instance) {
      OAuthCSRFProtection.instance = new OAuthCSRFProtection();
    }
    return OAuthCSRFProtection.instance;
  }

  // ============================
  // Cryptographic Functions
  // ============================

  /**
   * Generate cryptographically secure random state
   */
  private generateSecureState(): string {
    const buffer = randomBytes(OAUTH_CSRF_CONFIG.STATE_LENGTH);
    return buffer.toString('hex');
  }

  /**
   * Generate PKCE code verifier and challenge
   */
  private generatePKCEChallenge(): PKCEChallenge {
    // Generate code verifier (43-128 characters, URL-safe)
    const codeVerifier = randomBytes(OAUTH_CSRF_CONFIG.CODE_VERIFIER_LENGTH)
      .toString('base64url')
      .slice(0, OAUTH_CSRF_CONFIG.CODE_VERIFIER_LENGTH);

    // Generate code challenge (SHA256 hash of verifier)
    const codeChallenge = createHash('sha256').update(codeVerifier).digest('base64url');

    return {
      codeVerifier,
      codeChallenge,
      codeChallengeMethod: 'S256',
    };
  }

  /**
   * Generate cryptographically secure nonce
   */
  private generateNonce(): string {
    return randomBytes(OAUTH_CSRF_CONFIG.NONCE_LENGTH).toString('hex');
  }

  /**
   * Timing-safe string comparison to prevent timing attacks
   */
  private timingSafeCompare(a: string, b: string): boolean {
    if (a.length !== b.length) {
      return false;
    }

    try {
      const bufferA = Buffer.from(a, 'utf8');
      const bufferB = Buffer.from(b, 'utf8');
      return timingSafeEqual(bufferA, bufferB);
    } catch {
      return false;
    }
  }

  // ============================
  // Rate Limiting
  // ============================

  private checkRateLimit(sessionId: string): boolean {
    const now = Date.now();
    const oneMinuteAgo = now - 60 * 1000;

    if (!this.rateLimitTracker.has(sessionId)) {
      this.rateLimitTracker.set(sessionId, []);
    }

    const requests = this.rateLimitTracker.get(sessionId)!;

    // Remove old requests
    const recentRequests = requests.filter((time) => time > oneMinuteAgo);
    this.rateLimitTracker.set(sessionId, recentRequests);

    // Check if rate limit exceeded
    if (recentRequests.length >= OAUTH_CSRF_CONFIG.MAX_STATE_GENERATIONS_PER_MINUTE) {
      this.logSecurityEvent({
        type: 'CSRF_ATTACK_DETECTED',
        timestamp: now,
        details: {
          sessionId,
          requestCount: recentRequests.length,
          reason: 'Rate limit exceeded for state generation',
        },
        severity: 'HIGH',
      });
      return false;
    }

    // Record this request
    recentRequests.push(now);
    this.rateLimitTracker.set(sessionId, recentRequests);

    return true;
  }

  // ============================
  // State Management
  // ============================

  /**
   * Generate OAuth state with CSRF protection
   */
  public generateOAuthState(params: {
    provider: string;
    redirectUri: string;
    scopes: string[];
    sessionId?: string;
    enablePKCE?: boolean;
    enableNonce?: boolean;
  }): OAuthState {
    const {
      provider,
      redirectUri,
      scopes,
      sessionId = 'anonymous',
      enablePKCE = OAUTH_CSRF_CONFIG.ENABLE_PKCE,
      enableNonce = OAUTH_CSRF_CONFIG.ENABLE_NONCE,
    } = params;

    // Check rate limiting
    if (!this.checkRateLimit(sessionId)) {
      throw new Error('Rate limit exceeded for OAuth state generation');
    }

    // Validate inputs
    if (!provider || !redirectUri || !Array.isArray(scopes) || scopes.length === 0) {
      throw new Error('Invalid parameters for OAuth state generation');
    }

    const now = Date.now();
    const state = this.generateSecureState();

    // Generate PKCE challenge if enabled
    let pkceData: Partial<PKCEChallenge> = {};
    if (enablePKCE) {
      pkceData = this.generatePKCEChallenge();
    }

    // Generate nonce if enabled
    const nonce = enableNonce ? this.generateNonce() : undefined;

    const oauthState: OAuthState = {
      state,
      codeVerifier: pkceData.codeVerifier,
      codeChallenge: pkceData.codeChallenge,
      codeChallengeMethod: pkceData.codeChallengeMethod,
      nonce,
      createdAt: now,
      expiresAt: now + OAUTH_CSRF_CONFIG.STATE_EXPIRY_MS,
      provider,
      redirectUri,
      scopes,
    };

    // Validate the state object
    const validation = StateSchema.safeParse(oauthState);
    if (!validation.success) {
      throw new Error(`Invalid OAuth state generated: ${validation.error.message}`);
    }

    // Store the state
    this.stateStorage.set(state, oauthState);

    // Cleanup old states for this session if needed
    this.cleanupOldStatesForSession(sessionId);

    this.logSecurityEvent({
      type: 'STATE_VALIDATION_SUCCESS',
      timestamp: now,
      details: {
        provider,
        sessionId,
        enablePKCE,
        enableNonce,
        scopesCount: scopes.length,
      },
      severity: 'LOW',
    });

    return oauthState;
  }

  /**
   * Validate OAuth state from callback
   */
  public validateOAuthState(params: {
    state: string;
    code?: string;
    error?: string;
    sessionId?: string;
  }): StateValidationResult {
    const { state, code, error, sessionId = 'anonymous' } = params;
    const now = Date.now();

    try {
      // Basic validation
      if (!state || state.length !== 64) {
        return this.createValidationError('Invalid state parameter format', 'MEDIUM');
      }

      // Check if state exists
      const storedState = this.stateStorage.get(state);
      if (!storedState) {
        return this.createValidationError('State not found or invalid', 'HIGH');
      }

      // Check for replay attacks
      if (this.usedStates.has(state)) {
        return this.createValidationError('State replay attack detected', 'CRITICAL');
      }

      // Check expiration
      if (now > storedState.expiresAt) {
        this.stateStorage.delete(state);
        return this.createValidationError('State expired', 'MEDIUM');
      }

      // Timing-safe validation
      if (!this.timingSafeCompare(state, storedState.state)) {
        return this.createValidationError('State validation failed', 'HIGH');
      }

      // Mark state as used
      this.usedStates.add(state);
      this.stateStorage.delete(state);

      // Log successful validation
      this.logSecurityEvent({
        type: 'STATE_VALIDATION_SUCCESS',
        timestamp: now,
        details: {
          provider: storedState.provider,
          sessionId,
          hasCode: !!code,
          hasError: !!error,
        },
        severity: 'LOW',
      });

      return {
        isValid: true,
        state: storedState,
      };
    } catch (err) {
      return this.createValidationError(
        `State validation error: ${err instanceof Error ? err.message : 'Unknown error'}`,
        'HIGH'
      );
    }
  }

  /**
   * Validate PKCE code verifier
   */
  public validatePKCECodeVerifier(codeVerifier: string, codeChallenge: string): boolean {
    try {
      const computedChallenge = createHash('sha256').update(codeVerifier).digest('base64url');

      return this.timingSafeCompare(computedChallenge, codeChallenge);
    } catch {
      return false;
    }
  }

  // ============================
  // Helper Methods
  // ============================

  private createValidationError(
    message: string,
    severity: SecurityEvent['severity']
  ): StateValidationResult {
    const securityEvent: SecurityEvent = {
      type: 'STATE_VALIDATION_FAILED',
      timestamp: Date.now(),
      details: { message },
      severity,
    };

    this.logSecurityEvent(securityEvent);

    return {
      isValid: false,
      error: message,
      securityEvent,
    };
  }

  private logSecurityEvent(event: SecurityEvent): void {
    this.securityEvents.push(event);

    // Log to console for development/debugging
    if (process.env.NODE_ENV !== 'production') {
      console.log(`[OAuth CSRF] ${event.type}:`, event.details);
    }

    // In production, you might want to send to monitoring service
    if (process.env.NODE_ENV === 'production' && event.severity === 'CRITICAL') {
      // TODO: Send to security monitoring service
      console.error('[SECURITY ALERT] OAuth CSRF Protection:', event);
    }
  }

  private cleanupExpiredStates(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [state, oauthState] of this.stateStorage.entries()) {
      if (now > oauthState.expiresAt) {
        this.stateStorage.delete(state);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logSecurityEvent({
        type: 'STATE_VALIDATION_SUCCESS',
        timestamp: now,
        details: {
          action: 'cleanup_expired_states',
          cleanedCount,
        },
        severity: 'LOW',
      });
    }
  }

  private cleanupOldStatesForSession(sessionId: string): void {
    // This is a simplified cleanup - in a real implementation,
    // you'd want to track states per session
    if (this.stateStorage.size > OAUTH_CSRF_CONFIG.MAX_STATES_PER_SESSION * 2) {
      const oldestStates = Array.from(this.stateStorage.entries())
        .sort(([, a], [, b]) => a.createdAt - b.createdAt)
        .slice(0, OAUTH_CSRF_CONFIG.MAX_STATES_PER_SESSION);

      for (const [state] of oldestStates) {
        this.stateStorage.delete(state);
      }
    }
  }

  private cleanupSecurityEvents(): void {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.securityEvents = this.securityEvents.filter((event) => event.timestamp > oneHourAgo);
  }

  // ============================
  // Public API Methods
  // ============================

  /**
   * Get authorization URL with CSRF protection
   */
  public buildAuthorizationURL(params: {
    provider: string;
    clientId: string;
    redirectUri: string;
    scopes: string[];
    authorizationEndpoint: string;
    sessionId?: string;
    additionalParams?: Record<string, string>;
  }): { url: string; state: OAuthState } {
    const {
      provider,
      clientId,
      redirectUri,
      scopes,
      authorizationEndpoint,
      sessionId,
      additionalParams = {},
    } = params;

    // Generate OAuth state with CSRF protection
    const oauthState = this.generateOAuthState({
      provider,
      redirectUri,
      scopes,
      sessionId,
    });

    // Build URL parameters
    const urlParams = new URLSearchParams({
      response_type: 'code',
      client_id: clientId,
      redirect_uri: redirectUri,
      scope: scopes.join(' '),
      state: oauthState.state,
      ...additionalParams,
    });

    // Add PKCE parameters if enabled
    if (oauthState.codeChallenge) {
      urlParams.set('code_challenge', oauthState.codeChallenge);
      urlParams.set('code_challenge_method', oauthState.codeChallengeMethod || 'S256');
    }

    // Add nonce if enabled
    if (oauthState.nonce) {
      urlParams.set('nonce', oauthState.nonce);
    }

    const url = `${authorizationEndpoint}?${urlParams.toString()}`;

    return { url, state: oauthState };
  }

  /**
   * Validate OAuth callback with comprehensive security checks
   */
  public validateCallback(params: {
    callbackParams: Record<string, string>;
    sessionId?: string;
  }): StateValidationResult & { callbackData?: any } {
    const { callbackParams, sessionId } = params;

    try {
      // Validate callback parameters
      const validation = CallbackParamsSchema.safeParse(callbackParams);
      if (!validation.success) {
        return this.createValidationError(
          `Invalid callback parameters: ${validation.error.message}`,
          'HIGH'
        );
      }

      const { code, state, error, error_description } = validation.data;

      // Check for OAuth errors
      if (error) {
        return this.createValidationError(
          `OAuth provider error: ${error}${error_description ? ` - ${error_description}` : ''}`,
          'MEDIUM'
        );
      }

      // Validate state
      const stateValidation = this.validateOAuthState({
        state,
        code,
        sessionId,
      });

      if (!stateValidation.isValid) {
        return stateValidation;
      }

      return {
        ...stateValidation,
        callbackData: {
          code,
          state: stateValidation.state,
        },
      };
    } catch (err) {
      return this.createValidationError(
        `Callback validation error: ${err instanceof Error ? err.message : 'Unknown error'}`,
        'HIGH'
      );
    }
  }

  /**
   * Get security events for monitoring
   */
  public getSecurityEvents(since?: number): SecurityEvent[] {
    const sinceTime = since || Date.now() - 24 * 60 * 60 * 1000; // Last 24 hours
    return this.securityEvents.filter((event) => event.timestamp > sinceTime);
  }

  /**
   * Get current security statistics
   */
  public getSecurityStats(): {
    activeStates: number;
    usedStates: number;
    securityEvents: number;
    criticalEvents: number;
  } {
    const criticalEvents = this.securityEvents.filter((e) => e.severity === 'CRITICAL').length;

    return {
      activeStates: this.stateStorage.size,
      usedStates: this.usedStates.size,
      securityEvents: this.securityEvents.length,
      criticalEvents,
    };
  }

  /**
   * Clear all states and events (for testing or emergency)
   */
  public clearAll(): void {
    this.stateStorage.clear();
    this.usedStates.clear();
    this.securityEvents = [];
    this.rateLimitTracker.clear();
  }
}

// ============================
// Singleton Instance
// ============================

export const oauthCSRF = OAuthCSRFProtection.getInstance();

// ============================
// Utility Functions
// ============================

/**
 * Generate a secure random string for use in OAuth flows
 */
export function generateSecureRandomString(length: number = 32): string {
  return randomBytes(length).toString('hex');
}

/**
 * Validate URL to prevent open redirect attacks
 */
export function validateRedirectUri(uri: string, allowedHosts: string[]): boolean {
  try {
    const url = new URL(uri);
    return allowedHosts.some((host) => {
      if (host.startsWith('*.')) {
        const domain = host.slice(2);
        return url.hostname === domain || url.hostname.endsWith(`.${domain}`);
      }
      return url.hostname === host;
    });
  } catch {
    return false;
  }
}

/**
 * Create a secure OAuth error response
 */
export function createOAuthError(
  error: string,
  description?: string
): {
  error: string;
  error_description?: string;
  timestamp: string;
} {
  return {
    error,
    error_description: description,
    timestamp: new Date().toISOString(),
  };
}
