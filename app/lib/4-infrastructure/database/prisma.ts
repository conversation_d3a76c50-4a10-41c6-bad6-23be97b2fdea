/**
 * Prisma数据库客户端
 * 基础设施层 - 数据库连接管理
 */

import { PrismaClient } from '@prisma/client';

declare global {
  var __prisma: PrismaClient | undefined;
}

/**
 * 创建Prisma客户端实例
 * 在开发环境中使用全局变量避免热重载时创建多个连接
 */
function createPrismaClient(): PrismaClient {
  // 从环境变量获取连接池配置
  const connectionLimit = parseInt(process.env.DB_CONNECTION_LIMIT || '100', 10);
  const poolTimeout = parseInt(process.env.DB_POOL_TIMEOUT || '20', 10);
  const connectTimeout = parseInt(process.env.DB_CONNECT_TIMEOUT || '60', 10);

  // 使用原始DATABASE_URL，不添加复杂的连接池参数
  const databaseUrl =
    process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5432/dictionary';

  // 🔍 DEBUG: 连接池配置
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 Prisma数据库连接配置:', {
      databaseUrl: databaseUrl.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'), // 隐藏密码
    });
  }

  const client = new PrismaClient({
    log:
      process.env.NODE_ENV === 'development'
        ? ['info', 'warn', 'error'] // 添加info日志来查看连接池信息
        : ['warn', 'error'],
    errorFormat: 'pretty',
  });

  // 🔍 DEBUG: 在开发环境中输出连接池信息
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 Prisma客户端已创建，连接池信息将在首次查询时显示');
  }

  return client;
}

/**
 * 获取Prisma客户端实例
 * 单例模式确保整个应用只有一个数据库连接池
 */
export const prisma = globalThis.__prisma || createPrismaClient();

if (process.env.NODE_ENV === 'development') {
  globalThis.__prisma = prisma;
}

/**
 * 优雅关闭数据库连接
 */
export async function disconnectPrisma(): Promise<void> {
  await prisma.$disconnect();
}

/**
 * 数据库健康检查
 */
export async function checkDatabaseHealth(): Promise<boolean> {
  try {
    await prisma.$queryRaw`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
}

/**
 * 数据库连接池监控装饰器
 */
export function withConnectionPoolMonitoring<T extends any[], R>(
  operation: string,
  fn: (...args: T) => Promise<R>
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const connectionId = Math.random().toString(36).substring(2, 8);
    const startTime = performance.now();

    // 🔍 DEBUG: 数据库连接开始
    const { Logger } = await import('../../utils/logger');
    Logger.debug(`[CONN-${connectionId}] 🔌 数据库连接请求`, {
      connectionId,
      operation,
      timestamp: new Date().toISOString(),
    });

    try {
      // 获取连接前的统计
      const statsBefore = await getDatabaseStats();
      Logger.debug(`[CONN-${connectionId}] 📊 连接前状态`, {
        connectionId,
        openConnections: statsBefore.openConnections,
        idleConnections: statsBefore.idleConnections,
        busyConnections: statsBefore.busyConnections,
      });

      const result = await fn(...args);

      const executionTime = performance.now() - startTime;
      const statsAfter = await getDatabaseStats();

      // 🔍 DEBUG: 数据库操作完成
      Logger.debug(`[CONN-${connectionId}] ✅ 数据库操作完成`, {
        connectionId,
        operation,
        executionTime: Math.round(executionTime),
        openConnections: statsAfter.openConnections,
        idleConnections: statsAfter.idleConnections,
        busyConnections: statsAfter.busyConnections,
      });

      return result;
    } catch (error) {
      const errorTime = performance.now() - startTime;

      // 🔍 DEBUG: 数据库操作失败
      Logger.debug(`[CONN-${connectionId}] ❌ 数据库操作失败`, {
        connectionId,
        operation,
        errorTime: Math.round(errorTime),
        error: error instanceof Error ? error.message : String(error),
      });

      throw error;
    }
  };
}

/**
 * 获取数据库连接统计
 */
export async function getDatabaseStats(): Promise<{
  connectionCount: number;
  activeQueries: number;
  version: string;
  openConnections: number;
  idleConnections: number;
  busyConnections: number;
  poolStats?: {
    totalConnections: number;
    idleConnections: number;
    activeConnections: number;
  };
}> {
  try {
    const [connectionResult, versionResult, poolStatsResult] = await Promise.all([
      prisma.$queryRaw<Array<{ count: bigint }>>`
        SELECT count(*) as count
        FROM pg_stat_activity
        WHERE state = 'active'
      `,
      prisma.$queryRaw<Array<{ version: string }>>`SELECT version()`,
      // 获取连接池详细统计
      prisma.$queryRaw<
        Array<{
          total_conns: number;
          active_conns: number;
          idle_conns: number;
        }>
      >`
        SELECT
          count(*) as total_conns,
          count(*) FILTER (WHERE state = 'active') as active_conns,
          count(*) FILTER (WHERE state = 'idle') as idle_conns
        FROM pg_stat_activity
        WHERE datname = current_database()
      `,
    ]);

    const poolStats = poolStatsResult[0];

    return {
      connectionCount: Number(connectionResult[0]?.count || 0),
      activeQueries: Number(poolStats?.active_conns || 0),
      version: versionResult[0]?.version || 'unknown',
      openConnections: Number(poolStats?.total_conns || 0),
      idleConnections: Number(poolStats?.idle_conns || 0),
      busyConnections: Number(poolStats?.active_conns || 0),
      poolStats: poolStats
        ? {
            totalConnections: Number(poolStats.total_conns),
            idleConnections: Number(poolStats.idle_conns),
            activeConnections: Number(poolStats.active_conns),
          }
        : undefined,
    };
  } catch (error) {
    console.error('Failed to get database stats:', error);
    return {
      connectionCount: 0,
      activeQueries: 0,
      version: 'unknown',
      openConnections: 0,
      idleConnections: 0,
      busyConnections: 0,
    };
  }
}

/**
 * 执行数据库事务
 */
export async function executeTransaction<T>(
  operation: (
    tx: Omit<
      PrismaClient,
      '$connect' | '$disconnect' | '$on' | '$transaction' | '$use' | '$extends'
    >
  ) => Promise<T>
): Promise<T> {
  return prisma.$transaction(operation);
}

/**
 * 批量操作辅助函数
 */
export async function batchOperation<T, R>(
  items: T[],
  operation: (batch: T[]) => Promise<R[]>,
  batchSize: number = 100
): Promise<R[]> {
  const results: R[] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    const batch = items.slice(i, i + batchSize);
    const batchResults = await operation(batch);
    results.push(...batchResults);
  }

  return results;
}

// 进程退出时清理连接
process.on('beforeExit', async () => {
  await disconnectPrisma();
});

process.on('SIGINT', async () => {
  await disconnectPrisma();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await disconnectPrisma();
  process.exit(0);
});
