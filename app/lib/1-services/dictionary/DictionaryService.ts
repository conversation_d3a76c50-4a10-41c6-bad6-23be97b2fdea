/**
 * 词典业务服务
 * 封装词典查询的核心业务逻辑
 */

import {
  SearchResult,
  QueryMetadata,
  WordFormRelationship,
} from '../../3-domain/value-objects/SearchResult';
import { Vocabulary } from '../../3-domain/entities/Vocabulary';
import {
  IVocabularyRepository,
  IWordFormatRepository,
  ICacheRepository,
} from '../../2-repositories/interfaces/IRepository';
import {
  CacheSource,
  CacheMetadata,
  CacheLogger,
  CachePerformanceTracker,
} from '../../utils/cache-types';
import {
  relationshipCache,
  suggestionCache,
  CacheKeys as AppCacheKeys,
} from '../../4-infrastructure/cache/ApplicationCache';

export interface DictionaryServiceDependencies {
  vocabularyRepository: IVocabularyRepository;
  wordFormatRepository: IWordFormatRepository;
  cacheRepository: ICacheRepository;
}

/**
 * 词典服务类
 * 负责协调各个组件完成词典查询业务
 */
export class DictionaryService {
  constructor(private dependencies: DictionaryServiceDependencies) {}

  /**
   * 查询词汇（主要业务方法）
   * 实现多策略查询和缓存逻辑
   */
  async lookupWord(searchTerm: string, options: LookupOptions = {}): Promise<SearchResult> {
    const startTime = Date.now();
    const serviceId = Math.random().toString(36).substring(2, 10);
    const normalizedTerm = this.normalizeSearchTerm(searchTerm);

    // 🔍 DEBUG: 服务查询开始
    const { Logger } = await import('../../utils/logger');
    Logger.debug(`[SVC-${serviceId}] 🔍 DictionaryService查询开始`, {
      serviceId,
      searchTerm,
      normalizedTerm,
      useCache: options.useCache !== false,
      cacheTTL: options.cacheTTL,
      timestamp: new Date().toISOString(),
    });

    try {
      // 1. 检查缓存
      let cacheCheckTime = 0;
      if (options.useCache !== false) {
        const cacheStart = performance.now();
        Logger.debug(`[SVC-${serviceId}] 🎯 检查缓存`, { serviceId, term: normalizedTerm });

        const cachedResult = await this.getCachedResult(normalizedTerm);
        cacheCheckTime = performance.now() - cacheStart;

        if (cachedResult) {
          Logger.debug(`[SVC-${serviceId}] ✅ 缓存命中`, {
            serviceId,
            cacheCheckTime: Math.round(cacheCheckTime),
            totalTime: Date.now() - startTime,
          });
          return cachedResult.withCacheMetadata(true);
        } else {
          Logger.debug(`[SVC-${serviceId}] ❌ 缓存未命中`, {
            serviceId,
            cacheCheckTime: Math.round(cacheCheckTime),
          });
        }
      }

      // 2. 执行查询策略
      const strategyStart = performance.now();
      Logger.debug(`[SVC-${serviceId}] 🔍 执行查询策略`, { serviceId });

      const result = await this.executeSearchStrategies(normalizedTerm, startTime);
      const strategyTime = performance.now() - strategyStart;

      Logger.debug(`[SVC-${serviceId}] ✅ 查询策略完成`, {
        serviceId,
        strategyTime: Math.round(strategyTime),
        found: result.isFound(),
        strategy: result.queryMetadata?.searchStrategy,
      });

      // 3. 缓存结果
      if (options.useCache !== false && result.isFound()) {
        const cacheSetStart = performance.now();
        Logger.debug(`[SVC-${serviceId}] 💾 设置缓存`, { serviceId });

        await this.cacheResult(normalizedTerm, result, options.cacheTTL);
        const cacheSetTime = performance.now() - cacheSetStart;

        Logger.debug(`[SVC-${serviceId}] ✅ 缓存设置完成`, {
          serviceId,
          cacheSetTime: Math.round(cacheSetTime),
        });
      }

      const totalTime = Date.now() - startTime;
      Logger.debug(`[SVC-${serviceId}] 🎉 服务查询完成`, {
        serviceId,
        totalTime,
        cacheCheckTime: Math.round(cacheCheckTime),
        strategyTime: Math.round(strategyTime),
        found: result.isFound(),
      });

      return result.withCacheMetadata(false);
    } catch (error) {
      const errorTime = Date.now() - startTime;
      Logger.debug(`[SVC-${serviceId}] ❌ 服务查询失败`, {
        serviceId,
        errorTime,
        error: error instanceof Error ? error.message : String(error),
      });

      console.error('Dictionary lookup error:', error);
      return this.createErrorResult(normalizedTerm, startTime, error);
    }
  }

  /**
   * 批量查询词汇
   */
  async lookupWords(searchTerms: string[], options: LookupOptions = {}): Promise<SearchResult[]> {
    const normalizedTerms = searchTerms.map((term) => this.normalizeSearchTerm(term));

    // 并行查询，但限制并发数
    const batchSize = options.batchSize || 10;
    const results: SearchResult[] = [];

    for (let i = 0; i < normalizedTerms.length; i += batchSize) {
      const batch = normalizedTerms.slice(i, i + batchSize);
      const batchResults = await Promise.all(batch.map((term) => this.lookupWord(term, options)));
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * 获取相关词汇建议
   */
  async getRelatedSuggestions(searchTerm: string, limit: number = 5): Promise<string[]> {
    const cacheKey = AppCacheKeys.suggestions(searchTerm);
    const cachedSuggestions = suggestionCache.get(cacheKey);
    if (cachedSuggestions) {
      CacheLogger.logHit(
        CacheSource.MEMORY_CACHE,
        { key: cacheKey, layer: 'app-suggestion-cache' },
        searchTerm
      );
      return cachedSuggestions;
    }

    try {
      //
      // Action: Modified
      // Timestamp: [2025-06-10 10:27:00 +08:00]
      // Reason: [适配仓储层 findSimilar 方法的返回值变化，现在它只返回轻量级对象，服务层只需提取 word 字符串。同时添加建议缓存支持。]
      // Principle_Applied: [Decoupling - 服务层不关心仓储层如何获取数据，只关心最终结果。Performance - 缓存频繁访问的建议数据。]
      //
      // {{START MODIFICATIONS}}
      const vocabs = await this.dependencies.vocabularyRepository.findSimilar(searchTerm, limit);
      const suggestions = vocabs.map((vocab) => vocab.word);
      // {{END MODIFICATIONS}}
      suggestionCache.set(cacheKey, suggestions);
      CacheLogger.logSet(CacheSource.MEMORY_CACHE, cacheKey, suggestionCache.defaultTTL / 1000);
      return suggestions;
    } catch (error) {
      console.error('获取建议时出错:', error);
      return [];
    }
  }

  /**
   * 执行搜索策略
   * 按优先级依次尝试不同的查询策略
   */
  private async executeSearchStrategies(
    searchTerm: string,
    startTime: number
  ): Promise<SearchResult> {
    let queryCount = 0;
    let vocabulary: Vocabulary | null = null;
    let relationships: WordFormRelationship[] = [];
    let strategy: 'exact_vocabulary' | 'main_wordformat' | 'derived_wordformat' =
      'exact_vocabulary';
    let isBaseFormQuery = false;

    // 策略1: 直接词汇匹配
    const directResult = await this.tryDirectVocabularyLookup(searchTerm);
    queryCount++;

    if (directResult) {
      vocabulary = directResult.vocabulary;
      // relationships = directResult.relationships; // Remove: Build relationships once at the end
      strategy = 'exact_vocabulary';
      isBaseFormQuery = true;
    } else {
      // 策略2: 主要词形匹配
      const mainFormResult = await this.tryMainWordFormatLookup(searchTerm);
      queryCount++;
      if (mainFormResult) {
        vocabulary = mainFormResult.vocabulary;
        // relationships = mainFormResult.relationships; // Remove
        strategy = 'main_wordformat';
        isBaseFormQuery = false;
      } else {
        // 策略3: 派生词形匹配
        const derivedFormResult = await this.tryDerivedWordFormatLookup(searchTerm);
        queryCount++;
        if (derivedFormResult) {
          vocabulary = derivedFormResult.vocabulary;
          // relationships = derivedFormResult.relationships; // Remove
          strategy = 'derived_wordformat';
          isBaseFormQuery = false;
        }
      }
    }

    // 优化：在所有查找策略结束后，统一获取一次相关建议
    let suggestions: string[];
    const suggestionCacheKey = AppCacheKeys.suggestions(searchTerm);
    const cachedSuggestions = suggestionCache.get(suggestionCacheKey);
    if (cachedSuggestions) {
      CacheLogger.logHit(
        CacheSource.MEMORY_CACHE,
        { key: suggestionCacheKey, layer: 'app-suggestion-cache' },
        searchTerm
      );
      suggestions = cachedSuggestions;
    } else {
      suggestions = await this.getRelatedSuggestions(searchTerm); // This will also set the cache
    }
    queryCount++;

    if (vocabulary) {
      relationships = await this.buildWordFormRelationships(vocabulary); // Build relationships here

      const metadata = this.createQueryMetadata(
        searchTerm,
        searchTerm,
        strategy,
        isBaseFormQuery,
        startTime
      );
      return SearchResult.create(
        vocabulary,
        relationships,
        metadata,
        suggestions
      ).withDatabaseStats(queryCount);
    }

    // 未找到结果
    const processingTime = Date.now() - startTime;

    return SearchResult.notFound(searchTerm, processingTime, suggestions).withDatabaseStats(
      queryCount
    );
  }

  /**
   * 策略1: 直接词汇查询（带缓存来源跟踪）
   */
  private async tryDirectVocabularyLookup(word: string): Promise<LookupResultData | null> {
    // 使用增强的Repository方法获取缓存元数据
    const vocabularyRepo = this.dependencies.vocabularyRepository as any;

    let vocabulary: Vocabulary | null = null;
    let cacheMetadata: CacheMetadata | undefined = undefined;

    // 检查是否支持带元数据的查询
    if (typeof vocabularyRepo.findByWordWithMetadata === 'function') {
      const result = await vocabularyRepo.findByWordWithMetadata(word);
      vocabulary = result.data;
      cacheMetadata = result.cacheMetadata;
    } else {
      // 回退到标准方法
      vocabulary = await this.dependencies.vocabularyRepository.findByWordOptimized(word);
    }

    if (!vocabulary) return null;

    return {
      vocabulary,
      relationships: [], // Placeholder, will be built later
      cacheMetadata, // 传递缓存元数据
    };
  }

  /**
   * 策略2: 主要词形查询
   */
  private async tryMainWordFormatLookup(word: string): Promise<LookupResultData | null> {
    const mainForms = await this.dependencies.wordFormatRepository.findMainFormsByForm(word);
    if (!mainForms.length) return null;

    const vocabularyId = mainForms[0].vocabularyId;
    if (!vocabularyId) return null;

    const vocabulary = await this.dependencies.vocabularyRepository.findById(vocabularyId);
    if (!vocabulary) return null;

    return {
      vocabulary,
      relationships: [], // Placeholder, will be built later
    };
  }

  /**
   * 策略3: 派生词形查询
   */
  private async tryDerivedWordFormatLookup(word: string): Promise<LookupResultData | null> {
    const derivedForms = await this.dependencies.wordFormatRepository.findDerivedFormsByForm(word);
    if (!derivedForms.length) return null;

    const baseFormId = derivedForms[0].baseFormId;
    if (!baseFormId) return null;

    const baseForm = await this.dependencies.wordFormatRepository.findById(baseFormId);
    if (!baseForm?.vocabularyId) return null;

    const vocabulary = await this.dependencies.vocabularyRepository.findById(baseForm.vocabularyId);
    if (!vocabulary) return null;

    return {
      vocabulary,
      relationships: [], // Placeholder, will be built later
    };
  }

  /**
   * 获取缓存结果 - 直接返回API响应格式，带缓存来源跟踪
   */
  async getCachedApiResponse(
    searchTerm: string
  ): Promise<{ data: any; cacheMetadata: CacheMetadata } | null> {
    try {
      const cacheKey = `search:${searchTerm}`;
      const startTime = performance.now();

      const cached = await this.dependencies.cacheRepository.get<any>(cacheKey);

      if (!cached) {
        CacheLogger.logMiss(searchTerm, [CacheSource.REDIS_CACHE]);
        return null;
      }

      const hitTime = performance.now() - startTime;

      // 记录Redis缓存命中
      CacheLogger.logHit(
        CacheSource.REDIS_CACHE,
        {
          key: cacheKey,
          hitTime,
          layer: 'redis-distributed',
        },
        searchTerm
      );

      CachePerformanceTracker.recordHit(CacheSource.REDIS_CACHE, hitTime);

      // 在缓存的响应中添加缓存来源信息
      const responseWithCacheInfo = {
        ...cached,
        queryMetadata: {
          ...cached.queryMetadata,
          cacheHit: true,
          cacheSource: CacheSource.REDIS_CACHE,
          cacheHitTime: hitTime,
        },
      };

      return {
        data: responseWithCacheInfo,
        cacheMetadata: {
          source: CacheSource.REDIS_CACHE,
          hitTime,
          key: cacheKey,
          layer: 'redis-distributed',
        },
      };
    } catch (error) {
      console.error(`[Cache ERROR]`, { key: `search:${searchTerm}`, error });
      return null;
    }
  }

  /**
   * 获取缓存结果 - 保留原方法用于内部逻辑
   */
  private async getCachedResult(_searchTerm: string): Promise<SearchResult | null> {
    // 这个方法现在主要用于内部逻辑，实际API应该使用getCachedApiResponse
    return null;
  }

  /**
   * 缓存查询结果 - 直接缓存API响应格式
   */
  private async cacheResult(searchTerm: string, result: SearchResult, ttl?: number): Promise<void> {
    try {
      const cacheKey = `search:${searchTerm}`;
      // 直接缓存API响应格式，避免缓存命中时的重复转换
      const cacheData = result.toApiResponse();

      const cacheTTL = ttl || 3600;
      await this.dependencies.cacheRepository.set(cacheKey, cacheData, cacheTTL);
    } catch (error) {
      console.error(`[Cache SET ERROR]`, { key: `search:${searchTerm}`, error });
    }
  }

  /**
   * 标准化搜索词
   */
  private normalizeSearchTerm(term: string): string {
    return term.trim().toLowerCase();
  }

  /**
   * 创建查询元数据
   */
  private createQueryMetadata(
    searchTerm: string,
    matchedForm: string,
    strategy: 'exact_vocabulary' | 'main_wordformat' | 'derived_wordformat',
    isBaseFormQuery: boolean,
    startTime: number
  ): QueryMetadata {
    return {
      searchTerm,
      matchedForm,
      searchStrategy: strategy,
      isBaseFormQuery,
      processingTimeMs: Date.now() - startTime,
    };
  }

  /**
   * 创建错误结果
   */
  private createErrorResult(searchTerm: string, startTime: number, error: any): SearchResult {
    console.error('Dictionary service error:', error);
    const processingTime = Date.now() - startTime;
    return SearchResult.notFound(searchTerm, processingTime, []);
  }

  // {{CHENGQI:
  // Action: Modified
  // Timestamp: [2025-05-29 04:35:00 +08:00]
  // Reason: [优化词形关系构建逻辑，通过formats字段追溯到真正的原型并获取完整变体]
  // Principle_Applied: [数据完整性 - 确保返回完整的词形关系]
  // Optimization: [通过baseForm追溯机制获取完整的变体列表]
  // Architectural_Note (AR): [改进查询策略，支持从任意变体查找完整关系]
  // Documentation_Note (DW): [更新函数逻辑以支持完整的词形关系查询]
  // }}
  // {{START MODIFICATIONS}}
  /**
   * 构建词形关系
   */
  private async buildWordFormRelationships(
    vocabulary: Vocabulary,
    useCache: boolean = true
  ): Promise<WordFormRelationship[]> {
    const cacheKey = AppCacheKeys.relationships(vocabulary.id);
    if (useCache) {
      const cachedRelationships = relationshipCache.get(cacheKey);
      if (cachedRelationships) {
        CacheLogger.logHit(
          CacheSource.MEMORY_CACHE,
          { key: cacheKey, layer: 'app-relationship-cache' },
          vocabulary.word
        );
        return cachedRelationships;
      }
    }
    try {
      // 1. 获取vocabulary的formats（直接关联的WordFormat）
      const vocabularyFormats = vocabulary.formats;

      if (!vocabularyFormats || vocabularyFormats.length === 0) {
        return [];
      }

      // 2. 找到真正的原型WordFormat
      let trueBaseFormId: number | null = null;
      let trueBaseForm: any = null;

      // 首先检查是否有原型形式
      const baseFormatInVocab = vocabularyFormats.find((f) => f.name === '原型');

      if (baseFormatInVocab) {
        if (!baseFormatInVocab.baseFormId) {
          // 这个vocabulary本身就包含原型
          trueBaseFormId = baseFormatInVocab.id;
          trueBaseForm = baseFormatInVocab;
        } else {
          // 这个"原型"指向另一个原型，需要追溯
          trueBaseFormId = baseFormatInVocab.baseFormId;
          trueBaseForm = await this.dependencies.wordFormatRepository.findById(trueBaseFormId);
        }
      } else {
        // 没有原型形式，检查第一个format的baseFormId
        const firstFormat = vocabularyFormats[0];
        if (firstFormat.baseFormId) {
          trueBaseFormId = firstFormat.baseFormId;
          trueBaseForm = await this.dependencies.wordFormatRepository.findById(trueBaseFormId);
        } else {
          // 第一个format就是原型
          trueBaseFormId = firstFormat.id;
          trueBaseForm = firstFormat;
        }
      }

      if (!trueBaseFormId || !trueBaseForm) {
        return [];
      }

      // 3. 获取所有以这个原型为baseForm的变体
      const allRelatedForms =
        await this.dependencies.wordFormatRepository.findByBaseFormId(trueBaseFormId);

      // 4. 构建完整的形式列表（包含原型本身）
      const allForms = [trueBaseForm, ...allRelatedForms];

      // {{CHENGQI:
      // Action: Added
      // Timestamp: [2025-05-29 16:45:00 +08:00]
      // Reason: [修复重复\"原型\"条目问题，在构建allForms后进行去重处理]
      // Principle_Applied: [DRY - 避免重复数据，确保数据一致性]
      // Optimization: [通过去重逻辑避免API返回重复的词形记录]
      // Architectural_Note (AR): [在数据层面解决重复问题，确保API响应的准确性]
      // Documentation_Note (DW): [添加去重逻辑以处理数据库中可能存在的重复WordFormat记录]
      // }}
      // {{START MODIFICATIONS}}
      // 5. 去重处理，避免重复的name-form组合 - 优化版本
      const seen = new Set<string>();
      const uniqueAllForms = allForms.filter((item) => {
        const key = `${item.name}:${item.form}`;
        if (seen.has(key)) {
          return false;
        }
        seen.add(key);
        return true;
      });
      // {{END MODIFICATIONS}}

      const relationship: WordFormRelationship = {
        baseForm: trueBaseForm.form,
        baseFormId: trueBaseFormId,
        allForms: uniqueAllForms.map((f) => ({
          id: f.id,
          name: f.name,
          form: f.form,
          isMainForm: f.vocabularyId !== null,
        })),
        totalFormsCount: uniqueAllForms.length,
      };

      if (useCache) {
        relationshipCache.set(cacheKey, [relationship]);
        CacheLogger.logSet(CacheSource.MEMORY_CACHE, cacheKey, relationshipCache.defaultTTL / 1000);
      }

      return [relationship];
    } catch (error) {
      console.error('Error building word form relationships:', error);
      return [];
    }
  }
  // {{END MODIFICATIONS}}
}

/**
 * 查询选项接口
 */
export interface LookupOptions {
  useCache?: boolean;
  cacheTTL?: number;
  batchSize?: number;
  includeRelationships?: boolean;
  maxSuggestions?: number;
}

/**
 * 内部查询结果数据结构
 */
interface LookupResultData {
  vocabulary: Vocabulary;
  relationships: WordFormRelationship[];
  cacheMetadata?: CacheMetadata; // 缓存元数据
}
