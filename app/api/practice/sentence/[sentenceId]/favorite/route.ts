/**
 * 句子收藏管理 API
 * POST /api/practice/sentence/[sentenceId]/favorite - 收藏句子
 * DELETE /api/practice/sentence/[sentenceId]/favorite - 取消收藏句子
 * 
 * 管理用户的句子收藏状态
 */

import { getServerSession } from 'next-auth/next';
import { NextRequest, NextResponse } from 'next/server';
import { createPracticeService } from '@/lib/1-services/practice/PracticeService';
import { ConflictError, NotFoundError, ValidationError } from '@/lib/types/api-types';

interface RouteContext {
  params: Promise<{
    sentenceId: string;
  }>;
}

export async function POST(request: NextRequest, context: RouteContext) {
  try {
    // 用户认证
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { sentenceId: sentenceIdParam } = await context.params;
    const sentenceId = parseInt(sentenceIdParam);

    // 验证句子 ID
    if (isNaN(sentenceId) || sentenceId <= 0) {
      return NextResponse.json(
        { error: 'Invalid sentence ID' },
        { status: 400 }
      );
    }

    const practiceService = createPracticeService();

    try {
      const userSavedSentence = await practiceService.saveSentenceToFavorites(userId, sentenceId);

      return NextResponse.json({
        success: true,
        data: {
          userSavedSentence,
          message: 'Sentence saved to favorites successfully'
        }
      });

    } catch (error) {
      if (error instanceof ConflictError) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      
      if (error instanceof NotFoundError) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      
      if (error instanceof ValidationError) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
      
      throw error;
    }

  } catch (error) {
    console.error('Error saving sentence to favorites:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to save sentence to favorites'
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, context: RouteContext) {
  try {
    // 用户认证
    const session = await getServerSession();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const { sentenceId: sentenceIdParam } = await context.params;
    const sentenceId = parseInt(sentenceIdParam);

    // 验证句子 ID
    if (isNaN(sentenceId) || sentenceId <= 0) {
      return NextResponse.json(
        { error: 'Invalid sentence ID' },
        { status: 400 }
      );
    }

    const practiceService = createPracticeService();

    try {
      const removed = await practiceService.removeSentenceFromFavorites(userId, sentenceId);

      return NextResponse.json({
        success: true,
        data: {
          removed,
          message: 'Sentence removed from favorites successfully'
        }
      });

    } catch (error) {
      if (error instanceof NotFoundError) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      
      throw error;
    }

  } catch (error) {
    console.error('Error removing sentence from favorites:', error);
    
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Failed to remove sentence from favorites'
      },
      { status: 500 }
    );
  }
}