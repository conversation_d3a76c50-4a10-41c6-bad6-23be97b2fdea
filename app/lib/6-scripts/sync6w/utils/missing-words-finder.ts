#!/usr/bin/env tsx

/**
 * 查找未匹配单词的工具
 * 用于识别在频率排名设置过程中未找到的单词
 */

import { readFile } from 'fs/promises';
import { join } from 'path';
import * as XLSX from 'xlsx';
import { PrismaClient } from '@prisma/client';

// 配置常量
const EXCEL_FILE_PATH = './app/lib/6-scripts/sync6w/SUBTLEXusfrequencyabove1.xls';

/**
 * 未找到单词的信息
 */
export interface MissingWordInfo {
  word: string;
  expectedRank: number;
  reason: 'not_in_database' | 'no_freq_rank' | 'api_failed';
  details?: string;
}

/**
 * 从Excel文件中提取指定数量的单词
 */
async function extractWordsFromExcel(filePath: string, count: number): Promise<string[]> {
  try {
    const fileBuffer = await readFile(filePath);
    const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // 转换为JSON格式
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    // 跳过标题行，提取单词列（假设单词在第一列）
    const words: string[] = [];
    for (let i = 1; i < Math.min(jsonData.length, count + 1); i++) {
      const row = jsonData[i] as any[];
      if (row && row[0] && typeof row[0] === 'string') {
        const word = row[0].trim().toLowerCase();
        if (word) {
          words.push(word);
        }
      }
    }

    return words;
  } catch (error) {
    console.error('读取Excel文件失败:', error);
    throw error;
  }
}

/**
 * 查找未匹配的单词
 */
export async function findMissingWords(
  totalWords: number = 500,
  prismaClient: PrismaClient
): Promise<MissingWordInfo[]> {
  console.log('🔍 开始查找未匹配的单词...');

  try {
    // 1. 从Excel文件读取原始单词列表
    console.log('📖 读取Excel文件中的单词列表...');
    const excelPath = join(process.cwd(), EXCEL_FILE_PATH);
    const originalWords = await extractWordsFromExcel(excelPath, totalWords);

    console.log(`📝 从Excel文件读取了 ${originalWords.length} 个单词`);

    // 2. 查询数据库中的词汇状态
    console.log('🔍 检查数据库中的词汇状态...');
    const missingWords: MissingWordInfo[] = [];

    for (let i = 0; i < originalWords.length; i++) {
      const word = originalWords[i];
      const expectedRank = i + 1;

      try {
        // 查找词汇是否存在
        const vocabulary = await prismaClient.vocabulary.findFirst({
          where: { word: word },
          select: { id: true, word: true, freqRank: true },
        });

        if (!vocabulary) {
          // 单词不在数据库中
          missingWords.push({
            word,
            expectedRank,
            reason: 'not_in_database',
            details: '单词未在数据库中找到',
          });
          console.log(`❌ 未找到: "${word}" (排名 ${expectedRank})`);
        } else if (vocabulary.freqRank === null || vocabulary.freqRank === undefined) {
          // 单词存在但没有频率排名
          missingWords.push({
            word,
            expectedRank,
            reason: 'no_freq_rank',
            details: '单词存在但缺少频率排名',
          });
          console.log(`⚠️  无排名: "${word}" (排名 ${expectedRank})`);
        } else if (vocabulary.freqRank !== expectedRank) {
          // 排名不匹配（可能是之前的错误）
          console.log(
            `🔄 排名不匹配: "${word}" (期望 ${expectedRank}, 实际 ${vocabulary.freqRank})`
          );
        } else {
          // 正常情况
          console.log(`✅ 正常: "${word}" (排名 ${expectedRank})`);
        }
      } catch (error) {
        console.error(`❌ 检查单词 "${word}" 时出错:`, error);
        missingWords.push({
          word,
          expectedRank,
          reason: 'api_failed',
          details: `数据库查询失败: ${(error as Error).message}`,
        });
      }
    }

    // 3. 统计结果
    console.log('\n📊 统计结果:');
    console.log(`总单词数: ${originalWords.length}`);
    console.log(`未找到单词数: ${missingWords.length}`);
    console.log(`成功匹配数: ${originalWords.length - missingWords.length}`);

    // 按原因分类统计
    const reasonStats = missingWords.reduce(
      (acc, word) => {
        acc[word.reason] = (acc[word.reason] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    console.log('\n📈 失败原因统计:');
    Object.entries(reasonStats).forEach(([reason, count]) => {
      const reasonText =
        {
          not_in_database: '不在数据库中',
          no_freq_rank: '缺少频率排名',
          api_failed: 'API查询失败',
        }[reason] || reason;
      console.log(`  ${reasonText}: ${count} 个`);
    });

    return missingWords;
  } catch (error) {
    console.error('查找未匹配单词失败:', error);
    throw error;
  }
}

/**
 * 获取需要重新查询的单词列表
 */
export function getWordsToRetry(missingWords: MissingWordInfo[]): string[] {
  // 只重新查询不在数据库中的单词
  return missingWords.filter((word) => word.reason === 'not_in_database').map((word) => word.word);
}

/**
 * 获取需要更新排名的单词列表
 */
export function getWordsToUpdateRank(
  missingWords: MissingWordInfo[]
): Array<{ word: string; rank: number }> {
  // 获取存在但缺少排名的单词
  return missingWords
    .filter((word) => word.reason === 'no_freq_rank')
    .map((word) => ({ word: word.word, rank: word.expectedRank }));
}
