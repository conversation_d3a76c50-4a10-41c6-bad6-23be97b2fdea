/**
 * @fileoverview PracticeService业务逻辑单元测试
 * 
 * 测试覆盖范围：
 * - 生词本管理方法（getUserWordList, addWordsToUserList, removeWordsFromUserList）
 * - 批量操作的事务处理逻辑
 * - 重复添加检测和处理逻辑
 * - 删除操作的数据完整性检查
 * - 错误处理和边界情况
 * - 分页和排序功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { PracticeService } from '../PracticeService';
import { ValidationError } from '@/lib/types/api-types';

// Mock Prisma before it's imported by other modules
vi.mock('@/lib/4-infrastructure/database/prisma', () => ({
  prisma: {
    userWordProficiency: {
      findMany: vi.fn(),
      count: vi.fn(),
      deleteMany: vi.fn(),
    },
  },
}));

// Import the mocked prisma to access it in tests
import { prisma } from '@/lib/4-infrastructure/database/prisma';
const mockPrisma = vi.mocked(prisma);

// Mock repositories and services
const mockUserProficiencyRepo = {
  findManyByUser: vi.fn(),
  findByUserAndWord: vi.fn(),
  upsertMany: vi.fn(),
  getUserStats: vi.fn(),
  create: vi.fn(),
  update: vi.fn(),
};

const mockVocabularyRepo = {
  findByWord: vi.fn(),
  findManyByWords: vi.fn(),
  findManyByIds: vi.fn(),
  findById: vi.fn(),
};

const mockProficiencyService = {
  getUserProficiencyStats: vi.fn(),
  getRecommendedWordsForPractice: vi.fn(),
  getWordsForReview: vi.fn(),
  toggleWordMarking: vi.fn(),
};

// Test data
const mockUser = {
  id: 'user-123',
  name: 'Test User',
  email: '<EMAIL>',
};

const mockVocabulary = [
  {
    id: 1,
    word: 'example',
    phonetics: ['ɪɡˈzæmpl'],
    freqRank: 100,
    explains: [
      {
        id: 1,
        partOfSpeech: 'noun',
        phonetic: 'ɪɡˈzæmpl',
        definition: 'a thing characteristic of its kind',
        chineseDefinition: '例子',
      },
    ],
  },
  {
    id: 2,
    word: 'test',
    phonetics: ['test'],
    freqRank: 200,
    explains: [
      {
        id: 2,
        partOfSpeech: 'noun',
        phonetic: 'test',
        definition: 'a procedure intended to establish the quality',
        chineseDefinition: '测试',
      },
    ],
  },
];

const mockUserWordProficiency = [
  {
    id: 1,
    userId: 'user-123',
    wordId: 1,
    practiceCount: 5,
    errorCount: 1,
    averageTime: 2500,
    proficiencyScore: 0.8,
    isMarked: false,
    lastPracticed: new Date('2025-07-30'),
    vocabulary: mockVocabulary[0],
  },
  {
    id: 2,
    userId: 'user-123',
    wordId: 2,
    practiceCount: 3,
    errorCount: 2,
    averageTime: 3000,
    proficiencyScore: 0.5,
    isMarked: true,
    lastPracticed: new Date('2025-07-29'),
    vocabulary: mockVocabulary[1],
  },
];

describe('PracticeService生词本管理测试', () => {
  let practiceService: PracticeService;

  beforeEach(() => {
    vi.clearAllMocks();
    practiceService = new PracticeService(
      mockUserProficiencyRepo as any,
      mockVocabularyRepo as any,
      mockProficiencyService as any
    );
  });

  describe('getUserWordList - 获取用户生词列表', () => {
    it('应该成功返回用户生词列表', async () => {
      // Arrange
      const options = {
        page: 1,
        limit: 20,
        sortBy: 'time' as const,
        order: 'desc' as const,
      };

      mockUserProficiencyRepo.findManyByUser.mockResolvedValue(mockUserWordProficiency);
      mockPrisma.userWordProficiency.count.mockResolvedValue(2);

      // Act
      const result = await practiceService.getUserWordList(mockUser.id, options);

      // Assert
      expect(mockUserProficiencyRepo.findManyByUser).toHaveBeenCalledWith(mockUser.id, {
        sortBy: 'lastPracticed',
        sortOrder: 'desc',
        limit: 20,
        offset: 0,
      });

      expect(mockPrisma.userWordProficiency.count).toHaveBeenCalledWith({
        where: { userId: mockUser.id },
      });

      expect(result).toEqual({
        words: [
          {
            id: 1,
            word: 'example',
            phonetics: ['ɪɡˈzæmpl'],
            freqRank: 100,
            proficiency: {
              practiceCount: 5,
              errorCount: 1,
              averageTime: 2500,
              proficiencyScore: 0.8,
              isMarked: false,
              lastPracticed: new Date('2025-07-30'),
            },
          },
          {
            id: 2,
            word: 'test',
            phonetics: ['test'],
            freqRank: 200,
            proficiency: {
              practiceCount: 3,
              errorCount: 2,
              averageTime: 3000,
              proficiencyScore: 0.5,
              isMarked: true,
              lastPracticed: new Date('2025-07-29'),
            },
          },
        ],
        pagination: {
          page: 1,
          limit: 20,
          total: 2,
          totalPages: 1,
          hasMore: false,
        },
      });
    });

    it('应该支持按熟练度排序', async () => {
      // Arrange
      const options = {
        page: 1,
        limit: 20,
        sortBy: 'proficiency' as const,
        order: 'asc' as const,
      };

      mockUserProficiencyRepo.findManyByUser.mockResolvedValue([]);
      mockPrisma.userWordProficiency.count.mockResolvedValue(0);

      // Act
      await practiceService.getUserWordList(mockUser.id, options);

      // Assert
      expect(mockUserProficiencyRepo.findManyByUser).toHaveBeenCalledWith(mockUser.id, {
        sortBy: 'proficiencyScore',
        sortOrder: 'asc',
        limit: 20,
        offset: 0,
      });
    });

    it('应该支持按字母序排序', async () => {
      // Arrange
      const options = {
        page: 1,
        limit: 20,
        sortBy: 'alphabetical' as const,
        order: 'asc' as const,
      };

      mockPrisma.userWordProficiency.findMany.mockResolvedValue(mockUserWordProficiency);
      mockPrisma.userWordProficiency.count.mockResolvedValue(2);

      // Act
      await practiceService.getUserWordList(mockUser.id, options);

      // Assert
      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: { userId: mockUser.id },
        include: {
          vocabulary: {
            select: {
              id: true,
              word: true,
              phonetics: true,
              freqRank: true,
            },
          },
        },
        orderBy: { vocabulary: { word: 'asc' } },
        take: 20,
        skip: 0,
      });
    });

    it('应该正确计算分页信息', async () => {
      // Arrange
      const options = {
        page: 2,
        limit: 5,
        sortBy: 'time' as const,
        order: 'desc' as const,
      };

      mockUserProficiencyRepo.findManyByUser.mockResolvedValue([]);
      mockPrisma.userWordProficiency.count.mockResolvedValue(12);

      // Act
      const result = await practiceService.getUserWordList(mockUser.id, options);

      // Assert
      expect(mockUserProficiencyRepo.findManyByUser).toHaveBeenCalledWith(
        mockUser.id,
        expect.objectContaining({
          offset: 5, // (page - 1) * limit
          limit: 5,
        })
      );

      expect(result.pagination).toEqual({
        page: 2,
        limit: 5,
        total: 12,
        totalPages: 3,
        hasMore: true,
      });
    });

    it('应该处理空结果', async () => {
      // Arrange
      const options = {
        page: 1,
        limit: 20,
        sortBy: 'time' as const,
        order: 'desc' as const,
      };

      mockUserProficiencyRepo.findManyByUser.mockResolvedValue([]);
      mockPrisma.userWordProficiency.count.mockResolvedValue(0);

      // Act
      const result = await practiceService.getUserWordList(mockUser.id, options);

      // Assert
      expect(result).toEqual({
        words: [],
        pagination: {
          page: 1,
          limit: 20,
          total: 0,
          totalPages: 0,
          hasMore: false,
        },
      });
    });
  });

  describe('addWordsToUserList - 批量添加生词', () => {
    it('应该成功通过单词文本添加生词', async () => {
      // Arrange
      const words = ['example', 'test'];

      mockVocabularyRepo.findManyByWords.mockResolvedValue(mockVocabulary);
      mockPrisma.userWordProficiency.findMany.mockResolvedValue([]);
      mockUserProficiencyRepo.upsertMany.mockResolvedValue(undefined);

      // Act
      const result = await practiceService.addWordsToUserList(mockUser.id, words);

      // Assert
      expect(mockVocabularyRepo.findManyByWords).toHaveBeenCalledWith(words);
      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: {
          userId: mockUser.id,
          wordId: { in: [1, 2] },
        },
        select: { wordId: true },
      });
      expect(mockUserProficiencyRepo.upsertMany).toHaveBeenCalledWith([
        {
          userId: mockUser.id,
          wordId: 1,
          practiceCount: 0,
          errorCount: 0,
          averageTime: 0,
          proficiencyScore: 0,
          isMarked: false,
        },
        {
          userId: mockUser.id,
          wordId: 2,
          practiceCount: 0,
          errorCount: 0,
          averageTime: 0,
          proficiencyScore: 0,
          isMarked: false,
        },
      ]);

      expect(result).toEqual({
        added: 2,
        skipped: 0,
        errors: [],
      });
    });

    it('应该成功通过ID添加生词', async () => {
      // Arrange
      const wordIds = [1, 2];

      mockVocabularyRepo.findManyByIds.mockResolvedValue(mockVocabulary);
      mockPrisma.userWordProficiency.findMany.mockResolvedValue([]);
      mockUserProficiencyRepo.upsertMany.mockResolvedValue(undefined);

      // Act
      const result = await practiceService.addWordsToUserList(
        mockUser.id,
        undefined,
        wordIds
      );

      // Assert
      expect(mockVocabularyRepo.findManyByIds).toHaveBeenCalledWith(wordIds);
      expect(result).toEqual({
        added: 2,
        skipped: 0,
        errors: [],
      });
    });

    it('应该处理重复添加的单词', async () => {
      // Arrange
      const words = ['example', 'test'];

      mockVocabularyRepo.findManyByWords.mockResolvedValue(mockVocabulary);
      // 模拟已存在的单词记录
      mockPrisma.userWordProficiency.findMany.mockResolvedValue([
        { wordId: 1 },
      ]);
      mockUserProficiencyRepo.upsertMany.mockResolvedValue(undefined);

      // Act
      const result = await practiceService.addWordsToUserList(mockUser.id, words);

      // Assert
      expect(mockUserProficiencyRepo.upsertMany).toHaveBeenCalledWith([
        {
          userId: mockUser.id,
          wordId: 2, // 只添加不重复的单词
          practiceCount: 0,
          errorCount: 0,
          averageTime: 0,
          proficiencyScore: 0,
          isMarked: false,
        },
      ]);

      expect(result).toEqual({
        added: 1,
        skipped: 1,
        errors: [],
      });
    });

    it('应该处理部分单词不存在的情况', async () => {
      // Arrange
      const words = ['example', 'nonexistent'];

      // 只返回一个找到的单词
      mockVocabularyRepo.findManyByWords.mockResolvedValue([mockVocabulary[0]]);
      mockPrisma.userWordProficiency.findMany.mockResolvedValue([]);
      mockUserProficiencyRepo.upsertMany.mockResolvedValue(undefined);

      // Act
      const result = await practiceService.addWordsToUserList(mockUser.id, words);

      // Assert
      expect(result).toEqual({
        added: 1,
        skipped: 0,
        errors: ['Words not found: nonexistent'],
      });
    });

    it('应该要求提供words或wordIds参数', async () => {
      // Act & Assert
      await expect(
        practiceService.addWordsToUserList(mockUser.id)
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('removeWordsFromUserList - 批量删除生词', () => {
    it('应该成功删除生词', async () => {
      // Arrange
      const wordIds = [1, 2];

      mockPrisma.userWordProficiency.findMany.mockResolvedValue([
        { wordId: 1 },
        { wordId: 2 },
      ]);
      mockPrisma.userWordProficiency.deleteMany.mockResolvedValue({ count: 2 });

      // Act
      const result = await practiceService.removeWordsFromUserList(mockUser.id, wordIds);

      // Assert
      expect(mockPrisma.userWordProficiency.findMany).toHaveBeenCalledWith({
        where: {
          userId: mockUser.id,
          wordId: { in: wordIds },
        },
        select: { wordId: true },
      });

      expect(mockPrisma.userWordProficiency.deleteMany).toHaveBeenCalledWith({
        where: {
          userId: mockUser.id,
          wordId: { in: wordIds },
        },
      });

      expect(result).toEqual({
        deleted: 2,
        notFound: 0,
      });
    });

    it('应该处理部分单词不在生词本中的情况', async () => {
      // Arrange
      const wordIds = [1, 2, 3];

      // 只找到两个单词在生词本中
      mockPrisma.userWordProficiency.findMany.mockResolvedValue([
        { wordId: 1 },
        { wordId: 2 },
      ]);
      mockPrisma.userWordProficiency.deleteMany.mockResolvedValue({ count: 2 });

      // Act
      const result = await practiceService.removeWordsFromUserList(mockUser.id, wordIds);

      // Assert
      expect(result).toEqual({
        deleted: 2,
        notFound: 1,
      });
    });

    it('应该处理没有找到任何单词的情况', async () => {
      // Arrange
      const wordIds = [999];

      mockPrisma.userWordProficiency.findMany.mockResolvedValue([]);
      mockPrisma.userWordProficiency.deleteMany.mockResolvedValue({ count: 0 });

      // Act
      const result = await practiceService.removeWordsFromUserList(mockUser.id, wordIds);

      // Assert
      expect(result).toEqual({
        deleted: 0,
        notFound: 1,
      });
    });

    it('应该处理删除操作失败', async () => {
      // Arrange
      const wordIds = [1];

      mockPrisma.userWordProficiency.findMany.mockRejectedValue(
        new Error('Database connection failed')
      );

      // Act & Assert
      await expect(
        practiceService.removeWordsFromUserList(mockUser.id, wordIds)
      ).rejects.toThrow('Failed to remove words from user list');
    });
  });

  describe('getUserWordCount - 获取用户生词总数', () => {
    it('应该返回正确的生词总数', async () => {
      // Arrange
      mockPrisma.userWordProficiency.count.mockResolvedValue(42);

      // Act
      const result = await practiceService.getUserWordCount(mockUser.id);

      // Assert
      expect(mockPrisma.userWordProficiency.count).toHaveBeenCalledWith({
        where: { userId: mockUser.id },
      });
      expect(result).toBe(42);
    });

    it('应该处理空的生词本', async () => {
      // Arrange
      mockPrisma.userWordProficiency.count.mockResolvedValue(0);

      // Act
      const result = await practiceService.getUserWordCount(mockUser.id);

      // Assert
      expect(result).toBe(0);
    });
  });

  describe('getUserWordStats - 获取用户生词统计', () => {
    it('应该返回合并的统计信息', async () => {
      // Arrange
      const mockStats = {
        totalWords: 100,
        averageProficiency: 0.75,
        wordsNeedingReview: 15,
      };
      const mockProficiencyLevel = {
        proficiencyLevel: 'intermediate',
      };

      mockUserProficiencyRepo.getUserStats.mockResolvedValue(mockStats);
      mockProficiencyService.getUserProficiencyStats.mockResolvedValue(mockProficiencyLevel);

      // Act
      const result = await practiceService.getUserWordStats(mockUser.id);

      // Assert
      expect(mockUserProficiencyRepo.getUserStats).toHaveBeenCalledWith(mockUser.id);
      expect(mockProficiencyService.getUserProficiencyStats).toHaveBeenCalledWith(mockUser.id);
      expect(result).toEqual({
        ...mockStats,
        proficiencyLevel: 'intermediate',
      });
    });
  });

  describe('getRecommendedWords - 获取推荐练习单词', () => {
    it('应该返回推荐单词列表', async () => {
      // Arrange
      const mockRecommendedWords = [
        { wordId: 1, priority: 'high' },
        { wordId: 2, priority: 'medium' },
      ];
      mockProficiencyService.getRecommendedWordsForPractice.mockResolvedValue(mockRecommendedWords);

      // Act
      const result = await practiceService.getRecommendedWords(mockUser.id, 10);

      // Assert
      expect(mockProficiencyService.getRecommendedWordsForPractice).toHaveBeenCalledWith(mockUser.id, 10);
      expect(result).toEqual(mockRecommendedWords);
    });

    it('应该使用默认的limit值', async () => {
      // Arrange
      mockProficiencyService.getRecommendedWordsForPractice.mockResolvedValue([]);

      // Act
      await practiceService.getRecommendedWords(mockUser.id);

      // Assert
      expect(mockProficiencyService.getRecommendedWordsForPractice).toHaveBeenCalledWith(mockUser.id, 20);
    });
  });

  describe('getWordsForReview - 获取需要复习的单词', () => {
    it('应该返回需要复习的单词', async () => {
      // Arrange
      const mockReviewWords = [
        { wordId: 1, daysSinceLastPractice: 10 },
        { wordId: 3, daysSinceLastPractice: 8 },
      ];
      mockProficiencyService.getWordsForReview.mockResolvedValue(mockReviewWords);

      // Act
      const result = await practiceService.getWordsForReview(mockUser.id, 5);

      // Assert
      expect(mockProficiencyService.getWordsForReview).toHaveBeenCalledWith(mockUser.id, 5);
      expect(result).toEqual(mockReviewWords);
    });

    it('应该使用默认的天数值', async () => {
      // Arrange
      mockProficiencyService.getWordsForReview.mockResolvedValue([]);

      // Act
      await practiceService.getWordsForReview(mockUser.id);

      // Assert
      expect(mockProficiencyService.getWordsForReview).toHaveBeenCalledWith(mockUser.id, 7);
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理数据库连接失败', async () => {
      // Arrange
      mockUserProficiencyRepo.findManyByUser.mockRejectedValue(
        new Error('Database connection failed')
      );

      // Act & Assert
      await expect(
        practiceService.getUserWordList(mockUser.id, {
          page: 1,
          limit: 20,
          sortBy: 'time',
          order: 'desc',
        })
      ).rejects.toThrow('Database connection failed');
    });

    it('应该处理添加操作的数据库错误', async () => {
      // Arrange
      const words = ['example'];

      mockVocabularyRepo.findManyByWords.mockResolvedValue([mockVocabulary[0]]);
      mockPrisma.userWordProficiency.findMany.mockRejectedValue(
        new Error('Database error during check')
      );

      // Act & Assert
      await expect(
        practiceService.addWordsToUserList(mockUser.id, words)
      ).rejects.toThrow('Failed to add words to user list');
    });
  });
});