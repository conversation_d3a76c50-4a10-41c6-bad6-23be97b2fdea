import { NextRequest, NextResponse } from 'next/server';
import { CSRFProtection, SecurityHeadersManager } from '@/lib/utils/cookies';

/**
 * GET /api/auth/csrf-token
 * 获取CSRF Token
 */
export async function GET(request: NextRequest) {
  try {
    // 生成新的CSRF Token
    const csrfToken = CSRFProtection.setCSRFToken();

    const response = NextResponse.json({
      csrfToken,
      message: 'CSRF token generated successfully',
    });

    // 添加安全头部
    SecurityHeadersManager.addSecurityHeaders(response, request);

    return response;
  } catch (error) {
    console.error('CSRF token generation error:', error);
    return NextResponse.json({ error: 'Failed to generate CSRF token' }, { status: 500 });
  }
}

/**
 * POST /api/auth/csrf-token/validate
 * 验证CSRF Token
 */
export async function POST(request: NextRequest) {
  try {
    const isValid = await CSRFProtection.validateCSRFToken(request);

    const response = NextResponse.json({
      valid: isValid,
      message: isValid ? 'CSRF token is valid' : 'CSRF token is invalid',
    });

    // 添加安全头部
    SecurityHeadersManager.addSecurityHeaders(response);

    return response;
  } catch (error) {
    console.error('CSRF token validation error:', error);
    return NextResponse.json({ error: 'Failed to validate CSRF token' }, { status: 500 });
  }
}
