/**
 * 缓存来源类型定义
 * 用于跟踪数据的实际来源，便于调试和性能分析
 */

export enum CacheSource {
  DATABASE = 'database',
  REDIS_CACHE = 'redis_cache',
  MEMORY_CACHE = 'memory_cache',
  API_CACHE = 'api_cache',
}

export interface CacheMetadata {
  source: CacheSource;
  hitTime?: number; // 缓存命中耗时（毫秒）
  key?: string; // 缓存键
  ttl?: number; // 剩余TTL（秒）
  layer?: string; // 缓存层级描述
}

export interface CacheStats {
  totalRequests: number;
  databaseHits: number;
  redisHits: number;
  memoryHits: number;
  apiHits: number;
  hitRate: number;
  averageResponseTime: number;
}

/**
 * 缓存日志配置
 */
export class CacheLogConfig {
  static readonly ENABLE_CACHE_LOGS = process.env.ENABLE_CACHE_LOGS !== 'false';
  static readonly ENABLE_DETAILED_LOGS = process.env.ENABLE_DETAILED_CACHE_LOGS === 'true';
  static readonly HIGH_CONCURRENCY_MODE = process.env.HIGH_CONCURRENCY_MODE === 'true';

  /**
   * 是否应该记录缓存日志
   */
  static shouldLog(): boolean {
    return this.ENABLE_CACHE_LOGS && !this.HIGH_CONCURRENCY_MODE;
  }

  /**
   * 是否应该记录详细日志
   */
  static shouldLogDetailed(): boolean {
    return this.ENABLE_DETAILED_LOGS && this.shouldLog();
  }
}

/**
 * 缓存性能跟踪器
 */
export class CachePerformanceTracker {
  private static stats: CacheStats = {
    totalRequests: 0,
    databaseHits: 0,
    redisHits: 0,
    memoryHits: 0,
    apiHits: 0,
    hitRate: 0,
    averageResponseTime: 0,
  };

  private static responseTimes: number[] = [];

  /**
   * 记录缓存命中
   */
  static recordHit(source: CacheSource, responseTime: number): void {
    if (!CacheLogConfig.shouldLog()) return;

    this.stats.totalRequests++;
    this.responseTimes.push(responseTime);

    // 只保留最近1000次请求的响应时间
    if (this.responseTimes.length > 1000) {
      this.responseTimes = this.responseTimes.slice(-1000);
    }

    switch (source) {
      case CacheSource.DATABASE:
        this.stats.databaseHits++;
        break;
      case CacheSource.REDIS_CACHE:
        this.stats.redisHits++;
        break;
      case CacheSource.MEMORY_CACHE:
        this.stats.memoryHits++;
        break;
      case CacheSource.API_CACHE:
        this.stats.apiHits++;
        break;
    }

    // 更新统计信息
    const cacheHits = this.stats.redisHits + this.stats.memoryHits + this.stats.apiHits;
    this.stats.hitRate = this.stats.totalRequests > 0 ? cacheHits / this.stats.totalRequests : 0;
    this.stats.averageResponseTime =
      this.responseTimes.reduce((sum, time) => sum + time, 0) / this.responseTimes.length;
  }

  /**
   * 获取当前统计信息
   */
  static getStats(): CacheStats {
    return { ...this.stats };
  }

  /**
   * 重置统计信息
   */
  static reset(): void {
    this.stats = {
      totalRequests: 0,
      databaseHits: 0,
      redisHits: 0,
      memoryHits: 0,
      apiHits: 0,
      hitRate: 0,
      averageResponseTime: 0,
    };
    this.responseTimes = [];
  }

  /**
   * 获取缓存命中率分布
   */
  static getHitRateDistribution(): Record<string, number> {
    const total = this.stats.totalRequests;
    if (total === 0) return {};

    return {
      database: (this.stats.databaseHits / total) * 100,
      redis: (this.stats.redisHits / total) * 100,
      memory: (this.stats.memoryHits / total) * 100,
      api: (this.stats.apiHits / total) * 100,
    };
  }
}

/**
 * 缓存日志工具
 */
export class CacheLogger {
  /**
   * 记录缓存命中日志
   */
  static logHit(source: CacheSource, metadata: Partial<CacheMetadata>, searchTerm?: string): void {
    if (!CacheLogConfig.shouldLog()) return;

    const logData: any = {
      source,
      term: searchTerm,
      ...metadata,
    };

    //
    // Action: Modified
    // Timestamp: [2025-06-10 10:26:00 +08:00]
    // Reason: [优化日志输出，当数据源是数据库时，使用更准确的"数据源"而非"缓存命中"措辞，避免混淆。]
    // Principle_Applied: [Clarity - 日志信息应该清晰易懂，准确反映系统行为。]
    //
    // {{START MODIFICATIONS}}
    if (CacheLogConfig.shouldLogDetailed() || source === CacheSource.DATABASE) {
      // Always log database source for clarity
      const prefix = source === CacheSource.DATABASE ? '📦 [DATA-SOURCE]' : '🎯 [CACHE-HIT]';
      console.log(`${prefix} ${source.toUpperCase()}:`, logData);
    }
    // {{END MODIFICATIONS}}
  }

  /**
   * 记录缓存未命中日志
   */
  static logMiss(searchTerm: string, attemptedSources: CacheSource[]): void {
    if (!CacheLogConfig.shouldLogDetailed()) return;

    console.log(`❌ [CACHE-MISS] ${searchTerm}:`, {
      attempted: attemptedSources,
      fallback: CacheSource.DATABASE,
    });
  }

  /**
   * 记录缓存设置日志
   */
  static logSet(source: CacheSource, key: string, ttl?: number): void {
    if (!CacheLogConfig.shouldLogDetailed()) return;

    console.log(`💾 [CACHE-SET] ${source.toUpperCase()}:`, {
      key,
      ttl: ttl ? `${ttl}s` : 'default',
    });
  }

  /**
   * 记录缓存统计信息
   */
  static logStats(): void {
    if (!CacheLogConfig.shouldLog()) return;

    const stats = CachePerformanceTracker.getStats();
    const distribution = CachePerformanceTracker.getHitRateDistribution();

    console.log('📊 [CACHE-STATS]:', {
      totalRequests: stats.totalRequests,
      hitRate: `${(stats.hitRate * 100).toFixed(1)}%`,
      avgResponseTime: `${stats.averageResponseTime.toFixed(1)}ms`,
      distribution: Object.entries(distribution)
        .map(([key, value]) => `${key}: ${value.toFixed(1)}%`)
        .join(', '),
    });
  }
}
