# 3. 非功能性需求 (Non-Functional Requirements)

- **NFR1: 性能 (Performance)**

  - **NFR1.1:** 对于大型词库（如GRE），系统必须采用分页或分块加载，确保用户选择词库后的初始加载时间在2秒以内。
  - **NFR1.2:** 打字过程中的按键响应必须是即时的（<100ms延迟）。

- **NFR2: 用户体验 (User Experience)**

  - **NFR2.1:** 在“词汇练习”和“生词练习”的每一轮结束后，系统应展示一个总结/成功页面。
  - **NFR2.2:** 总结页面必须包含以下指标：整体打字速度 (WPM)、出错的单词列表及各单词的出错次数、练习过程中每个单词所花费的时间。
  - **NFR2.3:** 系统需要提供一个历史图表，可视化用户打字速度（WPM）随时间的变化趋势。
  - **NFR2.4:** “生词练习”模式的单词列表，**只应展示**用户手动“标注”过或在练习中出过错的单词。
  - **NFR2.5:** “句子练习”模式在完成一句后，应直接过渡到下一句，**不显示**中间的总结页面。

- **NFR3: 兼容性与依赖逻辑 (Compatibility & Dependencies)**
  - **NFR3.1:** 整个打字练习功能的用户界面必须是**响应式**的，能在桌面和移动设备上良好运行。
  - **NFR3.2:** 用户**必须登录**后才能访问所有打字练习页面。
  - **NFR3.3:** 如果用户的生词本为空，“生词练习”的入口或页面应被隐藏或禁用。
  - **NFR3.4:** 如果用户没有收藏任何句子，“句子练习”模式应从一个默认的公共句库中随机提供句子。
