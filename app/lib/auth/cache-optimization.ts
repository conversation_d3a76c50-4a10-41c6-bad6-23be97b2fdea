import { Redis } from 'ioredis';
// import { LRUCache } from 'lru-cache';

// Multi-level cache hierarchy
export interface CacheLevel {
  get<T>(key: string): T | undefined | Promise<T | undefined>;
  set<T>(key: string, value: T, ttl?: number): void | Promise<void>;
  delete(key: string): void | Promise<void>;
  clear(): void | Promise<void>;
  size: number;
}

// L1 Cache - In-memory LRU
export class L1Cache implements CacheLevel {
  private cache: any;
  private hits = 0;
  private misses = 0;

  constructor(options: { maxSize: number; ttl: number }) {
    this.cache = new Map();
  }

  get<T>(key: string): T | undefined {
    const value = this.cache.get(key);
    if (value !== undefined) {
      this.hits++;
      return value;
    }
    this.misses++;
    return undefined;
  }

  set<T>(key: string, value: T, ttl?: number): void {
    this.cache.set(key, value);
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  get size(): number {
    return this.cache.size;
  }

  getStats() {
    return {
      hits: this.hits,
      misses: this.misses,
      hitRate: this.hits / (this.hits + this.misses) || 0,
      size: this.size,
    };
  }
}

// L2 Cache - Redis
export class L2Cache implements CacheLevel {
  private redis: Redis;
  private hits = 0;
  private misses = 0;

  constructor(redisOptions: { host: string; port: number; password?: string }) {
    this.redis = new Redis({
      ...redisOptions,
      lazyConnect: true,
      maxRetriesPerRequest: 3,
      // retryDelayOnFailover: 100,
    });
  }

  async get<T>(key: string): Promise<T | undefined> {
    try {
      const value = await this.redis.get(key);
      if (value !== null) {
        this.hits++;
        return JSON.parse(value);
      }
      this.misses++;
      return undefined;
    } catch (error) {
      this.misses++;
      return undefined;
    }
  }

  async set<T>(key: string, value: T, ttl = 3600): Promise<void> {
    try {
      await this.redis.setex(key, ttl, JSON.stringify(value));
    } catch (error) {
      // Fail silently for cache operations
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      // Fail silently
    }
  }

  async clear(): Promise<void> {
    try {
      await this.redis.flushdb();
    } catch (error) {
      // Fail silently
    }
  }

  get size(): number {
    return 0; // Redis doesn't provide efficient size operation
  }

  getStats() {
    return {
      hits: this.hits,
      misses: this.misses,
      hitRate: this.hits / (this.hits + this.misses) || 0,
    };
  }
}

// Multi-level Cache Manager
export class MultiLevelCacheManager {
  private l1Cache: L1Cache;
  private l2Cache: L2Cache;
  private warmingInProgress = new Set<string>();

  constructor(
    l1Options: { maxSize: number; ttl: number },
    l2Options: { host: string; port: number; password?: string }
  ) {
    this.l1Cache = new L1Cache(l1Options);
    this.l2Cache = new L2Cache(l2Options);
  }

  async get<T>(key: string): Promise<T | undefined> {
    // Try L1 first
    const l1Value = this.l1Cache.get<T>(key);
    if (l1Value !== undefined) {
      return l1Value;
    }

    // Try L2
    const l2Value = await this.l2Cache.get<T>(key);
    if (l2Value !== undefined) {
      // Warm L1 cache
      this.l1Cache.set(key, l2Value);
      return l2Value;
    }

    return undefined;
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    // Set in both levels
    this.l1Cache.set(key, value, ttl);
    await this.l2Cache.set(key, value, ttl);
  }

  async delete(key: string): Promise<void> {
    this.l1Cache.delete(key);
    await this.l2Cache.delete(key);
  }

  async clear(): Promise<void> {
    this.l1Cache.clear();
    await this.l2Cache.clear();
  }

  // Cache warming for frequently accessed keys
  async warmCache(keys: string[], fetcher: (key: string) => Promise<any>): Promise<void> {
    const pendingKeys = keys.filter((key) => !this.warmingInProgress.has(key));

    await Promise.all(
      pendingKeys.map(async (key) => {
        if (this.warmingInProgress.has(key)) return;

        this.warmingInProgress.add(key);
        try {
          const value = await fetcher(key);
          if (value !== undefined) {
            await this.set(key, value);
          }
        } finally {
          this.warmingInProgress.delete(key);
        }
      })
    );
  }

  getStats() {
    return {
      l1: this.l1Cache.getStats(),
      l2: this.l2Cache.getStats(),
    };
  }
}

// Intelligent cache eviction based on access patterns
export class IntelligentCacheEviction {
  private accessPatterns = new Map<
    string,
    { count: number; lastAccess: number; frequency: number }
  >();
  private evictionHistory = new Map<string, number>();

  recordAccess(key: string): void {
    const now = Date.now();
    const current = this.accessPatterns.get(key) || { count: 0, lastAccess: now, frequency: 0 };

    current.count++;
    current.frequency = current.count / (now - current.lastAccess + 1);
    current.lastAccess = now;

    this.accessPatterns.set(key, current);
  }

  shouldEvict(key: string, threshold: number = 0.1): boolean {
    const pattern = this.accessPatterns.get(key);
    if (!pattern) return true;

    const timeSinceAccess = Date.now() - pattern.lastAccess;
    const recentlyEvicted = this.evictionHistory.get(key);

    // Don't evict if recently accessed or frequently used
    if (timeSinceAccess < 60000 || pattern.frequency > threshold) {
      return false;
    }

    // Don't evict if recently re-added after eviction
    if (recentlyEvicted && Date.now() - recentlyEvicted < 300000) {
      return false;
    }

    return true;
  }

  recordEviction(key: string): void {
    this.evictionHistory.set(key, Date.now());
    this.accessPatterns.delete(key);
  }

  getAccessStats(key: string) {
    return this.accessPatterns.get(key);
  }
}

// Performance-optimized cache interface
export interface CacheOptions {
  l1MaxSize: number;
  l1TTL: number;
  l2Host: string;
  l2Port: number;
  l2Password?: string;
  enableIntelligentEviction: boolean;
}

export class PerformanceCache {
  private multiCache: MultiLevelCacheManager;
  private evictionManager!: IntelligentCacheEviction;
  private metrics = {
    requests: 0,
    hits: 0,
    misses: 0,
    errors: 0,
  };

  constructor(options: CacheOptions) {
    this.multiCache = new MultiLevelCacheManager(
      { maxSize: options.l1MaxSize, ttl: options.l1TTL },
      { host: options.l2Host, port: options.l2Port, password: options.l2Password }
    );

    if (options.enableIntelligentEviction) {
      this.evictionManager = new IntelligentCacheEviction();
    }
  }

  async get<T>(key: string): Promise<T | undefined> {
    this.metrics.requests++;

    try {
      if (this.evictionManager) {
        this.evictionManager.recordAccess(key);
      }

      const value = await this.multiCache.get<T>(key);

      if (value !== undefined) {
        this.metrics.hits++;
      } else {
        this.metrics.misses++;
      }

      return value;
    } catch (error) {
      this.metrics.errors++;
      return undefined;
    }
  }

  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    try {
      await this.multiCache.set(key, value, ttl);
    } catch (error) {
      this.metrics.errors++;
    }
  }

  async warmCache(keys: string[], fetcher: (key: string) => Promise<any>): Promise<void> {
    await this.multiCache.warmCache(keys, fetcher);
  }

  getMetrics() {
    return {
      ...this.metrics,
      hitRate: this.metrics.hits / this.metrics.requests || 0,
      errorRate: this.metrics.errors / this.metrics.requests || 0,
      cacheStats: this.multiCache.getStats(),
    };
  }
}

// Factory function for creating performance cache
export function createPerformanceCache(options: Partial<CacheOptions> = {}): PerformanceCache {
  const defaultOptions: CacheOptions = {
    l1MaxSize: 10000,
    l1TTL: 300, // 5 minutes
    l2Host: process.env.REDIS_HOST || 'localhost',
    l2Port: parseInt(process.env.REDIS_PORT || '6379'),
    l2Password: process.env.REDIS_PASSWORD,
    enableIntelligentEviction: true,
  };

  return new PerformanceCache({ ...defaultOptions, ...options });
}
