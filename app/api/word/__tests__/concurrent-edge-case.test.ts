/**
 * @fileoverview 并发场景和边界情况测试
 * 
 * 测试覆盖范围：
 * - 并发请求处理
 * - 数据竞争条件测试
 * - 边界值和极限情况
 * - 异常输入处理
 * - 内存和性能边界测试
 * - 事务一致性验证
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest } from 'next/server';

// Use vi.hoisted to ensure mocks are properly hoisted
const mockRequireAuth = vi.hoisted(() => vi.fn());

// Mock实现 - 提前声明
const mockPracticeService = {
  getUserWordList: vi.fn(),
  addWordsToUserList: vi.fn(),
  removeWordsFromUserList: vi.fn(),
};

// Mock所有依赖
vi.mock('@/lib/1-services/practice/PracticeService', () => ({
  createPracticeService: vi.fn(() => mockPracticeService),
}));
vi.mock('@/lib/1-services/auth/AuthMiddleware', () => ({
  requireAuth: mockRequireAuth,
}));

// Mock NextResponse with proper structure
vi.mock('next/server', () => ({
  NextRequest: vi.fn(),
  NextResponse: {
    json: vi.fn((data, options) => ({
      json: async () => data,
      status: options?.status || 200,
    })),
  },
}));

// Mock认证上下文
const mockAuthContext = {
  user: { id: 'user-123', name: 'Test User', email: '<EMAIL>' },
  authMethod: 'session' as const,
  clientType: 'web' as const,
  permissions: [],
  scopes: ['word:read', 'word:write'],
  ipAddress: '127.0.0.1',
  userAgent: 'test-agent',
};

describe('并发场景和边界情况测试', () => {
  let GET: any, POST: any, DELETE: any;

  beforeEach(async () => {
    // 只清除 mockPracticeService 的调用历史
    mockPracticeService.getUserWordList.mockClear();
    mockPracticeService.addWordsToUserList.mockClear();
    mockPracticeService.removeWordsFromUserList.mockClear();
    
    // Setup mocks
    mockRequireAuth.mockImplementation((options: any) => {
      return (handler: any) => {
        return async (request: NextRequest) => {
          return await handler(request, mockAuthContext);
        };
      };
    });

    // Import route handlers after mocks are set up
    const routeModule = await import('../route');
    GET = routeModule.GET;
    POST = routeModule.POST;
    DELETE = routeModule.DELETE;
  });

  describe('并发请求处理', () => {
    it('应该正确处理并发的GET请求', async () => {
      // Arrange
      const mockWordList = {
        words: [{ id: 1, word: 'test' }],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasMore: false },
      };
      mockPracticeService.getUserWordList.mockResolvedValue(mockWordList);

      const requests = Array.from({ length: 10 }, (_, i) => 
        new NextRequest(`http://localhost:3000/api/word?page=${i + 1}`)
      );

      // Act
      const responses = await Promise.all(
        requests.map(req => GET(req))
      );

      // Assert
      expect(responses).toHaveLength(10);
      for (const response of responses) {
        expect(response.status).toBe(200);
        const data = await response.json();
        expect(data.success).toBe(true);
      }
      expect(mockPracticeService.getUserWordList).toHaveBeenCalledTimes(10);
    });

    it('应该正确处理并发的POST请求', async () => {
      // Arrange
      const mockAddResult = { added: 1, skipped: 0, errors: [] };
      mockPracticeService.addWordsToUserList.mockResolvedValue(mockAddResult);

      const requests = Array.from({ length: 5 }, (_, i) => 
        new NextRequest('http://localhost:3000/api/word', {
          method: 'POST',
          body: JSON.stringify({ words: [`word${i}`] }),
          headers: { 'content-type': 'application/json' },
        })
      );

      // Act
      const responses = await Promise.all(
        requests.map(req => POST(req))
      );

      // Assert
      expect(responses).toHaveLength(5);
      for (const response of responses) {
        expect(response.status).toBe(201);
        const data = await response.json();
        expect(data.success).toBe(true);
      }
      expect(mockPracticeService.addWordsToUserList).toHaveBeenCalledTimes(5);
    });

    it('应该处理混合并发请求（GET/POST/DELETE）', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockResolvedValue({
        words: [],
        pagination: { page: 1, limit: 20, total: 0, totalPages: 0, hasMore: false },
      });
      mockPracticeService.addWordsToUserList.mockResolvedValue({
        added: 1, skipped: 0, errors: [],
      });
      mockPracticeService.removeWordsFromUserList.mockResolvedValue({
        deleted: 1, notFound: 0,
      });

      const getRequest = new NextRequest('http://localhost:3000/api/word');
      const postRequest = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['test'] }),
        headers: { 'content-type': 'application/json' },
      });
      const deleteRequest = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [1] }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const [getResponse, postResponse, deleteResponse] = await Promise.all([
        GET(getRequest),
        POST(postRequest),
        DELETE(deleteRequest),
      ]);

      // Assert
      expect(getResponse.status).toBe(200);
      expect(postResponse.status).toBe(201);
      expect(deleteResponse.status).toBe(200);
    });

    it('应该在高并发下保持数据一致性', async () => {
      // Arrange
      let callCount = 0;
      mockPracticeService.addWordsToUserList.mockImplementation(async () => {
        callCount++;
        // 模拟并发竞争
        await new Promise(resolve => setTimeout(resolve, Math.random() * 10));
        return { added: 1, skipped: 0, errors: [] };
      });

      const requests = Array.from({ length: 20 }, () => 
        new NextRequest('http://localhost:3000/api/word', {
          method: 'POST',
          body: JSON.stringify({ words: ['concurrent-test'] }),
          headers: { 'content-type': 'application/json' },
        })
      );

      // Act
      const responses = await Promise.all(
        requests.map(req => POST(req))
      );

      // Assert
      expect(responses).toHaveLength(20);
      expect(callCount).toBe(20);
      
      // 验证所有请求都成功处理
      for (const response of responses) {
        expect(response.status).toBe(201);
      }
    });
  });

  describe('边界值测试', () => {
    it('应该处理最小分页参数', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockResolvedValue({
        words: [],
        pagination: { page: 1, limit: 1, total: 0, totalPages: 0, hasMore: false },
      });

      const request = new NextRequest(
        'http://localhost:3000/api/word?page=1&limit=1'
      );

      // Act
      const response = await GET(request);

      // Assert
      expect(response.status).toBe(200);
      expect(mockPracticeService.getUserWordList).toHaveBeenCalledWith(
        'user-123',
        expect.objectContaining({ page: 1, limit: 1 })
      );
    });

    it('应该处理最大分页参数', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockResolvedValue({
        words: [],
        pagination: { page: 1, limit: 100, total: 0, totalPages: 0, hasMore: false },
      });

      const request = new NextRequest(
        'http://localhost:3000/api/word?page=1&limit=100'
      );

      // Act
      const response = await GET(request);

      // Assert
      expect(response.status).toBe(200);
      expect(mockPracticeService.getUserWordList).toHaveBeenCalledWith(
        'user-123',
        expect.objectContaining({ page: 1, limit: 100 })
      );
    });

    it('应该拒绝超出限制的分页参数', async () => {
      // Arrange
      const request = new NextRequest(
        'http://localhost:3000/api/word?page=0&limit=1001'
      );

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid query parameters');
    });

    it('应该处理空的单词列表添加', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: [] }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
    });

    it('应该处理大量单词的批量添加', async () => {
      // Arrange
      const largeWordList = Array.from({ length: 1000 }, (_, i) => `word${i}`);
      mockPracticeService.addWordsToUserList.mockResolvedValue({
        added: 1000, skipped: 0, errors: [],
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: largeWordList }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(mockPracticeService.addWordsToUserList).toHaveBeenCalledWith(
        'user-123',
        largeWordList,
        undefined
      );
    });

    it('应该限制单词列表的最大长度', async () => {
      // Arrange
      const tooManyWords = Array.from({ length: 10001 }, (_, i) => `word${i}`);

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: tooManyWords }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid request body');
    });
  });

  describe('异常输入处理', () => {
    it('应该处理无效的JSON格式', async () => {
      // Arrange
      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: '{ invalid json }',
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Invalid JSON format');
    });

    it('应该处理特殊字符的单词', async () => {
      // Arrange
      const specialWords = ['café', 'naïve', 'résumé', '测试', '🚀emoji'];
      mockPracticeService.addWordsToUserList.mockResolvedValue({
        added: 5, skipped: 0, errors: [],
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: specialWords }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(mockPracticeService.addWordsToUserList).toHaveBeenCalledWith(
        'user-123',
        specialWords,
        undefined
      );
    });

    it('应该处理非常长的单词', async () => {
      // Arrange
      const veryLongWord = 'a'.repeat(1000);
      mockPracticeService.addWordsToUserList.mockResolvedValue({
        added: 0, skipped: 0, errors: ['Word too long'],
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: [veryLongWord] }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.errors).toContain('Word too long');
    });

    it('应该处理SQL注入尝试', async () => {
      // Arrange
      const maliciousWords = ["'; DROP TABLE users; --", "1' OR '1'='1"];
      mockPracticeService.addWordsToUserList.mockResolvedValue({
        added: 0, skipped: 0, errors: ['Invalid characters in word'],
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: maliciousWords }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.errors).toContain('Invalid characters in word');
    });

    it('应该处理XSS攻击尝试', async () => {
      // Arrange
      const xssWords = ['<script>alert("xss")</script>', 'javascript:alert(1)'];
      mockPracticeService.addWordsToUserList.mockResolvedValue({
        added: 0, skipped: 0, errors: ['Invalid characters in word'],
      });

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: xssWords }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.errors).toContain('Invalid characters in word');
    });
  });

  describe('错误边界测试', () => {
    it('应该处理数据库连接超时', async () => {
      // Arrange
      mockPracticeService.getUserWordList.mockImplementation(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
        throw new Error('Database connection timeout');
      });

      const request = new NextRequest('http://localhost:3000/api/word');

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Internal server error');
    });

    it('应该处理内存不足错误', async () => {
      // Arrange
      mockPracticeService.addWordsToUserList.mockRejectedValue(
        new Error('JavaScript heap out of memory')
      );

      const request = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['test'] }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const response = await POST(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Internal server error');
    });

    it('应该处理网络中断恢复', async () => {
      // Arrange
      let attempt = 0;
      mockPracticeService.getUserWordList.mockImplementation(async () => {
        attempt++;
        if (attempt === 1) {
          throw new Error('Network error');
        }
        return {
          words: [],
          pagination: { page: 1, limit: 20, total: 0, totalPages: 0, hasMore: false },
        };
      });

      // Act - 第一次请求失败
      const request1 = new NextRequest('http://localhost:3000/api/word');
      const response1 = await GET(request1);
      expect(response1.status).toBe(500);

      // Act - 第二次请求成功
      const request2 = new NextRequest('http://localhost:3000/api/word');
      const response2 = await GET(request2);
      const data2 = await response2.json();

      // Assert
      expect(response2.status).toBe(200);
      expect(data2.success).toBe(true);
    });
  });

  describe('性能边界测试', () => {
    it('应该在合理时间内处理大量数据', async () => {
      // Arrange
      const largeDataset = Array.from({ length: 10000 }, (_, i) => ({
        id: i,
        word: `word${i}`,
        phonetics: [`word${i}`],
        freqRank: i,
        proficiency: {
          practiceCount: i,
          errorCount: 0,
          averageTime: 1000,
          proficiencyScore: 0.8,
          isMarked: false,
          lastPracticed: new Date(),
        },
      }));

      mockPracticeService.getUserWordList.mockResolvedValue({
        words: largeDataset,
        pagination: { page: 1, limit: 10000, total: 10000, totalPages: 1, hasMore: false },
      });

      const request = new NextRequest(
        'http://localhost:3000/api/word?limit=10000'
      );

      // Act
      const startTime = Date.now();
      const response = await GET(request);
      const endTime = Date.now();
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(10000);
      // 确保响应时间在合理范围内（调整为适合的阈值）
      expect(endTime - startTime).toBeLessThan(5000); // 5秒
    });

    it('应该处理内存压力下的请求', async () => {
      // Arrange
      const memoryIntensiveData = Array.from({ length: 1000 }, (_, i) => ({
        id: i,
        word: 'x'.repeat(1000), // 每个单词1KB
        phonetics: Array.from({ length: 10 }, () => 'x'.repeat(100)),
        freqRank: i,
        proficiency: {
          practiceCount: i,
          errorCount: 0,
          averageTime: 1000,
          proficiencyScore: 0.8,
          isMarked: false,
          lastPracticed: new Date(),
        },
      }));

      mockPracticeService.getUserWordList.mockResolvedValue({
        words: memoryIntensiveData,
        pagination: { page: 1, limit: 1000, total: 1000, totalPages: 1, hasMore: false },
      });

      const request = new NextRequest(
        'http://localhost:3000/api/word?limit=1000'
      );

      // Act
      const response = await GET(request);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(1000);
    });
  });

  describe('数据竞争条件测试', () => {
    it('应该处理同时添加和删除相同单词的竞争', async () => {
      // Arrange
      let operationCount = 0;
      mockPracticeService.addWordsToUserList.mockImplementation(async () => {
        operationCount++;
        await new Promise(resolve => setTimeout(resolve, 10));
        return { added: 1, skipped: 0, errors: [] };
      });
      
      mockPracticeService.removeWordsFromUserList.mockImplementation(async () => {
        operationCount++;
        await new Promise(resolve => setTimeout(resolve, 10));
        return { deleted: 1, notFound: 0 };
      });

      const addRequest = new NextRequest('http://localhost:3000/api/word', {
        method: 'POST',
        body: JSON.stringify({ words: ['race-condition-test'] }),
        headers: { 'content-type': 'application/json' },
      });

      const deleteRequest = new NextRequest('http://localhost:3000/api/word', {
        method: 'DELETE',
        body: JSON.stringify({ wordIds: [1] }),
        headers: { 'content-type': 'application/json' },
      });

      // Act
      const [addResponse, deleteResponse] = await Promise.all([
        POST(addRequest),
        DELETE(deleteRequest),
      ]);

      // Assert
      expect(addResponse.status).toBe(201);
      expect(deleteResponse.status).toBe(200);
      expect(operationCount).toBe(2);
    });

    it('应该处理多个用户同时访问的竞争', async () => {
      // Arrange
      const userContexts = [
        { ...mockAuthContext, user: { ...mockAuthContext.user, id: 'user-1' } },
        { ...mockAuthContext, user: { ...mockAuthContext.user, id: 'user-2' } },
        { ...mockAuthContext, user: { ...mockAuthContext.user, id: 'user-3' } },
      ];

      mockRequireAuth.mockImplementation((options: any) => {
        return (handler: any) => {
          return async (request: NextRequest) => {
            // 根据请求随机分配用户上下文
            const userIndex = Math.floor(Math.random() * 3);
            return await handler(request, userContexts[userIndex]);
          };
        };
      });

      mockPracticeService.getUserWordList.mockImplementation(async (userId) => ({
        words: [{ id: 1, word: `word-for-${userId}` }],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasMore: false },
      }));

      const requests = Array.from({ length: 9 }, () => 
        new NextRequest('http://localhost:3000/api/word')
      );

      // Act
      const responses = await Promise.all(
        requests.map(req => GET(req))
      );

      // Assert
      expect(responses).toHaveLength(9);
      for (const response of responses) {
        expect(response.status).toBe(200);
        const data = await response.json();
        expect(data.success).toBe(true);
      }
      expect(mockPracticeService.getUserWordList).toHaveBeenCalledTimes(9);
    });
  });
});