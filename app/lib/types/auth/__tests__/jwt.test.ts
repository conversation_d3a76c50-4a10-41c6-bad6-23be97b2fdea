/**
 * JWT Types Unit Tests
 * JWT类型单元测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  JWTPayload,
  AccessTokenPayload,
  RefreshTokenPayload,
  TokenResponse,
  UserInfo,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  LogoutRequest,
  LogoutResponse,
} from '../jwt';
import { TOKEN_CONSTANTS } from '../index';

describe('JWT Types', () => {
  describe('JWTPayload', () => {
    it('should have required fields', () => {
      const payload: JWTPayload = {
        sub: 'user123',
        email: '<EMAIL>',
        iat: Date.now(),
        exp: Date.now() + 3600,
      };

      expect(payload.sub).toBe('user123');
      expect(payload.email).toBe('<EMAIL>');
      expect(payload.iat).toBeDefined();
      expect(payload.exp).toBeDefined();
    });

    it('should support optional fields', () => {
      const payload: JWTPayload = {
        sub: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar: 'https://example.com/avatar.png',
        provider: 'google',
        iat: Date.now(),
        exp: Date.now() + 3600,
        iss: 'lucid-bd',
        aud: 'lucid-bd-users',
      };

      expect(payload.name).toBe('Test User');
      expect(payload.avatar).toBe('https://example.com/avatar.png');
      expect(payload.provider).toBe('google');
      expect(payload.iss).toBe('lucid-bd');
      expect(payload.aud).toBe('lucid-bd-users');
    });
  });

  describe('AccessTokenPayload', () => {
    it('should extend JWTPayload with access token specific fields', () => {
      const payload: AccessTokenPayload = {
        sub: 'user123',
        email: '<EMAIL>',
        type: 'access',
        iat: Date.now(),
        exp: Date.now() + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      };

      expect(payload.type).toBe('access');
      expect(payload.sub).toBe('user123');
      expect(payload.email).toBe('<EMAIL>');
    });

    it('should support optional permissions and sessionId', () => {
      const payload: AccessTokenPayload = {
        sub: 'user123',
        email: '<EMAIL>',
        type: 'access',
        permissions: ['read', 'write'],
        sessionId: 'session123',
        iat: Date.now(),
        exp: Date.now() + TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
      };

      expect(payload.permissions).toEqual(['read', 'write']);
      expect(payload.sessionId).toBe('session123');
    });
  });

  describe('RefreshTokenPayload', () => {
    it('should extend JWTPayload with refresh token specific fields', () => {
      const payload: RefreshTokenPayload = {
        sub: 'user123',
        email: '<EMAIL>',
        type: 'refresh',
        tokenId: 'refresh_token_123',
        iat: Date.now(),
        exp: Date.now() + TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN,
      };

      expect(payload.type).toBe('refresh');
      expect(payload.tokenId).toBe('refresh_token_123');
    });
  });

  describe('TokenResponse', () => {
    it('should have all required fields', () => {
      const response: TokenResponse = {
        accessToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
        refreshToken: 'refresh_token_123',
        expiresIn: TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
        tokenType: 'Bearer',
        user: {
          id: 'user123',
          email: '<EMAIL>',
          isActive: true,
        },
      };

      expect(response.accessToken).toBeDefined();
      expect(response.refreshToken).toBeDefined();
      expect(response.expiresIn).toBe(TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN);
      expect(response.tokenType).toBe('Bearer');
      expect(response.user).toBeDefined();
    });
  });

  describe('UserInfo', () => {
    it('should have required fields', () => {
      const user: UserInfo = {
        id: 'user123',
        email: '<EMAIL>',
        isActive: true,
      };

      expect(user.id).toBe('user123');
      expect(user.email).toBe('<EMAIL>');
      expect(user.isActive).toBe(true);
    });

    it('should support optional fields', () => {
      const user: UserInfo = {
        id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        avatar: 'https://example.com/avatar.png',
        provider: 'google',
        isActive: true,
        lastLoginAt: new Date(),
      };

      expect(user.name).toBe('Test User');
      expect(user.avatar).toBe('https://example.com/avatar.png');
      expect(user.provider).toBe('google');
      expect(user.lastLoginAt).toBeInstanceOf(Date);
    });
  });

  describe('LoginRequest', () => {
    it('should have required fields', () => {
      const request: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
      };

      expect(request.email).toBe('<EMAIL>');
      expect(request.password).toBe('password123');
    });

    it('should support optional remember field', () => {
      const request: LoginRequest = {
        email: '<EMAIL>',
        password: 'password123',
        remember: true,
      };

      expect(request.remember).toBe(true);
    });
  });

  describe('LoginResponse', () => {
    it('should extend TokenResponse with login specific fields', () => {
      const response: LoginResponse = {
        accessToken: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...',
        refreshToken: 'refresh_token_123',
        expiresIn: TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
        tokenType: 'Bearer',
        user: {
          id: 'user123',
          email: '<EMAIL>',
          isActive: true,
        },
        success: true,
        message: '登录成功',
      };

      expect(response.success).toBe(true);
      expect(response.message).toBe('登录成功');
    });
  });

  describe('RefreshTokenRequest', () => {
    it('should have required refreshToken field', () => {
      const request: RefreshTokenRequest = {
        refreshToken: 'refresh_token_123',
      };

      expect(request.refreshToken).toBe('refresh_token_123');
    });
  });

  describe('RefreshTokenResponse', () => {
    it('should have all required fields', () => {
      const response: RefreshTokenResponse = {
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
        expiresIn: TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN,
        tokenType: 'Bearer',
      };

      expect(response.accessToken).toBe('new_access_token');
      expect(response.refreshToken).toBe('new_refresh_token');
      expect(response.expiresIn).toBe(TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN);
      expect(response.tokenType).toBe('Bearer');
    });
  });

  describe('LogoutRequest', () => {
    it('should support optional fields', () => {
      const request: LogoutRequest = {
        refreshToken: 'refresh_token_123',
        all: true,
      };

      expect(request.refreshToken).toBe('refresh_token_123');
      expect(request.all).toBe(true);
    });

    it('should work with empty object', () => {
      const request: LogoutRequest = {};
      expect(request).toBeDefined();
    });
  });

  describe('LogoutResponse', () => {
    it('should have required fields', () => {
      const response: LogoutResponse = {
        success: true,
        message: '登出成功',
      };

      expect(response.success).toBe(true);
      expect(response.message).toBe('登出成功');
    });
  });
});

describe('TOKEN_CONSTANTS', () => {
  it('should have correct access token expiration', () => {
    expect(TOKEN_CONSTANTS.ACCESS_TOKEN_EXPIRES_IN).toBe(15 * 60); // 15 minutes
  });

  it('should have correct refresh token expiration', () => {
    expect(TOKEN_CONSTANTS.REFRESH_TOKEN_EXPIRES_IN).toBe(30 * 24 * 60 * 60); // 30 days
  });

  it('should have correct token type', () => {
    expect(TOKEN_CONSTANTS.TOKEN_TYPE).toBe('Bearer');
  });

  it('should have correct JWT algorithm', () => {
    expect(TOKEN_CONSTANTS.JWT_ALGORITHM).toBe('RS256');
  });

  it('should have correct issuer and audience', () => {
    expect(TOKEN_CONSTANTS.JWT_ISSUER).toBe('lucid-bd');
    expect(TOKEN_CONSTANTS.JWT_AUDIENCE).toBe('lucid-bd-users');
  });
});
