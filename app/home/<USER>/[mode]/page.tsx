import { TypingModeSwitcher } from '@/components/typer/TypingModeSwitcher';
import { notFound } from 'next/navigation';

// 支持的练习模式
const validModes = ['typing-test', 'word-practice', 'sentence-practice'];

interface PageProps {
  params: Promise<{
    mode: string;
  }>;
}

/**
 * 动态路由页面组件
 * 根据 URL 参数动态渲染对应的打字练习模式
 */
export default async function TyperModePage({ params }: PageProps) {
  const { mode } = await params;

  // 验证路由参数是否有效
  if (!validModes.includes(mode)) {
    notFound();
  }

  return <TypingModeSwitcher mode={mode} />;
}

/**
 * 生成静态路由参数
 * 预生成所有有效的练习模式路由
 */
export async function generateStaticParams() {
  return validModes.map((mode) => ({
    mode: mode,
  }));
}

/**
 * 生成页面元数据
 */
export async function generateMetadata({ params }: PageProps) {
  const { mode } = await params;

  const titles: Record<string, string> = {
    'typing-test': '正常打字训练',
    'word-practice': '用户生词训练',
    'sentence-practice': '句子生成训练',
  };

  const title = titles[mode] || '打字练习';

  return {
    title: `${title} - Lucid BD`,
    description: `${title}模式，提升你的英语打字技能`,
  };
}
