# OAuth 配置指南

## 🔧 Google OAuth 设置

### 1. 创建 Google Cloud 项目

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 记下项目 ID

### 2. 启用 Google+ API

1. 在左侧导航中选择 "API 和服务" > "库"
2. 搜索 "Google+ API" 并点击
3. 点击 "启用" 按钮

### 3. 创建 OAuth 2.0 凭据

1. 转到 "API 和服务" > "凭据"
2. 点击 "创建凭据" > "OAuth 2.0 客户端 ID"
3. 如果首次创建，需要先配置 OAuth 同意屏幕：
   - 用户类型选择 "外部"
   - 填写应用名称、用户支持邮箱等必填信息
   - 在作用域页面，可以跳过添加作用域
   - 在测试用户页面，添加你的测试邮箱
4. 创建 OAuth 2.0 客户端 ID：
   - 应用类型选择 "Web 应用程序"
   - 名称：任意名称（如 "Lucid BD Local Dev"）
   - 授权的重定向 URI 添加：
     ```
     http://localhost:4000/api/auth/callback/google
     ```
5. 创建后会显示客户端 ID 和客户端密钥，复制这两个值

### 4. 配置环境变量

在 `.env.local` 文件中设置：

```bash
GOOGLE_CLIENT_ID="你的Google客户端ID"
GOOGLE_CLIENT_SECRET="你的Google客户端密钥"
```

## 🔧 GitHub OAuth 设置

### 1. 创建 GitHub OAuth App

1. 访问 [GitHub Developer Settings](https://github.com/settings/developers)
2. 点击 "New OAuth App"
3. 填写信息：
   - Application name: `Lucid BD Local Dev`
   - Homepage URL: `http://localhost:4000`
   - Authorization callback URL: `http://localhost:4000/api/auth/callback/github`
4. 点击 "Register application"
5. 复制 Client ID 和生成 Client Secret

### 2. 配置环境变量

在 `.env.local` 文件中设置：

```bash
GITHUB_ID="你的GitHub客户端ID"
GITHUB_SECRET="你的GitHub客户端密钥"
```

## 🔧 NextAuth 密钥设置

生成一个安全的随机密钥：

```bash
openssl rand -base64 32
```

或者使用在线生成器：https://generate-secret.vercel.app/32

在 `.env.local` 中设置：

```bash
NEXTAUTH_SECRET="生成的随机密钥"
```

## 🔧 测试配置

1. 重启开发服务器：

   ```bash
   pnpm dev
   ```

2. 访问 `http://localhost:4000/login`
3. 点击 "Login with Google" 或 "Login with GitHub"
4. 应该能够正常跳转到 OAuth 提供商进行授权

## 🚨 常见问题

### Google OAuth 错误

- **Error 400: redirect_uri_mismatch**
  - 检查重定向 URI 是否正确设置为 `http://localhost:4000/api/auth/callback/google`
- **Error: access_denied**
  - 检查 OAuth 同意屏幕是否配置完成
  - 确保测试用户列表中包含你的邮箱

### GitHub OAuth 错误

- **Error: The redirect_uri MUST match the registered callback URL**
  - 检查回调 URL 是否正确设置为 `http://localhost:4000/api/auth/callback/github`

### 网络超时错误

- 检查网络连接是否正常
- 确保防火墙没有阻止 OAuth 请求
- 如果在公司网络环境，可能需要配置代理

## 📝 生产环境配置

在生产环境中，需要：

1. 更新重定向 URI 为生产域名
2. 使用强随机密钥
3. 启用 HTTPS
4. 配置正确的 NEXTAUTH_URL
