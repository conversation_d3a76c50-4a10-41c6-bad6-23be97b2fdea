/**
 * Performance Regression Test Suite
 *
 * Automated regression testing to ensure performance improvements are maintained
 * and no performance degradation occurs during development
 *
 * Test Goals:
 * - Establish performance baselines
 * - Detect performance regressions
 * - Validate performance improvements persist
 * - Monitor performance trends over time
 */

import { promises as fs } from 'fs';
import { join } from 'path';
import { runLightweightSecurityPerformanceTest } from './lightweight-performance-test';
import { runBenchmarkComparison } from './benchmark-comparison';
import { runCachePerformanceTest } from './cache-performance-test';
import { runConcurrencyStressTest } from './concurrency-stress-test';

// ============================================================================
// Performance Baseline Configuration
// ============================================================================

interface PerformanceBaseline {
  version: string;
  timestamp: string;
  testEnvironment: {
    nodeVersion: string;
    platform: string;
    memory: number;
    cpuCount: number;
  };
  metrics: {
    responseTime: {
      minimal: number;
      standard: number;
      strict: number;
    };
    throughput: number;
    memoryUsage: number;
    cacheHitRate: number;
    concurrencyHandling: number;
    errorRate: number;
  };
  rawResults: {
    lightweightTest: any;
    benchmarkComparison: any;
    cacheTest: any;
    stressTest: any;
  };
}

interface RegressionTestConfig {
  baselineFile: string;
  toleranceThresholds: {
    responseTime: number; // Percentage tolerance
    throughput: number;
    memoryUsage: number;
    cacheHitRate: number;
    errorRate: number;
  };
  warningThresholds: {
    responseTime: number;
    throughput: number;
    memoryUsage: number;
    cacheHitRate: number;
    errorRate: number;
  };
  trendAnalysis: {
    enabled: boolean;
    historyLength: number;
    degradationThreshold: number;
  };
}

const DEFAULT_REGRESSION_CONFIG: RegressionTestConfig = {
  baselineFile: join(process.cwd(), 'performance-baseline.json'),
  toleranceThresholds: {
    responseTime: 10, // 10% tolerance
    throughput: 15,
    memoryUsage: 20,
    cacheHitRate: 5,
    errorRate: 50,
  },
  warningThresholds: {
    responseTime: 5, // 5% warning threshold
    throughput: 10,
    memoryUsage: 15,
    cacheHitRate: 3,
    errorRate: 25,
  },
  trendAnalysis: {
    enabled: true,
    historyLength: 10,
    degradationThreshold: 3, // 3% degradation over trend
  },
};

// ============================================================================
// Performance Regression Results
// ============================================================================

interface RegressionResult {
  metric: string;
  baseline: number;
  current: number;
  change: number;
  changePercentage: number;
  status: 'PASS' | 'WARN' | 'FAIL';
  threshold: number;
  message: string;
}

interface RegressionTestResults {
  version: string;
  timestamp: string;
  testDuration: number;
  overall: {
    status: 'PASS' | 'WARN' | 'FAIL';
    passCount: number;
    warnCount: number;
    failCount: number;
  };
  results: RegressionResult[];
  trends: {
    metric: string;
    trend: 'IMPROVING' | 'STABLE' | 'DEGRADING';
    changeOverTime: number;
    confidence: number;
  }[];
  recommendations: string[];
  newBaseline: PerformanceBaseline;
}

// ============================================================================
// Performance Regression Test Runner
// ============================================================================

class PerformanceRegressionTest {
  private config: RegressionTestConfig;
  private baseline: PerformanceBaseline | null = null;
  private history: PerformanceBaseline[] = [];

  constructor(config: Partial<RegressionTestConfig> = {}) {
    this.config = { ...DEFAULT_REGRESSION_CONFIG, ...config };
  }

  async runRegressionTest(): Promise<RegressionTestResults> {
    console.log('🔍 Starting Performance Regression Test');
    console.log('='.repeat(60));
    console.log('');

    const startTime = Date.now();

    // Load baseline and history
    await this.loadBaseline();
    await this.loadHistory();

    // Run all performance tests
    console.log('🚀 Running comprehensive performance tests...');
    const [lightweightResults, benchmarkResults, cacheResults, stressResults] = await Promise.all([
      runLightweightSecurityPerformanceTest(),
      runBenchmarkComparison(),
      runCachePerformanceTest(),
      runConcurrencyStressTest(),
    ]);

    // Create new baseline
    const newBaseline = this.createBaseline(
      lightweightResults,
      benchmarkResults,
      cacheResults,
      stressResults
    );

    // Compare with baseline
    const regressionResults = this.compareWithBaseline(newBaseline);

    // Analyze trends
    const trends = this.analyzeTrends(newBaseline);

    // Generate recommendations
    const recommendations = this.generateRecommendations(regressionResults, trends);

    const testDuration = Date.now() - startTime;

    const results: RegressionTestResults = {
      version: this.getCurrentVersion(),
      timestamp: new Date().toISOString(),
      testDuration,
      overall: this.calculateOverallStatus(regressionResults),
      results: regressionResults,
      trends,
      recommendations,
      newBaseline,
    };

    // Save results
    await this.saveResults(results);

    this.printRegressionResults(results);
    return results;
  }

  private async loadBaseline(): Promise<void> {
    try {
      const data = await fs.readFile(this.config.baselineFile, 'utf-8');
      this.baseline = JSON.parse(data);
      console.log(`📊 Loaded baseline from ${this.config.baselineFile}`);
    } catch (error) {
      console.log('📊 No baseline found, will create new baseline');
      this.baseline = null;
    }
  }

  private async loadHistory(): Promise<void> {
    try {
      const historyFile = this.config.baselineFile.replace('.json', '-history.json');
      const data = await fs.readFile(historyFile, 'utf-8');
      this.history = JSON.parse(data);
      console.log(`📈 Loaded ${this.history.length} historical baselines`);
    } catch (error) {
      console.log('📈 No performance history found');
      this.history = [];
    }
  }

  private createBaseline(
    lightweightResults: any,
    benchmarkResults: any,
    cacheResults: any,
    stressResults: any
  ): PerformanceBaseline {
    return {
      version: this.getCurrentVersion(),
      timestamp: new Date().toISOString(),
      testEnvironment: {
        nodeVersion: process.version,
        platform: process.platform,
        memory: process.memoryUsage().heapTotal,
        cpuCount: require('os').cpus().length,
      },
      metrics: {
        responseTime: {
          minimal: lightweightResults.responseTimeStats.minimal.avg,
          standard: lightweightResults.responseTimeStats.standard.avg,
          strict: lightweightResults.responseTimeStats.strict.avg,
        },
        throughput: lightweightResults.throughput,
        memoryUsage: lightweightResults.memoryStats.peak,
        cacheHitRate: cacheResults.overall.hitRate,
        concurrencyHandling: stressResults.overall.throughput.overall,
        errorRate: stressResults.overall.failedRequests / stressResults.overall.totalRequests,
      },
      rawResults: {
        lightweightTest: lightweightResults,
        benchmarkComparison: benchmarkResults,
        cacheTest: cacheResults,
        stressTest: stressResults,
      },
    };
  }

  private compareWithBaseline(newBaseline: PerformanceBaseline): RegressionResult[] {
    if (!this.baseline) {
      console.log('📊 No baseline for comparison, creating initial baseline');
      return [];
    }

    const results: RegressionResult[] = [];

    // Response time comparisons
    const responseTimeMetrics = [
      { key: 'minimal', name: 'Response Time (Minimal)' },
      { key: 'standard', name: 'Response Time (Standard)' },
      { key: 'strict', name: 'Response Time (Strict)' },
    ];

    for (const metric of responseTimeMetrics) {
      const baseline =
        this.baseline.metrics.responseTime[
          metric.key as keyof typeof this.baseline.metrics.responseTime
        ];
      const current =
        newBaseline.metrics.responseTime[
          metric.key as keyof typeof newBaseline.metrics.responseTime
        ];

      results.push(
        this.createRegressionResult(
          metric.name,
          baseline,
          current,
          this.config.toleranceThresholds.responseTime,
          this.config.warningThresholds.responseTime,
          'lower' // Lower is better for response times
        )
      );
    }

    // Throughput comparison
    results.push(
      this.createRegressionResult(
        'Throughput',
        this.baseline.metrics.throughput,
        newBaseline.metrics.throughput,
        this.config.toleranceThresholds.throughput,
        this.config.warningThresholds.throughput,
        'higher' // Higher is better for throughput
      )
    );

    // Memory usage comparison
    results.push(
      this.createRegressionResult(
        'Memory Usage',
        this.baseline.metrics.memoryUsage,
        newBaseline.metrics.memoryUsage,
        this.config.toleranceThresholds.memoryUsage,
        this.config.warningThresholds.memoryUsage,
        'lower' // Lower is better for memory usage
      )
    );

    // Cache hit rate comparison
    results.push(
      this.createRegressionResult(
        'Cache Hit Rate',
        this.baseline.metrics.cacheHitRate,
        newBaseline.metrics.cacheHitRate,
        this.config.toleranceThresholds.cacheHitRate,
        this.config.warningThresholds.cacheHitRate,
        'higher' // Higher is better for cache hit rate
      )
    );

    // Concurrency handling comparison
    results.push(
      this.createRegressionResult(
        'Concurrency Handling',
        this.baseline.metrics.concurrencyHandling,
        newBaseline.metrics.concurrencyHandling,
        this.config.toleranceThresholds.throughput,
        this.config.warningThresholds.throughput,
        'higher' // Higher is better for concurrency
      )
    );

    // Error rate comparison
    results.push(
      this.createRegressionResult(
        'Error Rate',
        this.baseline.metrics.errorRate,
        newBaseline.metrics.errorRate,
        this.config.toleranceThresholds.errorRate,
        this.config.warningThresholds.errorRate,
        'lower' // Lower is better for error rate
      )
    );

    return results;
  }

  private createRegressionResult(
    metric: string,
    baseline: number,
    current: number,
    tolerance: number,
    warning: number,
    direction: 'higher' | 'lower'
  ): RegressionResult {
    const change = current - baseline;
    const changePercentage = (change / baseline) * 100;

    let status: 'PASS' | 'WARN' | 'FAIL' = 'PASS';
    let threshold = tolerance;
    let message = '';

    if (direction === 'lower') {
      // For metrics where lower is better (response time, memory usage, error rate)
      if (changePercentage > tolerance) {
        status = 'FAIL';
        message = `Regression detected: ${metric} increased by ${changePercentage.toFixed(2)}%`;
      } else if (changePercentage > warning) {
        status = 'WARN';
        threshold = warning;
        message = `Warning: ${metric} increased by ${changePercentage.toFixed(2)}%`;
      } else {
        message =
          changePercentage < 0
            ? `Improvement: ${metric} decreased by ${Math.abs(changePercentage).toFixed(2)}%`
            : `Stable: ${metric} within acceptable range`;
      }
    } else {
      // For metrics where higher is better (throughput, cache hit rate)
      if (changePercentage < -tolerance) {
        status = 'FAIL';
        message = `Regression detected: ${metric} decreased by ${Math.abs(changePercentage).toFixed(2)}%`;
      } else if (changePercentage < -warning) {
        status = 'WARN';
        threshold = warning;
        message = `Warning: ${metric} decreased by ${Math.abs(changePercentage).toFixed(2)}%`;
      } else {
        message =
          changePercentage > 0
            ? `Improvement: ${metric} increased by ${changePercentage.toFixed(2)}%`
            : `Stable: ${metric} within acceptable range`;
      }
    }

    return {
      metric,
      baseline,
      current,
      change,
      changePercentage,
      status,
      threshold,
      message,
    };
  }

  private analyzeTrends(newBaseline: PerformanceBaseline): RegressionTestResults['trends'] {
    if (!this.config.trendAnalysis.enabled || this.history.length < 3) {
      return [];
    }

    const trends: RegressionTestResults['trends'] = [];
    const recentHistory = this.history.slice(-this.config.trendAnalysis.historyLength);
    recentHistory.push(newBaseline);

    const metrics = [
      { key: 'throughput', name: 'Throughput', direction: 'higher' as const },
      { key: 'memoryUsage', name: 'Memory Usage', direction: 'lower' as const },
      { key: 'cacheHitRate', name: 'Cache Hit Rate', direction: 'higher' as const },
      { key: 'concurrencyHandling', name: 'Concurrency Handling', direction: 'higher' as const },
    ];

    for (const metric of metrics) {
      const values = recentHistory.map(
        (h) => h.metrics[metric.key as keyof typeof h.metrics] as number
      );
      const trend = this.calculateTrend(values);

      trends.push({
        metric: metric.name,
        trend: this.interpretTrend(trend, metric.direction),
        changeOverTime: trend,
        confidence: this.calculateTrendConfidence(values),
      });
    }

    return trends;
  }

  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;

    const n = values.length;
    const sumX = (n * (n + 1)) / 2;
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = values.reduce((sum, y, i) => sum + (i + 1) * y, 0);
    const sumX2 = (n * (n + 1) * (2 * n + 1)) / 6;

    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    const firstValue = values[0];

    return ((slope * (n - 1)) / firstValue) * 100; // Percentage change over time
  }

  private interpretTrend(
    trend: number,
    direction: 'higher' | 'lower'
  ): 'IMPROVING' | 'STABLE' | 'DEGRADING' {
    const threshold = this.config.trendAnalysis.degradationThreshold;

    if (Math.abs(trend) < threshold) {
      return 'STABLE';
    }

    if (direction === 'higher') {
      return trend > 0 ? 'IMPROVING' : 'DEGRADING';
    } else {
      return trend < 0 ? 'IMPROVING' : 'DEGRADING';
    }
  }

  private calculateTrendConfidence(values: number[]): number {
    if (values.length < 3) return 0;

    // Calculate coefficient of variation
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);
    const cv = stdDev / mean;

    // Convert to confidence (lower CV = higher confidence)
    return Math.max(0, Math.min(1, 1 - cv));
  }

  private generateRecommendations(
    results: RegressionResult[],
    trends: RegressionTestResults['trends']
  ): string[] {
    const recommendations: string[] = [];

    // Check for failures
    const failures = results.filter((r) => r.status === 'FAIL');
    if (failures.length > 0) {
      recommendations.push('URGENT: Address performance regressions immediately');
      failures.forEach((failure) => {
        recommendations.push(`- Fix ${failure.metric}: ${failure.message}`);
      });
    }

    // Check for warnings
    const warnings = results.filter((r) => r.status === 'WARN');
    if (warnings.length > 0) {
      recommendations.push('Monitor performance warnings closely');
      warnings.forEach((warning) => {
        recommendations.push(`- Watch ${warning.metric}: ${warning.message}`);
      });
    }

    // Check for degrading trends
    const degradingTrends = trends.filter((t) => t.trend === 'DEGRADING');
    if (degradingTrends.length > 0) {
      recommendations.push('Address degrading performance trends');
      degradingTrends.forEach((trend) => {
        recommendations.push(`- Investigate ${trend.metric} degradation over time`);
      });
    }

    // General recommendations
    if (recommendations.length === 0) {
      recommendations.push('Performance is stable - continue monitoring');
      recommendations.push('Consider implementing additional optimizations');
    }

    return recommendations;
  }

  private calculateOverallStatus(results: RegressionResult[]): RegressionTestResults['overall'] {
    const passCount = results.filter((r) => r.status === 'PASS').length;
    const warnCount = results.filter((r) => r.status === 'WARN').length;
    const failCount = results.filter((r) => r.status === 'FAIL').length;

    let status: 'PASS' | 'WARN' | 'FAIL' = 'PASS';
    if (failCount > 0) {
      status = 'FAIL';
    } else if (warnCount > 0) {
      status = 'WARN';
    }

    return {
      status,
      passCount,
      warnCount,
      failCount,
    };
  }

  private async saveResults(results: RegressionTestResults): Promise<void> {
    // Save new baseline
    await fs.writeFile(this.config.baselineFile, JSON.stringify(results.newBaseline, null, 2));

    // Update history
    this.history.push(results.newBaseline);
    if (this.history.length > this.config.trendAnalysis.historyLength) {
      this.history = this.history.slice(-this.config.trendAnalysis.historyLength);
    }

    const historyFile = this.config.baselineFile.replace('.json', '-history.json');
    await fs.writeFile(historyFile, JSON.stringify(this.history, null, 2));

    // Save full results
    const resultsFile = this.config.baselineFile.replace('.json', `-results-${Date.now()}.json`);
    await fs.writeFile(resultsFile, JSON.stringify(results, null, 2));

    console.log(`💾 Results saved to ${resultsFile}`);
  }

  private getCurrentVersion(): string {
    try {
      const packageJson = require('../package.json');
      return packageJson.version || '1.0.0';
    } catch {
      return '1.0.0';
    }
  }

  private printRegressionResults(results: RegressionTestResults): void {
    console.log('\n📊 PERFORMANCE REGRESSION TEST RESULTS');
    console.log('='.repeat(60));
    console.log('');

    // Overall status
    console.log('🏆 OVERALL STATUS');
    console.log('-'.repeat(40));
    console.log(`Test Status: ${results.overall.status}`);
    console.log(`Pass: ${results.overall.passCount}`);
    console.log(`Warn: ${results.overall.warnCount}`);
    console.log(`Fail: ${results.overall.failCount}`);
    console.log(`Test Duration: ${(results.testDuration / 1000).toFixed(2)}s`);
    console.log('');

    // Detailed results
    if (results.results.length > 0) {
      console.log('📈 REGRESSION ANALYSIS');
      console.log('-'.repeat(60));
      console.log('Metric                 | Status | Baseline | Current  | Change   | Message');
      console.log('-'.repeat(60));

      for (const result of results.results) {
        const statusIcon = result.status === 'PASS' ? '✅' : result.status === 'WARN' ? '⚠️' : '❌';
        const baselineStr = this.formatMetricValue(result.baseline);
        const currentStr = this.formatMetricValue(result.current);
        const changeStr = `${result.changePercentage >= 0 ? '+' : ''}${result.changePercentage.toFixed(2)}%`;

        console.log(
          `${result.metric.padEnd(22)} | ${statusIcon} ${result.status.padEnd(4)} | ${baselineStr.padStart(8)} | ${currentStr.padStart(8)} | ${changeStr.padStart(8)} | ${result.message}`
        );
      }
      console.log('');
    }

    // Trend analysis
    if (results.trends.length > 0) {
      console.log('📊 TREND ANALYSIS');
      console.log('-'.repeat(50));
      console.log('Metric                 | Trend      | Change   | Confidence');
      console.log('-'.repeat(50));

      for (const trend of results.trends) {
        const trendIcon =
          trend.trend === 'IMPROVING' ? '📈' : trend.trend === 'DEGRADING' ? '📉' : '➡️';
        const changeStr = `${trend.changeOverTime >= 0 ? '+' : ''}${trend.changeOverTime.toFixed(2)}%`;
        const confidenceStr = `${(trend.confidence * 100).toFixed(0)}%`;

        console.log(
          `${trend.metric.padEnd(22)} | ${trendIcon} ${trend.trend.padEnd(9)} | ${changeStr.padStart(8)} | ${confidenceStr.padStart(10)}`
        );
      }
      console.log('');
    }

    // Recommendations
    if (results.recommendations.length > 0) {
      console.log('💡 RECOMMENDATIONS');
      console.log('-'.repeat(40));
      results.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
      console.log('');
    }

    // Final assessment
    console.log('🔍 FINAL ASSESSMENT');
    console.log('-'.repeat(40));
    if (results.overall.status === 'PASS') {
      console.log('✅ No performance regressions detected');
      console.log('✅ Performance is stable and within acceptable thresholds');
    } else if (results.overall.status === 'WARN') {
      console.log('⚠️  Performance warnings detected');
      console.log('⚠️  Monitor performance closely and consider optimizations');
    } else {
      console.log('❌ Performance regressions detected');
      console.log('❌ Immediate action required to address performance issues');
    }
  }

  private formatMetricValue(value: number): string {
    if (value < 1) {
      return (value * 100).toFixed(2) + '%';
    } else if (value < 1000) {
      return value.toFixed(2);
    } else if (value < 1000000) {
      return (value / 1000).toFixed(2) + 'K';
    } else {
      return (value / 1000000).toFixed(2) + 'M';
    }
  }
}

// ============================================================================
// Main Execution
// ============================================================================

export async function runPerformanceRegressionTest(): Promise<RegressionTestResults> {
  const test = new PerformanceRegressionTest();
  return await test.runRegressionTest();
}

// Run tests if this file is executed directly
if (require.main === module) {
  runPerformanceRegressionTest()
    .then((results) => {
      console.log('\n🎉 Performance regression test completed!');

      // Exit with appropriate code
      if (results.overall.status === 'FAIL') {
        process.exit(1);
      } else if (results.overall.status === 'WARN') {
        process.exit(2);
      } else {
        process.exit(0);
      }
    })
    .catch((error) => {
      console.error('❌ Performance regression test failed:', error);
      process.exit(1);
    });
}
