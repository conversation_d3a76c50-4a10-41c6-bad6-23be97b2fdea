#!/usr/bin/env tsx

/**
 * 失败原因分析工具
 * 用于分析为什么某些单词无法成功导入或设置排名
 */

import type { DictionaryQueryResult } from '../../../utils/dict-types.js';

/**
 * 失败分析结果
 */
export interface FailureAnalysis {
  word: string;
  category: 'api_error' | 'invalid_word' | 'parsing_error' | 'database_error' | 'unknown';
  reason: string;
  suggestion: string;
  canRetry: boolean;
  details?: any;
}

/**
 * API响应分析结果
 */
export interface ApiResponseAnalysis {
  totalWords: number;
  successfulWords: string[];
  illegalWords: string[];
  missingWords: string[];
  errors: Array<{
    word: string;
    error: string;
  }>;
}

/**
 * 分析API响应结果
 */
export function analyzeApiResponse(
  requestedWords: string[],
  apiResult: DictionaryQueryResult
): ApiResponseAnalysis {
  const analysis: ApiResponseAnalysis = {
    totalWords: requestedWords.length,
    successfulWords: [],
    illegalWords: [],
    missingWords: [],
    errors: [],
  };

  try {
    if (apiResult.statusCode === 200 && apiResult.data) {
      // 成功的API调用
      const responseData = apiResult.data;

      // 获取成功返回的单词
      if (responseData.words && Array.isArray(responseData.words)) {
        analysis.successfulWords = responseData.words.map((word) => word.word);
      }

      // 获取非法单词列表
      if (responseData.illegalWords && Array.isArray(responseData.illegalWords)) {
        analysis.illegalWords = responseData.illegalWords;
      }

      // 找出缺失的单词（请求了但没有返回的）
      const returnedWords = new Set([...analysis.successfulWords, ...analysis.illegalWords]);
      analysis.missingWords = requestedWords.filter((word) => !returnedWords.has(word));
    } else {
      // API调用失败
      requestedWords.forEach((word) => {
        analysis.errors.push({
          word,
          error: apiResult.error?.message || '未知API错误',
        });
      });
    }
  } catch (error) {
    // 解析错误
    requestedWords.forEach((word) => {
      analysis.errors.push({
        word,
        error: `响应解析失败: ${(error as Error).message}`,
      });
    });
  }

  return analysis;
}

/**
 * 分析单个单词的失败原因
 */
export function analyzeWordFailure(
  word: string,
  context: {
    apiResponse?: DictionaryQueryResult;
    importError?: any;
    dbError?: any;
  }
): FailureAnalysis {
  const { apiResponse, importError, dbError } = context;

  // 数据库错误
  if (dbError) {
    return {
      word,
      category: 'database_error',
      reason: `数据库操作失败: ${dbError.message || dbError}`,
      suggestion: '检查数据库连接和表结构，可能需要重试',
      canRetry: true,
      details: dbError,
    };
  }

  // 导入错误
  if (importError) {
    return {
      word,
      category: 'database_error',
      reason: `数据导入失败: ${importError.message || importError}`,
      suggestion: '检查数据格式和数据库约束，可能需要清理数据后重试',
      canRetry: true,
      details: importError,
    };
  }

  // API响应分析
  if (apiResponse) {
    if (apiResponse.statusCode !== 200) {
      return {
        word,
        category: 'api_error',
        reason: `API调用失败 (状态码: ${apiResponse.statusCode}): ${apiResponse.error?.message || '未知错误'}`,
        suggestion: '检查网络连接和API密钥，稍后重试',
        canRetry: true,
        details: apiResponse.error,
      };
    }

    // 检查是否在非法单词列表中
    if (apiResponse.data?.illegalWords?.includes(word)) {
      return {
        word,
        category: 'invalid_word',
        reason: 'API认为这是无效或无法识别的单词',
        suggestion: '检查单词拼写，或者这可能是专有名词、缩写或非标准词汇',
        canRetry: false,
        details: { illegalWords: apiResponse.data.illegalWords },
      };
    }

    // 检查是否在返回的单词中
    const returnedWords = apiResponse.data?.words?.map((w) => w.word) || [];
    if (!returnedWords.includes(word)) {
      return {
        word,
        category: 'parsing_error',
        reason: 'API返回成功但未包含请求的单词',
        suggestion: '可能是API解析问题，可以尝试单独查询这个单词',
        canRetry: true,
        details: { returnedWords },
      };
    }
  }

  // 未知错误
  return {
    word,
    category: 'unknown',
    reason: '未知原因导致的失败',
    suggestion: '建议手动检查单词和相关日志，然后重试',
    canRetry: true,
    details: context,
  };
}

/**
 * 批量分析失败原因
 */
export function batchAnalyzeFailures(
  words: string[],
  apiResults: DictionaryQueryResult[],
  importErrors: Array<{ word: string; error: any }> = []
): FailureAnalysis[] {
  const analyses: FailureAnalysis[] = [];
  const importErrorMap = new Map(importErrors.map((e) => [e.word, e.error]));

  // 分析每个API结果
  apiResults.forEach((apiResult) => {
    if (apiResult.statusCode !== 200) {
      // API调用失败，影响所有请求的单词
      const requestedWords = words; // 这里可能需要更精确的映射
      requestedWords.forEach((word) => {
        if (!analyses.find((a) => a.word === word)) {
          analyses.push(analyzeWordFailure(word, { apiResponse: apiResult }));
        }
      });
    } else if (apiResult.data) {
      // API调用成功，分析具体的单词
      const responseAnalysis = analyzeApiResponse(words, apiResult);

      // 分析非法单词
      responseAnalysis.illegalWords.forEach((word) => {
        analyses.push(analyzeWordFailure(word, { apiResponse: apiResult }));
      });

      // 分析缺失单词
      responseAnalysis.missingWords.forEach((word) => {
        analyses.push(analyzeWordFailure(word, { apiResponse: apiResult }));
      });

      // 分析导入错误
      responseAnalysis.successfulWords.forEach((word) => {
        const importError = importErrorMap.get(word);
        if (importError) {
          analyses.push(analyzeWordFailure(word, { importError }));
        }
      });
    }
  });

  // 分析剩余的导入错误
  importErrors.forEach(({ word, error }) => {
    if (!analyses.find((a) => a.word === word)) {
      analyses.push(analyzeWordFailure(word, { importError: error }));
    }
  });

  return analyses;
}

/**
 * 生成失败分析报告
 */
export function generateFailureReport(analyses: FailureAnalysis[]): string {
  const report: string[] = [];

  report.push('📋 失败原因分析报告');
  report.push('='.repeat(50));

  // 统计信息
  const categoryStats = analyses.reduce(
    (acc, analysis) => {
      acc[analysis.category] = (acc[analysis.category] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  report.push('\n📊 失败类型统计:');
  Object.entries(categoryStats).forEach(([category, count]) => {
    const categoryName =
      {
        api_error: 'API错误',
        invalid_word: '无效单词',
        parsing_error: '解析错误',
        database_error: '数据库错误',
        unknown: '未知错误',
      }[category] || category;
    report.push(`  ${categoryName}: ${count} 个`);
  });

  // 可重试统计
  const retryableCount = analyses.filter((a) => a.canRetry).length;
  report.push(`\n🔄 可重试: ${retryableCount} 个`);
  report.push(`❌ 不可重试: ${analyses.length - retryableCount} 个`);

  // 详细分析
  report.push('\n📝 详细分析:');
  analyses.forEach((analysis, index) => {
    report.push(`\n${index + 1}. "${analysis.word}"`);
    report.push(`   类型: ${analysis.category}`);
    report.push(`   原因: ${analysis.reason}`);
    report.push(`   建议: ${analysis.suggestion}`);
    report.push(`   可重试: ${analysis.canRetry ? '是' : '否'}`);
  });

  return report.join('\n');
}
