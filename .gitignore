# Dependencies
/node_modules
/.pnpm-store

.pnpm-version
pnpm-lock.yaml

# testing
/coverage

# next.js
/.next/
/out/

# Production
/build

# Misc
.DS_Store
*.pem

# Keys
/.keys/

# Debug
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*
app/lib/providers/test/openaikeys/apikeys.txt

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# winsurf
/windsurf
/CodeFlattened

# husky
.husky/_

# prisma
/app/generated/prisma/*
!/app/generated/prisma/.gitkeep

# log
# 任何log文件夹
**/log/
*/logs/
/logs/
*.log
*/test-results/
**/test-results/
/test-results/

# 测试数据文件
**/test-data/
*/test-data/
/test-data/
test-data/

# 调试API（内部测试用，不对外暴露）
app/api/debug/

# @generate-docs.sh
docs/code2prompt-output

# ai-agent
.bmad-core/
.claude/
.gemini/
.cursor/
.promptx/
.serena/

