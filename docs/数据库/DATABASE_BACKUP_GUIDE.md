# 📋 数据库备份恢复完整方案

## 🎯 解决方案概述

针对你的需求，我设计了一套**灵活的分类备份恢复系统**，可以完美处理：
- ✅ Schema变更后的数据恢复  
- ✅ 新增表/字段的兼容处理
- ✅ 删除字段的优雅处理
- ✅ 分类选择性恢复

## 📊 数据分类策略

根据你的33万+条数据，分为4个类别：

| 分类 | 表名 | 记录数 | 变更频率 | 重要性 |
|------|------|---------|----------|--------|
| **CORE** | Vocabulary, Explain, Definition, WordFormat | 32万+ | 低 | 🔴 极高 |
| **AUXILIARY** | WordList, WordListEntry | 1.4万+ | 中 | 🟡 中等 |
| **USER** | User, Account, Session等 | 少量 | 高 | 🟢 中等 |
| **LEARNING** | UserWordStat, UserWordProficiency等 | 0条 | 高 | 🟢 低 |

## 🚀 使用方法

### 1️⃣ 创建备份

```bash
# 创建完整分类备份
node backup-database.js

# 备份将保存到: ./backups/backup_2024-xx-xxTxx-xx/
# ├── core/           # 核心词典数据 (32万条)
# ├── auxiliary/      # 辅助数据 (1.4万条)  
# ├── user/           # 用户数据 (4条)
# ├── learning/       # 学习数据 (0条)
# └── backup_summary.json  # 备份摘要
```

### 2️⃣ 恢复数据

```bash
# 🔍 预览模式 - 查看将要恢复什么
node restore-database.js ./backups/backup_2024-xx-xxTxx-xx core --dry-run

# 📥 恢复核心词典数据 (最安全，变更少)
node restore-database.js ./backups/backup_2024-xx-xxTxx-xx core

# 📥 恢复多个分类
node restore-database.js ./backups/backup_2024-xx-xxTxx-xx core auxiliary

# 📥 恢复全部数据
node restore-database.js ./backups/backup_2024-xx-xxTxx-xx all

# 📥 覆盖模式 (替换已存在记录)
node restore-database.js ./backups/backup_2024-xx-xxTxx-xx core --replace
```

## 🛡️ Schema变更处理

### 场景1: 新增字段
假设你在 `User` 表新增了 `nickname` 字段：

```javascript
// 在 restore-database.js 中配置默认值
const FIELD_MAPPING = {
  User: {
    defaults: {
      'nickname': '默认昵称'  // 为新字段设置默认值
    }
  }
};
```

### 场景2: 重命名字段  
假设你把 `hashedPw` 重命名为 `passwordHash`：

```javascript
const FIELD_MAPPING = {
  User: {
    'hashedPw': 'passwordHash'  // 旧字段名 → 新字段名
  }
};
```

### 场景3: 删除字段
假设你删除了某个不需要的字段：

```javascript
const FIELD_MAPPING = {
  User: {
    ignore: ['deletedField']  // 恢复时忽略这些字段
  }
};
```

### 场景4: 新增表
新表在恢复时会被自动跳过，不会影响恢复过程。

## 💡 实用场景示例

### 情况1: 开发环境重置
```bash
# 1. 备份生产数据
node backup-database.js

# 2. 开发完新功能后，恢复核心数据到新schema
node restore-database.js ./backups/backup_xxx core --dry-run  # 先预览
node restore-database.js ./backups/backup_xxx core            # 实际恢复
```

### 情况2: 部分数据恢复
```bash
# 只恢复词典核心数据，不恢复用户数据
node restore-database.js ./backups/backup_xxx core auxiliary
```

### 情况3: 灾难恢复
```bash
# 全量恢复所有数据
node restore-database.js ./backups/backup_xxx all
```

## 🔧 高级配置

### 自定义批处理大小
```bash
# 处理大量数据时调整批处理大小
node restore-database.js ./backups/backup_xxx core --batch=500
```

### 字段映射高级配置
```javascript
const FIELD_MAPPING = {
  Vocabulary: {
    // 复杂映射逻辑
    transform: (record) => {
      // 自定义转换逻辑
      if (record.oldFormat) {
        record.newFormat = convertFormat(record.oldFormat);
        delete record.oldFormat;
      }
      return record;
    }
  }
};
```

## 📈 性能特点

- **备份速度**: ~33万条记录约2-3分钟
- **恢复speed**: 支持1000条/批次，可调节
- **内存占用**: 分批处理，内存友好
- **磁盘占用**: JSON+SQL双格式，约100-200MB

## ⚠️ 注意事项

1. **备份频率**: 建议每次大规模数据导入后备份
2. **版本管理**: 备份文件名包含时间戳，便于版本管理  
3. **测试恢复**: 重要操作前先用 `--dry-run` 预览
4. **权限检查**: 确保数据库连接权限充足
5. **磁盘空间**: 确保有足够空间存储备份文件

## 🎉 总结

这套方案让你可以：
- ✅ **安全备份**: 分类备份，避免全量操作风险
- ✅ **灵活恢复**: 按需恢复指定分类数据  
- ✅ **Schema兼容**: 完美处理表结构变更
- ✅ **增量安全**: 智能跳过已存在记录
- ✅ **操作可控**: 预览模式确保操作安全

**最佳实践**: 先恢复核心词典数据(core)，再根据需要恢复其他分类。