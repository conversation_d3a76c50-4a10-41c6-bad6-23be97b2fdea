import { describe, it, expect, vi, beforeEach } from 'vitest';
import { NextRequest, NextResponse } from 'next/server';

// 简化的Mock设置
const mockRequireAuth = vi.fn();
const mockAuthContext = {
  user: { id: 'user-123', name: 'Test User', email: '<EMAIL>' },
  method: 'jwt' as const,
  clientType: 'mobile' as const,
  scopes: ['word:read', 'word:write'],
  ipAddress: '127.0.0.1',
  userAgent: 'test-agent',
};

// Mock the AuthMiddleware module
vi.mock('../AuthMiddleware', () => ({
  requireAuth: mockRequireAuth,
}));

describe('AuthMiddleware 基础测试', () => {
  let mockRequest: NextRequest;
  let mockHandler: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockRequest = new NextRequest('http://localhost:3000/api/test', {
      headers: {
        'authorization': 'Bearer test-token',
        'user-agent': 'test-agent',
      },
    });

    mockHandler = vi.fn().mockResolvedValue(
      NextResponse.json({ success: true, data: 'test response' })
    );

    // 设置默认的requireAuth行为
    mockRequireAuth.mockImplementation((options = {}) => {
      return (handler: any) => {
        return async (request: NextRequest) => {
          // 简化的认证逻辑
          const authHeader = request.headers.get('authorization');
          if (!authHeader?.startsWith('Bearer ')) {
            return NextResponse.json(
              { error: 'No valid authentication found' },
              { status: 401 }
            );
          }
          
          // 检查客户端类型限制
          if (options.clientTypes && !options.clientTypes.includes(mockAuthContext.clientType)) {
            return NextResponse.json(
              { error: `Client type '${mockAuthContext.clientType}' not allowed` },
              { status: 403 }
            );
          }
          
          // 检查权限作用域
          if (options.scopes && options.scopes.length > 0) {
            const hasRequiredScope = options.scopes.some((scope: string) =>
              mockAuthContext.scopes.includes(scope)
            );
            if (!hasRequiredScope) {
              return NextResponse.json(
                { error: `Required scopes: ${options.scopes.join(', ')}` },
                { status: 403 }
              );
            }
          }
          
          return handler(request, mockAuthContext);
        };
      };
    });
  });

  describe('基础认证功能', () => {
    it('应该成功创建认证中间件', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth();
      const wrappedHandler = middleware(mockHandler);

      // Act
      const response = await wrappedHandler(mockRequest);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(mockHandler).toHaveBeenCalledWith(mockRequest, mockAuthContext);
    });

    it('应该拒绝没有Authorization头的请求', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth();
      const wrappedHandler = middleware(mockHandler);
      
      const requestWithoutAuth = new NextRequest('http://localhost:3000/api/test');

      // Act
      const response = await wrappedHandler(requestWithoutAuth);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(401);
      expect(data.error).toBe('No valid authentication found');
      expect(mockHandler).not.toHaveBeenCalled();
    });

    it('应该支持客户端类型限制', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth({
        clientTypes: ['web'], // 只允许web，但mockAuthContext是mobile
      });
      const wrappedHandler = middleware(mockHandler);

      // Act
      const response = await wrappedHandler(mockRequest);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(data.error).toBe("Client type 'mobile' not allowed");
      expect(mockHandler).not.toHaveBeenCalled();
    });

    it('应该支持权限作用域验证', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth({
        scopes: ['admin:write'], // 需要admin权限，但用户只有word权限
      });
      const wrappedHandler = middleware(mockHandler);

      // Act
      const response = await wrappedHandler(mockRequest);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(403);
      expect(data.error).toBe('Required scopes: admin:write');
      expect(mockHandler).not.toHaveBeenCalled();
    });

    it('应该在满足所有条件时允许访问', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth({
        clientTypes: ['mobile', 'web'], // 允许mobile
        scopes: ['word:read'], // 用户有这个权限
      });
      const wrappedHandler = middleware(mockHandler);

      // Act
      const response = await wrappedHandler(mockRequest);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(mockHandler).toHaveBeenCalledWith(mockRequest, mockAuthContext);
    });
  });

  describe('配置选项测试', () => {
    it('应该支持空配置（默认行为）', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth(); // 无配置
      const wrappedHandler = middleware(mockHandler);

      // Act
      const response = await wrappedHandler(mockRequest);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });

    it('应该支持复合配置', async () => {
      // Arrange
      const { requireAuth } = await import('../AuthMiddleware');
      const middleware = requireAuth({
        clientTypes: ['mobile'],
        scopes: ['word:read', 'word:write'],
      });
      const wrappedHandler = middleware(mockHandler);

      // Act
      const response = await wrappedHandler(mockRequest);
      const data = await response.json();

      // Assert
      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
    });
  });

  describe('Mock函数验证', () => {
    it('应该正确调用requireAuth函数', async () => {
      // Arrange & Act
      const { requireAuth } = await import('../AuthMiddleware');
      const options = { clientTypes: ['mobile'] };
      requireAuth(options);

      // Assert
      expect(mockRequireAuth).toHaveBeenCalledWith(options);
    });

    it('应该验证Mock函数的调用次数', async () => {
      // Arrange & Act
      const { requireAuth } = await import('../AuthMiddleware');
      requireAuth();
      requireAuth({ scopes: ['test'] });

      // Assert
      expect(mockRequireAuth).toHaveBeenCalledTimes(2);
    });
  });
});