/**
 * 高并发数据库连接测试
 * 验证Prisma在高并发下是否会创建更多连接
 */

import { prisma } from '../app/lib/4-infrastructure/database/prisma';

// 模拟慢查询来强制使用更多连接
async function slowQuery(id: number, delay: number = 1000): Promise<any> {
  const startTime = Date.now();

  // 使用原生SQL执行一个慢查询
  const result = await prisma.$queryRaw`
    SELECT 
      v.id, 
      v.word,
      pg_sleep(${delay / 1000.0}),
      NOW() as query_time
    FROM "Vocabulary" v 
    WHERE v.id = ${id}
    LIMIT 1
  `;

  const endTime = Date.now();
  console.log(`🔍 查询 ${id} 完成，耗时: ${endTime - startTime}ms`);

  return result;
}

async function testHighConcurrency() {
  console.log('🚀 开始高并发数据库连接测试...\n');

  try {
    // 1. 初始连接
    await prisma.$connect();
    console.log('✅ 初始连接建立\n');

    // 2. 获取初始连接池状态
    console.log('📊 初始连接池状态:');
    const initialMetrics = await (prisma as any).$metrics?.json();
    if (initialMetrics) {
      const poolMetrics =
        initialMetrics.gauges?.filter((gauge: any) => gauge.key.includes('pool_connections')) || [];

      poolMetrics.forEach((metric: any) => {
        console.log(`   ${metric.description}: ${metric.value}`);
      });
    }
    console.log('');

    // 3. 启动高并发慢查询
    console.log('🔥 启动50个并发慢查询（每个查询1秒）...');
    const concurrentQueries = Array.from({ length: 50 }, (_, i) => slowQuery(i + 1, 1000));

    // 4. 在查询执行过程中监控连接池状态
    const monitorInterval = setInterval(async () => {
      try {
        const metrics = await (prisma as any).$metrics?.json();
        if (metrics) {
          const openConnections =
            metrics.gauges?.find((g: any) => g.key === 'prisma_pool_connections_open')?.value || 0;

          const busyConnections =
            metrics.gauges?.find((g: any) => g.key === 'prisma_pool_connections_busy')?.value || 0;

          const idleConnections =
            metrics.gauges?.find((g: any) => g.key === 'prisma_pool_connections_idle')?.value || 0;

          console.log(
            `📊 实时连接池状态: 总连接=${openConnections}, 忙碌=${busyConnections}, 空闲=${idleConnections}`
          );
        }
      } catch (error) {
        // 忽略监控错误
      }
    }, 500); // 每500ms监控一次

    // 5. 等待所有查询完成
    const startTime = Date.now();
    const results = await Promise.all(concurrentQueries);
    const endTime = Date.now();

    clearInterval(monitorInterval);

    console.log(`\n✅ 所有查询完成！`);
    console.log(`   - 总查询数: ${results.length}`);
    console.log(`   - 总耗时: ${endTime - startTime}ms`);
    console.log(`   - 平均耗时: ${Math.round((endTime - startTime) / results.length)}ms`);

    // 6. 获取最终连接池状态
    console.log('\n📊 最终连接池状态:');
    const finalMetrics = await (prisma as any).$metrics?.json();
    if (finalMetrics) {
      const poolMetrics =
        finalMetrics.gauges?.filter((gauge: any) => gauge.key.includes('pool_connections')) || [];

      poolMetrics.forEach((metric: any) => {
        console.log(`   ${metric.description}: ${metric.value}`);
      });

      const counters =
        finalMetrics.counters?.filter((counter: any) => counter.key.includes('pool_connections')) ||
        [];

      counters.forEach((counter: any) => {
        console.log(`   ${counter.description}: ${counter.value}`);
      });
    }

    // 7. 分析结果
    console.log('\n🔍 性能分析:');
    const totalTime = endTime - startTime;
    const expectedSequentialTime = 50 * 1000; // 50个查询 × 1秒
    const parallelismFactor = expectedSequentialTime / totalTime;

    console.log(`   - 如果串行执行预期时间: ${expectedSequentialTime}ms`);
    console.log(`   - 实际并行执行时间: ${totalTime}ms`);
    console.log(`   - 并行度: ${parallelismFactor.toFixed(2)}x`);

    if (parallelismFactor > 10) {
      console.log('   ✅ 连接池工作正常，实现了良好的并行性');
    } else if (parallelismFactor > 5) {
      console.log('   ⚠️ 连接池部分工作，但可能存在瓶颈');
    } else {
      console.log('   ❌ 连接池可能存在问题，并行性较差');
    }
  } catch (error) {
    console.error('❌ 高并发测试失败:', error);
  } finally {
    await prisma.$disconnect();
    console.log('\n🔌 数据库连接已关闭');
  }
}

// 运行测试
testHighConcurrency().catch(console.error);
